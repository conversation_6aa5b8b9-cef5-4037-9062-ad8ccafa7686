package com.qcc.frame.commons.ienum;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ReportGroupMappingEnum {
    CORP_G("CORP_G", "C", UnitGroupEnum.CN_UNIT),
    CORP_G_HK("CORP_G_HK", "C", UnitGroupEnum.HK_UNIT),
    CORP_G_SG("CORP_G_SG", "C", UnitGroupEnum.SG_UNIT),
    CORP_G_MY("CORP_G_MY", "C", UnitGroupEnum.MY_UNIT),
    CORP_G_TH("CORP_G_TH", "C", UnitGroupEnum.TH_UNIT),
    CORP_G_PH("CORP_G_PH", "C", UnitGroupEnum.PH_UNIT),
    CORP_G_GB("CORP_G_GB", "C", UnitGroupEnum.GB_UNIT),
    CORP_G_AU("CORP_G_AU", "C", UnitGroupEnum.AU_UNIT),
    CORP_G_NZ("CORP_G_NZ", "C", UnitGroupEnum.NZ_UNIT),
    CORP_G_PK("CORP_G_PK", "C", UnitGroupEnum.PK_UNIT),
    CORP_G_JO("CORP_G_JO", "C", UnitGroupEnum.JO_UNIT),
    CORP_G_KW("CORP_G_KW", "C", UnitGroupEnum.KW_UNIT),
    CORP_G_OM("CORP_G_OM", "C", UnitGroupEnum.OM_UNIT),
    CORP_G_SA("CORP_G_SA", "C", UnitGroupEnum.SA_UNIT),
    CORP_G_AE("CORP_G_AE", "C", UnitGroupEnum.AE_UNIT),
    
    /*
    // 中东地区
    CORP_G_IR("CORP_G_IR", "C", UnitGroupEnum.IR_UNIT),
    CORP_G_LB("CORP_G_LB", "C", UnitGroupEnum.LB_UNIT),
    CORP_G_QA("CORP_G_QA", "C", UnitGroupEnum.QA_UNIT),
    CORP_G_PS("CORP_G_PS", "C", UnitGroupEnum.PS_UNIT),
    CORP_G_SY("CORP_G_SY", "C", UnitGroupEnum.SY_UNIT),
    CORP_G_TR("CORP_G_TR", "C", UnitGroupEnum.TR_UNIT),
    CORP_G_YE("CORP_G_YE", "C", UnitGroupEnum.YE_UNIT),
    CORP_G_BH("CORP_G_BH", "C", UnitGroupEnum.BH_UNIT),
    CORP_G_IQ("CORP_G_IQ", "C", UnitGroupEnum.IQ_UNIT),
    CORP_G_EG("CORP_G_EG", "C", UnitGroupEnum.EG_UNIT),
    CORP_G_LY("CORP_G_LY", "C", UnitGroupEnum.LY_UNIT),
    CORP_G_TN("CORP_G_TN", "C", UnitGroupEnum.TN_UNIT),
    CORP_G_DZ("CORP_G_DZ", "C", UnitGroupEnum.DZ_UNIT),
    CORP_G_SD("CORP_G_SD", "C", UnitGroupEnum.SD_UNIT),
    
    // 非洲地区
    CORP_G_KM("CORP_G_KM", "C", UnitGroupEnum.KM_UNIT),
    CORP_G_DJ("CORP_G_DJ", "C", UnitGroupEnum.DJ_UNIT),
    CORP_G_ER("CORP_G_ER", "C", UnitGroupEnum.ER_UNIT),
    CORP_G_ET("CORP_G_ET", "C", UnitGroupEnum.ET_UNIT),
    CORP_G_KE("CORP_G_KE", "C", UnitGroupEnum.KE_UNIT),
    CORP_G_MG("CORP_G_MG", "C", UnitGroupEnum.MG_UNIT),
    CORP_G_SO("CORP_G_SO", "C", UnitGroupEnum.SO_UNIT),
    CORP_G_SS("CORP_G_SS", "C", UnitGroupEnum.SS_UNIT),
    CORP_G_TZ("CORP_G_TZ", "C", UnitGroupEnum.TZ_UNIT),
    CORP_G_UG("CORP_G_UG", "C", UnitGroupEnum.UG_UNIT),
    CORP_G_BI("CORP_G_BI", "C", UnitGroupEnum.BI_UNIT),
    CORP_G_CM("CORP_G_CM", "C", UnitGroupEnum.CM_UNIT),
    CORP_G_CF("CORP_G_CF", "C", UnitGroupEnum.CF_UNIT),
    CORP_G_TD("CORP_G_TD", "C", UnitGroupEnum.TD_UNIT),
    CORP_G_CG("CORP_G_CG", "C", UnitGroupEnum.CG_UNIT),
    CORP_G_CD("CORP_G_CD", "C", UnitGroupEnum.CD_UNIT),
    CORP_G_GQ("CORP_G_GQ", "C", UnitGroupEnum.GQ_UNIT),
    CORP_G_GA("CORP_G_GA", "C", UnitGroupEnum.GA_UNIT),
    CORP_G_ST("CORP_G_ST", "C", UnitGroupEnum.ST_UNIT),
    CORP_G_BJ("CORP_G_BJ", "C", UnitGroupEnum.BJ_UNIT),
    CORP_G_BF("CORP_G_BF", "C", UnitGroupEnum.BF_UNIT),
    CORP_G_CV("CORP_G_CV", "C", UnitGroupEnum.CV_UNIT),
    CORP_G_CI("CORP_G_CI", "C", UnitGroupEnum.CI_UNIT),
    CORP_G_GM("CORP_G_GM", "C", UnitGroupEnum.GM_UNIT),
    CORP_G_GH("CORP_G_GH", "C", UnitGroupEnum.GH_UNIT),
    CORP_G_GN("CORP_G_GN", "C", UnitGroupEnum.GN_UNIT),
    CORP_G_GW("CORP_G_GW", "C", UnitGroupEnum.GW_UNIT),
    CORP_G_LR("CORP_G_LR", "C", UnitGroupEnum.LR_UNIT),
    CORP_G_ML("CORP_G_ML", "C", UnitGroupEnum.ML_UNIT),
    CORP_G_MR("CORP_G_MR", "C", UnitGroupEnum.MR_UNIT),
    CORP_G_NE("CORP_G_NE", "C", UnitGroupEnum.NE_UNIT),
    CORP_G_NG("CORP_G_NG", "C", UnitGroupEnum.NG_UNIT),
    CORP_G_SN("CORP_G_SN", "C", UnitGroupEnum.SN_UNIT),
    CORP_G_SL("CORP_G_SL", "C", UnitGroupEnum.SL_UNIT),
    CORP_G_TG("CORP_G_TG", "C", UnitGroupEnum.TG_UNIT),
    CORP_G_AO("CORP_G_AO", "C", UnitGroupEnum.AO_UNIT),
    CORP_G_BW("CORP_G_BW", "C", UnitGroupEnum.BW_UNIT),
    CORP_G_SZ("CORP_G_SZ", "C", UnitGroupEnum.SZ_UNIT),
    CORP_G_LS("CORP_G_LS", "C", UnitGroupEnum.LS_UNIT),
    CORP_G_NA("CORP_G_NA", "C", UnitGroupEnum.NA_UNIT),
    CORP_G_MW("CORP_G_MW", "C", UnitGroupEnum.MW_UNIT),
    CORP_G_MZ("CORP_G_MZ", "C", UnitGroupEnum.MZ_UNIT),
    CORP_G_ZM("CORP_G_ZM", "C", UnitGroupEnum.ZM_UNIT),
    CORP_G_ZW("CORP_G_ZW", "C", UnitGroupEnum.ZW_UNIT),
    */
    
    CORP_G_CY("CORP_G_CY", "C", UnitGroupEnum.CY_UNIT),
    CORP_G_MA("CORP_G_MA", "C", UnitGroupEnum.MA_UNIT),
    CORP_G_MU("CORP_G_MU", "C", UnitGroupEnum.MU_UNIT),
    CORP_G_RW("CORP_G_RW", "C", UnitGroupEnum.RW_UNIT),
    CORP_G_SC("CORP_G_SC", "C", UnitGroupEnum.SC_UNIT),
    CORP_G_ZA("CORP_G_ZA", "C", UnitGroupEnum.ZA_UNIT),

    CORP_G_TW("CORP_G_TW", "C", UnitGroupEnum.TW_UNIT),
    CORP_G_MO("CORP_G_MO", "C", UnitGroupEnum.MO_UNIT),
    CORP_G_JP("CORP_G_JP", "C", UnitGroupEnum.JP_UNIT),
    CORP_G_KR("CORP_G_KR", "C", UnitGroupEnum.KR_UNIT),
    CORP_G_VN("CORP_G_VN", "C", UnitGroupEnum.VN_UNIT),
    CORP_G_ID("CORP_G_ID", "C", UnitGroupEnum.ID_UNIT),
    CORP_G_KH("CORP_G_KH", "C", UnitGroupEnum.KH_UNIT),
    CORP_G_MM("CORP_G_MM", "C", UnitGroupEnum.MM_UNIT),
    CORP_G_BN("CORP_G_BN", "C", UnitGroupEnum.BN_UNIT),
    CORP_G_IN("CORP_G_IN", "C", UnitGroupEnum.IN_UNIT),
    CORP_G_IL("CORP_G_IL", "C", UnitGroupEnum.IL_UNIT),
    CORP_G_RU("CORP_G_RU", "C", UnitGroupEnum.RU_UNIT),
    CORP_G_CZ("CORP_G_CZ", "C", UnitGroupEnum.CZ_UNIT),
    CORP_G_SK("CORP_G_SK", "C", UnitGroupEnum.SK_UNIT),
    CORP_G_HU("CORP_G_HU", "C", UnitGroupEnum.HU_UNIT),
    CORP_G_PL("CORP_G_PL", "C", UnitGroupEnum.PL_UNIT),
    CORP_G_RO("CORP_G_RO", "C", UnitGroupEnum.RO_UNIT),
    CORP_G_EE("CORP_G_EE", "C", UnitGroupEnum.EE_UNIT),
    CORP_G_LV("CORP_G_LV", "C", UnitGroupEnum.LV_UNIT),
    CORP_G_LT("CORP_G_LT", "C", UnitGroupEnum.LT_UNIT),
    CORP_G_DK("CORP_G_DK", "C", UnitGroupEnum.DK_UNIT),
    CORP_G_FI("CORP_G_FI", "C", UnitGroupEnum.FI_UNIT),
    CORP_G_NO("CORP_G_NO", "C", UnitGroupEnum.NO_UNIT),
    CORP_G_SE("CORP_G_SE", "C", UnitGroupEnum.SE_UNIT),
    CORP_G_FR("CORP_G_FR", "C", UnitGroupEnum.FR_UNIT),
    CORP_G_DE("CORP_G_DE", "C", UnitGroupEnum.DE_UNIT),
    CORP_G_NL("CORP_G_NL", "C", UnitGroupEnum.NL_UNIT),
    CORP_G_BE("CORP_G_BE", "C", UnitGroupEnum.BE_UNIT),
    CORP_G_LU("CORP_G_LU", "C", UnitGroupEnum.LU_UNIT),
    CORP_G_CH("CORP_G_CH", "C", UnitGroupEnum.CH_UNIT),
    CORP_G_AT("CORP_G_AT", "C", UnitGroupEnum.AT_UNIT),
    CORP_G_IE("CORP_G_IE", "C", UnitGroupEnum.IE_UNIT),
    CORP_G_IT("CORP_G_IT", "C", UnitGroupEnum.IT_UNIT),
    CORP_G_ES("CORP_G_ES", "C", UnitGroupEnum.ES_UNIT),
    CORP_G_GR("CORP_G_GR", "C", UnitGroupEnum.GR_UNIT),
    CORP_G_MT("CORP_G_MT", "C", UnitGroupEnum.MT_UNIT),
    CORP_G_SI("CORP_G_SI", "C", UnitGroupEnum.SI_UNIT),
    CORP_G_HR("CORP_G_HR", "C", UnitGroupEnum.HR_UNIT),
    CORP_G_MH("CORP_G_MH", "C", UnitGroupEnum.MH_UNIT),
    CORP_G_WS("CORP_G_WS", "C", UnitGroupEnum.WS_UNIT),
    CORP_G_US_AL("CORP_G_US_AL", "C", UnitGroupEnum.US_AL_UNIT),
    CORP_G_US_AK("CORP_G_US_AK", "C", UnitGroupEnum.US_AK_UNIT),
    CORP_G_US_AZ("CORP_G_US_AZ", "C", UnitGroupEnum.US_AZ_UNIT),
    CORP_G_US_CA("CORP_G_US_CA", "C", UnitGroupEnum.US_CA_UNIT),
    CORP_G_US_CO("CORP_G_US_CO", "C", UnitGroupEnum.US_CO_UNIT),
    CORP_G_US_CT("CORP_G_US_CT", "C", UnitGroupEnum.US_CT_UNIT),
    CORP_G_US_DE("CORP_G_US_DE", "C", UnitGroupEnum.US_DE_UNIT),
    CORP_G_US_FL("CORP_G_US_FL", "C", UnitGroupEnum.US_FL_UNIT),
    CORP_G_US_HI("CORP_G_US_HI", "C", UnitGroupEnum.US_HI_UNIT),
    CORP_G_US_ID("CORP_G_US_ID", "C", UnitGroupEnum.US_ID_UNIT),
    CORP_G_US_KS("CORP_G_US_KS", "C", UnitGroupEnum.US_KS_UNIT),
    CORP_G_US_KY("CORP_G_US_KY", "C", UnitGroupEnum.US_KY_UNIT),
    CORP_G_US_LA("CORP_G_US_LA", "C", UnitGroupEnum.US_LA_UNIT),
    CORP_G_US_ME("CORP_G_US_ME", "C", UnitGroupEnum.US_ME_UNIT),
    CORP_G_US_MI("CORP_G_US_MI", "C", UnitGroupEnum.US_MI_UNIT),
    CORP_G_US_MN("CORP_G_US_MN", "C", UnitGroupEnum.US_MN_UNIT),
    CORP_G_US_MS("CORP_G_US_MS", "C", UnitGroupEnum.US_MS_UNIT),
    CORP_G_US_NJ("CORP_G_US_NJ", "C", UnitGroupEnum.US_NJ_UNIT),
    CORP_G_US_NY("CORP_G_US_NY", "C", UnitGroupEnum.US_NY_UNIT),
    CORP_G_US_NC("CORP_G_US_NC", "C", UnitGroupEnum.US_NC_UNIT),
    CORP_G_US_ND("CORP_G_US_ND", "C", UnitGroupEnum.US_ND_UNIT),
    CORP_G_US_RI("CORP_G_US_RI", "C", UnitGroupEnum.US_RI_UNIT),
    CORP_G_US_UT("CORP_G_US_UT", "C", UnitGroupEnum.US_UT_UNIT),
    CORP_G_US_WA("CORP_G_US_WA", "C", UnitGroupEnum.US_WA_UNIT),
    CORP_G_US_WV("CORP_G_US_WV", "C", UnitGroupEnum.US_WV_UNIT),
    CORP_G_US_WY("CORP_G_US_WY", "C", UnitGroupEnum.US_WY_UNIT),
    CORP_G_CA("CORP_G_CA", "C", UnitGroupEnum.CA_UNIT),
    CORP_G_BM("CORP_G_BM", "C", UnitGroupEnum.BM_UNIT),
    CORP_G_JM("CORP_G_JM", "C", UnitGroupEnum.JM_UNIT),
    CORP_G_PR("CORP_G_PR", "C", UnitGroupEnum.PR_UNIT),
    CORP_G_KY("CORP_G_KY", "C", UnitGroupEnum.KY_UNIT),
    CORP_G_VG("CORP_G_VG", "C", UnitGroupEnum.VG_UNIT),
    CORP_G_BZ("CORP_G_BZ", "C", UnitGroupEnum.BZ_UNIT),
    CORP_G_BR("CORP_G_BR", "C", UnitGroupEnum.BR_UNIT),
    CORP_G_AR("CORP_G_AR", "C", UnitGroupEnum.AR_UNIT),

    PERS_G("PERS_G", "P"),
    PERS_NOT_RPT("PERS_NOT_RPT", "P"),
    SCAN_G("SCAN_G", "P"), // added for lvcy v2.0.9 KNZT-5362
    MAP_G("MAP_G", ""),
    VERIFY_G("VERIFY_G", "P"),
    BENEFIT_G("BENEFIT_G", ""),
    ;
    private final String rptGroup;
    private final String keyNoType;
    private final UnitGroupEnum unitGroupEnum;

    ReportGroupMappingEnum(String rptGroup, String keyNoType, UnitGroupEnum unitGroupEnum) {
        this.rptGroup = rptGroup;
        this.keyNoType = keyNoType;
        this.unitGroupEnum = unitGroupEnum;
    }

    ReportGroupMappingEnum(String rptGroup, String keyNoType) {
        this.rptGroup = rptGroup;
        this.keyNoType = keyNoType;
        this.unitGroupEnum = null;
    }

    public String getRptGroup() {
        return rptGroup;
    }

    public String getKeyNoType() {
        return keyNoType;
    }

    public UnitGroupEnum getUnitGroupEnum() {
        return unitGroupEnum;
    }

    public String getNationCode() {
        return Optional.ofNullable(unitGroupEnum)
                .map(UnitGroupEnum::getGlobalAreaEnum)
                .map(GlobalAreaEnum::getNameCode)
                .orElse(null);
    }

    public static String getKeyNoTypeByReportGroup(String id) {
        for(ReportGroupMappingEnum f: ReportGroupMappingEnum.values()) {
            if(StringUtils.equals(f.getRptGroup(), id)) {
                return f.getKeyNoType();
            }
        }
        return "";
    }

    public static ReportGroupMappingEnum getReportGroupByCode(String code) {
        for(ReportGroupMappingEnum f: ReportGroupMappingEnum.values()) {
            if(StringUtils.equals(f.getRptGroup(), code)) {
                return f;
            }
        }
        return null;
    }

    public static ReportGroupMappingEnum getByRegionCode4Corp(String nationCode, String regPlaceCode) {
        UnitGroupEnum unitGroupEnum = UnitGroupEnum.getByRegionCode(nationCode, regPlaceCode);
        if (unitGroupEnum == null) {
            return null;
        }
        for (ReportGroupMappingEnum enumItem : ReportGroupMappingEnum.values()) {
            if (enumItem.getUnitGroupEnum() == unitGroupEnum && StringUtils.equals(enumItem.getKeyNoType(), "C")) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * added for v2.1.5 fengsw KNZT-6325
     * 根据nationCode找到对应的报告产品分组
     *
     * @param nationCode
     * @return
     */
    public static String getRptGroupByRegionCode4Corp(String nationCode, String regPlaceCode) {
        ReportGroupMappingEnum mappingEnum = getByRegionCode4Corp(nationCode, regPlaceCode);
        return mappingEnum == null ? null : mappingEnum.getRptGroup();
    }

    /**
     * 获取是海外并且基础数据是内部的列表
     *
     * @return
     */
    public static List<ReportGroupMappingEnum> getBasicOverseaEnumList() {
        return Stream.of(values()).filter(enumItem -> {
            if (enumItem.getUnitGroupEnum() != null) {
                return UnitGroupEnum.getBasicOverseaEnumList().contains(enumItem.getUnitGroupEnum());
            }
            return false;
        }).collect(Collectors.toList());
    }

}
