package com.qcc.frame.commons.ienum;

import lombok.Getter;

/**
 * api code 枚举
 *
 * <AUTHOR>
 * @datetime 2024/9/12 14:26
 */
@Getter
public enum ApiCodeEnum {
    BASIC("1001", "KYC Basic"),
    UBO("2001", "UBO"),
    ADVANCED("3001", "KYC Advanced"),
    VERIFY_CORP("4001", "Identity Verification (Three-Field Verification)"),
    LITE("029", "KYC Lite"),
    MERCHANT_ONBOARDING("5001", "Merchant Onboarding"),
    FIN_TAX("6001", "Financial & Tax"),
    PERS_BASIC("7001", "Executive Snapshot"),
    LITIGATION("8001", "CN Litigation"),
    ;
    private final String code;
    private final String codeDesc;

    ApiCodeEnum(String code, String codeDesc) {
        this.code = code;
        this.codeDesc = codeDesc;
    }

    public static ApiCodeEnum getEnumByCode(String code) {
        for (ApiCodeEnum enumItem : values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (ApiCodeEnum modeEnum : ApiCodeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getCodeDesc();
            }
        }
        return "";
    }
}
