package com.qcc.frame.commons.ienum;

import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权益类型
 * <AUTHOR>
 * @datetime 8/5/2025 7:09 下午
 */
@Getter
public enum BenefitTypeEnum {
    BASE("BASE", "Basic Monitor", "KYC043", 10),
    BASE_UBO("BASE_UBO", "Basic & UBO Monitor", "KYC053", 20),
    NEWS("NEWS", "News Monitor", "KYC033", 30),
    BASE_NEWS("BASE_NEWS", "Basic & News Monitor", "KYC063", 40),
    BASE_NEWS_UBO("BASE_NEWS_UBO", "Basic, News & UBO Monitor", "KYC073", 50),

    MAP_CN_COMM("MAP_CN_COMM", "CN-Data Maps", null, 60),
    MAP_CN_REL_3ENTITY("MAP_CN_REL_3ENTITY", "CN-Find Relationships (3 Entities)", null, 70),
    ;

    private final String code;
    private final String name;
    private final String kzzProductCode;
    private final Integer sort;

    BenefitTypeEnum(String code, String name, String kzzProductCode, Integer sort) {
        this.code = code;
        this.name = name;
        this.kzzProductCode = kzzProductCode;
        this.sort = sort;
    }


    public static BenefitTypeEnum getByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getSortByCode(String code) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (benefitTypeEnum.getCode().equals(code)) {
                return benefitTypeEnum.getSort();
            }
        }
        return 9999;
    }

    public static BenefitTypeEnum getByKzzProductCode(String kzzProductCode) {
        for (BenefitTypeEnum benefitTypeEnum : BenefitTypeEnum.values()) {
            if (StringUtils.equals(benefitTypeEnum.getKzzProductCode(), kzzProductCode)) {
                return benefitTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getMonitorBenefitTypeList() {
        return Lists.newArrayList(BASE.getCode(), BASE_UBO.getCode(), NEWS.getCode(), BASE_NEWS.getCode(), BASE_NEWS_UBO.getCode());
    }

    public static List<String> withNews() {
        return Lists.newArrayList(NEWS.getCode(), BASE_NEWS.getCode(), BASE_NEWS_UBO.getCode());
    }

    public static List<String> getMapBenefitTypeList() {
        return Lists.newArrayList(MAP_CN_COMM.getCode(), MAP_CN_REL_3ENTITY.getCode());
    }

    public static List<String> getTrialMapBenefitTypeList() {
        return Lists.newArrayList(MAP_CN_COMM.getCode(), MAP_CN_REL_3ENTITY.getCode(), MAP_CN_COMM.getCode());
    }


    private static final Map<String, List<String>> BENEFIT_REPORT_TYPE = new HashMap<String, List<String>>(10) {
        {
            put(MAP_CN_REL_3ENTITY.getCode(), Lists.newArrayList(ReportTypeEnum.MAP_RELATION_3.getCode()));
            put(MAP_CN_COMM.getCode(), Lists.newArrayList(ReportTypeEnum.MAP_OWNERSHIP.getCode(), ReportTypeEnum.MAP_NETWORK.getCode()));
        }
    };

    public static String getBenefitByReportType(String reportType) {
        for (Map.Entry<String, List<String>> entry : BENEFIT_REPORT_TYPE.entrySet()) {
            if (entry.getValue().contains(reportType)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 根据benefitType获取对应的reportType列表（支持一对多）
     *
     * @param benefitType 权益类型
     * @return 对应的reportType列表
     */
    public static List<String> getReportTypesByBenefit(String benefitType) {
        return BENEFIT_REPORT_TYPE.getOrDefault(benefitType, Lists.newArrayList());
    }

}