package com.qcc.frame.jee.commons.utils;

import com.alibaba.fastjson.JSON;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.EncryptionConstants;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.WriterProperties;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.PdfDocumentContentParser;
import com.itextpdf.kernel.pdf.canvas.parser.listener.IPdfTextLocation;
import com.itextpdf.kernel.pdf.canvas.parser.listener.RegexBasedLocationExtractionStrategy;
import com.itextpdf.layout.font.FontProvider;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * pdf 工具类
 * added for v1.9.5 KNZT-4117
 * <AUTHOR>
 * @datetime 2024/8/30 14:37
 */
public class PdfUtils {
    protected static Logger logger = LoggerFactory.getLogger(PdfUtils.class);

    /**
     * 根据模版以及参数，生成pdf<br/>
     * <strong>注意该方法生成file后，需要手动删除</strong>
     *
     * @param templateId
     * @param templateContent
     * @param paramMap
     * @return File
     */
    public static File createPdf(String templateId, String templateContent, Map<String, Object> paramMap) throws IOException {
        String uuid = IdGenUtil.uuid();
        String filePath = uuid + ".pdf";
        File file = new File(filePath);
        logger.info("createPdf begin, templateId:{}, paramMap:{}", templateId, JSON.toJSONString(paramMap));
        try (FileOutputStream out = new FileOutputStream(file)) {
            String content = FreemarkerUtils.parseTemplate(templateId, templateContent, paramMap);
            FontProvider fontProvider = new DefaultFontProvider(true, true, true);
            FontProgram fontProgram = FontProgramFactory.createFont("font/Inter-Regular.otf");
            fontProvider.addFont(fontProgram);
            FontProgram fontProgramBold = FontProgramFactory.createFont("font/Inter-Bold.otf");
            fontProvider.addFont(fontProgramBold);
            FontProgram fontProgramCn = FontProgramFactory.createFont("font/SanJiJinSongJianTi-2.ttf");
            fontProvider.addFont(fontProgramCn);
            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(content, out, converterProperties);
            logger.info("createPdf end, templateId:{}", templateId);
        } catch (Exception e) {
            logger.error("createPdf error, templateId:{}", templateId,e);
            throw e;
        }
        return file;
    }

    /**
     * 生成pdf并上传华为云，并且把文件删除
     *
     * @param route
     * @param templateId
     * @param templateContent
     * @param paramMap
     * @return String
     */
    public static String createAndUploadPdf(String route, String templateId, String templateContent, Map<String, Object> paramMap) throws IOException {
        File pdf = null;
        try {
            pdf = createPdf(templateId, templateContent, paramMap);
            String url = HuaweiObsServUtils.getInstance().putObject(route, pdf);
            logger.info("uploadPdf end, templateId:{}, url:{}", templateId, url);
            return url;
        } catch (Exception e) {
            logger.error("", e);
            throw e;
        } finally {
            if (Objects.nonNull(pdf)) {
                FileUtils.deleteFile(pdf.getPath());
            }
        }
    }


    /**
     * 根据关键词遮挡pdf内容，并上传华为云
     * added for lvcy v2.0.6 KNZT-5664
     *
     * @param file       文件
     * @param outFile    保存的文件
     * @param keyword    关键词
     * @param maxPageNum 处理最大页码数
     * @return String
     */
    public static String blockOutByKeywordAndUpload(File file, File outFile, String keyword, int maxPageNum) throws IOException {
        if (file == null || !file.exists()) {
            logger.error("blockOutByKeyword file doesn't exist");
            return null;
        }
        String resUrl = null;
        logger.info("blockOutByKeyword start, file:{}, keyword:{}", file.getName(), keyword);
        WriterProperties wp = new WriterProperties();
        //禁止打印 复制 等操作
        wp.setStandardEncryption(null, null, 0, EncryptionConstants.DO_NOT_ENCRYPT_METADATA);
        try (FileOutputStream fileOutputStream = new FileOutputStream(outFile);
             PdfDocument pdfDoc = new PdfDocument(new PdfReader(file), new PdfWriter(fileOutputStream, wp))) {
            for (int pageNum = 1; pageNum <= Math.min(maxPageNum, pdfDoc.getNumberOfPages()); ++pageNum) {
                PdfPage page = pdfDoc.getPage(pageNum);
                RegexBasedLocationExtractionStrategy strategy = new RegexBasedLocationExtractionStrategy(keyword);
                PdfDocumentContentParser parser = new PdfDocumentContentParser(pdfDoc);
                parser.processContent(pageNum, strategy);
                Collection<IPdfTextLocation> locationsOnPage = strategy.getResultantLocations();

                Rectangle pageSize = page.getPageSize();
                if (!locationsOnPage.isEmpty()) {
                    Rectangle rectangle = extractRectangle(locationsOnPage);

                    PdfCanvas canvas = new PdfCanvas(page);
                    //初始阶段完成 开始替换
                    canvas.saveState();
                    //背景覆盖
                    canvas.setFillColor(ColorConstants.WHITE);
                    // 定位
                    // 限制矩形的高度，确保它们不会超出页面的边界
                    float height = Math.min(rectangle.getHeight() + 20, pageSize.getHeight() - rectangle.getY());
                    canvas.rectangle(1.0F, (int) rectangle.getY() - 10.0F, pageSize.getWidth()- 1.0F, height);
                    //填充
                    canvas.fill();
                    canvas.endText();
                    //还原状态
                    canvas.restoreState();
                }
            }

        } catch (Exception e) {
            logger.error("blockOutByKeyword error, ", e);
            throw e;
        } finally {
            if (outFile.exists()) {
                resUrl = HuaweiObsServUtils.getInstance().putObject(outFile.getName(), outFile);
                logger.info("blockOutByKeyword end, url:{}", resUrl);
            }
            FileUtils.deleteFile(outFile.getAbsolutePath());
            FileUtils.deleteFile(file.getAbsolutePath());
        }
        return resUrl;
    }


    private static Rectangle extractRectangle(Collection<IPdfTextLocation> locations) {
        float minX = Float.MAX_VALUE, maxX = Float.MIN_VALUE, minY = Float.MAX_VALUE, maxY = Float.MIN_VALUE;
        for (IPdfTextLocation location : locations) {
            Rectangle rect = location.getRectangle();
            minX = Math.min(minX, rect.getX());
            maxX = Math.max(maxX, rect.getX() + rect.getWidth());
            minY = Math.min(minY, rect.getY());
            maxY = Math.max(maxY, rect.getY() + rect.getHeight());
        }
        return new Rectangle(minX, minY, maxX - minX, maxY - minY);
    }
}
