package com.qcc.frame.jee.commons.utils;

import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.jee.commons.service.MessageException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import shade.jetbrains.annotations.Contract;

import java.util.Collection;

/**
 * added for v1.8.8 KNZT-3324
 * 业务异常工具类
 *
 * <AUTHOR>
 * @datetime 2024/6/26 13:44
 */
public class MsgExceptionUtils {
    private static final Logger logger = LoggerFactory.getLogger(MsgExceptionUtils.class);

    public static void failBuild(boolean fail, String message, String... args) throws MessageException {
        if (fail) {
            logArgs(args);
            throw new MessageException(message, args);
        }
    }

    public static void failBuild(boolean fail) throws MessageException {
        if (fail) {
            throw new MessageException("err.access");
        }
    }

    public static void failBuild(boolean fail, MessageExceptionEnum messageExceptionEnum, String... args) throws MessageException {
        if (fail) {
            logArgs(args);
            throw new MessageException(messageExceptionEnum, args);
        }
    }

    public static void failBuild(boolean fail, CommonExcepEnum commonExcepEnum, String... args) throws MessageException {
        failBuild(fail, commonExcepEnum.getMessage(), args);
    }

    // 构建默认的MessageException，并且记录日志
    // 适用于上层捕获后不记录异常栈的场景，方便定位异常发生的原始位置
    public static void failBuildAndLogError(boolean fail, Logger logger, String logMessage) throws MessageException {
        if (fail) {
            logger.error(logMessage);
            throw new MessageException("err.access");
        }
    }

    public static void failBuildAndLogWarn(boolean fail, Logger logger, String logMessage) throws MessageException {
        if (fail) {
            logger.warn(logMessage);
            throw new MessageException("err.access");
        }
    }

    @Contract("null -> !null; !null -> !null")
    public static void checkIsNull(Object obj) throws MessageException {
        checkIsNull(obj, "err.access", "");
    }


    public static void checkIsNull(Object obj, String message, String... args) throws MessageException {
        if (obj == null) {
            logArgs(args);
            throw new MessageException(message, args);
        }
        if (obj instanceof Collection && ((Collection<?>) obj).isEmpty()) {
            logArgs(args);
            throw new MessageException(message, args);
        }
        if (obj instanceof String && StringUtils.isBlank((String) obj)) {
            logArgs(args);
            throw new MessageException(message, args);
        }
    }


    public static void checkIsNull(Object obj, CommonExcepEnum commonExcepEnum, String... args) throws MessageException {
        checkIsNull(obj, commonExcepEnum.getMessage(), args);
    }

    private static void logArgs(String[] args) {
        if (args != null && args.length > 0) {
            logger.info("MsgExceptionUtils args: {}", StringUtils.join(args, ", "));
        }
    }


    public enum CommonExcepEnum {
        PARAM_IS_NULL("err.param.invalid"),
        RECORD_NOT_FOUND("err.system.record.not.exist"),
        ;

        private final String message;

        CommonExcepEnum(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }
    }

}
