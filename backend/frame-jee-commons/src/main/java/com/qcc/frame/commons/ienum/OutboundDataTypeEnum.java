package com.qcc.frame.commons.ienum;

/**
 * added for v1.6.5 KNZT-2804
 * 额度keyNo类型
 *
 * <AUTHOR>
 * @datetime 2024/4/2 14:12
 */
public enum OutboundDataTypeEnum {

    // updated for v1.8.5 KNZT-3802
    LEGAL_REP("LEGAL_REP", "Legal Representative", Integer.MAX_VALUE),
    SHARE("SHARE", "Shareholders", Integer.MAX_VALUE),
    KEY_PERS("KEY_PERS", "Key Persons", Integer.MAX_VALUE),
    UBO("UBO", "UBO", Integer.MAX_VALUE),
    HIST_LEGAL_REP("HIST_LEGAL_REP", "Historical Legal Representative", Integer.MAX_VALUE),
    HIST_SHARE("HIST_SHARE", "Historical Shareholders", Integer.MAX_VALUE),
    HIST_KEY_PERS("HIST_KEY_PERS", "Historical Key Persons", Integer.MAX_VALUE),
    HQ_CN("HQ_CN", "Headquarters in China", Integer.MAX_VALUE),
    DISH_JUDGE("DISH_JUDGE", "Dishonest Judgement Debtors", 1000),
    HIST_DISH_JUDGE("HIST_DISH_JUDGE", "Historical Dishonest Judgement Debtors", 1000),
    HIGH_CONS_REST("HIGH_CONS_REST", "Restriction of High Consumption", 1000),
    HIST_HIGH_CONS_REST("HIST_HIGH_CONS_REST", "Historical Restriction of High Consumption", 1000),
    REST_DEP("REST_DEP", "Restricted Departure", 1000),
    EQ_PLED("EQ_PLED", "Equity Pledge", Integer.MAX_VALUE),
    ACTUAL_CONT("ACTUAL_CONT", "Actual Controller", Integer.MAX_VALUE),
    EXECU("EXECU", "EXECUTIVE", Integer.MAX_VALUE),
    ;

    private final String code;
    private final String desc;
    private final int limitRecords; // 维度限制条数, 需要是CommTblGlobalOutboundListSyncTaskNewTranService.pageSize整数倍

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }

    public int getLimitRecords() {
        return limitRecords;
    }

    OutboundDataTypeEnum(String code, String desc, int limitRecords) {
        this.code = code;
        this.desc = desc;
        this.limitRecords = limitRecords;
    }

    public static String getDesc(String code) {
        for (OutboundDataTypeEnum modeEnum : OutboundDataTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getDesc();
            }
        }
        return "";
    }
}
