package com.qcc.frame.jee.commons.model.json;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.GroupItem;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value="接口返回JSON对象", description="")
public class JsonResultList<T> extends BaseJsonResult {
	@ApiModelProperty(value = "接口返回列表 - 用于服务器端分页", example = "", position=40)
	private List<T> resultList;
	@ApiModelProperty(value = "记录总数 - 用于服务器端分页", example = "", position=50)
	private long totalCount;
	@ApiModelProperty(position=60)
	private String jsonStr;
	@ApiModelProperty(value = "统计信息", position=70)
	private List<GroupItem> groupItems;

	public static <T> JsonResultList<T> buildSuccess(List<T> list) {
		JsonResultList<T> result = new JsonResultList<T>();
		result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResultList(list);
		return result;
	}

	public static <T> JsonResultList<T> buildSuccess(List<T> list, long totalCount) {
		JsonResultList<T> result = new JsonResultList<T>();
		result.setStatus(Constants.Result.SUCCESS_STR);
		result.setResultList(list);
		result.setTotalCount(totalCount);
		return result;
	}

    public static <T> JsonResultList<T> buildSuccess(List<T> list, long totalCount, List<GroupItem> groupItems) {
        JsonResultList<T> result = new JsonResultList<T>();
        result.setStatus(Constants.Result.SUCCESS_STR);
        result.setResultList(list);
        result.setTotalCount(totalCount);
        result.setGroupItems(groupItems);
        return result;
    }

	public static <T> JsonResultList<T> buildSuccess(Page<T> page) {
		return buildSuccess(page.getList(), page.getCount());
	}

	public static <T> JsonResultList<T> buildFail(String msg, String ...args) {
		JsonResultList<T> result = new JsonResultList<T>();
		result.setStatus(Constants.Result.FALSE_STR);
		result.setMsg(I18NUtil.getMessage(msg, args));
		return result;
	}

	public static <T> JsonResultList<T> buildFail(MessageException e) {
		JsonResultList<T> result = new JsonResultList<>();
		result.setStatus(e.getErrCode() != null ? e.getErrCode() : Constants.Result.FALSE_STR);
		result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
		return result;
	}

	public List<T> getResultList() {
		return resultList;
	}
	public void setResultList(List<T> resultList) {
		this.resultList = resultList;
	}
	public long getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}
	public String getJsonStr() {
		return jsonStr;
	}
	public void setJsonStr(String jsonStr) {
		this.jsonStr = jsonStr;
	}

	public List<GroupItem> getGroupItems() {
		return groupItems;
	}

	public void setGroupItems(List<GroupItem> groupItems) {
		this.groupItems = groupItems;
	}
}
