package com.qcc.frame.jee.commons.model;

import java.util.ArrayList;
import java.util.List;

public class PageDataBO<T> {

    private List<T> list;
    private Long total;

    public PageDataBO() {
        this.list = new ArrayList<>(0);
        this.total = 0L;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public static <T> PageDataBO<T> build(List<T> list, Long total) {
        PageDataBO<T> pageDataBO = new PageDataBO<>();
        pageDataBO.setList(list);
        pageDataBO.setTotal(total);
        return pageDataBO;
    }
}
