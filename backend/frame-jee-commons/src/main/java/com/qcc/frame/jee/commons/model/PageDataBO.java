package com.qcc.frame.jee.commons.model;

import com.qcc.frame.jee.commons.model.json.JsonResultList;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PageDataBO<T> {

    private List<T> list;
    private Long total;
    private List<GroupItem> groupItems;

    public PageDataBO() {
        this.list = new ArrayList<>(0);
        this.total = 0L;
    }

    public static <T> PageDataBO<T> build(List<T> list, Long total) {
        PageDataBO<T> pageDataBO = new PageDataBO<>();
        pageDataBO.setList(list);
        pageDataBO.setTotal(total);
        return pageDataBO;
    }

    public static <T> PageDataBO<T> build(List<T> list, Long total, List<GroupItem> groupItems) {
        PageDataBO<T> pageDataBO = new PageDataBO<>();
        pageDataBO.setList(list);
        pageDataBO.setTotal(total);
        pageDataBO.setGroupItems(groupItems);
        return pageDataBO;
    }

    public static <T> PageDataBO<T> build(JsonResultList<T> jsonResultList) {
        PageDataBO<T> pageDataBO = new PageDataBO<>();
        pageDataBO.setList(jsonResultList.getResultList());
        pageDataBO.setTotal(jsonResultList.getTotalCount());
        pageDataBO.setGroupItems(jsonResultList.getGroupItems());
        return pageDataBO;
    }
}
