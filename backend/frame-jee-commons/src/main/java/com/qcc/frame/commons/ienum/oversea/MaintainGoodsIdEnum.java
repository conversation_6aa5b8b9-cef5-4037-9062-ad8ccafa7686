package com.qcc.frame.commons.ienum.oversea;

import com.qcc.frame.commons.ienum.ReportTypeEnum;
import lombok.Getter;

import java.util.List;

@Getter
public enum MaintainGoodsIdEnum {
    HK_ICRIS("185", ReportTypeEnum.hkIcrisCorpList()),
    HK_IRD("kychkird", ReportTypeEnum.hkIrdCorpList()),
    SG("176", ReportTypeEnum.sgCorpList()),
    MY("282", ReportTypeEnum.myCorpList()),
    AU("kycau001", ReportTypeEnum.auCorpList()),
    NZ("kycnz001", ReportTypeEnum.nzCorpList()),
    TW("kyctw001", ReportTypeEnum.twCorpList()),
    ;

    private final String goodsId;
    private final List<String> reportTypes;

    MaintainGoodsIdEnum(String goodsId, List<String> reportTypes) {
        this.goodsId = goodsId;
        this.reportTypes = reportTypes;
    }

    public static String getGoodsIdByReportType(String reportType) {
        for (MaintainGoodsIdEnum item : values()) {
            if (item.getReportTypes().contains(reportType)) {
                return item.getGoodsId();
            }
        }
        return null;
    }
}
