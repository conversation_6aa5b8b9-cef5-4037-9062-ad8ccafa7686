package com.qcc.frame.jee.commons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.util.EncryptUtil;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.service.ServiceException;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.Map.Entry;

public class HttpUtils {
	protected static Logger logger = LoggerFactory.getLogger(HttpUtils.class);

	public final static String GLOBAL_HEADER_CLIENT_ID = "global-client-id"; //added for v1.5.6 KNZT-2545

	//	private static final int HTTP_CLIENT_TIMEOUT = 10000;
    // TODO 临时默认接口调用超时时间 40000ms = 40s
    private static final int HTTP_CLIENT_TIMEOUT = 40000;

    public static final int HTTP_CLIENT_TIMEOUT_8000 = 8000;
	public static final int HTTP_CLIENT_TIMEOUT_10000 = 10000;
	public static final int HTTP_CLIENT_TIMEOUT_20000 = 20000;
	public static final int HTTP_CLIENT_TIMEOUT_30000 = 30000;
	public static final int HTTP_CLIENT_TIMEOUT_65000 = 65000;
	public static final int HTTP_CLIENT_TIMEOUT_120000 = 120000;
	public static final int HTTP_CLIENT_TIMEOUT_300000 = 300000;
	public static final int HTTP_CLIENT_TIMEOUT_600000 = 600000;
	public static final int HTTP_CLIENT_TIMEOUT_900000 = 900000;
		
	
	public static String get(String url, Map<String, String> paramMap) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(HTTP_CLIENT_TIMEOUT).setConnectionRequestTimeout(HTTP_CLIENT_TIMEOUT)
		        .setSocketTimeout(HTTP_CLIENT_TIMEOUT).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;
		long startTime = System.currentTimeMillis();
		String uri = "";
		try {
			URIBuilder builder = new URIBuilder(url);
			Iterator<String> it = paramMap.keySet().iterator();
			while(it.hasNext()) {
				String key = it.next();
				builder.addParameter(key, paramMap.get(key));
			}
			
			httpGet = new HttpGet(builder.build());
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			uri = httpGet.getURI().getPath();
	
	        String data = EntityUtils.toString(response.getEntity(),"UTF-8");
	        return data;
	    } catch (ClientProtocolException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (ParseException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (IOException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (URISyntaxException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
	    }
	}
	
	public static String get(String url, Map<String, String> paramMap, int timeout) throws MessageException {
		return get(url, paramMap, timeout, null);
	}
	
	public static String get(String url, Map<String, String> paramMap, int timeout, Map<String, String> headerMap) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;
		long startTime = System.currentTimeMillis();
		String logUrl = null;
		try {
			URIBuilder builder = new URIBuilder(url);
			Iterator<String> it = paramMap.keySet().iterator();
			while(it.hasNext()) {
				String key = it.next();
				builder.addParameter(key, paramMap.get(key));
			}
			
			httpGet = new HttpGet(builder.build());
			logUrl = builder.build().toString();
			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					httpGet.addHeader(entry.getKey(), entry.getValue());
				}
			}
			
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
	
	        String data = EntityUtils.toString(response.getEntity(),"UTF-8");
	        return data;
	    } catch (ClientProtocolException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (ParseException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (IOException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (URISyntaxException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", logUrl, startTime, endTime, endTime - startTime);
	    }
	}
	
	/**
	 * 调用企查查云聚接口
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String getYunJuApi(String url, Map<String, String> paramMap, int timeout) throws MessageException {
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
		paramMap.put("key", user.getCompany().getYunjuKey());
		return get(url, paramMap, timeout, getHeaderMapForPro(user.getCompany().getYunjuKey(), user.getCompany().getYunjuSecretKey()));
	}

	/**
	 * added for v1.5.6 KNZT-2545 调用企查查云聚接口 并将请求ip传入请求头
	 *
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String getYunJuApiWithReqIp(String url, Map<String, String> paramMap, int timeout) throws MessageException {
			User user = UserUtils.getUser();
			if (user == null || user.getCompany() == null) {
				throw new MessageException("warn.login.status.timeout");
			}
			paramMap.put("key", user.getCompany().getYunjuKey());
			Map<String, String> headerMap = getHeaderMapForPro(user.getCompany().getYunjuKey(), user.getCompany().getYunjuSecretKey());
			String clientIp = WebContextHolder.getClientIp();
			headerMap.put(GLOBAL_HEADER_CLIENT_ID, clientIp);
			return get(url, paramMap, timeout, headerMap, false);
	}

	/**
	 * added for v1.5.7 KNZT-2601 
	 * 自定义GET是否需要打印日志
	 * 
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @param headerMap
	 * @param needLogFlag
	 * @return
	 * @throws MessageException
	 */
	public static String get(String url, Map<String, String> paramMap, int timeout, Map<String, String> headerMap, boolean needLogFlag) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;
		long startTime = System.currentTimeMillis();
		String logUrl = null;
		try {
			URIBuilder builder = new URIBuilder(url);
			for (String key : paramMap.keySet()) {
				builder.addParameter(key, paramMap.get(key));
			}
			httpGet = new HttpGet(builder.build());
			logUrl = builder.build().toString();
			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					httpGet.addHeader(entry.getKey(), entry.getValue());
				}
			}
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			return EntityUtils.toString(response.getEntity(),"UTF-8");
		} catch (ParseException | IOException | URISyntaxException e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
			if(needLogFlag) {
				// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
				long endTime = System.currentTimeMillis();
				logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", logUrl, startTime, endTime, endTime - startTime);
			}
		}
	}

	/**
	 * 调用国内接口 - 针对公开接口
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String getYunJuApiNoUser(String url, Map<String, String> paramMap, int timeout) throws MessageException {
		String openApiQccComKey = Global.getConfig("openApi.qcc.com.key");
		String OPEN_API_QCC_COM_SECRETKEY = Global.getConfig("openApi.qcc.com.secretKey");
		paramMap.put("key", openApiQccComKey);
		return get(url, paramMap, timeout, getHeaderMapForPro(openApiQccComKey, OPEN_API_QCC_COM_SECRETKEY));
	}

	/**
	 * added for v1.0.8 KNZT-451
	 * updated for KNZT-1421【优化】【国际版】国际版后端调用开放平台服务解锁香港企业时，,增加companyid入参并传递到开放平台接口，用于开放平台计费
	 * updated for v2.1.0 chenbl KNZT-5238
	 * 开放平台海外服务需要 传入companyId进行计费处理
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String getYunJuGlobalApi(String url, Map<String, String> paramMap, int timeout) throws MessageException {
		return getYunJuGlobalApi(url, paramMap, null, timeout);
	}

	// added for v2.1.0 chenbl KNZT-5238
	public static String getYunJuGlobalApi(String url, Map<String, String> paramMap, Map<String, String> headerMap, int timeout) throws MessageException {

		String openApiGlobalQccComKey = Global.getConfig("openApi.global.qcc.com.key");
		paramMap.put("key", openApiGlobalQccComKey);
//		return get(url, paramMap, timeout, getHeaderMapForPro(openApiGlobalQccComKey, openApiGlobalQccComSecretKey));
		User user = UserUtils.getUser();
		if (user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
        Map<String, String> headerMapForPro = getHeaderMapForPro(openApiGlobalQccComKey, Global.getConfig("openApi.global.qcc.com.secretKey"));
        headerMapForPro.put("CompanyId", user.getCompany().getId());
        if (MapUtils.isNotEmpty(headerMap)) {
            headerMapForPro.putAll(headerMap);
        }
        return get(url, paramMap, timeout, headerMapForPro);
	}
	/**
	 * 调用企查查云聚接口
	 * @param url
	 * @param paramMap
	 * @return
	 * @throws MessageException
	 */
	public static String postYunJuApi(String url, Map<String, String> paramMap) throws MessageException{
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
		paramMap.put("key", user.getCompany().getYunjuKey());
		return post(url,paramMap,getHeaderMapForPro(user.getCompany().getYunjuKey(), user.getCompany().getYunjuSecretKey()));
	}

	/**
	 * 调用国内接口 - 针对公开接口
	 * @param url
	 * @param paramMap
	 * @return
	 * @throws MessageException
	 */
	public static String postJsonYunJuApiNoUser(String url, Map<String, Object> paramMap) throws MessageException{
		String openApiQccComKey = Global.getConfig("openApi.qcc.com.key");
		String openApiQccComSecretkey = Global.getConfig("openApi.qcc.com.secretKey");
		url = url + "?key=" + openApiQccComKey;
		return postJson(url,paramMap, getHeaderMapForPro(openApiQccComKey, openApiQccComSecretkey));
	}

	public static String postJsonYunJuApi(String url, Map<String, Object> paramMap) throws MessageException{
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}


		return postJson(url,paramMap,getHeaderMapForPro(user.getCompany().getYunjuKey(), user.getCompany().getYunjuSecretKey()));
	}
	
	/**
	 * 调用企查查专业版接口
	 * @param url
	 * @param paramMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String getProApi(String url, Map<String, String> paramMap, int timeout) throws MessageException {
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
		paramMap.put("key", user.getCompany().getProKey());
		return get(url, paramMap, timeout, getHeaderMapForPro(user.getCompany().getProKey(), user.getCompany().getProSecretKey()));
	}

	public static String postProApi(String url, Map<String, String> paramMap) throws MessageException{
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
		paramMap.put("key", user.getCompany().getProKey());
		return post(url,paramMap,getHeaderMapForPro(user.getCompany().getProKey(), user.getCompany().getProSecretKey()));
	}

	public static String postJsonProApi(String url, Map<String, Object> paramMap) throws MessageException{
		User user = UserUtils.getUser();
		if(user == null || user.getCompany() == null) {
			throw new MessageException("warn.login.status.timeout");
		}
		paramMap.put("proKey", user.getCompany().getProKey());
		return postJson(url,paramMap,getHeaderMapForPro(user.getCompany().getProKey(), user.getCompany().getProSecretKey()));
	}

	/**
	 * added for v1.6.5 KNZT-2810
	 * 
	 * @param url
	 * @param paramMap
	 * @return
	 * @throws MessageException
	 */
	public static String postJsonProApiNoUser(String url, Map<String, Object> paramMap) throws MessageException{
		String proQccComKey = Global.getConfig("pro.qcc.com.key");
		String proQccComSecretKey = Global.getConfig("pro.qcc.com.secretKey");
		paramMap.put("proKey", proQccComKey);
		return postJson(url,paramMap,getHeaderMapForPro(proQccComKey, proQccComSecretKey));
	}

	// added for KNZT-887 -内部调用
	public static String postJson4ProApi(String url, Map <String, String> paramsMap, int timeout) throws MessageException {
		String jsonStr = JSONObject.toJSONString(paramsMap);
		return postJson(url, jsonStr, timeout, true);
	}
    /**
     * <开放型接口调用第三方接口、无用户身份验证>
     *
     * @param url:
     * @param paramMap:
     * @param headerMap:
     * @param timeout:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/9/22 2:12 下午
     */
    public static String getOpenProApi(String url, Map<String, String> paramMap, Map<String, String> headerMap, int timeout) throws MessageException {
        return get(url, paramMap, timeout, headerMap);
    }

    public static String postOpenProApi(String url, Map<String, String> paramMap, Map<String, String> headerMap) throws MessageException {
        return post(url, paramMap, headerMap);
    }


	public static String post(String url, Map <String, String> paramsMap) {
		return post(url, paramsMap, null);
	}
	
	public static String post(String url, Map <String, String> paramsMap, Map<String, String> headerMap) {
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(HTTP_CLIENT_TIMEOUT_30000).setConnectionRequestTimeout(HTTP_CLIENT_TIMEOUT_30000)
		        .setSocketTimeout(HTTP_CLIENT_TIMEOUT_30000).build();
        CloseableHttpClient client = HttpClients.createDefault();
        String responseText = "";
        CloseableHttpResponse response = null;
        HttpPost method = null;
		long startTime = System.currentTimeMillis();
		String uri = "";
        try {
            method = new HttpPost(url);
            method.setConfig(requestConfig);
            if (paramsMap != null) {
                List <NameValuePair> paramList = new ArrayList <NameValuePair> ();
                for (Map.Entry <String, String> param: paramsMap.entrySet()) {
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), param.getValue());
                    paramList.add(pair);
                }
                method.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));
            }
            
            if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					method.addHeader(entry.getKey(), entry.getValue());
				}
			}
            
            response = client.execute(method);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                responseText = EntityUtils.toString(entity, "UTF-8");
            }
			uri = method.getURI().getPath();
        } catch (Exception e) {
            logger.error("", e);
            throw new ServiceException(e);
        } finally {
        	close(response, method, client);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
        }
        return responseText;
    }
	
	public static void close(CloseableHttpResponse response, HttpPost method, CloseableHttpClient client) {
		try {
        	if(response != null) {
        		response.close();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
        try {
        	if(method != null) {
        		method.releaseConnection();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
        try {
        	if(client != null) {
        		client.close();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
	}
	
	public static void close(CloseableHttpResponse response, HttpGet method, CloseableHttpClient client) {
		try {
        	if(response != null) {
        		response.close();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
        try {
        	if(method != null) {
        		method.releaseConnection();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
        try {
        	if(client != null) {
        		client.close();
        	}
        } catch (Exception e) {
        	logger.error("", e);
        }
	}

	public static void close(CloseableHttpResponse response, HttpPut method, CloseableHttpClient client) {
		try {
			if (response != null) {
				response.close();
			}
		} catch (Exception e) {
			logger.error("", e);
		}
		try {
			if (method != null) {
				method.releaseConnection();
			}
		} catch (Exception e) {
			logger.error("", e);
		}
		try {
			if (client != null) {
				client.close();
			}
		} catch (Exception e) {
			logger.error("", e);
		}
	}
	
	/**
	 * 如果通过post json访问node js接口, 需要调用该方法
	 * @param url
	 * @param paramsMap
	 * @param timeout
	 * @return
	 * @throws MessageException
	 */
	public static String postJson4NodeJS(String url, Map <String, String> paramsMap, int timeout) throws MessageException {
		String jsonStr = JSONObject.toJSONString(paramsMap);
		return postJson(url, jsonStr, timeout, true);
	}
	
	public static String postJson(String url, Map <String, String> paramsMap) throws MessageException {
		String jsonStr = JSONObject.toJSONString(paramsMap);
		return postJson(url, jsonStr, HTTP_CLIENT_TIMEOUT);
	}
	
	public static String postJson(String url, String jsonStr) throws MessageException {
		return postJson(url, jsonStr, HTTP_CLIENT_TIMEOUT);
	}
	
	
	public static String postJson(String url, String jsonStr, int timeout) throws MessageException {
		return postJson(url, jsonStr, timeout, false);
	}
	public static String postJson(String url, String jsonStr, int timeout, boolean nodeJsFlag) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		CloseableHttpClient client = HttpClients.createDefault();
        String responseText = "";
        CloseableHttpResponse response = null;
        HttpPost method = null;
		long startTime = System.currentTimeMillis();
		String uri = "";
        try {
            method = new HttpPost(url);
            method.setConfig(requestConfig);
            StringEntity entityStr = new StringEntity(jsonStr,"UTF-8");//解决中文乱码问题
            if(!nodeJsFlag) {
            	entityStr.setContentEncoding("UTF-8");
            }
            entityStr.setContentType("application/json");
            method.setEntity(entityStr);
            
            response = client.execute(method);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                responseText = EntityUtils.toString(entity, "UTF-8");
            }
			uri = method.getURI().getPath();
        } catch (Exception e) {
            logger.error("", e);
            throw new ServiceException(e);
        } finally {
        	close(response, method, client);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
        }
        return responseText;
	}

	public static String postJson(String url, Map<String, Object> paramMap, Map<String, String> headerMap) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(HTTP_CLIENT_TIMEOUT_65000).setConnectionRequestTimeout(HTTP_CLIENT_TIMEOUT_65000)
				.setSocketTimeout(HTTP_CLIENT_TIMEOUT_65000).build();
		CloseableHttpClient client = HttpClients.createDefault();
		String responseText = "";
		CloseableHttpResponse response = null;
		HttpPost method = null;
		long startTime = System.currentTimeMillis();
		try {
			method = new HttpPost(url);
			method.setConfig(requestConfig);
			StringEntity entityStr = new StringEntity(JSON.toJSONString(paramMap),"UTF-8");//解决中文乱码问题
			entityStr.setContentType("application/json");
			method.setEntity(entityStr);

			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					method.addHeader(entry.getKey(), entry.getValue());
				}
			}

			response = client.execute(method);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseText = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (Exception e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, method, client);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", url + "?" + JSON.toJSONString(paramMap), startTime, endTime, endTime - startTime);
		}
		return responseText;
	}
	
	public static String get(String url, Map<String, String> paramMap, int retryTime, int timeout) throws Exception {
		return get(url, paramMap, retryTime, timeout, null);
	}
	
	public static String get(String url, Map<String, String> paramMap, int retryTime, int timeout, Map<String, String> headerMap) throws Exception {
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		
		try {
			URIBuilder builder = new URIBuilder(url);
			Iterator<String> it = paramMap.keySet().iterator();
			while(it.hasNext()) {
				String key = it.next();
				builder.addParameter(key, paramMap.get(key));
			}
			
			String data = null;
			for(int i=0; i <= retryTime; i++) {
				CloseableHttpClient httpclient = HttpClients.createDefault();
				CloseableHttpResponse response = null;
				HttpGet httpGet = null;
				long startTime = System.currentTimeMillis();
				String uri = "";
				try {
					httpGet = new HttpGet(builder.build());
					if(headerMap != null) {
						for (Entry<String, String> entry : headerMap.entrySet()) {
							httpGet.addHeader(entry.getKey(), entry.getValue());
						}
					}
					
					httpGet.setConfig(requestConfig);
					response = httpclient.execute(httpGet);
			
			        data = EntityUtils.toString(response.getEntity(),"UTF-8");
					uri = httpGet.getURI().getPath();
					logger.info("requesturl[" + uri + "], cost [" + (System.currentTimeMillis() - startTime) + "]ms");
			        break;
				} catch(Exception e) {
					if(e instanceof ConnectTimeoutException || e instanceof SocketTimeoutException) {
						if(i>0) {
							logger.error("retry " + i + " time fail");
						}
						if(i == retryTime) {
							throw e;
						}
					} else {
						throw e;
					}
				} finally {
					close(response, httpGet, httpclient);
				}
			}
			return data;
	    } catch (Exception e) {
	    	if(e instanceof ConnectTimeoutException || e instanceof SocketTimeoutException) {
				logger.error("retry " + retryTime + " times, still get timeout:", e);
	    	} else {
	    		logger.error("", e);
	    	}
	    	throw e;
	    }
	}
	
	/**
	 * 得到文件内容
	 * @param url
	 * @param timeout
	 * @return
	 */
	public static String getFileContent(String url, int timeout) {
		if(StringUtils.isBlank(url)) {
			return null;
		}
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;

		try {
			URIBuilder builder = new URIBuilder(url);
			httpGet = new HttpGet(builder.build());
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				HttpEntity entity = response.getEntity();
				InputStream in = entity.getContent();
				return StreamUtils.InputStreamTOString(in);
			}
			return null;
	    } catch (Exception e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } finally {
			close(response, httpGet, httpclient);
	    }
	}
	
	public static File getFile(String url, String suffix) {
		return getFile(url, suffix, HTTP_CLIENT_TIMEOUT);
	}
	
	public static File getFile(String url, String suffix, int timeout) {
		if(StringUtils.isBlank(url)) {
			return null;
		}
		if(url.indexOf(" ") != -1) { 
			url = StringUtils.replace(url, " ", "%20");
		}
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;
		long startTime = System.currentTimeMillis();
		String uri = "";

		File result = null;
		try {
			result = File.createTempFile("TMP_REQ", suffix);
			URIBuilder builder = new URIBuilder(url);
			
			httpGet = new HttpGet(builder.build());
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				HttpEntity entity = response.getEntity();
				InputStream in = entity.getContent();
				FileUtils.copyInputStreamToFile(in, result);
				return result;
			}
			uri = httpGet.getURI().getPath();
			return null;
	    } catch (ClientProtocolException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (ParseException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (IOException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (URISyntaxException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
	    }
	}
	
	public static void downloadUrl(HttpServletResponse resp, String url, String fileName) {
		downloadUrl(resp, url, fileName, HTTP_CLIENT_TIMEOUT);
	}
	
	public static void downloadUrl(HttpServletResponse resp, String url, String fileName, int timeout) {
		if(StringUtils.isBlank(url)) {
			return;
		}
		if(url.indexOf(" ") != -1) { 
			url = StringUtils.replace(url, " ", "%20");
		}
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
		        .setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;

		long startTime = System.currentTimeMillis();
		String uri = "";		
		try {
			URIBuilder builder = new URIBuilder(url);
			
			httpGet = new HttpGet(builder.build());
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				HttpEntity entity = response.getEntity();
				InputStream in = entity.getContent();
				FileUtils.downloadFile(resp, fileName, in);
			}
			uri = httpGet.getURI().getPath();
			return;
	    } catch (ClientProtocolException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (ParseException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (IOException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (URISyntaxException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
	    }
	}

	public static void postDownload(HttpServletResponse resp, String url, String jsonStr, String fileName) {
		postDownload(resp, url, jsonStr, fileName, HTTP_CLIENT_TIMEOUT);
	}
	public static void postDownload(HttpServletResponse resp, String url, String jsonStr, String fileName, int timeout) {
		if(StringUtils.isBlank(url)) {
			return;
		}
		if(url.indexOf(" ") != -1) {
			url = StringUtils.replace(url, " ", "%20");
		}
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient client = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		HttpPost method = null;
		long startTime = System.currentTimeMillis();
		String uri = "";

		try {
			method = new HttpPost(url);
			method.setConfig(requestConfig);
			StringEntity entityStr = new StringEntity(jsonStr,"UTF-8");//解决中文乱码问题
			entityStr.setContentType("application/json");
			method.setEntity(entityStr);

			response = client.execute(method);

			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				HttpEntity entity = response.getEntity();
				InputStream in = entity.getContent();
				FileUtils.downloadFile(resp, fileName, in);
			}
			uri = method.getURI().getPath();
			return;
		} catch (ClientProtocolException e) {
			logger.error("", e);
			throw new ServiceException(e);
		} catch (ParseException e) {
			logger.error("", e);
			throw new ServiceException(e);
		} catch (IOException e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, method, client);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
		}
	}
	
	public static File getFile(String url) {
		if(StringUtils.isBlank(url)) {
			return null;
		}
		if(url.indexOf(" ") != -1) { 
			url = StringUtils.replace(url, " ", "%20");
		}
		RequestConfig requestConfig = RequestConfig.custom()
		        .setConnectTimeout(HTTP_CLIENT_TIMEOUT).setConnectionRequestTimeout(HTTP_CLIENT_TIMEOUT)
		        .setSocketTimeout(HTTP_CLIENT_TIMEOUT).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();//Creates CloseableHttpClient instance with default configuration.
		HttpGet httpGet = null;
		CloseableHttpResponse response = null;

		File result = null;
		try {
			result = File.createTempFile("TMP_REQ", "." + StringUtils.getFileExt(url));
			URIBuilder builder = new URIBuilder(url);
			
			httpGet = new HttpGet(builder.build());
			httpGet.setConfig(requestConfig);
			response = httpclient.execute(httpGet);
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
				HttpEntity entity = response.getEntity();
				InputStream in = entity.getContent();
				FileUtils.copyInputStreamToFile(in, result);
				return result;
			}
			return null;
	    } catch (ClientProtocolException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (ParseException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (IOException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
	    } catch (URISyntaxException e) {
	    	logger.error("", e);
	    	throw new ServiceException(e);
		} finally {
			close(response, httpGet, httpclient);
	    }
	}

	// added for v1.7.6-2 KNZT-3361
	public static String getOpenApiAdmin(String url, Map<String, String> paramMap) throws MessageException {
		String openApiGlobalAdminQccComKey = Global.getConfig("openApi.global.admin.qcc.com.key");
		String openApiGlobalAdminQccComSecretKey = Global.getConfig("openApi.global.admin.qcc.com.secretKey");
		paramMap.put("key", openApiGlobalAdminQccComKey);
		Map<String, String> headerMap = HttpUtils.getHeaderMapForOpenApi(openApiGlobalAdminQccComKey, openApiGlobalAdminQccComSecretKey);
		return get(url, paramMap, HTTP_CLIENT_TIMEOUT_10000, headerMap);
	}

	public static String postOpenApiAdmin(String url, Map<String, Object> paramMap) throws MessageException {
		return postJsonForOpenApiAdmin(url, JSON.toJSONString(paramMap));
	}

	public static String postJsonForOpenApiAdmin(String url, String jsonStr) throws MessageException {
		String openApiGlobalAdminQccComKey = Global.getConfig("openApi.global.admin.qcc.com.key");
		String openApiGlobalAdminQccComSecretKey = Global.getConfig("openApi.global.admin.qcc.com.secretKey");
		if (StringUtils.isNotBlank(url)) {
			url = url + "?key=" + openApiGlobalAdminQccComKey;
		}
		// updated for KNZT-2205【bug】在kyc修改账号服务结束时间，国际版api后台，操作记录中操作人不正确
		Map<String, String> headerMap = HttpUtils.getHeaderMapForOpenApi(openApiGlobalAdminQccComKey, openApiGlobalAdminQccComSecretKey);
		return postJson(url, jsonStr, headerMap, HTTP_CLIENT_TIMEOUT_10000);
	}

	public static String postJson(String url, String jsonStr, Map<String, String> headerMap, int timeout) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient client = HttpClients.createDefault();
		String responseText = "";
		CloseableHttpResponse response = null;
		HttpPost method = null;
		long startTime = System.currentTimeMillis();
		String uri = "";
		try {
			method = new HttpPost(url);
			method.setConfig(requestConfig);
			StringEntity entityStr = new StringEntity(jsonStr,"UTF-8");//解决中文乱码问题
			entityStr.setContentType("application/json");
			method.setEntity(entityStr);
			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					method.addHeader(entry.getKey(), entry.getValue());
				}
			}

			response = client.execute(method);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseText = EntityUtils.toString(entity, "UTF-8");
			}
			uri = method.getURI().getPath();
		} catch (Exception e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, method, client);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", uri, startTime, endTime, endTime - startTime);
		}
		return responseText;
	}
	public static String putOpenApiAdmin(String url, Map<String, String> paramMap) throws MessageException {
		String openApiGlobalAdminQccComKey = Global.getConfig("openApi.global.admin.qcc.com.key");
		String openApiGlobalAdminQccComSecretKey = Global.getConfig("openApi.global.admin.qcc.com.secretKey");
		if (StringUtils.isNotBlank(url)) {
			url = url + "?key=" + openApiGlobalAdminQccComKey;
		}
		Map<String, String> headerMap = HttpUtils.getHeaderMapForPro(openApiGlobalAdminQccComKey, openApiGlobalAdminQccComSecretKey);
		return put(url, paramMap, headerMap, HTTP_CLIENT_TIMEOUT_10000);
	}

	public static String postJson(String url, Map<String, Object> paramMap, Map<String, String> headerMap,int timeout) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient client = HttpClients.createDefault();
		String responseText = "";
		CloseableHttpResponse response = null;
		HttpPost method = null;
		try {
			method = new HttpPost(url);
			method.setConfig(requestConfig);
			StringEntity entityStr = new StringEntity(JSONObject.toJSONString(paramMap),"UTF-8");//解决中文乱码问题
			entityStr.setContentType("application/json");
			method.setEntity(entityStr);

			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					method.addHeader(entry.getKey(), entry.getValue());
				}
			}

			response = client.execute(method);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseText = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (Exception e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, method, client);
		}
		return responseText;
	}

	public static String put(String url, Map<String, String> paramMap, Map<String, String> headerMap,int timeout) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPut httpPut = null;
		CloseableHttpResponse response = null;
		long startTime = System.currentTimeMillis();
		String logUrl = null;
		String responseText = "";
		try {
			URIBuilder builder = new URIBuilder(url);
			for (String key : paramMap.keySet()) {
				builder.addParameter(key, paramMap.get(key));
			}

			httpPut = new HttpPut(builder.build());
			logUrl = builder.build().toString();
			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					httpPut.addHeader(entry.getKey(), entry.getValue());
				}
			}

			httpPut.setConfig(requestConfig);
			response = httpclient.execute(httpPut);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseText = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (Exception e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, httpPut, httpclient);
			// updated for v1.7.8 KNZT-3472 接口打印日志增加接口开始结束时间
			long endTime = System.currentTimeMillis();
			logger.info("requesturl[{}] begin at {}, end at {}, took {} ms.", logUrl, startTime, endTime, endTime - startTime);
		}
		return responseText;
	}
	
	public static String putJson(String url, Map<String, Object> paramMap, Map<String, String> headerMap,int timeout) throws MessageException {
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout).build();
		CloseableHttpClient client = HttpClients.createDefault();
		String responseText = "";
		CloseableHttpResponse response = null;
		HttpPut method = null;
		try {
			method = new HttpPut(url);
			method.setConfig(requestConfig);
			StringEntity entityStr = new StringEntity(JSONObject.toJSONString(paramMap),"UTF-8");//解决中文乱码问题
			entityStr.setContentType("application/json");
			method.setEntity(entityStr);

			if(headerMap != null) {
				for (Entry<String, String> entry : headerMap.entrySet()) {
					method.addHeader(entry.getKey(), entry.getValue());
				}
			}

			response = client.execute(method);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseText = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (Exception e) {
			logger.error("", e);
			throw new ServiceException(e);
		} finally {
			close(response, method, client);
		}
		return responseText;
	}

	public static Map<String, String> getHeaderMapForPro(String key, String secretKey) {
		long tm = System.currentTimeMillis() / 1000;
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Timespan", tm+"");
		headerMap.put("Token", EncryptUtil.encodeMd5(key + tm + secretKey));
		return headerMap;
	}

	/**
	 * added for KNZT-2205【bug】在kyc修改账号服务结束时间，国际版api后台，操作记录中操作人不正确
	 */
	public static Map<String, String> getHeaderMapForOpenApi(String key, String secretKey) {
		long tm = System.currentTimeMillis() / 1000;
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Timespan", tm+"");
		headerMap.put("Token", EncryptUtil.encodeMd5(key + tm + secretKey));
		headerMap.put("operator", UserUtils.getUserLoginName());
		return headerMap;
	}

	public static void main(String []args) throws IOException {

	}


}
