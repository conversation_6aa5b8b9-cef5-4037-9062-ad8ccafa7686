package com.qcc.frame.jee.commons.utils;

import com.alibaba.fastjson.JSON;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.hash.Hashing;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.RequestTO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

//import java.util.Locale;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.servlet.LocaleResolver;

/**
 * 字符串工具类, 继承org.apache.commons.lang3.StringUtils类
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {
	
	private static Logger logger = LoggerFactory.getLogger(StringUtils.class);
	
    private static final char SEPARATOR = '_';
    private static final String CHARSET_NAME = "UTF-8";

	private static final Pattern PATTERN_ENGLISH = Pattern.compile("[A-Za-z]+");
	private static final Pattern PATTERN_ALL_ENGLISH = Pattern.compile("^[a-zA-Z0-9\\s\\p{Punct}]*$");
	private static final Pattern PATTERN_CN = Pattern.compile("\\p{IsHan}");
	// 正则表达式匹配成对的 <em> 标签，忽略大小写
	public static final Pattern PAIRED_EM_TAGS_PATTERN = Pattern.compile("(?i)<em>(.*?)</em>");

	// added for v2.1.5 chenbl KNZT-6475
	public static final String SPEC_CHAR_REGEX = "[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]";
	public static final Pattern PATTERN_SPEC_CHAR = Pattern.compile(SPEC_CHAR_REGEX);

	public static final char COMMA = ',';

	public static boolean isContainEnglish(String str) {
		if(StringUtils.isNotBlank(str)) {
			Matcher m = PATTERN_ENGLISH.matcher(str);
			if (m.find()) {
				return true;
			}
		}
		return false;
	}


	/**
	 * added for v1.3.6 KNZT-1783 判断是否有中文
	 * @param str 待检验的string
	 * @return true-有中文 false-没有中文
	 */
	public static boolean isContainChinese(String str) {
		if (StringUtils.isNotBlank(str)) {
			Matcher m = PATTERN_CN.matcher(str);
			return m.find();
		}
		return false;
	}


	/**
	 * 是否是中文公司名称 如有改动，一并修改 isContainCorpNameCn
	 * @param str
	 * @return
	 */
	public static boolean isCorpNameCn(String str){
		str = (str != null ? str.trim() : str);
		if(StringUtils.isNotBlank(str)) {
			String pattern = ".*(合作总社|公司|会社|厂|局|厅|台|企业|企业办|影剧院|村委会|村委|居委会|管委会|镇|村|办事处|办公室|委员会|中心|合作社|政府|供销社|基金|银行|经委|集团|服务部|事务所|联合社|联合会|所|管理处|活动室|信用社|大学|中学|小学|学校|学院|幼儿园|医院|研究所|管理部|管理区|经营部|贸易部|设计院|宾馆|旅行社|管理中心|（普通合伙）|（合伙企业）|（有限合伙）|\\(合伙企业\\)|\\(普通合伙\\)|\\(有限合伙\\))$";//updated for v1.0.7 KNZT-481
			if(str.matches(pattern)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 如有改动，一并修改 isCorpNameCn
	 * @param str
	 * @return
	 */
	public static boolean isContainCorpNameCn(String str){
		str = (str != null ? str.trim() : str);
		if(StringUtils.isNotBlank(str)) {
			String pattern = ".*(合作总社|公司|会社|厂|局|厅|台|企业|企业办|影剧院|村委会|村委|居委会|管委会|镇|村|办事处|办公室|委员会|中心|合作社|政府|供销社|基金|银行|经委|集团|服务部|事务所|联合社|联合会|所|管理处|活动室|信用社|大学|中学|小学|学校|学院|幼儿园|医院|研究所|管理部|管理区|经营部|贸易部|设计院|宾馆|旅行社|管理中心|（普通合伙）|（合伙企业）|（有限合伙）|\\(合伙企业\\)|\\(普通合伙\\)|\\(有限合伙\\)).*";
            return str.matches(pattern);
		}
		return false;
	}

	// added for v2.1.5 chenbl KNZT-6475
	public static boolean containsSpecChar(String text) {
		if (StringUtils.isBlank(text)) {
			return false;
		}
		return PATTERN_SPEC_CHAR.matcher(text).find();
	}

    /**
     * 移除4字节的文字
     * @param text
     * @return
     */
    public static String removeSpecChar(String text) {
		try {
			//https://stackoverflow.com/questions/27820971/why-a-surrogate-java-regexp-finds-hyphen-minus
			//https://blog.csdn.net/shootyou/article/details/44852639
			return text.replaceAll(SPEC_CHAR_REGEX, "");
		} catch (Exception e) {
			return text;
		}
        
    }
    
	public static String removeBom(String str) {
		try {
			if(str != null) {
				byte[] bomChar = str.getBytes("UTF-8");
				if (bomChar[0] == -17 && bomChar[1] == -69 && bomChar[2] == -65) {  
					byte[] nbs = new byte[bomChar.length - 3];  
		            System.arraycopy(bomChar, 3, nbs, 0, nbs.length); 
		            return (new String(nbs, "UTF-8"));
				}
			}
		} catch(Exception e) {
			
		}
		return str;
	}
    
    /**
     * 得到随机字符串(数字)
     * @param num
     * @return
     */
    public static String getRandomNum(int num) {
    	return RandomStringUtils.random(num, "0123456789");
    }
    
    /**
     * 得到随机字符串(数字/小写)
     * @param num
     * @return
     */
    public static String getRandomChar(int num) {
    	return RandomStringUtils.random(num, "0123456789abcdefghijklmnopqrstuvwxyz");
    }
    
    /**
     * 得到随机字符串(数字/小写/大写)
     * @param num
     * @return
     */
    public static String getRandomChar2(int num) {
    	return RandomStringUtils.random(num, "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
    }
    
    /**
     * 得到随机字符串(数字/大写)
     * @param num
     * @return
     */
    public static String getRandomChar3(int num) {
    	return RandomStringUtils.random(num, "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ");
    }
    
    /**
     * 转换为字节数组
     * @param str
     * @return
     */
    public static byte[] getBytes(String str){
    	if (str != null){
    		try {
				return str.getBytes(CHARSET_NAME);
			} catch (UnsupportedEncodingException e) {
				logger.error("", e);
				return null;
			}
    	}else{
    		return null;
    	}
    }

    public static String nullToString(String str) {
		if(str == null) {
			return "";
		}
		return str;
	}
    
    /**
     * 转换为字节数组
     * @param bytes
     * @return
     */
    public static String toString(byte[] bytes){
    	try {
			return new String(bytes, CHARSET_NAME);
		} catch (UnsupportedEncodingException e) {
			logger.error("", e);
			return EMPTY;
		}
    }
    
    public static String toObjString(Object obj){
    	try {
			if(obj == null) {return EMPTY;}
			return obj.toString();
		} catch (Exception e) {
			return EMPTY;
		}
    }
    
    public static String removeHtmlFromName(String name) {
    	if(name != null) {
    		return name.replaceAll("<em>", "").replaceAll("</em>", "");
    	}
    	return "";
    }
    
    /**
     * 是否包含字符串
     * @param str 验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inString(String str, String... strs){
    	if (str != null){
        	for (String s : strs){
        		if (str.equals(trim(s))){
        			return true;
        		}
        	}
    	}
    	return false;
    }
    
    public static boolean hasDigit(String content) {
        boolean flag = false;
        Pattern p = Pattern.compile(".*\\d+.*");
        Matcher m = p.matcher(content);
        if (m.matches()) {
            flag = true;
        }
        return flag;
    }

	/**
	 * 是否仅包含数字字符
	 *
	 * @param input
	 * @return
	 */
	public static boolean onlyDigit(String input) {
		return input != null && input.matches("\\d+");
	}
    
	/**
	 * 邮箱是否合法
	 */ 
    public static boolean isEmail(String email) {
    	return validateEmail(email);
    	/*if (isBlank(email)){
			return false;
		}
    	String regEx = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";
        Pattern p =  Pattern.compile(regEx);
        Matcher m = p.matcher(email);
        return m.matches();*/
    }
 
	/**
	 * 替换掉HTML标签方法
	 */
	public static String replaceHtml(String html) {
		if (isBlank(html)){
			return "";
		}
		String regEx = "<.+?>";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(html);
		String s = m.replaceAll("");
		return s;
	}
	
	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * @param html
	 * @return
	 */
	public static String replaceMobileHtml(String html){
		if (html == null){
			return "";
		}
		return html.replaceAll("<([a-z]+?)\\s+?.*?>", "<$1>");
	}

	public static boolean isJson(String str) {
		if(StringUtils.isNotBlank(str)) {
			try {
				Object obj = JSON.parse(str);
				return true;
			} catch (Throwable e) {
				return false;
			}
		}
		return false;
	}
	
	/**
	 * 替换JSON里的html
	 * @param txt
	 * @return
	 */
	public static String replaceHtml4Json(String txt){
		if (txt == null){
			return "";
		}
		return replace(replace(txt, "<", "&lt;"), "?", "&gt;");
	}
	
	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * @param txt
	 * @return
	 */
	public static String toHtml(String txt){
		if (txt == null){
			return "";
		}
		return replace(replace(Encodes.escapeHtml(txt), "\n", "<br/>"), "\t", "&nbsp; &nbsp; ");
	}

	public static int compare(String str1, String str2) {
		if (StringUtils.isNotBlank(str1) && StringUtils.isNotBlank(str2)) {
			return str1.compareTo(str2);
		}
		if (StringUtils.isNotBlank(str1)) {
			return -1;
		}
		if (StringUtils.isNotBlank(str2)) {
			return 1;
		}
		return 0;
	}

	/**
	 * 缩略字符串（不区分中英文字符）
	 * @param str 目标字符串
	 * @param length 截取长度
	 * @return
	 */
	public static String abbr(String str, int length) {
		if (str == null) {
			return "";
		}
		try {
			StringBuilder sb = new StringBuilder();
			int currentLength = 0;
			for (char c : replaceHtml(StringEscapeUtils.unescapeHtml4(str)).toCharArray()) {
				currentLength += String.valueOf(c).getBytes("GBK").length;
				if (currentLength <= length - 3) {
					sb.append(c);
				} else {
					sb.append("...");
					break;
				}
			}
			return sb.toString();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return "";
	}
	
	public static String abbr2(String param, int length) {
		if (param == null) {
			return "";
		}
		StringBuilder result = new StringBuilder();
		int n = 0;
		char temp;
		boolean isCode = false; // 是不是HTML代码
		boolean isHTML = false; // 是不是HTML特殊字符,如&nbsp;
		for (int i = 0; i < param.length(); i++) {
			temp = param.charAt(i);
			if (temp == '<') {
				isCode = true;
			} else if (temp == '&') {
				isHTML = true;
			} else if (temp == '>' && isCode) {
				n = n - 1;
				isCode = false;
			} else if (temp == ';' && isHTML) {
				isHTML = false;
			}
			try {
				if (!isCode && !isHTML) {
					n += String.valueOf(temp).getBytes("GBK").length;
				}
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}

			if (n <= length - 3) {
				result.append(temp);
			} else {
				result.append("...");
				break;
			}
		}
		// 取出截取字符串中的HTML标记
		String temp_result = result.toString().replaceAll("(>)[^<>]*(<?)",
				"$1$2");
		// 去掉不需要结素标记的HTML标记
		temp_result = temp_result
				.replaceAll(
						"</?(AREA|BASE|BASEFONT|BODY|BR|COL|COLGROUP|DD|DT|FRAME|HEAD|HR|HTML|IMG|INPUT|ISINDEX|LI|LINK|META|OPTION|P|PARAM|TBODY|TD|TFOOT|TH|THEAD|TR|area|base|basefont|body|br|col|colgroup|dd|dt|frame|head|hr|html|img|input|isindex|li|link|meta|option|p|param|tbody|td|tfoot|th|thead|tr)[^<>]*/?>",
						"");
		// 去掉成对的HTML标记
		temp_result = temp_result.replaceAll("<([a-zA-Z]+)[^<>]*>(.*?)</\\1>",
				"$2");
		// 用正则表达式取出标记
		Pattern p = Pattern.compile("<([a-zA-Z]+)[^<>]*>");
		Matcher m = p.matcher(temp_result);
		List<String> endHTML = Lists.newArrayList();
		while (m.find()) {
			endHTML.add(m.group(1));
		}
		// 补全不成对的HTML标记
		for (int i = endHTML.size() - 1; i >= 0; i--) {
			result.append("</");
			result.append(endHTML.get(i));
			result.append(">");
		}
		return result.toString();
	}
	
	/**
	 * 转换为Double类型
	 */
	public static Double toDouble(Object val){
		if (val == null){
			return 0D;
		}
		try {
			return Double.valueOf(trim(val.toString()));
		} catch (Exception e) {
			return 0D;
		}
	}

	/**
	 * 转换为Float类型
	 */
	public static Float toFloat(Object val){
		return toDouble(val).floatValue();
	}

	/**
	 * 转换为Long类型
	 */
	public static Long toLong(Object val){
		return toDouble(val).longValue();
	}

	/**
	 * 转换为Integer类型
	 */
	public static Integer toInteger(Object val){
		return toLong(val).intValue();
	}
	
	/**
	 * 获得i18n字符串
	 
	public static String getMessage(String code, Object[] args) {
		LocaleResolver localLocaleResolver = (LocaleResolver) SpringContextHolder.getBean(LocaleResolver.class);
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();  
		Locale localLocale = localLocaleResolver.resolveLocale(request);
		return SpringContextHolder.getApplicationContext().getMessage(code, args, localLocale);
	}
	*/
	/**
	 * 获得用户远程地址
	 */
	public static String getRemoteAddr(HttpServletRequest request){
		/*String remoteAddr = request.getHeader("requestip");
        if (isBlank(remoteAddr)) {
        	remoteAddr = request.getHeader("X-Real-IP");
        	if (isBlank(remoteAddr)) {
	        	remoteAddr = request.getHeader("X-Forwarded-For");
//	        	if (isBlank(remoteAddr)) {
//	        		remoteAddr = request.getHeader("Proxy-Client-IP");
//	            	if (isBlank(remoteAddr)) {
//	            		remoteAddr = request.getHeader("WL-Proxy-Client-IP");
//	            	}
//	            }
        	}
        }*/
		String remoteAddr = request.getHeader("X-Forwarded-For");
		if(isNotBlank(remoteAddr)) {
			int index = remoteAddr.indexOf(",");
			if(index != -1) {
				remoteAddr = remoteAddr.substring(0, index);
			}
		}
        return remoteAddr != null ? remoteAddr : request.getRemoteAddr();
	}

	/**
	 * 获取真实远程地址
	 * @param request
	 * @return
	 */
	public static String getRealRemoteAddr(HttpServletRequest request) {
		Enumeration<String> headerNames = request.getHeaderNames();
		while (headerNames.hasMoreElements()) {
			String headerName = headerNames.nextElement();
			String headerValue = request.getHeader(headerName);
		}
		String remoteAddr = request.getHeader("X-Forwarded-For");
		if (StringUtils.isNotBlank(remoteAddr)) {
			logger.info("getRealRemoteAddr X-Forwarded-For:{}", remoteAddr);
			if(remoteAddr.contains(",")) {
				remoteAddr = remoteAddr.substring(0, remoteAddr.indexOf(","));
			}
			return remoteAddr;
		}
		remoteAddr = request.getHeader("remoteip");
		if (StringUtils.isNotBlank(remoteAddr)) {
			logger.info("getRealRemoteAddr remoteip:{}", remoteAddr);
			return remoteAddr;
		}
		remoteAddr = request.getHeader("X-Real-IP");
		if (StringUtils.isNotBlank(remoteAddr)) {
			logger.info("getRealRemoteAddr X-Real-IP:{}", remoteAddr);
			return remoteAddr;
		}
		return request.getRemoteAddr();
	}

	/**
	 * 驼峰命名法工具
	 * @return
	 * 		toCamelCase("hello_world") == "helloWorld" 
	 * 		toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 * 		toUnderScoreCase("helloWorld") = "hello_world"
	 */
    public static String toCamelCase(String s) {
        if (s == null) {
            return null;
        }

        s = s.toLowerCase();

        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            if (c == SEPARATOR) {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
	 * 驼峰命名法工具
	 * @return
	 * 		toCamelCase("hello_world") == "helloWorld" 
	 * 		toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 * 		toUnderScoreCase("helloWorld") = "hello_world"
	 */
    public static String toCapitalizeCamelCase(String s) {
        if (s == null) {
            return null;
        }
        s = toCamelCase(s);
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }
    
    /**
	 * 驼峰命名法工具
	 * @return
	 * 		toCamelCase("hello_world") == "helloWorld" 
	 * 		toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 * 		toUnderScoreCase("helloWorld") = "hello_world"
	 */
    public static String toUnderScoreCase(String s) {
        if (s == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            boolean nextUpperCase = true;

            if (i < (s.length() - 1)) {
                nextUpperCase = Character.isUpperCase(s.charAt(i + 1));
            }

            if ((i > 0) && Character.isUpperCase(c)) {
                if (!upperCase || !nextUpperCase) {
                    sb.append(SEPARATOR);
                }
                upperCase = true;
            } else {
                upperCase = false;
            }

            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }
 
    /**
     * 转换为JS获取对象值，生成三目运算返回结果
     * @param objectString 对象串
     *   例如：row.user.id
     *   返回：!row?'':!row.user?'':!row.user.id?'':row.user.id
     */
    public static String jsGetVal(String objectString){
    	StringBuilder result = new StringBuilder();
    	StringBuilder val = new StringBuilder();
    	String[] vals = split(objectString, ".");
    	for (int i=0; i<vals.length; i++){
    		val.append("." + vals[i]);
    		result.append("!"+(val.substring(1))+"?'':");
    	}
    	result.append(val.substring(1));
    	return result.toString();
    }
    
    /** 
     * 通过正则表达式的方式获取字符串中指定字符的个数 
     * @param text 指定的字符串 
     * @return 指定字符的个数 
     */  
    public static int pattern(String text, String findstr) {  
        // 根据指定的字符构建正则  
        Pattern pattern = Pattern.compile(findstr);  
        // 构建字符串和正则的匹配  
        Matcher matcher = pattern.matcher(text);  
        int count = 0;  
        // 循环依次往下匹配  
        while (matcher.find()){ // 如果匹配,则数量+1  
            count++;  
        }  
        return  count;  
    }
    
    public static String toPercent(BigDecimal data) {
    	
    	if(data != null) {
    		BigDecimal percent = data.multiply(new BigDecimal(100));
    		NumberFormat nf = NumberFormat.getInstance();
    		nf.setMaximumFractionDigits(4);
    		BigDecimal setScale = new BigDecimal(nf.format(percent));
    		return setScale.toPlainString() + "%";
    	} else {
    		return "";
    	}
    }
    
    public static String toPercentStr(BigDecimal data) {
    	
    	if(data != null) {
    		NumberFormat nf = NumberFormat.getInstance();
    		nf.setMaximumFractionDigits(4);
    		BigDecimal setScale = new BigDecimal(nf.format(data));
    		return setScale.toPlainString() + "%";
    	} else {
    		return "";
    	}
    }
    
    public static String toFormatStr(BigDecimal data) {
    	
    	if(data != null) {
    		if(data.compareTo(new BigDecimal(100)) > 0) {
    			return "";
    		}
    		NumberFormat nf = NumberFormat.getInstance();
    		nf.setMaximumFractionDigits(4);
    		BigDecimal setScale = new BigDecimal(nf.format(data));
    		return setScale.toPlainString() + "%";
    	} else {
    		return "";
    	}
    }

    
    public static boolean checkCorpStatusNormal(String status) {
    	boolean flag = false;
    	if(StringUtils.isNotBlank(status)) {
	    	List<String> normalStatusList = new ArrayList<>();
	    	normalStatusList.add("在业");
	    	normalStatusList.add("存续");
	    	normalStatusList.add("筹建");
	    	normalStatusList.add("清算");
	    	normalStatusList.add("迁入");
	    	normalStatusList.add("迁出");
	//    	normalStatusList.add("停业");
	//    	normalStatusList.add("撤销");
	//    	normalStatusList.add("吊销");
	//    	normalStatusList.add("注销");
	    	normalStatusList.add("仍注册");
	//    	normalStatusList.add("已告解散");
	//    	normalStatusList.add("不再是独立的实体");
	//    	normalStatusList.add("废止");
	//    	normalStatusList.add("废止清算完结");
	//    	normalStatusList.add("废止许可");
	//    	normalStatusList.add("废止许可完结");
	//    	normalStatusList.add("废止认许");
	//    	normalStatusList.add("废止认许完结");
	    	normalStatusList.add("接管");
	//    	normalStatusList.add("撤回认许");
	//    	normalStatusList.add("撤回认许完结");
	//    	normalStatusList.add("撤销设立");
	//    	normalStatusList.add("撤销完结");
	    	normalStatusList.add("撤销无需清算");
	//    	normalStatusList.add("撤销许可");
	//    	normalStatusList.add("撤销认许");
	//    	normalStatusList.add("撤销认许完结");
	    	normalStatusList.add("核准报备");
	    	normalStatusList.add("核准设立");
	//    	normalStatusList.add("设立但已解散");
	    	normalStatusList.add("核准许可报备");
	    	normalStatusList.add("核准许可登记");
	    	normalStatusList.add("核准认许");
	//    	normalStatusList.add("清理");
	//    	normalStatusList.add("清理完结");
	//    	normalStatusList.add("破产");
	//    	normalStatusList.add("破产清算完结");
	//    	normalStatusList.add("破产程序终结");
	//    	normalStatusList.add("解散");
	//    	normalStatusList.add("解散清算完结");
	    	normalStatusList.add("重整");
	//    	normalStatusList.add("合并解散");
	    	
	    	if(normalStatusList.contains(status)) {
	    		flag = true;
	    	}
    	} else {
    		flag = true;
    	}
    	return flag;
    }
    
    public static String changeCnCurvesToEn(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		str = str.trim();
    		if(StringUtils.contains(str, "（") || StringUtils.contains(str, "）")) {
	    		String newStr = str.replaceAll("（", "(");
	    		return newStr.replaceAll("）", ")");
    		} else {
    			return str;
    		}
    	}
    	return null;
    }
    
    public static String toDash(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		return str;
    	} else {
    		return "-";
    	}
    }
    
    public static String dash2Blank(String str) {
    	if("-".equals(str)) {
    		return "";
    	}
    	return str;
    }
    
    public static String getFirstLetter(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		return str.substring(0,1);
    	} else {
    		return "-";
    	}
    }
    
    public static BigDecimal percentStrToBigDecimal(String str) {
    	BigDecimal decimal = new BigDecimal(0);
    	//str == "2.033%"
    	if(StringUtils.isNotBlank(str)) {
    		if(StringUtils.contains(str, "%")) {
    			decimal = new BigDecimal(str.substring(0, str.indexOf("%")));
    		} else if(StringUtils.contains(str, "％")) {
    			decimal = new BigDecimal(str.substring(0, str.indexOf("％")));
    		}
    		decimal = new BigDecimal(str.substring(0, str.indexOf("%")));
    	}
    	return decimal;
    }
    
    public static String hiddenStr(String origStr, int hideLength) {
    	if(StringUtils.isNotBlank(origStr)) {
            int startIndex = origStr.length()/2-hideLength/2;
            String replaceSymbol = "*";//替换符号，这里用“*”为例
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i<origStr.length();i++){
                char number = origStr.charAt(i);
                if (i>=startIndex&&i<startIndex+hideLength){
                    stringBuilder.append(replaceSymbol);
                }else {
                    stringBuilder.append(number);
                }
            }
            return stringBuilder.toString();
    	}
    	return null;
    }
    
    public static BigDecimal formatSubConAmtToBigDecimal(String str) {
    	BigDecimal decimal = new BigDecimal(0); 
    	if(StringUtils.isNotBlank(str)) {
    		String[] arr = str.split(",");
    		for(int i=0; i<arr.length; i++) {
    			if(NumberUtils.isDec(arr[i])) {
        			decimal = decimal.add(new BigDecimal(arr[i]));
        		} else
        		if(NumberUtils.isDec(StringUtils.substringBefore(arr[i], "万"))) {
        			decimal = decimal.add(new BigDecimal(StringUtils.substringBefore(arr[i], "万")));
        		}
    		}
    		
    	}
    	return decimal;
    }
    
    
    public static String encodeDownloadFileName(String filename) {
    	if(WebContextHolder.isDownloadBrowserFlag()) {//请不要自己在代码里设置该值, 该值是由框架决定的
    		logger.info("[encodeDownloadFileName]" + filename);
	    	RequestTO requestTO = WebContextHolder.getRequest();
	    	if(requestTO != null && requestTO.getBrowserUserAgent() != null) {
	    		String userAgent = requestTO.getBrowserUserAgent();
	    		if (userAgent.contains("MSIE")||userAgent.contains("Trident")||userAgent.contains("Edge")) {
					logger.info("[encodeDownloadFileName] MSIE");
	                try {  
	                	filename = URLEncoder.encode(filename, "UTF-8");  
	                } catch (Exception e) {  
	                    e.printStackTrace();                      
	                }  
	            } else {
					logger.info("[encodeDownloadFileName] NOT IE");
	                //非IE浏览器的处理：  
	                try {
	                	filename = new String(filename.getBytes("UTF-8"),"ISO-8859-1");  
	                } catch (Exception e) {
	                    e.printStackTrace();
	                }  
	            }  
	    	} 
    	} else {
			logger.info("[encodeDownloadFileName] NORMAL" + filename);
    		try {
    			filename= URLEncoder.encode(filename, "UTF8");
    		} catch (Exception e) {
                e.printStackTrace();
            } 
    	}
    	if(filename != null) {
    		filename=filename.replaceAll("\\+","%20");
    	}
    	return filename;
    }
    
    public static String removeBlankInJson(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		//str = replaceHtml(str);
    		return str.replaceAll("\\{\\s*\"", "{\"").replaceAll("\"\\s*\\}", "\"}").replaceAll("\"\\s*:", "\":").replaceAll("\\[\\s*\\{", "[{").replaceAll("\\}\\s*\\]", "}]").replaceAll(",\\s*\"", ",\"").replaceAll("\\},\\s*\\{", "},{");
    	}
    	return null;
    }
    
    public static String removeTags(String str){
    	if(StringUtils.isNotBlank(str)) {
    		str = StringUtils.remove(str, "[");
    		str = StringUtils.remove(str, "]");
    		return replaceHtml(str);
    	}
    	return "";
    }
    
    public static String removeTags(List<String> strList){
    	if(strList != null && !strList.isEmpty()) {
    		String result = "";
    		for(String str: strList) {
    			result = result + replaceHtml(str);
    		}
    		return result;
    	}
    	return "";
    }
    
    public static boolean validateChineseChar(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		String pattern = "^[\u4e00-\u9fa5]{0,}$";
    		if(str.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }
    
    public static boolean validateMobile(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		String pattern = "^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\\d{8}$";
    		if(str.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }
    
    public static boolean validateID(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		String pattern = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    		if(str.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }
    
    public static boolean validatePasswordFormart(String pwd) {
    	if(StringUtils.isNotBlank(pwd)) {
    		String pattern = "^(?![^a-zA-Z]+$)(?!\\D+$).{6,}$";
    		if(pwd.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }

	// updated for v2.1.8 chenbl KNZT-6662 原有正则对于长字符串校验性能很差
    public static boolean validateEmail(String str) {
    	return EmailValidateUtil.isValidEmail(str);
    }
    
    public static boolean valildateCreditCode(String str){
    	str = (str != null ? str.trim() : str);
    	if(StringUtils.isNotBlank(str)) {
    		String pattern = "^9[1-3]{1}[\\d]{6}[\\dA-Z]{9,10}$";
    		if(str.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }
    
    public static boolean valildateRegistNo(String str){
    	str = (str != null ? str.trim() : str);
    	if(StringUtils.isNotBlank(str)) {
    		String pattern = "^\\d{6}[\\dA-Z]{7,13}$";
    		if(str.matches(pattern)) {
    			return true;
    		}
    	} 
    	return false;
    }
    
    
    public static Set<String> split2Set(String val, boolean replaceCnChar) {
		if(val == null) {
	        return null;
	    }
		if(replaceCnChar) {
			val = val.replace("，", ",");
		}
		String []strArr = val.split(",");
		Set<String> list = new HashSet<String>();
		for(String str : strArr) {
			if(StringUtils.isNotBlank(str)) {
				list.add(StringUtils.trim(str));
			}
		}
	    return list;
	}
    public static List<String> split2List(String val, String splitChar) {
    	List<String> list = new ArrayList<String>();
    	if(StringUtils.isNotBlank(val)) {
	    	String []strArr = val.split(splitChar);
			for(String str : strArr) {
				if(StringUtils.isNotBlank(str) && !list.contains(StringUtils.trim(str))) {
					list.add(StringUtils.trim(str));
				}
			}
    	}
	    return list;
    }

	// 只是简单分割，不去重或者trim等逻辑
	public static List<String> split2ListV2(String val, String... separatorArr) {
		if (Objects.isNull(val)) {
			return new ArrayList<>();
		}
		if (separatorArr == null || separatorArr.length == 0) {
			separatorArr = new String[]{","};
		}
		// 将分隔符数组转换为正则表达式
		String regex = String.join("|", Arrays.stream(separatorArr)
				.map(Pattern::quote) // 对每个分隔符进行转义，以防止特殊字符影响正则表达式
				.toArray(String[]::new));

		// 使用正则表达式分割字符串，并将结果转换为列表
		return Arrays.stream(val.split(regex)).collect(Collectors.toList());
	}

    public static List<String> split(String val, boolean replaceCnChar) {
		if(val == null) {
	        return null;
	    }
		if(replaceCnChar) {
			val = val.replace("，", ",");
		}
		String []strArr = val.split(",");
		List<String> list = new ArrayList<String>();
		for(String str : strArr) {
			if(StringUtils.isNotBlank(str)) {
				list.add(StringUtils.trim(str));
			}
		}
	    return list;
	}
    
    public static String getListStr(List<String> list) {
    	if(list == null || list.isEmpty()) {
    		return "";
    	}
    	StringBuilder result = new StringBuilder();
    	int size = list.size();
    	for(int i=0; i < size; i++) {
    		result.append(list.get(i));
    		if(i != size - 1) {
    			result.append(",");
    		}
    	}
    	return result.toString();
    }

    public static boolean onlyCharAndNumr(String str) {
		str = (str != null ? str.trim() : str);
		if(StringUtils.isNotBlank(str)) {
			String pattern = "^[0-9a-zA-Z]*$";
			if(str.matches(pattern)) {
				return true;
			}
		}
		return false;

	}
    
    /**
     * java生成随机数字和字母组合
     * 
     * @param length [生成随机数的长度]
     * 
     */
    public static String getCharAndNumr(int length) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }
    
    public static boolean isBigDecimal(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		return Pattern.matches("([1-9]\\d*\\.?\\d*)|(0\\.\\d*[1-9])", str);
    	}
    	return false;
    }

    public static String filterEmoji(String nickName) {
		StringBuilder sBuilder = new StringBuilder();
		byte[] t1 = nickName.getBytes();
		for (int i = 0; i < t1.length;) {
			byte tt = t1[i];
			int code = 0;
			if ((tt & 0x80) == 0x0) {
				// 单字节。
				code = tt;
				i++;
			} else if ((tt & 0xE0) == 0xC0) {
				// 2个字节时：编码 110XXXXX 10XXXXXX
				// 0x1F->11111 ,0x3F 111111
				// t1[i] & 0x1F ->取第一组的后5位，先占到第11-6位置（<<6)
				// t1[i+1] & 0x3F->取第二组的后6位)
				code = (((int) (t1[i] & 0x1F)) << 6) | ((int) (t1[i + 1] & 0x3F));
				i += 2;
			} else if ((tt & 0xF0) == 0xE0) {
				// 3个字节时：编码 1110XXXX 10XXXXXX 10XXXXXX
				// 0x0F->1111 ,0x3F 111111
				// t1[i] & 0x0F ->取第一组的后4位，先占到第16-12位置（<<12)
				// t1[i+1] & 0x3F->取第二组的后6位，占到第11到6位置(<<6)
				// t1[i+2] & 0x3F->取第三组的后6位，
				code = (((int) (t1[i] & 0x0F)) << 12) | (((int) (t1[i + 1] & 0x3F)) << 6) | (t1[i + 2] & 0x3F);
				i += 3;
			} else if ((tt & 0xF8) == 0xF0) {
				// 4个字节时：编码 11110XXX 10XXXXXX 10XXXXXX 10XXXXXX
				code = (((int) (t1[i] & 0x07)) << 18) | (((int) (t1[i + 1] & 0x3F)) << 12) | (((int) (t1[i + 2] & 0x3F)) << 6) | (t1[i + 3] & 0x3F);
				i += 4;
			}

			if(code!=0) {sBuilder.append((char) code);}
		}

		return (sBuilder.toString());

	}
    
    /**
     * 将字符串列表转换成以separator分隔的字符串
     * @param list
     * @return
     */
    public static String list2Str(List<String> list, String separator) {
    	StringBuilder result = new StringBuilder();
    	for(String str : list) {
    		result.append(separator).append(str);
    	}
    	return result.length() > 0 ? result.substring(separator.length()) : "";
    }
    
    public static String replaceBlank(String str) {
    	String dest = "";
    	if(str!=null) {
    		Pattern p = Pattern.compile("\\s*|\t");
    		Matcher m = p.matcher(str);
    		dest = m.replaceAll(",");
    		dest = dest.trim();
    		dest = dest.replace(",,", " ");
    		dest = dest.replace(",", "");
    	}
    	return dest;
    }
    
    /**
     * 如果有一个为空, 返回true
     * @param strArr
     * @return
     */
    public static boolean orBlank(String ...strArr) {
    	for(String str : strArr) {
	        if (isBlank(str)) {
	            return true;
	        }
    	}
    	return false;
    }
    
    /**
     * 如果全部都是数字, 返回true
     * @param strArr
     * @return
     */
    public static boolean andNumber(String ...strArr) {
    	for(String str : strArr) {
	        if (isBlank(str)) {
	            return false;
	        }
	        if(!isNumeric(str)) {
	        	return false;
	        }
    	}
    	return true;
    }
    
    /**
     * 如果全为空, 返回true
     * @param strArr
     * @return
     */
    public static boolean andBlank(String ...strArr) {
    	for(String str : strArr) {
	        if (isNotBlank(str)) {
	            return false;
	        }
    	}
    	return true;
    }
    
    public static List<String> findInt(List<String> keyNoList) {
    	List<String> qccCompList = new ArrayList<>();
    	for(String str:keyNoList) {
    		qccCompList.add(str);
    	}
    	return qccCompList;
    }
    
    public static String getValue(String val, String defaultVal) {
    	if(isBlank(val)) {
    		return defaultVal;
    	}
    	return val;
    }

	public static String getNotBlankStr(String ...strArr) {
    	if(strArr != null) {
    		for(String str : strArr) {
    			if(StringUtils.isNotBlank(str)) {
    				return str;
				}
			}
		}
		return null;
	}

    /**
     * 移除html标记
     * @param content
     * @return
     */
    public static String stripHtml(String content) {
    	if(content != null) {
    		return content.replaceAll("\\<.*?>", "");
    	}
    	return "";
    }
    
    /**
     * 得到文件后缀
     * test.txt  --> "txt"
     * test.	 --> ""
     * test		 --> ""
     * null		 --> ""
     * @param path
     * @return
     */
    public static String getFileExt(String path) {
    	if(path == null) {
    		return "";
    	}
    	int index = path.lastIndexOf(".");
    	if(index != -1) {
    		return path.substring(index + 1);
    	}
    	return "";
    }
    
    public static String subIndexString(String str, String indexStr) {
    	if(str != null && str.indexOf(indexStr) != -1) {
    		return str.substring(str.indexOf(indexStr) + indexStr.length());
    	}
    	return str;
    }
    
    public static String replaceBlank3(String s) {
        String result= null;
        if (s == null) {
            return result;
        } else {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(s);
            result= m.replaceAll("");
            return result;
        }
    }
    
    public static boolean containsEnglish(String charaString) {
    	if(StringUtils.isNotBlank(charaString)) {
    		return charaString.matches(".*[a-zA-z].*");
    	}
    	return false;
	}

	public static boolean isEnglish(String input) {
		if (StringUtils.isBlank(input)) {
			logger.warn("input is blank");
			return false;
		}
		return PATTERN_ALL_ENGLISH.matcher(input).matches();
	}
    
    public static String removeBlankInChineseName(String str) {
    	if(StringUtils.isNotBlank(str)) {
    		str = str.trim();
    		if(containsEnglish(str)) {// 英文名企业不去中间空格
    			return str;
    		} else {// 中文企业名去除中间的空格
    			return str.replaceAll("\\s*", "");
    		}
    	}
    	return null;
    }
   
	/**
	 * 隐藏身份证号码
	 *
	 * @param idNumber
	 * @return
	 */
	public static String maskIdNumber(String idNumber) {
		return idNumber.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
	}

	/**
	 * 隐藏手机号码
	 *
	 * @param phoneNumber
	 * @return
	 */
	public static String maskPhoneNumber(String phoneNumber) {
		return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
	}
    
	/**
	 * 验证手机号码
	 * @param mobile
	 * @param regex
	 * @return
	 */
	public static boolean mobileValidator(String mobile, String regex) {
		if(mobile != null) {
			return mobile.matches(regex);
		}
		return false;
	}
	
	/**
	 * 替换查询条件的字符串
	 * @param str
	 * @return
	 */
	public static String replaceStr4Es(String str) {
		if(str != null) {
			return str.replaceAll("\"", "");
		}
		return "";
	}
	
	/**
	 * 处理数据去除单位，保留两位小数
	 * @param value
	 * @return
	 */
	public static String handleValue(String value) {
		if(isNotBlank(value)) {
			NumberFormat nf = NumberFormat.getInstance();
			nf.setMaximumFractionDigits(2);
			nf.setGroupingUsed(false);
			if("万".equals(value.substring(value.length()-1))) {
				if(!NumberUtils.isDec(value.substring(0,value.length()-1))) {
					return "0";
				}
				BigDecimal bigDecimal = new BigDecimal(value.substring(0,value.length()-1)).divide(new BigDecimal("10000"));
				return nf.format(bigDecimal);
			}else if(value.length() > 1 && "万亿".equals(value.substring(value.length()-2,value.length()))) {
				if(!NumberUtils.isDec(value.substring(0,value.length()-2))) {
					return "0";
				}
				BigDecimal bigDecimal = new BigDecimal(value.substring(0,value.length()-2)).multiply(new BigDecimal("10000"));
				return nf.format(bigDecimal);
			}else if("亿".equals(value.substring(value.length()-1))){
				if(!NumberUtils.isDec(value.substring(0,value.length()-1))) {
					return "0";
				}
				BigDecimal bigDecimal = new BigDecimal(value.substring(0,value.length()-1));
				return nf.format(bigDecimal);
			}else {
				if(!NumberUtils.isDec(value)) {
					return "0";
				}
				BigDecimal bigDecimal = new BigDecimal(value);
				return nf.format(bigDecimal);
			}
		}
		return "-";
	}
	
	/**
	 * 处理数据除以10000保留两位小数
	 * @param value
	 * @return
	 */
	public static String handleValueTwo(String value) {
		if(isNotBlank(value)) {
			if(NumberUtils.isDec(value)) {
				NumberFormat nf = NumberFormat.getInstance();
				nf.setMaximumFractionDigits(2);
				nf.setGroupingUsed(false);
				BigDecimal bigDecimal = new BigDecimal(value).divide(new BigDecimal("10000"));
				return nf.format(bigDecimal);
			}else{
				return "0";
			}
		}else{
			return "0";
		}
	}

	public static List<String> getPercent(String str) {
		if(StringUtils.isNotBlank(str)) {
			String dd = str.replaceAll("[^\\d.%]", "_").replace("_+", "_");
			if(StringUtils.isNotBlank(dd)) {
				String[] as = dd.split("_+");
				if(as != null) {
					List<String> list = Arrays.asList(as);
					return list.stream().filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
				}
			}
		}

		return null;
	}

	public static int getStrSizeBySeparator(String str, String separator) {
		if(!StringUtils.orBlank(str, separator)) {
			String[] arr = StringUtils.split(str, separator);
			if(arr != null) {
				return arr.length;
			}
		}
		return 0;
	}

	/**
	 * 判断str是否在equalToArr中
	 * @param str
	 * @param equalToArr
	 * @return
	 */
	public static boolean equalsAny(String str, String... equalToArr) {
		if(equalToArr != null) {
			for(String to : equalToArr) {
				if(equals(str, to)) {
					return true;
				}
			}
		}
		return false;
	}

    public static void main(String []args) {

    }

	 /**
	  * [身份证号] 前2位，后2位，其他用星号隐藏每位1个星号<例子:45**************47>
	  * id 不超过4位直接返回原样
	  * 
	  */
	public static String maskIdCard(String carId){
		if (isBlank(carId)) {
			return "";
		}
		if (carId.length() <= 4) {
			return carId;
		}
		return left(carId, 2).concat(leftPad(right(carId, 2), length(carId) - 2, "*"));
	}

	public static String toFirstUpper(String str) {
		if(isNotBlank(str)) {
			return str.substring(0, 1).toUpperCase() + str.substring(1);
		}
		return "";
	}

	public static boolean containsAnyFromArr(String str, String ...searchArr) {
		if(searchArr != null && searchArr.length > 0) {
			for(String search : searchArr) {
				if(StringUtils.contains(str, search)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 生成订单号 - 无法完全保证唯一性, 不要作为主键使用
	 * @return
	 */
	public static String generateNo() {
		String val = IdGenUtil.uuid();
		String result = Hashing.murmur3_128(0).hashString(val, Charset.defaultCharset()).asLong() + "";
		result = result.replace("-", "");
		int num = 20 - result.length();
		if(num > 0) {
			return result + StringUtils.getRandomNum(num);//这边使用字母能保证唯一
		} else {
			return result;
		}
	}

	/**
	 * 生成api订单号 - 无法完全保证唯一性, 不要作为主键使用
	 * added for v1.9.7 KNZT-4708
	 *
	 * @return String
	 */
	public static String generateApiOrderNo() {
		return "API" + generateNo();
	}


	/**
	 * 生成加密字符串
	 *
	 * @param inputStr
	 * @return
	 */
	public static String getSha256Token4Validate(String inputStr) {
		if(StringUtils.isBlank(inputStr)){
			return null;
		}
		String md5Value = DigestUtils.sha256Hex(inputStr + "7906175eeea411ea990b0c42a106ce722718d3d314da11ea89b97cd30");
		return DigestUtils.sha256Hex(md5Value + md5Value.substring(0, 20));
	}

	public static boolean validateSha256Token(String inputStr, String token) {
		if(StringUtils.isNotBlank(inputStr)) {
			if(StringUtils.equals(getSha256Token4Validate(inputStr), token)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 将首字母大写，剩余小写
	 * @param str
	 * @return
	 */
	public static String upperFirst(String str) {
		if (isNotBlank(str)) {
			return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
		}
		return StringUtils.EMPTY;
	}


	/**
	 * 通过split分割合并args的多个String
	 * @param split 分割String
	 * @param args 待合并的多个String
	 * @return
	 */
	public static String mergeString(String split, String... args) {
		if (args == null || args.length == 0) {
			return "";
		}
		List<String> argList = Arrays.stream(args).filter(StringUtils::isNotBlank).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(argList)) {
			return "";
		}
		StringBuilder rtn = new StringBuilder(argList.get(0));
		if (argList.size() == 1) {
			return rtn.toString();
		}
		for (int i = 1; i < argList.size(); i++) {
			rtn.append(split).append(argList.get(i));
		}
		return rtn.toString();
	}

	/**
	 * added for v1.5.2 KNZT-2393
	 */
	public static String getUserNameFromEmail(String email){
		return StringUtils.substringBefore(email, "@");
	}

	/**
	 * 去除url中参数
	 * added for v1.6.0 KNZT-2716
	 * @param urlStr
	 * @return
	 */
	public static String removeParamsFromUrl(String urlStr) {
		int questionIndex = urlStr.indexOf('?');
		if (questionIndex != -1) {
			return urlStr.substring(0, questionIndex);
		}
		return urlStr;
	}

	/**
	 * 从url解析参数
	 * added for v1.6.0 KNZT-2716
	 * @param urlStr
	 * @return
	 */
	public static Map<String, String> getParamsFromUrl(String urlStr) {
		Map<String, String> params = new HashMap<>();
		int questionIndex = urlStr.indexOf('?');
		if (questionIndex != -1) {
			String query = urlStr.substring(questionIndex + 1);
			String[] pairs = query.split("&");
			for (String pair : pairs) {
				int idx = pair.indexOf("=");
				if (idx != -1) {
					String key = pair.substring(0, idx);
					String value = pair.substring(idx + 1);
					params.put(key, value);
				}
			}
		}
		return params;
	}

	/**
	 * 将参数拼到url后
	 * added for v1.6.0 KNZT-2716
	 * @param urlStr
	 * @param params
	 * @return
	 */
	public static String addParamsToUrl(String urlStr, Map<String, String> params) {
		StringBuilder sb = new StringBuilder(urlStr);
		if (!params.isEmpty()) {
			boolean isFirstParam = !urlStr.contains("?");
			for (Map.Entry<String, String> entry : params.entrySet()) {
				if (isFirstParam) {
					sb.append("?");
					isFirstParam = false;
				} else {
					sb.append("&");
				}
				sb.append(entry.getKey()).append("=").append(entry.getValue());
			}
		}
		return sb.toString();
	}

	/**
	 * 将参数转成url字符串
	 * added for v1.9.0 KNZT-4145
	 *
	 * @param params
	 * @return
	 */
	public static <T> String toUrlParamStr(Map<String, T> params) {
		if (params.isEmpty()) {
			return "";
		}
		return params.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));
	}

	/**
	 * added for v1.7.8 KNZT-3321
	 * 该方法目前仅用于人name中英文在一个字段中时，从分割出中英文名称
	 * 方法逻辑来源于开放平台
	 *
	 * @param name
	 * @return ["中文", "英文"]
	 */
	public static List<String> splitNameCnAndNameEnByName(String name) {
		if (isBlank(name)) {
			return Lists.newArrayList("", "");
		}
		name = name.trim();
		List<String> names = new ArrayList<>();
		// 使用正则表达式检查是否存在中文

		Pattern compile = Pattern.compile("^([\\u4e00-\\u9fa5]*\\s?)(.*)$");
		Matcher matcher = compile.matcher(name);
		if (matcher.find()) {
			names.add(trim(matcher.group(1)));
			names.add(trim(matcher.group(2)));
		}
		return names;
	}

	// added for v1.7.8 KNZT-3321
	// 将十六进制字符串转换为字节数组
	public static byte[] hexStr2ByteArr(String hexStr) {
		if(StringUtils.isBlank(hexStr)){
			return null;
		}
		int len = hexStr.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			// 将每两个字符转换为一个字节
			data[i / 2] = (byte) ((Character.digit(hexStr.charAt(i), 16) << 4)
					+ Character.digit(hexStr.charAt(i + 1), 16));
		}
		return data;
	}

	/**
	 * added for v1.7.8 KNZT-3321
	 * 替换指定字符串的指定区间内字符为"*"
	 * @param str
	 * @param startInclude
	 * @param endExclude
	 * @return
	 */
	public static String hide(String str, int startInclude, int endExclude) {
		return replace(str, startInclude, endExclude, '*');
	}

	/**
	 * added for v1.7.8 KNZT-3321
	 * 替换指定字符串的指定区间内字符为固定字符
	 * @param str	字符串
	 * @param startInclude 	开始位置（包含）
	 * @param endExclude	结束位置（不包含
	 * @param replacedChar	被替换的字符
	 * @return
	 */
	public static String replace(String str, int startInclude, int endExclude, char replacedChar) {
		if (isEmpty(str)) {
			return str;
		}
		final int strLength = str.length();
		if (startInclude > strLength) {
			return str;
		}
		if (endExclude > strLength) {
			endExclude = strLength;
		}
		if (startInclude > endExclude) {
			// 如果起始位置大于结束位置，不替换
			return str;
		}

		final char[] chars = new char[strLength];
		for (int i = 0; i < strLength; i++) {
			if (i >= startInclude && i < endExclude) {
				chars[i] = replacedChar;
			} else {
				chars[i] = str.charAt(i);
			}
		}
		return new String(chars);
	}

	/**
	 * added for v1.8.5 KNZT-3818
	 * 中文转简体
	 * @param txt
	 * @return
	 */
	public static String zhToSimple(String txt) {
		return ZhConverterUtil.toSimple(txt);
	}

	/**
	 * added for v1.8.5 KNZT-3818
	 * 中文转繁体
	 * @param txt
	 * @return
	 */
	public static String zhToTraditional(String txt) {
		if(isEmpty(txt)) {
			return "";
		}
		txt = txt.replace("线", "綫");
		return ZhConverterUtil.toTraditional(txt);
	}

	/**
	 * added for v1.8.5 KNZT-3818
	 * 生成中文的不同变体，包含原文、简体、繁体
	 *
	 * @param str
	 * @return
	 */
	public static Set<String> generateChineseVariants(String str) {
		HashSet<String> results = Sets.newHashSet();
		if (StringUtils.isNotBlank(str)) {
			results.add(str);
			if (StringUtils.isContainChinese(str)) {
				results.add(StringUtils.zhToSimple(str));
				results.add(StringUtils.zhToTraditional(str));
			}
		}
		return results;
	}

	// removed for v1.9.5 KNZT-4510 使用NumberUtils.formatRptUnit替代
//	/**
//	 * 格式化数字，正数前加"+"
//	 * added for v1.8.6 KNZT-3791
//	 *
//	 * @param number
//	 * @return
//	 */
//	public static String formatWithSign(BigDecimal number) {
//		if (number == null) {
//			return null;
//		}
//		if (number.compareTo(BigDecimal.ZERO) > 0) {
//			return "+" + number.stripTrailingZeros().toPlainString();
//		} else {
//			return number.stripTrailingZeros().toPlainString();
//		}
//	}

	/**
	 * added for v1.9.1 KNZT-4101
	 * 将字符串中的中文括号转换为英文括号。
	 *
	 * @param input 输入的字符串
	 * @return 转换后的字符串
	 */
	public static String convertBracketsCn2En(String input) {
		if (isBlank(input)) {
			return input;
		}
		return input.replace("（", "(").replace("）", ")")
				.replace("【", "[").replace("】", "]");
	}

	/**
	 * added for v1.9.1 KNZT-4101
	 * 将字符串中的中文括号转换为英文括号。
	 *
	 * @param input 输入的字符串
	 * @return 转换后的字符串
	 */
	public static String convertBracketsEn2Cn(String input) {
		if (isBlank(input)) {
			return input;
		}
		return input.replace("(", "（").replace(")", "）")
				.replace("[", "【").replace("]", "】");
	}

	/**
	 * 去除尾部标点符号
	 * added for v2.0.2 chenbl KNZT-5353
	 *
	 * @param input
	 * @return
	 */
	public static String removeEndPunctuation(String input) {
		Set<String> set = Sets.newHashSet(".", ",", "!", "?", ":", ";");
		for (String s : set) {
			input = removeEnd(input, s);
		}
		return input;
	}

	/**
	 * 检查用户名和密码是否相似, 需要至少连续similarLength位字符相同
	 * added for v2.0.4 chenbl KNZT-5442
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static boolean isSimilar(String str1, String str2, int similarLength) {
		if (StringUtils.isBlank(str1) || StringUtils.isBlank(str2)) {
			return false;
		}
		int str1Length = str1.length();
		int str2Length = str2.length();
		if (str1Length < similarLength || str2Length < similarLength) {
			return false;
		}
		if (StringUtils.equals(str1, str2)) {
			return true;
		}
		// 检查str1中是否有任何连续4个字符与str2中的任何连续4个字符相同
		for (int i = 0; i <= str1Length - similarLength; i++) {  // 减4是因为我们需要至少4个字符的连续
			String userSubstring = str1.substring(i, i + similarLength);
			for (int j = 0; j <= str2Length - similarLength; j++) {
				String passwordSubstring = str2.substring(j, j + similarLength);
				if (userSubstring.equals(passwordSubstring)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 检查文本中是否存在成对的 <em> 标签，忽略大小写。
	 * @param text 输入的文本
	 * @return 如果存在成对的 <em> 标签返回 true，否则返回 false
	 */
	public static boolean hasPairedEmTags(String text) {
		if (isBlank(text)) {
			return false;
		}
		Matcher matcher = PAIRED_EM_TAGS_PATTERN.matcher(text);
		return matcher.find();
	}

	public static String removeEmTags(String input) {
		if (StringUtils.isBlank(input)) {
			return input;
		}
		return input.replaceAll("(?i)</?em>", "");
	}

	// 查找所有匹配的 <em>...</em> 内容
	public static String getEmContent(String str) {
		if (isBlank(str)) {
			return "";
		}
		Matcher matcher = PAIRED_EM_TAGS_PATTERN.matcher(str);
		StringBuilder emContent = new StringBuilder();
		while (matcher.find()) {
			emContent.append(matcher.group(1)); // 添加匹配到的文本内容
		}
		return emContent.toString();
	}

	/**
	 * 给str中的words添加<em>标签
	 *
	 * @param text 待处理的字符串
	 * @param searchTerms 搜索词
	 * @return
	 */
	public static String highlightWithEm(String text, String searchTerms) {
		if (StringUtils.isBlank(text) || StringUtils.isBlank(searchTerms)) {
			return text;
		}
		// Split the search terms by spaces and generate all continuous combinations
		List<String> combinations = getContinuousCombinations(searchTerms.split("\\s+"));

		// Sort the combinations by length in descending order
		combinations.sort(Comparator.comparingInt(String::length).reversed());

		// Create a regex pattern with word boundaries
		StringBuilder patternBuilder = new StringBuilder();
		for (int i = 0; i < combinations.size(); i++) {
			if (i > 0) {
				patternBuilder.append("|");
			}
			patternBuilder.append("\\b").append(Pattern.quote(combinations.get(i))).append("\\b");
		}

		Pattern pattern = Pattern.compile(patternBuilder.toString(), Pattern.CASE_INSENSITIVE);
		Matcher matcher = pattern.matcher(text);

		// Replace occurrences of the search terms with highlighted versions
		StringBuffer sb = new StringBuffer();
		while (matcher.find()) {
			matcher.appendReplacement(sb, "<em>" + matcher.group() + "</em>");
		}
		matcher.appendTail(sb);

		return sb.toString();
	}

	private static List<String> getContinuousCombinations(String[] words) {
		List<String> combinations = new ArrayList<>();
		for (int start = 0; start < words.length; start++) {
			StringBuilder combination = new StringBuilder();
			for (int end = start; end < words.length; end++) {
				if (end > start) {
					combination.append(" ");
				}
				combination.append(words[end]);
				combinations.add(combination.toString());
			}
		}
		return combinations;
	}

	public static String addEm(String str) {
		if (StringUtils.isBlank(str)) {
			return str;
		}
		return "<em>" + str + "</em>";
	}

	// 处理手机号带区号的形式，形如+86 xxx，返回手机号部分xxx
	public static String removeAreaCode4Phone(String phone) {
		if (StringUtils.isBlank(phone)) {
			return phone;
		}
		List<String> split = split2ListV2(phone, " ");
		if (split.size() == 2) {
			return split.get(1);
		}
		return phone;
	}

	/**
	 * 获取URL链接的域名信息
	 * 
	 * @param url
	 * @return
	 */
	public static String getDomainFromUrl(String url) {
		if (isBlank(url)) {
			return "";
		}
		try {
			URL urlObj = new URL(url);
			return urlObj.getHost();
		} catch (Exception e) {
			return "";
		}
	}

	public static String joinIgnoreNull(Iterable<CharSequence> elements, String separator) {
		if (elements == null) {
			return "";
		}
		return StreamSupport.stream(elements.spliterator(), false)
				.filter(StringUtils::isNotBlank)
				.map(Object::toString)
				.collect(Collectors.joining(separator));
	}

	/**
	 * 格式化原始值和标准值，返回类似 “Standard Value (Original Value)” 的字符串
	 *
	 * @param standardValue 标准值
	 * @param originalValue 原始值
	 * @return 格式化后的字符串
	 */
	public static String formatOriginalWithStandardValue(String standardValue, String originalValue) {
		if (isBlank(originalValue)) {
			return standardValue;
		} else if (isBlank(standardValue)) {
			return originalValue;
		} else {
			if (equalsIgnoreCase(standardValue, originalValue)) {
				return standardValue;
			} else {
				return standardValue + " (" + originalValue + ")";
			}
		}
	}

	public static String getIfNotPersonKeyNo(String keyNo) {
		return startsWithIgnoreCase(keyNo, "p") ? null : keyNo;
	}

    public static boolean getIfPerson(String keyNo) {
        return startsWithIgnoreCase(keyNo, Constants.CorpTypePrefix.PERSON_PREFIX);
    }
}
