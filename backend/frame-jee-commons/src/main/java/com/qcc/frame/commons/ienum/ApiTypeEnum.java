package com.qcc.frame.commons.ienum;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * api type 枚举
 *
 * <AUTHOR>
 * @datetime 2024/9/12 14:26
 */
@Getter
public enum ApiTypeEnum {
    CN_BASIC("API_CN_BASIC", ReportTypeEnum.BASIC.getDesc(), ApiCodeEnum.BASIC.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.BASIC.getCode()),
    CN_UBO("API_CN_UBO", ReportTypeEnum.UBO.getDesc(), ApiCodeEnum.UBO.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.UBO.getCode()),
    CN_ADVANCED("API_CN_ADVANCED", ReportTypeEnum.ADVANCED.getDesc(), ApiCodeEnum.ADVANCED.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.ADVANCED.getCode()),
    CN_VERIFY_CORP("API_CN_VERIFY_CORP", ReportTypeEnum.VERIFY_CORP.getDesc(), ApiCodeEnum.VERIFY_CORP.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.VERIFY_CORP.getCode()),
    CN_LITE("API_CN_LITE", ReportTypeEnum.LITE.getDesc(), ApiCodeEnum.LITE.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.LITE.getCode()),
    CN_MERCHANT("API_CN_MERCHANT", ReportTypeEnum.MERCHANT.getDesc(), ApiCodeEnum.MERCHANT_ONBOARDING.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.MERCHANT.getCode()),
    CN_FIN_TAX("API_CN_FIN_TAX", ReportTypeEnum.FIN_TAX.getDesc(), ApiCodeEnum.FIN_TAX.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.FIN_TAX.getCode()),
    HK_LITE("API_HK_LITE", ReportTypeEnum.LITE.getDesc(), ApiCodeEnum.LITE.getCode(), UnitGroupEnum.HK_UNIT.getGroup(), null),// added for v2.3.5 fensw KNZT-8341 香港企业KYC-LITE,内部统计区分，不用做产品开通 无需做出境统计，所以不需要reportType
    CN_PERS_BASIC("API_CN_PERS_BASIC", ReportTypeEnum.PERS_BASIC.getDesc(), ApiCodeEnum.PERS_BASIC.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), ReportTypeEnum.PERS_BASIC.getCode()),
    CN_LITIGATION("API_CN_LITIGATION", ApiCodeEnum.LITIGATION.getCodeDesc(), ApiCodeEnum.LITIGATION.getCode(), UnitGroupEnum.CN_UNIT.getGroup(), null),// 司法案件单独产品
    ;
    private final String code;
    private final String desc;
    private final String apiCode;
    private final String unitGroup;
    private final String reportType;


    ApiTypeEnum(String code, String desc, String apiCode, String unitGroup, String reportType) {
        this.code = code;
        this.desc = desc;
        this.apiCode = apiCode;
        this.unitGroup = unitGroup;
        this.reportType = reportType;
    }

    public static List<String> withBasicList() {
        return Lists.newArrayList(CN_BASIC.getCode(), CN_UBO.getCode(), CN_MERCHANT.getCode());
    }

    public static List<String> withUBOList() {
        return Lists.newArrayList(CN_UBO.getCode(), CN_MERCHANT.getCode());
    }

    // 能查询单独ubo信息的API类型，UBO订单不支持（特定）
    public static List<String> canQueryCorpUBOList() {
        return Lists.newArrayList(CN_MERCHANT.getCode());
    }

    public static List<String> withCorpLitigationList() {
        return Lists.newArrayList(CN_LITIGATION.getCode());
    }

    public static List<String> withMerchant() {
        return Lists.newArrayList(CN_MERCHANT.getCode());
    }

    public static ApiTypeEnum getEnumByCode(String code) {
        for (ApiTypeEnum enumItem : values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }

    public static ApiTypeEnum getEnumByApiCode(String apiCode) {
        for (ApiTypeEnum enumItem : values()) {
            if (enumItem.getApiCode().equals(apiCode)) {
                return enumItem;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (ApiTypeEnum modeEnum : ApiTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getDesc();
            }
        }
        return "";
    }


    /**
     * 特殊逻辑说明
     * 服务名称 + (API)：只给扣费明细页面（purchase history-前台、后台扣费明细）
     *
     * @param code
     * @return
     */
    public static String getDesc4TransactionPage(String code) {
        for (ApiTypeEnum modeEnum : ApiTypeEnum.values()) {
            if (modeEnum.getCode().equals(code)) {
                return modeEnum.getDesc() + " (API)";
            }
        }
        return "";
    }

    // 支持异步回调获取企业数据的API接口
    public static List<String> withAsyncCorpInfoTypeList() {
        return Lists.newArrayList(CN_BASIC.getCode(), CN_UBO.getCode(), CN_ADVANCED.getCode(), CN_MERCHANT.getCode(), CN_FIN_TAX.getCode(), CN_LITIGATION.getCode());
    }

    // 支持异步回调获取人员数据的API接口
    public static List<String> withAsyncPersInfoTypeList() {
        return Lists.newArrayList(CN_PERS_BASIC.getCode());
    }

    // 具有企业数据的api接口
    public static List<String> withCorpInfoTypeList() {
        return Lists.newArrayList(CN_BASIC.getCode(), CN_UBO.getCode(), CN_ADVANCED.getCode(), CN_LITE.getCode(), CN_MERCHANT.getCode(), CN_FIN_TAX.getCode(), CN_LITIGATION.getCode());
    }

    // 具有高管数据的api接口
    public static List<String> withPersonInfoTypeList() {
        return Lists.newArrayList(CN_PERS_BASIC.getCode());
    }

    public static List<String> withApiCodeList() {
        return Lists.newArrayList(CN_BASIC.getApiCode(), CN_UBO.getApiCode(), CN_ADVANCED.getApiCode(), CN_VERIFY_CORP.getApiCode(), CN_LITE.getApiCode(), CN_MERCHANT.getApiCode(), CN_FIN_TAX.getApiCode(), CN_PERS_BASIC.getApiCode(), CN_LITIGATION.getApiCode());
    }

    // 需要回调的api接口
    public static List<String> withNeedCallbackTypeList() {
        return Lists.newArrayList(CN_BASIC.getCode(), CN_UBO.getCode(), CN_ADVANCED.getCode(), CN_MERCHANT.getCode(), CN_FIN_TAX.getCode(), CN_PERS_BASIC.getCode(), CN_LITIGATION.getCode());
    }

    // mock模型不提供接口名单
    public static List<String> withNoMockApiCodeList() {
        return Lists.newArrayList(CN_VERIFY_CORP.getApiCode(), CN_MERCHANT.getApiCode());
    }
}
