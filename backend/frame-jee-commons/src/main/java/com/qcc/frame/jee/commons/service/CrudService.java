package com.qcc.frame.jee.commons.service;

import com.google.common.collect.Lists;
import com.qcc.frame.jee.base.AbstractCondition;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.DataEntity;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Service基类
 */
//@Transactional(readOnly = true)
public abstract class CrudService<D extends CrudDao<T>, T extends DataEntity<T>> extends BaseService {
	
//	protected Logger logger = LoggerFactory.getLogger(getClass());

	/**
	 * 持久层对象
	 */
	@Autowired
	protected D dao;
	
	/**
	 * 获取单条数据
	 * @param id
	 * @return
	 */
	public T get(String id) {
		return dao.get(id);
	}
	
	/**
	 * 获取单条数据
	 * @param entity
	 * @return
	 */
	public T get(T entity) {
		return dao.get(entity);
	}
	
	/**
	 * 查询列表数据
	 * @param entity
	 * @return
	 */
	public List<T> findList(T entity) {
		return dao.findList(entity);
	}
	
	/**
	 * 查询分页数据
	 * @param condition
	 * @return
	 */
	public Page<T> findListByCondtion(AbstractCondition<T> condition){
		condition.getPage().setList(dao.findListByCondtion(condition));
		return condition.getPage();
	};
	/**
	 * 查询分页数据
	 * @param page 分页对象
	 * @param entity
	 * @return
	 */
	public Page<T> findPage(Page<T> page, T entity) {
		entity.setPage(page);
		page.setList(dao.findList(entity));
		return page;
	}

	/**
	 * 保存数据（插入或更新）
	 * @param entity
	 */
	//@Transactional(readOnly = false)
	public void save(T entity) {
		if (entity.getIsNewRecord()){
			entity.preInsert();
			dao.insert(entity);
		}else{
			entity.preUpdate();
			dao.update(entity);
		}
	}

	
	public String getLoginUserId() {
		if("job".equals(Global.getConfig("application.type"))) {
			return Global.getConfig("application.type.job.createby");
		} else {
			User user = UserUtils.getUser();
			if(user != null) {
				return user.getId();
			}
		}
		return "";
	}

	public void batchInsert(List<T> entityList) {
		if(entityList != null && entityList.size() > 0) {
			for(T entity : entityList) {
				entity.preInsert();
			}
//			DefaultSqlSessionFactory sqlSessionFactory = (DefaultSqlSessionFactory) SpringContextHolder.getBean("sqlSessionFactory");
//			SqlSession batchSqlSession = null;
//			try {
//			    batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
//			    String insertStatement = daoClazz.getName() + ".insert";
//			    for(int index = 0; index < entityList.size(); index++){
//			        batchSqlSession.insert(insertStatement, entityList.get(index));
//			        if(batchCount > 0 && index != 0 && index % batchCount == 0){
//			            batchSqlSession.commit();
//			        }
//			    }
//			    batchSqlSession.commit();
//			} catch (Exception e){
//			     logger.error("", e);
//			     throw new ServiceException(e);
//			} finally {
//			     if(batchSqlSession != null){
//			         batchSqlSession.close();
//			     }
//			}
			dao.batchInsert(entityList);
		}
	}

	public void batchPartialInsert(List<T> entityList) {
		if(entityList != null && entityList.size() > 0) {
			List<List<T>> partList = Lists.partition(entityList, 10000);
			for(List<T> list : partList) {
				for(T entity : list) {
					entity.preInsert();
				}
				dao.batchInsert(list);
			}
		}
	}
	
	/**
	 * 删除数据
	 * @param entity
	 */
	//@Transactional(readOnly = false)
	public void delete(T entity) {
		entity.preUpdate();
		dao.delete(entity);
	}

	public void physicallyDelete(String id) {
		dao.physicallyDelete(id);
	}

}
