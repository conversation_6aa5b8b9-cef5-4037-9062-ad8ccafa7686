package com.qcc.frame.jee.commons.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * added for KNZT-1040
 */
@Setter
@Getter
public class GroupItem implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 2546162149924879524L;
    private String key;
    private String type;
    private List<Item> items = new ArrayList<Item>();

    private String value;
    private String desc;

    public GroupItem() {
    }

    public GroupItem(String type, String key, String value) {
        this.type = type;
        this.key = key;
        this.value = value;
    }
}
