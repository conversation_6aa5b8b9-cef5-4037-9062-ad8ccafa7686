package com.qcc.frame.jee.commons.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * added for KNZT-1040
 */
public class GroupItem implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 2546162149924879524L;
    private String key;
    private String type;
    private List<Item> items = new ArrayList<Item>();

    private String value;
    private String desc;

    public GroupItem() {
    }

    public GroupItem(String type, String key, String value) {
        this.type = type;
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Item> getItems() {
        return items;
    }

    public void setItems(List<Item> items) {
        this.items = items;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
