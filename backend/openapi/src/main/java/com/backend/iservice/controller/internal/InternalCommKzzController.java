package com.backend.iservice.controller.internal;

import com.backend.common.modules.delivery.service.InvoiceBusinessService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.openapi.form.InternalInvoiceForm;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.web.BaseController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/internal")
public class InternalCommKzzController extends BaseController {
    @Resource
    private InvoiceBusinessService invoiceBusinessService;

    @RequestMapping(value = "/invoice/prepaid/generate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> generateInvoice(@RequestBody InternalInvoiceForm form) {
        invoiceBusinessService.asyncGenerateInvoice(form.getKzzInvoiceId());
        return JsonResult.buildSuccess();
    }
}
