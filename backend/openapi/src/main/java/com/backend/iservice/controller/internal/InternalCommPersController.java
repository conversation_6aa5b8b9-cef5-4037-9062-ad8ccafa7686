package com.backend.iservice.controller.internal;

import com.backend.common.global.gateway.EntityDetailCommonForm;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.global.gateway.person.PersonCommonForm;
import com.backend.common.model.json.JsonSimpleResult;
import com.backend.common.modules.common.form.OrderNoBaseForm;
import com.backend.common.modules.person_vrfy.model.OrderPersonVerifyResp;
import com.backend.common.modules.person_vrfy.service.PersonVerifyOrderService;
import com.backend.common.modules.report.form.ReportOrderInitForm;
import com.backend.common.modules.report.model.OrderInitInfoTO;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.openapi.form.ProCorpSearchForm;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.model.ApiVipHoldCompResult;
import com.backend.common.yunjuapi.model.ApiVipHoldCompTO;
import com.backend.common.yunjuapi.model.SeniorPersonCorpInfoResult;
import com.backend.common.yunjuapi.model.SeniorPersonCorpInfoTO;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.web.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

/**
 * add for v1.8.2 KNZT-3688 提供高管snapshot报告数据接口
 * 页面打印处理，内部调用模块-高管信息报告打印
 */
@Controller
@RequestMapping(value = "/internal/pers")
public class InternalCommPersController extends BaseController {

    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private PersonVerifyOrderService personVerifyOrderService;

    // 初始化根据keyNo获取人员中英文名称
    @RequestMapping(value = "/initOrder", method = RequestMethod.POST)
    @ResponseBody
    public JsonSimpleResult<OrderInitInfoTO> initOrder(@RequestBody ReportOrderInitForm form) {
        JsonSimpleResult<OrderInitInfoTO> result = new JsonSimpleResult<>();
        OrderInitInfoTO infoTO;
        try {
            infoTO = commTblCompReportOrderService.getInitOrderInfoForPers(form);
            result.setStatus(Constants.Result.SUCCESS_STR);
            result.setResult(infoTO);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
        }
        return result;
    }


    // "获取企业董监高人员的对外投资、在外任职及担任法定代表人的公司信息"
    @RequestMapping(value = "/listSeniorPerson", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listSeniorPerson(@RequestBody PersonCommonForm form) {
        // updated for v2.2.8 fengsw KNZT-7689
        form.setPersonId(form.getKeyNo());
        PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listSeniorPersonCorp", form);
        commTblCompReportOrderService.handleSeniorPersonInfo(pageDataBO);
        return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
        /*JsonResultList<SeniorPersonCorpInfoTO> result = new JsonResultList<>();
        try {
            SeniorPersonCorpInfoResult autocompleteTOResult = CompanyDetailsInterface.listSeniorPerson(form.getKeyNo(), form.getType(), form.getIsValid(), form.getPageIndex(), form.getPageSize(), false);
            if (autocompleteTOResult != null) {
                result.setResultList(autocompleteTOResult.getResult());
                result.setTotalCount(autocompleteTOResult.getPage() != null ? autocompleteTOResult.getPage().getTotalRecords() : 0);
            }
            result.setStatus(Constants.Result.SUCCESS_STR);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
        }
        return result;*/
    }

    // "获取控制企业", httpMethod = "POST", notes = "获取控制企业")
    @RequestMapping(value = "/getVipHoldCompList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ApiVipHoldCompTO> listVipHoldComp4Global(@RequestBody ProCorpSearchForm form) {
        JsonResultList<ApiVipHoldCompTO> resp = new JsonResultList<>();
        ApiVipHoldCompResult result = CompanyDetailsInterface.listVipHoldComp4Global(form.getKeyNo(), form.getPageIndex(), form.getPageSize(), false);
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
            resp.setResultList(result.getResultList());
            resp.setTotalCount(result.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    @RequestMapping(value = "/verify-detail/get", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<OrderPersonVerifyResp> getVerifyDetail(@RequestBody OrderNoBaseForm form) {
        try {
            OrderPersonVerifyResp orderPersonVerifyResp = personVerifyOrderService.getOrderPersonVerifyDetailByOrderNo(form.getOrderNo());
            return JsonResult.buildSuccess(orderPersonVerifyResp);
        } catch (MessageException e) {
            return JsonResult.buildFail(e.getMessage());
        }
    }

    //    @ApiOperation(value = "获取高管合作伙伴", httpMethod = "POST", notes = "获取高管合作伙伴")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "query")})
    @RequestMapping(value = "/listPersonPartners", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listPersonPartners(@RequestBody EntityDetailCommonForm form) {
        PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listPersonPartners", form);
        return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
    }

    //    @ApiOperation(value = "获取高管合作伙伴-合作详情", httpMethod = "POST", notes = "获取高管合作伙伴-合作详情")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keyNo", value = "人员keyNo", paramType = "body"),
//            @ApiImplicitParam(name = "targetKeyNo", value = "目标人员keyno", paramType = "body"),
//            @ApiImplicitParam(name = "companyKeyNo", value = "默认关联公司", paramType = "body"),
//            @ApiImplicitParam(name = "type", value = "1：正在合作 2：既往合作 0：历史合作", paramType = "body"),
//            @ApiImplicitParam(name = "isValid", value = "0：无效 1：有效", paramType = "body"),
//            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
//            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/listPersonPartnerCooperateDetail", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listPersonPartnerCooperateDetail(@RequestBody EntityDetailCommonForm form) {
        PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listPersonPartnerCooperateDetail", form);
        return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
    }
}

