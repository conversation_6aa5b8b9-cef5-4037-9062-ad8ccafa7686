package com.backend.iservice.controller.external;

import com.backend.common.modules.api.service.CommExternalApiBusinessService;
import com.backend.common.modules.api.service.CommExternalApiDataService;
import com.backend.common.openapi.model.ExternalApiJsonSimpleResultList;
import com.backend.common.openapi.model.ExternalApiPageQuery;
import com.backend.common.openapi.model.ExternalApiRespBaseTO;
import com.backend.common.openapi.model.ExternalApiJsonSimpleResult;
import com.backend.common.openapi.model.ExternalApiSeniorBaseInfoTO;
import com.backend.common.openapi.model.ExternalApiSeniorPersonBaseInfoTO;
import com.backend.common.openapi.model.ExternalApiSeniorPersonInfoTO;
import com.backend.common.openapi.model.ExternalApiValidateResult;
import com.backend.iservice.common.annotation.LogPersistence;
import com.qcc.frame.base.controller.BaseController;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import com.qcc.frame.jee.commons.utils.WebContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping(value = "/external/customer/pers/")
public class ExternalPersDataApiController extends BaseController {
    @Resource
    private CommExternalApiBusinessService apiBusinessService;
    @Resource
    private CommExternalApiDataService apiDataService;

    @LogPersistence
    @RequestMapping(value = "/getExecutive", method = RequestMethod.GET)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalApiRespBaseTO> getCorpBasicInfo(@RequestParam("orderNo") String orderNo) {
        String companyId = WebContextHolder.getRequest().getExtraKey();
        ExternalApiValidateResult validateResult = apiBusinessService.checkBeforeProvideApiData4External(companyId, orderNo);
        ExternalApiRespBaseTO corpBasicInfo = apiDataService.provideApiData4External(validateResult);
        return ExternalApiJsonSimpleResult.buildSuccess(corpBasicInfo);
    }

    // "获取企业董监高人员担任法定代表人的公司信息"
    @LogPersistence
    @RequestMapping(value = "/listLegalRepresentative", method = RequestMethod.GET)
    @ResponseBody
    public ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonBaseInfoTO> listLegalRepresentative(
            @RequestParam("orderNo") String orderNo,
            @RequestParam(value = "pageIndex", required = false) String pageIndex,
            @RequestParam(value = "pageSize", required = false) String pageSize) {
        String companyId = WebContextHolder.getRequest().getExtraKey();
        ExternalApiPageQuery query = apiBusinessService.validatePageParams(pageIndex, pageSize, 5000);
        String keyNo = apiBusinessService.checkApiOrderNoIsInValid(companyId, orderNo, ApiTypeEnum.withPersonInfoTypeList());
        return apiDataService.listLegalRepresentative4Person(keyNo, query.getPageIndex(), query.getPageSize(), "0", "1");
    }

    // "获取企业董监高人员对外投资的公司信息"
    @LogPersistence
    @RequestMapping(value = "/listShareholders", method = RequestMethod.GET)
    @ResponseBody
    public ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonInfoTO> listShareholders(
            @RequestParam("orderNo") String orderNo,
            @RequestParam(value = "pageIndex", required = false) String pageIndex,
            @RequestParam(value = "pageSize", required = false) String pageSize) {
        String companyId = WebContextHolder.getRequest().getExtraKey();
        ExternalApiPageQuery query = apiBusinessService.validatePageParams(pageIndex, pageSize, 5000);
        String keyNo = apiBusinessService.checkApiOrderNoIsInValid(companyId, orderNo, ApiTypeEnum.withPersonInfoTypeList());
        return apiDataService.listShareholders4Person(keyNo, query.getPageIndex(), query.getPageSize(), "1", "1");
    }

    // "获取企业董监高人员在外任职的公司信息"
    @LogPersistence
    @RequestMapping(value = "/listExecutives", method = RequestMethod.GET)
    @ResponseBody
    public ExternalApiJsonSimpleResultList<ExternalApiSeniorBaseInfoTO> listExecutives(
            @RequestParam("orderNo") String orderNo,
            @RequestParam(value = "pageIndex", required = false) String pageIndex,
            @RequestParam(value = "pageSize", required = false) String pageSize) {
        String companyId = WebContextHolder.getRequest().getExtraKey();
        ExternalApiPageQuery query = apiBusinessService.validatePageParams(pageIndex, pageSize, 5000);
        String keyNo = apiBusinessService.checkApiOrderNoIsInValid(companyId, orderNo, ApiTypeEnum.withPersonInfoTypeList());
        return apiDataService.listExecutives4Person(keyNo, query.getPageIndex(), query.getPageSize(), "2", "1");
    }
    //todo:360,根据订单号获取详情
}
