package com.backend.iservice.controller.external;

import com.backend.common.modules.api.service.CommExternalApiBusinessService;
import com.backend.common.openapi.form.ExternalApiVerificationForm;
import com.backend.common.openapi.model.ExternalAPIOrderResponse;
import com.backend.common.openapi.model.ExternalApiJsonSimpleResult;
import com.backend.common.openapi.model.ExternalApiVerificationRespTO;
import com.backend.iservice.common.annotation.LogPersistence;
import com.backend.common.openapi.form.ExternalApiSearchForm;
import com.qcc.frame.base.controller.BaseController;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 对外下单接口
 * 查询有数据则扣费，否则不扣费
 */
@Controller
@RequestMapping(value = "/external/customer")
public class ExternalOrderController extends BaseController {
    @Autowired
    private CommExternalApiBusinessService apiBusinessService;

    @LogPersistence
    @RequestMapping(value = "/corp/submitKYCBasicOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitBasicOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_BASIC);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @LogPersistence
    @RequestMapping(value = "/corp/submitKYCLiteOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitKYCLiteOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiLiteOrder(form.getQccCode());
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @LogPersistence
    @RequestMapping(value = "/corp/submitKYCUBOOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitKYCUBOOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_UBO);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @LogPersistence
    @RequestMapping(value = "/corp/submitKYCAdvancedOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitAdvancedOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_ADVANCED);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @RequestMapping(value = "/corp/submitVerificationOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalApiVerificationRespTO> submitVerificationOrder(@RequestBody ExternalApiVerificationForm form) {
        ExternalApiVerificationRespTO resultObj = apiBusinessService.submitApiVerificationOrder(form, ApiTypeEnum.CN_VERIFY_CORP);
        return ExternalApiJsonSimpleResult.buildSuccess(resultObj);
    }

    @RequestMapping(value = "/corp/submitMerchantOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitMerchantOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_MERCHANT);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @RequestMapping(value = "/corp/submitFTOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitFTOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_FIN_TAX);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @RequestMapping(value = "/corp/submitKYCLitigationOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitKYCLitigationOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder(form.getQccCode(), ApiTypeEnum.CN_LITIGATION);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }

    @RequestMapping(value = "/pers/submitKYCExecutiveOrder", method = RequestMethod.POST)
    @ResponseBody
    public ExternalApiJsonSimpleResult<ExternalAPIOrderResponse> submitExecutiveOrder(@RequestBody ExternalApiSearchForm form) {
        ExternalAPIOrderResponse response = apiBusinessService.submitApiOrder4Pers(form.getPersonKeyNo(), ApiTypeEnum.CN_PERS_BASIC);
        return ExternalApiJsonSimpleResult.buildSuccess(response);
    }
}

