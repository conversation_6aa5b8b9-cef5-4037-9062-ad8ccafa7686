package com.qcc.frame.utils;

import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.ReaderProperties;
import com.itextpdf.kernel.pdf.StampingProperties;
import com.itextpdf.signatures.*;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.util.zip.CRC32;
import java.util.zip.Checksum;

/**
 * added for v1.8.9 KNZT-4073
 * pdf 工具类
 *
 * <AUTHOR>
 * @datetime 2024/7/22 10:41
 */
public class PdfUtils {
    protected static Logger log = LoggerFactory.getLogger(PdfUtils.class);

    /**
     * added for v2.0.2 chenbl KNZT-5292
     * 加密pdf
     * @param oriFile
     * @param tarFile
     * @param password
     * @throws IOException
     * @throws DocumentException
     */
    public static void encryptPdf(File oriFile, File tarFile, String password) throws IOException, DocumentException {
        try (InputStream inputStream = new FileInputStream(oriFile);
             OutputStream outputStream = new FileOutputStream(tarFile)) {
            com.itextpdf.text.pdf.PdfReader pdfReader = new com.itextpdf.text.pdf.PdfReader(inputStream);
            PdfStamper pdfStamper = new PdfStamper(pdfReader, outputStream);
            pdfStamper.setEncryption(password.getBytes(),
                    password.getBytes(),
                    PdfWriter.ALLOW_PRINTING | PdfWriter.ALLOW_COPY,
                    PdfWriter.ENCRYPTION_AES_128);
            pdfStamper.close();
            pdfReader.close();
        }
    }

    /**
     * updated for v2.0.2 chenbl KNZT-5292
     * 电子签章
     *
     * @param oriFile  需要签章的pdf文件
     * @param tarFile 签完章的pdf文件
     * @param pdfPassword pdf密码
     */
    public static void signPdf(File oriFile, File tarFile,
                               String keystorePath, String keystorePwd, String pdfPassword) throws IOException, GeneralSecurityException {
        try (InputStream inputStream = new FileInputStream(oriFile);
             OutputStream outputStream = new FileOutputStream(tarFile)) {
            PdfReader reader = null;
            if (StringUtils.isNotBlank(pdfPassword)) {
                ReaderProperties readerProperties = new ReaderProperties();
                readerProperties.setPassword(pdfPassword.getBytes());
                reader = new PdfReader(inputStream, readerProperties);
            } else {
                reader = new PdfReader(inputStream);
            }
            //读取keystore ，获得私钥和证书链 jks
            KeyStore ks = KeyStore.getInstance("JKS");
            char[] keystorePwdCharArray = keystorePwd.toCharArray();
            ks.load(new FileInputStream(keystorePath), keystorePwdCharArray);
            String alias = ks.aliases().nextElement();
            PrivateKey pk = (PrivateKey) ks.getKey(alias, keystorePwdCharArray);
            Certificate[] certificates = ks.getCertificateChain(alias);
            //创建签章工具PdfSigner、设定数字签章的属性
            PdfSigner signer = new PdfSigner(reader, outputStream, new StampingProperties());
            //No such provider: BC : 问题解决，加BC库支持
            Security.addProvider(new BouncyCastleProvider());
            //摘要算法
            IExternalDigest digest = new BouncyCastleDigest();
            //签名算法
            IExternalSignature signature = new PrivateKeySignature(pk, DigestAlgorithms.SHA256, BouncyCastleProvider.PROVIDER_NAME);
            //调用itext签名方法完成pdf签章
            signer.setCertificationLevel(1); // 设置认证级别
            signer.signDetached(digest, signature, certificates, null, null, null, 0, PdfSigner.CryptoStandard.CMS);
        }
    }

    public static long getCRC32Checksum(File file) throws IOException {
        FileInputStream fis = null;
        ByteArrayOutputStream bos = null;
        try {
            fis = new FileInputStream(file);
            bos = new ByteArrayOutputStream();
            byte[] bytes = new byte[(int) file.length()];
            int len;
            while((len = fis.read(bytes))!=-1){
                bos.write(bytes,0,len);
            }
           return getCRC32Checksum(bos.toByteArray());
        } catch (IOException e) {
            log.error("file to byte error, e:");
            throw e;
        } finally {
            if (bos != null) {
                bos.close();
            }
            if (fis != null) {
                fis.close();
            }
        }

    }



    public static long getCRC32Checksum(byte[] bytes) {
        Checksum crc32 = new CRC32();
        crc32.update(bytes, 0, bytes.length);
        return crc32.getValue();
    }
}
