package com.qcc.modules.converter.controller;

import com.qcc.frame.exception.MessageException;
import com.qcc.modules.converter.form.ConvertCorpReportForm;
import com.qcc.modules.converter.form.TestForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qcc.frame.base.controller.BaseController;
import com.qcc.frame.json.JsonResult;
import com.qcc.modules.converter.service.TblWebConverterService;


@RestController
@RequestMapping(value = "/converter")
public class ConverterController extends BaseController {
	
	@Autowired
	private TblWebConverterService tblWebConverterService;


    /**
     * added for v1.7.9 KNZT-3396
     * 转换生成企业报告
     *
     * @param form
     * @return JsonResult<String>
     */
    @RequestMapping(value = "/corpReport")
    public JsonResult<String> convertCorpReport(ConvertCorpReportForm form) {
        JsonResult<String> result = new JsonResult<String>();
        try {
            tblWebConverterService.convertCorpReport(form);
            result.setResult("SUCCESS");
            result.setStatus("200");
        } catch (MessageException e) {
            result.setResult(e.getMessage());
            result.setStatus("500");
        }
        return result;
    }
	
	@RequestMapping(value = "convert2Pdf")
    public JsonResult<String> convert2Pdf() {
		JsonResult<String> result = new JsonResult<String>();
        return result; 
    }
	
	
	@RequestMapping(value = "test")
    public JsonResult<String> test(String id) {
		JsonResult<String> result = new JsonResult<String>();
//		tblWebConverterService.saveTest();
        return result;
    }

	@RequestMapping(value = "testJson")
    public JsonResult<String> testJson(@RequestBody TestForm form) {
		JsonResult<String> result = new JsonResult<String>();

        return result;
    }
}
