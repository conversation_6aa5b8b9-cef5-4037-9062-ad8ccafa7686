package com.qcc.modules.converter.service;

import java.io.File;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import com.qcc.frame.constant.SysConstants;
import com.qcc.frame.exception.MessageException;
import com.qcc.frame.utils.*;
import com.qcc.frame.utils.ChromeUtils;
import com.qcc.frame.utils.FileUtils;
import com.qcc.frame.utils.HttpUtils;
import com.qcc.frame.utils.PdfUtils;
import com.qcc.frame.utils.StringUtils;
import com.qcc.modules.converter.form.ConvertCorpReportForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qcc.frame.base.service.CrudService;
import com.qcc.frame.thirdparty_service.HuaweiObsServUtils;
import com.qcc.modules.converter.dao.TblWebConverterDao;
import com.qcc.modules.converter.dao.TblWebConverterEntityMapper;
import com.qcc.modules.converter.entity.TblWebConverter;


@Service
public class TblWebConverterService extends CrudService<TblWebConverterDao, TblWebConverterEntityMapper, TblWebConverter> {
	protected static Logger log = LoggerFactory.getLogger(TblWebConverterService.class);

	@Autowired
	private HuaweiObsServUtils huaweiObsServUtils;
	@Autowired
	private ExecutorService singleThreadExecutor;

	@Value("${convert.temp-file-path}")
	private String tempFilePath;

	@Value("${convert.url-template}")
	private String urlTemplate;

	@Value("${convert.chrome-path}")
	private String chromePath;

	@Value("${convert.sign.keystore.path}")
	private String keystorePath;

	@Value("${convert.sign.keystore.password}")
	private String keystorePwd;

	@Value("${http.global.url}")
	private String globalUrl;
	@Value("${http.global.key}")
	private String globalKey;
	@Value("${http.global.secretKey}")
	private String globalSecretKey;

	@Value("${common.aes.key}")
	private String commonAesKey;

	@Value("${common.aes.iv}")
	private String commonAesIv;

	/**
	 * added for v1.7.9 KNZT-3396 转换生成企业报告
	 *
	 * @param form
	 * @return
	 */
	public void convertCorpReport(ConvertCorpReportForm form) throws MessageException {
		String keyword = StringUtils.isNotBlank(form.getUrlKeyword()) ? "/" + form.getUrlKeyword() : "";
		String sourceUrl = String.format(urlTemplate, keyword, form.getKeyNo(), form.getReportType(), form.getOrderNo(), form.getPicId(), form.getLoginName()); // updated for v2.0.5 chenbl KNZT-5562
		if (StringUtils.isBlank(sourceUrl)) {
			throw new MessageException("未找到reportType对应的页面地址, reportType:" + form.getReportType());
		}
		// 保存记录
		TblWebConverter webConverter = new TblWebConverter();
		webConverter.setBeginTime(DateUtils.getCurrentDate());
		webConverter.setConvertStatus("P");
		webConverter.setRefTableSign("tbl_comp_report_order");
		webConverter.setRefTableId(form.getOrderId());
		webConverter.setConvertUrl(sourceUrl);
		super.save(webConverter);

		// updated for v2.0.2 chenbl KNZT-5292
		singleThreadExecutor.execute(() -> {
            List<File> tempFiles = new ArrayList<>();
            try {
				// 临时文件清理失败优化 updated for v2.0.5 chenbl KNZT-5599
                File printFile = new File(getRandomTempFileName(".pdf"));
				tempFiles.add(printFile);
                getPrintFile(sourceUrl, printFile);
                String docPassword = null;
				if (SysConstants.Report.NEED_ENCRYPT.equals(form.getNeedEncrypt())) {
					docPassword = StringUtils.generatePassword(8);
					// 加密报告
					String encryptFilePath = getRandomTempFileName(".pdf");
					File encryptFile = new File(encryptFilePath);
					tempFiles.add(encryptFile);
					PdfUtils.encryptPdf(printFile, encryptFile, docPassword);
					printFile = encryptFile;
					webConverter.setDocPassword(encrypt(docPassword));
				}
                // added for v1.8.9 KNZT-4073 加签
                String signFilePath = getRandomTempFileName(".pdf");
                File signFile = new File(signFilePath);
				tempFiles.add(signFile);
                PdfUtils.signPdf(printFile, signFile, keystorePath, keystorePwd, docPassword);
                printFile = signFile;
                long crc32Checksum = PdfUtils.getCRC32Checksum(printFile);
                // 上传华为云
                String name = String.format("Report/%s/%s.pdf", form.getOrderNo(), form.getFileName());
                String url = huaweiObsServUtils.putObject(name, printFile);
                // 更新报告
                int updatedCount = dao.executeRefOrderUpdate(webConverter.getRefTableId(), url);
                log.info("web convert update order, id:{}, url:{}, updatedCount:{}", webConverter.getRefTableId(), url, updatedCount);
                // 更新处理过程为成功
                webConverter.setConvertStatus("S");
                webConverter.setEndTime(DateUtils.getCurrentDate());
                webConverter.setGenUrl(url);
                webConverter.setFileCheckSum(String.valueOf(crc32Checksum));

                super.save(webConverter);
                // 增加回调 added for lvcy v2.0.2 KNZT-5131
                notifyOrder(form.getOrderId(), "Y", "");
                log.info("web convert success {} , form:{}", sourceUrl, form);
			} catch (Exception e) {
				log.error("web convert failed {} , orderId:{}, e:", sourceUrl, form.getOrderId(), e);
				// 更新处理过程为失败
				webConverter.setConvertStatus("F");
				webConverter.setEndTime(DateUtils.getCurrentDate());
				webConverter.setErrorInfo(StringUtils.left(Arrays.toString(e.getStackTrace()), 1000));
				super.save(webConverter);
				// 增加回调 added for lvcy v2.0.2 KNZT-5131
				notifyOrder(form.getOrderId(), "N", e.getMessage());
			} finally {
				for (File tempFile : tempFiles) {
					try {
						FileUtils.deleteFile(tempFile);
					} catch (Exception e) {
						log.error("web convert file operate failed e:", e);
					}
				}
			}
		});
	}

	// updated for v2.0.2 chenbl KNZT-5292
	private void getPrintFile(String sourceUrl, File tarFile) throws MessageException {
		boolean pdfRes = ChromeUtils.print2Pdf(tarFile.getAbsolutePath(), sourceUrl, chromePath, true);
		// 保存pdf
		if (pdfRes) {
			if (!tarFile.exists()) {
				throw new MessageException("页面加载失败, 未生成pdf, 地址:" + sourceUrl);
			}
			if (StringUtils.isNotBlank(tarFile.getName()) && StringUtils.containsAny(tarFile.getName(), "Error", "error")) {
				throw new MessageException("页面加载失败, 打印异常文件, 地址:" + sourceUrl);
			}
			double fileSizeInKB = tarFile.length() / 1024.0;
			if (fileSizeInKB < 60) {
				throw new MessageException("页面加载失败, 文件异常过小, 地址:" + sourceUrl);
			}
		} else {
			throw new MessageException("ChromeUtils.print2Pdf failed. 地址:" + sourceUrl);
		}
	}

	private String getRandomTempFileName(String suffix) {
		return tempFilePath + StringUtils.getRandomChar2(16) + suffix;
	}

	/**
	 * 通知订单生成报告成功或失败
	 * added for lvcy v2.0.2 KNZT-5131
	 *
	 * @param orderId
	 * @param status
	 * @param remark
	 * @return
	 */
	private void notifyOrder(String orderId, String status, String remark) {
		try {
			Map<String, String> paramMap = new HashMap<>();
			paramMap.put("orderId", orderId);
			paramMap.put("success", status);
			paramMap.put("remark", remark);
			paramMap.put("key", globalKey);
			Map<String, String> headerMap = new HashMap<>();
			long tm = System.currentTimeMillis() / 1000;
			headerMap.put("Timespan", tm+"");
			headerMap.put("Token", encodeMd5(globalKey + tm + globalSecretKey));
			String url = globalUrl + "/internal/report/reportGeneratedCallback";
			HttpUtils.post(url, paramMap, headerMap);
		} catch (Exception e) {
			log.error("notifyOrder failed, e:", e);
		}
	}

	private static String encodeMd5(String input) {
		try {
			MessageDigest md5 = MessageDigest.getInstance("md5");
			md5.update(input.getBytes("utf-8"));
			byte[] b = md5.digest();
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < b.length; i++) {
				String s = Integer.toHexString(b[i] & 0xFF);
				if (s.length() == 1) {
					sb.append("0");
				}
				sb.append(s.toUpperCase());
			}
			return sb.toString();
		} catch (Exception ex) {
			log.error(" Md5 String {" + input + "},failed....", ex);
		}
		return input;
	}

	// added for v2.0.2 chenbl KNZT-5292
	private String encrypt(String docPassword) {
		try {
			return AesUtil.encrypt(docPassword, commonAesKey, commonAesIv);
		} catch (Exception e) {
			throw new RuntimeException("加密失败", e);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void saveTest() {
		TblWebConverter entity = new TblWebConverter();
		entity.setRefTableSign("order");
		entity.setRefTableId("111");
		entity.setBeginTime(DateUtils.getCurrentDate());
		entity.setConvertStatus("P");
		entity.setConvertUrl("test");
		super.save(entity);
//		refTableSign;
//		private String ref_table_id;
//		private String convert_url;
//		private Date begin_time;
//		private Date end_time;
//		private String convert_status;
		
//		uploadFileTest();
		
		List<TblWebConverter> list = new ArrayList<>();
		
		for(int i = 0; i < 10; i++) {
			entity = new TblWebConverter();
			entity.setRefTableSign("order");
			entity.setRefTableId("111");
			entity.setBeginTime(DateUtils.getCurrentDate());
			entity.setConvertStatus("P");
			entity.setConvertUrl("test" + i);
			list.add(entity);
		}
		super.batchInsert(list);
		
//		throw new RuntimeException("====");
	}
	
	public void uploadFileTest() {
		File file = new File("D:\\DEV\\release-prod\\test.txt");
		
		String downloadUrl = huaweiObsServUtils.putObject("FrontUpd/34323421/5434543/" + IdGenUtil.uuid() + ".txt", file);
		System.out.println(downloadUrl);
	}
}
