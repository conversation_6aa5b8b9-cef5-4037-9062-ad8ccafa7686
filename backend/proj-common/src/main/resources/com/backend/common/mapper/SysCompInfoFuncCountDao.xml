<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.mapper.SysCompInfoFuncCountDao">
	<sql id="sysCompInfoFuncCountColumns">
		a.id AS "id",
		a.company_id AS "companyId",
		a.function_table_id AS "functionTableId",
		a.count_std AS "countStd",
		a.total_count AS "totalCount",
		a.consumed_count AS "consumedCount",
		a.main_func_flag AS "mainFuncFlag",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.version AS "version"
	</sql>

	<select id="get" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>

	<insert id="insert">
		INSERT INTO sys_comp_info_func_count(
			id,
			company_id,
			function_table_id,
			count_std,
			total_count,
			consumed_count,
			main_func_flag,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			version
		) VALUES (
			#{id},
			#{companyId},
			#{functionTableId},
			#{countStd},
			#{totalCount},
			#{consumedCount},
			#{mainFuncFlag},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{version}
		)
	</insert>

	<insert id="batchInsert">
		INSERT INTO sys_comp_info_func_count(
			id,
			company_id,
			function_table_id,
			count_std,
			total_count,
			consumed_count,
			main_func_flag,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date,
			version
		) VALUES
		<foreach collection="list" item="obj" index="index" separator=",">
		(
			#{obj.id},
			#{obj.companyId},
			#{obj.functionTableId},
			#{obj.countStd},
			#{obj.totalCount},
			#{obj.consumedCount},
			#{obj.mainFuncFlag},
			#{obj.delFlag},
			#{obj.createBy.id},
			#{obj.createDate},
			#{obj.updateBy.id},
			#{obj.updateDate},
			#{obj.version}
		)
		</foreach>
	</insert>

	<update id="update">
		UPDATE sys_comp_info_func_count SET
			company_id = #{companyId},
			function_table_id = #{functionTableId},
			count_std = #{countStd},
			del_flag = #{delFlag},
			total_count = #{totalCount},
			consumed_count = #{consumedCount},
			main_func_flag = #{mainFuncFlag},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			version = #{version}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE sys_comp_info_func_count SET
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>

	<select id="listFunctionCount" resultType="com.backend.common.modules.setting.model.FunctionCountTO">
		SELECT b.function_name "serviceContent",
		a.function_table_id "functionTableId",
		CASE WHEN a.count_std = 'Q' THEN '按量收费' WHEN a.count_std = 'T' THEN '按次数收费' WHEN a.count_std = 'DT' THEN '按日计次' WHEN a.count_std = 'U' THEN '按单元数收费' ELSE '' END "countStd",
		a.total_count,a.consumed_count,a.total_count - a.consumed_count "remainingCount",a.main_func_flag
		FROM sys_comp_info_func_count a
		LEFT JOIN sys_function_table b ON a.function_table_id=b.id AND b.del_flag='0'
		<where>
			a.company_id = #{companyId} AND a.del_flag = '0'
		</where>
		ORDER BY a.main_func_flag DESC, b.seq
	</select>

	<select id="getFunctionTableInfoByRoleIds" resultType="com.backend.common.entity.mapping.FunctionTableInfoTO">
		select distinct b.function_table_id as functionTableId , a.name as functionName
		from sys_role a
		left join sys_function_table_count_config b on a.enname = b.role_enname
		where a.del_flag = '0' and b.del_flag = '0'
		and a.id in
		<foreach collection="list" item="obj" index="index" separator="," open="(" close=")">
			#{obj}
		</foreach>
	</select>

	<select id="getFunctionTableInfoByRoleIdsAndCompanyId" resultType="com.backend.common.entity.mapping.FunctionTableInfoTO">
		select distinct b.function_table_id as functionTableId , a.name as functionName,
		c.total_count as totalCount
		from sys_role a
		left join sys_function_table_count_config b on a.enname = b.role_enname
		LEFT JOIN sys_comp_info_func_count c on c.function_table_id = b.function_table_id and c.company_id = #{companyId} and c.del_flag = '0'
		where a.del_flag = '0' and b.del_flag = '0'
		and a.id in
		<foreach collection="list" item="obj" index="index" separator="," open="(" close=")">
			#{obj}
		</foreach>
	</select>

	<select id="getFunctionCountInfoByFunctionTableId" resultType="com.backend.common.entity.mapping.FunctionCountInfoTO">
		select
			function_table_id as functionTableId,
			sum(total_count) as totalCount
		from
			sys_comp_info_func_count
		where
			del_flag = '0'
			and function_table_id =
			#{functionTableId}
	</select>

	<select id="getSysCompFuncCountDetailByCompanyIdIncludeDel" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.company_id = #{companyId}
	</select>

	<select id="getSysCompFuncCountDetailByCompanyId" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.company_id = #{companyId} AND a.del_flag = '0'
	</select>

	<select id="lockByFuncTableId" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.company_id = #{companyId} and del_flag='0' limit 1 for update
	</select>

	<select id="getCompInfoFuncCountByFuncTableId" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.company_id = #{companyId} and function_table_id = #{functionTableId} and del_flag='0' limit 1
	</select>

	<select id="getCompInfoFuncCountByFuncTableId4PageConsumedCount" resultType="com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount">
		SELECT
		<include refid="sysCompInfoFuncCountColumns"/>
		FROM sys_comp_info_func_count a
		WHERE a.company_id = #{companyId} and function_table_id = #{functionTableId} and del_flag='0' limit 1
	</select>
	<update id="deleteByCompanyId">
		UPDATE sys_comp_info_func_count SET
		del_flag = #{DEL_FLAG_DELETE},
		update_by = #{updateBy.id},
		update_date = #{updateDate}
		WHERE company_id = #{companyId}
	</update>

</mapper>
