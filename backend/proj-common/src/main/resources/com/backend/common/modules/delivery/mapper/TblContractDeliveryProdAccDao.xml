<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.backend.common.modules.delivery.mapper.TblContractDeliveryProdAccDao">
	<sql id="tblContractDeliveryProdAccColumns">
		a.id AS "id",
		a.company_id AS "companyId",
		a.contract_delivery_id AS "contractDeliveryId",
		a.function_table_id AS "functionTableId",
		a.begin_date AS "beginDate",
		a.end_date AS "endDate",
		a.total_unit AS "totalUnit",
		a.total_consumed_unit AS "totalConsumedUnit",
		a.type AS "type",
		a.actual_discount_rate AS "actualDiscountRate",
		a.actual_discount_amount AS "actualDiscountAmount",
		a.last_transfer_in_unit AS "lastTransferInUnit",
		a.broken_amount AS "brokenAmount",
		a.remark AS "remark",
		a.del_flag AS "delFlag",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate"
	</sql>
	
	<select id="get" resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT 
			<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		WHERE a.id = #{id} AND a.del_flag = '0'
	</select>
    <select id="getByContractDeliveryId"
            resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
			<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		WHERE a.contract_delivery_id = #{contractDeliveryId} AND a.company_id = #{companyId}  AND a.del_flag = '0'
	</select>
    <select id="getByCompanyId"
            resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
			<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		WHERE a.company_id = #{companyId}  AND a.del_flag = '0'
	</select>

    <select id="lockByCompanyId"
			resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
		<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		WHERE a.company_id = #{companyId} AND a.del_flag = '0'
		AND a.begin_date <![CDATA[ <= ]]> #{accDate} AND a.end_date <![CDATA[ >= ]]> #{accDate}
		for update
	</select>
	<select id="pageByCreateDate"
			resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
		<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		where
			a.del_flag = '0'
			<if test="lastMinDate != null">
				AND a.create_date &gt; #{lastMinDate}
			</if>
		ORDER BY a.create_date
		limit #{size}
	</select>
	<select id="lockById" resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
		<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		WHERE a.id = #{id} AND a.del_flag = '0'
		for update
	</select>
	<select id="pageCreditsBreakdownCondition"
			resultType="com.backend.common.modules.delivery.model.CreditsSummaryTO">
		SELECT
		totalCredit,
		expiryDate,
		daysUntilExpiry
		FROM (
		SELECT
		SUM(total_unit - total_consumed_unit) AS totalCredit,
		end_date AS expiryDate,
		DATEDIFF(end_date, CURDATE()) AS daysUntilExpiry
		FROM
		tbl_contract_delivery_prod_acc
		WHERE
		total_unit > total_consumed_unit
		AND company_id = #{obj.companyId}
		AND end_date >= CURDATE()
		AND del_flag = '0'
		GROUP BY
		end_date
		) AS grouped_results
		<if test="obj.sortedFiledList != null and obj.sortedFiledList.size() > 0">
			ORDER BY
			<foreach collection="obj.sortedFiledList" index="index" item="i" separator=",">
				${i.orderBy} ${i.order}
			</foreach>
		</if>
	</select>

	<insert id="insert">
		INSERT INTO tbl_contract_delivery_prod_acc(
			id,
			company_id,
			contract_delivery_id,
			function_table_id,
			begin_date,
			end_date,
			total_unit,
			total_consumed_unit,
			type,
			actual_discount_rate,
			actual_discount_amount,
			last_transfer_in_unit,
			broken_amount,
		    remark,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES (
			#{id},
			#{companyId},
			#{contractDeliveryId},
			#{functionTableId},
			#{beginDate},
			#{endDate},
			#{totalUnit},
			#{totalConsumedUnit},
			#{type},
			#{actualDiscountRate},
			#{actualDiscountAmount},
			#{lastTransferInUnit},
			#{brokenAmount},
			#{remark},
			#{delFlag},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate}
		)
	</insert>
	
	<insert id="batchInsert">
		INSERT INTO tbl_contract_delivery_prod_acc(
			id,
			company_id,
			contract_delivery_id,
			function_table_id,
			begin_date,
			end_date,
			total_unit,
			total_consumed_unit,
			type,
			actual_discount_rate,
			actual_discount_amount,
			last_transfer_in_unit,
			broken_amount,
			remark,
			del_flag,
			create_by,
			create_date,
			update_by,
			update_date
		) VALUES 
		<foreach collection="list" item="obj" index="index" separator=",">
		(
			#{obj.id},
			#{obj.companyId},
			#{obj.contractDeliveryId},
			#{obj.functionTableId},
			#{obj.beginDate},
			#{obj.endDate},
			#{obj.totalUnit},
			#{obj.totalConsumedUnit},
			#{obj.type},
			#{obj.actualDiscountRate},
			#{obj.actualDiscountAmount},
			#{obj.lastTransferInUnit},
			#{obj.brokenAmount},
			#{obj.remark},
			#{obj.delFlag},
			#{obj.createBy.id},
			#{obj.createDate},
			#{obj.updateBy.id},
			#{obj.updateDate}
		)
		</foreach>
	</insert>
	
	<update id="update">
		UPDATE tbl_contract_delivery_prod_acc SET 
			company_id = #{companyId},
			contract_delivery_id = #{contractDeliveryId},
			function_table_id = #{functionTableId},
			begin_date = #{beginDate},
			end_date = #{endDate},
			total_unit = #{totalUnit},
			total_consumed_unit = #{totalConsumedUnit},
			type = #{type},
			actual_discount_rate = #{actualDiscountRate},
			actual_discount_amount = #{actualDiscountAmount},
			last_transfer_in_unit = #{lastTransferInUnit},
			broken_amount = #{brokenAmount},
			remark = #{remark},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		UPDATE tbl_contract_delivery_prod_acc SET 
			del_flag = #{DEL_FLAG_DELETE},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>
	<update id="updateConsumedUnit">
		UPDATE tbl_contract_delivery_prod_acc SET
			total_consumed_unit = #{afterConsumedUnit},
			update_date = now()
		WHERE id = #{id} AND total_consumed_unit = #{beforeConsumedUnit}
	</update>
	<select id="pageByCondition"
			resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
		<include refid="tblContractDeliveryProdAccColumns"/>
		FROM tbl_contract_delivery_prod_acc a
		where
		a.del_flag = '0'
		and a.company_id = #{obj.companyId}
		<choose>
			<when test="obj.page != null and obj.page.orderBy != null and obj.page.orderBy != ''">
				ORDER BY ${obj.page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.begin_date DESC
			</otherwise>
		</choose>
	</select>
	<select id="existChangeProdAccInDateNoContract" resultType="java.lang.Integer">
		SELECT 1 FROM tbl_contract_delivery_prod_acc a
		WHERE a.del_flag = '0'
		  AND a.company_id = #{companyId}
		  AND (a.end_date = #{yesterday} or a.begin_date = #{date})
		  AND a.contract_delivery_id = 'NO_CONTRACT'
		limit 1
	</select>

	<select id="countExpireSoonAccountCredits" resultType="java.math.BigDecimal">
		SELECT
			SUM(total_unit - total_consumed_unit) AS totalCredit
		FROM
			tbl_contract_delivery_prod_acc
		WHERE
		  company_id = #{companyId}
			AND (total_unit - total_consumed_unit) > 0 AND end_date >= CURDATE() AND DATEDIFF(end_date, CURDATE()) &lt;= #{validity} AND del_flag = '0'
	</select>

	<select id="findUnconsumedExpiredAccounts" resultType="com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc">
		SELECT
			<include refid="tblContractDeliveryProdAccColumns"/>
		FROM
			tbl_contract_delivery_prod_acc a
		WHERE
			a.del_flag = '0'
			AND DATE(a.end_date) = DATE(#{expiredDate})
			AND a.total_unit > a.total_consumed_unit
	</select>

	<select id="findUnconsumedExpiredAccountIds" resultType="java.lang.String">
		SELECT
			a.id
		FROM
			tbl_contract_delivery_prod_acc a
		WHERE
			a.del_flag = '0'
			AND a.end_date = #{expiredDate}
			AND a.total_unit > a.total_consumed_unit
			AND a.type != 'A_INF'
	</select>

	<select id="pageChargeRecords"
			resultType="com.backend.common.modules.delivery.model.TopUpHistoryTO">
		SELECT * FROM (SELECT
		a.begin_date AS chargeDate,
		a.end_date AS expiryDate,
		a.total_unit AS topUpCredits,
		a.total_unit * a.actual_discount_amount AS payment,
		a.type AS  paymentMethod,
		b.amount_std AS amountStd,
		DATEDIFF(a.end_date, a.begin_date) AS validity
		FROM tbl_contract_delivery_prod_acc a
		LEFT JOIN tbl_contract_delivery b ON a.contract_delivery_id = b.id AND b.del_flag = '0'
		WHERE a.company_id = #{obj.companyId}
		AND a.del_flag = '0' AND a.type in ('P','G','TOP')
		) AS grouped_results
		<if test="obj.sortedFiledList != null and obj.sortedFiledList.size() > 0">
			ORDER BY
			<foreach collection="obj.sortedFiledList" index="index" item="i" separator=",">
				${i.orderBy} ${i.order}
			</foreach>
		</if>
	</select>
</mapper>