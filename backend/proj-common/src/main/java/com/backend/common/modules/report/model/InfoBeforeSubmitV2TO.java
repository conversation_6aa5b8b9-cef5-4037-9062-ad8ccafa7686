package com.backend.common.modules.report.model;

import com.backend.common.yunjuapi.model.QccOvsBasicIndustryInfo;
import com.backend.common.yunjuapi.model.*;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.*;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * added for v2.1.4 fengsw KNZT-6360
 * 新版企业或人员详情（下单前）
 */
@Data
public class InfoBeforeSubmitV2TO {
    private String keyNo;
    private String corpKeyNo;
    private String qccCode;
    private String leiCode;
    private String companyName;
    private String companyNameEn;
    private String imageUrl;
    private String nationCode;
    private String reportGroup;
    private String jurisdiction;
    private String regPlace;
    private String incorporationPlace; // 注册地
    private List<String> alias;
    private String registrationNumber;
    private String branchNo;
    private List<String> otherBrn;
    private String legalForm;
    private String dateOfIncorporation;
    private String status;//状态归一化 inactive active
    private String persName;
    private String persNameEn;
    private Long relatedCorpCount; // 高管关联企业数量
    private List<QccOvsBasicIndustryInfo> industry;
    private Boolean hasKYC;
    private String lastUpdatedOn;
    private Boolean ifEnglishNameFormal;
    private String compClass;
    private Boolean sourceIfIrd = false;

    @Data
    @NoArgsConstructor
    public static class Industry {
        private String code;
        private String desc;
        private String descEn;

        public Industry(String code, String desc, String descEn) {
            this.code = code;
            this.desc = desc;
            this.descEn = descEn;
        }
    }

    public static InfoBeforeSubmitV2TO buildCnCorpDetail(ApiGlobalCorpDetailTO apiGlobalCorpDetailTO) {
        if (Objects.isNull(apiGlobalCorpDetailTO)) return null;
        InfoBeforeSubmitV2TO rtn = new InfoBeforeSubmitV2TO();
        rtn.setCorpKeyNo(apiGlobalCorpDetailTO.getKeyNo());
        rtn.setQccCode(apiGlobalCorpDetailTO.getQccCode());
        rtn.setCompanyName(apiGlobalCorpDetailTO.getCorpName());
        rtn.setCompanyNameEn(apiGlobalCorpDetailTO.getEnglishName());
        rtn.setImageUrl(apiGlobalCorpDetailTO.getImageUrl());
        rtn.setJurisdiction(GlobalAreaEnum.CN.getShortNameEn());
        rtn.setRegistrationNumber(apiGlobalCorpDetailTO.getCreditCode());
        List<String> otherBrnList = new ArrayList<>();
        if (StringUtils.isNotBlank(apiGlobalCorpDetailTO.getOrgNo())) {
            otherBrnList.add(apiGlobalCorpDetailTO.getOrgNo());
        }
        if (StringUtils.isNotBlank(apiGlobalCorpDetailTO.getNo())) {
            otherBrnList.add(apiGlobalCorpDetailTO.getNo());
        }
        if (CollectionUtils.isNotEmpty(otherBrnList)) {
            rtn.setOtherBrn(otherBrnList);
        }
        rtn.setLegalForm(apiGlobalCorpDetailTO.getCompanyTypeEn());
        rtn.setDateOfIncorporation(apiGlobalCorpDetailTO.getStartDate());
        String shortStatusEnByShortStatusCn = RegistrationStatusEnum.getShortStatusEnByShortStatusCn(apiGlobalCorpDetailTO.getShortStatus());
        rtn.setStatus(ForeignCorpStatusEnum.getForeignStatus(shortStatusEnByShortStatusCn, null));
        return rtn;
    }

    public static InfoBeforeSubmitV2TO buildPersonDetail(PersonDetailInfoTO detail) {
        if (Objects.isNull(detail)) return null;
        InfoBeforeSubmitV2TO rtn = new InfoBeforeSubmitV2TO();
        rtn.setKeyNo(detail.getKeyNo());
        rtn.setCorpKeyNo(detail.getCorpKeyNo());
        rtn.setPersName(detail.getName());
        rtn.setPersNameEn(detail.getNameEn());
        rtn.setRelatedCorpCount(detail.getRelatedCorpCount());
        rtn.setCompanyName(detail.getCompanyName());
        rtn.setCompanyNameEn(detail.getCompanyNameEn());
        return rtn;
    }

    public static InfoBeforeSubmitV2TO buildOverseasCorpDetail(QccOvsBasicInfoCommTO basicInfo) { // 海外企业
        if (basicInfo == null) {
            return null;
        }
        InfoBeforeSubmitV2TO rtn = new InfoBeforeSubmitV2TO();
        rtn.setCorpKeyNo(basicInfo.getKeyNo());
        rtn.setQccCode(basicInfo.getQccCode());
        rtn.setCompanyName(basicInfo.getCompName());
        String compNameEn = basicInfo.getCompNameEn();
        rtn.setCompanyNameEn(compNameEn);
        rtn.setIfEnglishNameFormal(StringUtils.isNotBlank(compNameEn));
        rtn.setIndustry(basicInfo.getIndustryList());
        rtn.setOtherBrn(basicInfo.getOtherCompNo());
        rtn.setAlias(basicInfo.getCompNameAlias());
        rtn.setRegistrationNumber(StringUtils.joinIgnoreNull(Lists.newArrayList(basicInfo.getCompNo(), basicInfo.getBranchNo()), "-"));
        rtn.setBranchNo(basicInfo.getBranchNo());
        rtn.setRegPlace(basicInfo.getRegPlace());
        GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getByNameCode(basicInfo.getNationCode());
        if (Objects.nonNull(globalAreaEnum)) {
            rtn.setNationCode(globalAreaEnum.getNameCode());
            rtn.setJurisdiction(globalAreaEnum.getShortNameEn());
            rtn.setLegalForm(basicInfo.getCompTypeDetail());
            if (GlobalAreaEnum.HK.getNameCode().equals(basicInfo.getNationCode())) { // 公司为注册非香港公司时，填入公司成立地方
                rtn.setIncorporationPlace(basicInfo.getRegPlaceDetail());
                String statusEnByStatusCn = HkStatusEnum.getStatusEnByStatusCn(basicInfo.getCompStatusDetail());
                rtn.setStatus(ForeignCorpStatusEnum.getForeignStatus(statusEnByStatusCn, null));
            } else {
                rtn.setStatus(basicInfo.getCompStatusLabel()); // 根据国家来判断 不同国家，状态取值不同
            }
        }
        rtn.setCompClass(basicInfo.getCompClassDetail());
        rtn.setDateOfIncorporation(basicInfo.getCompStartDate());
        rtn.setSourceIfIrd(basicInfo.getSourceIfIrd());
        return rtn;
    }
}
