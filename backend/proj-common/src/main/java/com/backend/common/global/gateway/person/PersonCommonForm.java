package com.backend.common.global.gateway.person;

import java.util.HashMap;

public class PersonCommonForm extends HashMap<String, Object> {
    public void setPersonId(String personId) {
        super.put("personId", personId);
    }

    public String getKeyNo() {
        Object keyNo = super.get("keyNo");
        return keyNo != null ? keyNo.toString() : null;
    }

    public String getId() {
        Object id = super.get("id");
        return id != null ? id.toString() : null;
    }
}
