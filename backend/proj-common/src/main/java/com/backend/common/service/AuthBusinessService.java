package com.backend.common.service;

import com.backend.common.model.MenuAuthResult;
import com.backend.common.modules.benefit.entity.TblBenefitDelivery;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.setting.model.CompChargeUnitV2TO;
import com.qcc.frame.commons.ienum.BenefitPoolStatusEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryBenefitStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限业务服务
 * <AUTHOR>
 * @datetime 29/7/2025 5:48 下午
 */
@Service
public class AuthBusinessService {
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;
    @Autowired
    private CommTblBenefitDeliveryService commTblBenefitDeliveryService;

    public MenuAuthResult getMenuData() {
        List<CompChargeUnitTO> allEnabledChargeUnits = chargeUnitService.getCompChargeUnits(UserUtils.getUserCompanyId());
        List<CompChargeUnitV2TO> chargeUnitList = allEnabledChargeUnits.stream().map(CompChargeUnitV2TO::buildNewCompChargeUnitTO).collect(Collectors.toList());
        List<TblBenefitDelivery> mapBenefitList = commTblBenefitDeliveryService.getByCompanyId(UserUtils.getUserCompanyId()).stream()
                .filter(k -> BenefitTypeEnum.getMapBenefitTypeList().contains(k.getBenefitType())).collect(Collectors.toList());
        // 获取当前用户公司ID
        String currentCompanyId = UserUtils.getUserCompanyId();
        
        // 定义不显示图谱菜单的公司ID列表
        String[] excludedCompanyIds = {
            "08b2f839722e43ac995b1dfbe7b021cf",
            "d5e7cbe1742c40fa8488129dde840c63", 
            "277b2c38a6a041899dbe70f286236bae",
            "67fe6621758742f4b900449aeab08afe"
        };
        
        // 判断是否显示图谱菜单：非自助用户且不在排除列表中
        boolean mapMenu = !StringUtils.equalsAny(currentCompanyId, excludedCompanyIds);
        // 判断是否进入workspace
        boolean mapWorkspace;
        if (UserUtils.isSelf()) {
            mapWorkspace = mapBenefitList.stream().anyMatch(k -> BenefitPoolStatusEnum.EFFECT.getCode().equals(k.getStatus()));
        } else {
            mapWorkspace = chargeUnitList.stream().anyMatch(k -> ReportTypeEnum.getMapList().contains(k.getReportType()));
        }
        // 是否可以试用
        boolean mapCanTrial = CollectionUtils.isEmpty(mapBenefitList);
        // 判断图谱是否试用
        boolean mapTrial = mapBenefitList.stream()
                .anyMatch(k -> StringUtils.isBlank(k.getContractNo())
                        && BenefitPoolStatusEnum.EFFECT.getCode().equals(k.getStatus()));
        // 判断是否有被拒绝的试用
        boolean mapTrialRejected = mapBenefitList.stream()
                .anyMatch(k -> StringUtils.isBlank(k.getContractNo())
                        && DeliveryBenefitStatusEnum.REJECTED.getCode().equals(k.getStatus()));

        MenuAuthResult result = new MenuAuthResult();
        result.setChargeUnitList(chargeUnitList);
        result.setMapWorkspace(mapWorkspace);
        result.setMapCanTrial(mapCanTrial);
        result.setMapTrial(mapTrial);
        result.setMapMenu(mapMenu);
        result.setMapTrialRejected(mapTrialRejected);
        return result;
    }
}
