package com.backend.common.global.gateway.map;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.event.MonitorEventInterface;
import com.google.common.collect.Maps;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 图谱接口类
 * <AUTHOR>
 * @datetime 24/7/2025 10:56 上午
 */
public class DataMapInterface {
    private static final Logger logger = LoggerFactory.getLogger(DataMapInterface.class);


    public static Map<String, Boolean> checkNodeInMap(String mapId, List<String> keyNoList) throws MessageException {
        Map<String, Object> bodyJsonMap = Maps.newHashMap();
        bodyJsonMap.put("mapId", mapId);
        bodyJsonMap.put("nodeIds", keyNoList);
        String resp = proxy("/graph/is-nodes-in-map", bodyJsonMap, true);
        logger.info("/graph/is-nodes-in-map resp: {}", resp);
        JSONObject jsonObj = JsonUtils.parseObject(resp);
        MsgExceptionUtils.checkIsNull(jsonObj);
        String status = jsonObj.getString("status");
        MsgExceptionUtils.failBuild(Objects.nonNull(status)); // node服务会对非200的http，会有status的出参
        Map<String, Boolean> result = jsonObj.toJavaObject(new TypeReference<Map<String, Boolean>>() {
        });
        return Objects.isNull(result) ? Maps.newHashMap() : result;
    }

    public static boolean checkRootNode(String mapId, String keyNo) throws MessageException {
        Map<String, Object> bodyJsonMap = Maps.newHashMap();
        bodyJsonMap.put("mapId", mapId);
        bodyJsonMap.put("nodeId", keyNo);
        String resp = proxy("/graph/is-targe-node-of-map", bodyJsonMap, true);
        logger.info("/graph/is-targe-node-of-map resp: {}", resp);
        JSONObject jsonObj = JsonUtils.parseObject(resp);
        MsgExceptionUtils.checkIsNull(jsonObj);
        return jsonObj.getBooleanValue("isTargeNodeOfMap");
    }

    public static String syncMap(String mapId, String mapNo, List<String> keyNoList) throws MessageException {
        Map<String, Object> bodyJsonMap = Maps.newHashMap();
        bodyJsonMap.put("mapId", mapId);
        bodyJsonMap.put("preMapId", mapNo);
        bodyJsonMap.put("keyNoList", keyNoList);
        String resp = proxy("/map/sync-map", bodyJsonMap, true);
        logger.info("同步图谱响应: {}", resp);
        return resp;
    }

    public static String saveMap(String mapNo, String keyNo) throws MessageException {
        try {
            Map<String, Object> bodyJsonMap = Maps.newHashMap();
            bodyJsonMap.put("preMapId", mapNo);
            bodyJsonMap.put("keyNo", keyNo);
            String resp = proxy("/map/get-map-id", bodyJsonMap, true);
            logger.info("保存图谱响应: {}", resp);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            return jsonObj.getString("mapId");
        } catch (Exception e) {
            logger.error("图谱 /map/get-map-id error,", e);
            return IdGenUtil.uuid();
        }
    }

    public static String proxy(String url, Map<String, Object> bodyJsonMap, boolean isThrow) throws MessageException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("userId", UserUtils.getUserId());
        headerMap.put("companyId", UserUtils.getUserCompanyId());
        String resp = GatewayInvoker.postProxyJson("data-map", url, bodyJsonMap, headerMap);
        if (isThrow) {
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            MsgExceptionUtils.checkIsNull(jsonObj);
            String status = jsonObj.getString("status");
            if (StringUtils.isNotBlank(status)) {
                logger.error("图谱接口调用失败 resp: {}, req:{}", resp, JSONObject.toJSONString(bodyJsonMap));
                throw new MessageException("err.access");
            }
        }
        return resp;
    }


}