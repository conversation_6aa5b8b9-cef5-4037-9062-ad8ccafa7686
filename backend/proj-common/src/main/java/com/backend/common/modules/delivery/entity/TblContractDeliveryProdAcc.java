package com.backend.common.modules.delivery.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.DeliveryContracProdAccTypeEnum;
import com.qcc.frame.jee.commons.persistence.DataEntity;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;

/**
 * added for v1.8.8 KNZT-3324
 * <AUTHOR>
 * @datetime 2024/6/26 10:18
 */
public class TblContractDeliveryProdAcc extends DataEntity<TblContractDeliveryProdAcc> implements Comparable<TblContractDeliveryProdAcc> {
	
	private String contractDeliveryId;
	private String functionTableId;
	private Date beginDate;
	private Date endDate;
	private BigDecimal totalUnit;
	private BigDecimal totalConsumedUnit;
	private String type;
	private BigDecimal actualDiscountRate;
	private BigDecimal actualDiscountAmount;
	private BigDecimal lastTransferInUnit;
	private BigDecimal brokenAmount;
	private String remark;

	public BigDecimal calTotalRemainUnit() {
		return totalUnit.subtract(totalConsumedUnit);
	}

	public BigDecimal calTotalRemainUnitInAfter() {
		return totalUnit.subtract(Constants.MAX_UNIT).subtract(totalConsumedUnit);
	}

	public boolean ifInEffectRange() {
		// 是否在有效期
		LocalDate now = LocalDate.now();
		LocalDate endDate = DateUtils.toLocalDate(this.getEndDate());
		LocalDate beginDate = DateUtils.toLocalDate(this.getBeginDate());
		return DateUtils.checkInDateRange(now, beginDate, endDate);
	}

	public boolean ifInEffectRange(LocalDate now) {
		// 是否在有效期
		LocalDate endDate = DateUtils.toLocalDate(this.getEndDate());
		LocalDate beginDate = DateUtils.toLocalDate(this.getBeginDate());
		return DateUtils.checkInDateRange(now, beginDate, endDate);
	}


	public static TblContractDeliveryProdAcc buildFromProd(String prodAccType, TblContractDeliveryProd prod, TblContractDelivery contractDelivery, String companyId) {
		TblContractDeliveryProdAcc acc = new TblContractDeliveryProdAcc();
		acc.setType(prodAccType);
		acc.setContractDeliveryId(contractDelivery.getId());
		acc.setBeginDate(contractDelivery.getBeginDate());
		acc.setEndDate(contractDelivery.getEndDate());
		acc.setFunctionTableId(prod.getFunctionTableId());
		acc.setTotalUnit(DeliveryContracProdAccTypeEnum.AFTER_INF.getCode().equals(prodAccType) ? Constants.MAX_UNIT : prod.getTotalUnit());
		acc.setTotalConsumedUnit(BigDecimal.ZERO);
		acc.setActualDiscountRate(prod.getDiscountRate());
		acc.setActualDiscountAmount(prod.getDiscountAmount());
		acc.setBrokenAmount(BigDecimal.ZERO);
		acc.setCompanyId(companyId);
		acc.setLastTransferInUnit(BigDecimal.ZERO);
		return acc;
	}

	public static TblContractDeliveryProdAcc build4NoDelivery(String type, String companyId, BigDecimal totalUnit,
															  Date beginDate, Date endDate, String remark) {
		return build4NoDelivery(Constants.Delivery.NO_CONTRACT_DELIVERY_ID, type, companyId, totalUnit, beginDate, endDate, remark);
	}
	public static TblContractDeliveryProdAcc build4NoDelivery(String contractDeliveryId, String type, String companyId, BigDecimal totalUnit,
															  Date beginDate, Date endDate, String remark) {
		TblContractDeliveryProdAcc acc = new TblContractDeliveryProdAcc();
		acc.setType(type);
		acc.setContractDeliveryId(contractDeliveryId);
		acc.setBeginDate(beginDate);
		acc.setEndDate(endDate);
		acc.setFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
		acc.setTotalUnit(totalUnit);
		acc.setTotalConsumedUnit(BigDecimal.ZERO);
		acc.setActualDiscountRate(BigDecimal.ZERO);
		acc.setActualDiscountAmount(BigDecimal.ZERO);
		acc.setBrokenAmount(BigDecimal.ZERO);
		acc.setCompanyId(companyId);
		acc.setLastTransferInUnit(BigDecimal.ZERO);
		acc.setRemark(remark);
		if (StringUtils.isBlank(UserUtils.getUser().getId())) {
			acc.setUpdateBy(new User("system"));
			acc.setCreateBy(new User("system"));
		}
		return acc;
	}

	public String getContractDeliveryId() {
		return contractDeliveryId;
	}
	public void setContractDeliveryId(String contractDeliveryId) {
		this.contractDeliveryId = contractDeliveryId;
	}
	public String getFunctionTableId() {
		return functionTableId;
	}
	public void setFunctionTableId(String functionTableId) {
		this.functionTableId = functionTableId;
	}
	public BigDecimal getTotalUnit() {
		return totalUnit;
	}
	public void setTotalUnit(BigDecimal totalUnit) {
		this.totalUnit = totalUnit;
	}
	public BigDecimal getTotalConsumedUnit() {
		return totalConsumedUnit;
	}
	public void setTotalConsumedUnit(BigDecimal totalConsumedUnit) {
		this.totalConsumedUnit = totalConsumedUnit;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public BigDecimal getActualDiscountRate() {
		return actualDiscountRate;
	}
	public void setActualDiscountRate(BigDecimal actualDiscountRate) {
		this.actualDiscountRate = actualDiscountRate;
	}
	public BigDecimal getActualDiscountAmount() {
		return actualDiscountAmount;
	}
	public void setActualDiscountAmount(BigDecimal actualDiscountAmount) {
		this.actualDiscountAmount = actualDiscountAmount;
	}
	public BigDecimal getLastTransferInUnit() {
		return lastTransferInUnit;
	}
	public void setLastTransferInUnit(BigDecimal lastTransferInUnit) {
		this.lastTransferInUnit = lastTransferInUnit;
	}
	public BigDecimal getBrokenAmount() {
		return brokenAmount;
	}
	public void setBrokenAmount(BigDecimal brokenAmount) {
		this.brokenAmount = brokenAmount;
	}

	public Date getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * 排序规则
	 * 1.根据账户类型权重排序（试用、赠送、预付费、后付费）
	 * 2.endDate小的，即先过期的，优先
	 * 3.beginDate小的，即先生效的，优先
	 * 4.createDate小的，即先交付的，优先
	 *
	 * @param o the object to be compared.
	 * @return
	 */
	@Override
	public int compareTo(TblContractDeliveryProdAcc o) {
		int thisWeight = DeliveryContracProdAccTypeEnum.getConsumeWeightByCode(this.getType());
		int oWeight = DeliveryContracProdAccTypeEnum.getConsumeWeightByCode(o.getType());

		if (thisWeight > oWeight) {
			return -1;
		} else if (thisWeight < oWeight) {
			return 1;
		} else if (this.getEndDate().compareTo(o.getEndDate()) > 0) {
			return 1;
		} else if (this.getEndDate().compareTo(o.getEndDate()) < 0) {
			return -1;
		} else if (this.getBeginDate().compareTo(o.getBeginDate()) > 0) {
			return 1;
		} else if (this.getBeginDate().compareTo(o.getBeginDate()) < 0) {
			return -1;
		} else if (this.getCreateDate().compareTo(o.getCreateDate()) > 0) {
			return 1;
		} else if (this.getCreateDate().compareTo(o.getCreateDate()) < 0) {
			return -1;
		} else {
			return 0;
		}
	}

}
