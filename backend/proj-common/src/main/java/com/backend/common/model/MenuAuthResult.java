package com.backend.common.model;

import com.backend.common.modules.setting.model.CompChargeUnitV2TO;
import lombok.Data;

import java.util.List;

/**
 * 菜单数据出参
 * <AUTHOR>
 * @datetime 29/7/2025 5:32 下午
 */
@Data
public class MenuAuthResult {
    private List<CompChargeUnitV2TO> chargeUnitList;

    private Boolean mapMenu;
    private Boolean mapWorkspace;
    private Boolean mapTrial;
    private Boolean mapCanTrial;
    private Boolean mapTrialRejected;
}
