package com.backend.common.yunjuapi.model;

import java.io.Serializable;
import java.util.List;

/**
 * added for v1.5.6 KNZT-2598
 * 高管合作详情角色
 */
public class PartnerCooperationDetailRole {

    private String keyNo;
    private String name;
    private String nameEn;
    private String imageUrl;
    private List<Relation> role;

    public static class Relation implements Serializable {
        private static final long serialVersionUID = 6871774644881119521L;
        private String type;
        private String typeDesc;
        private String value;
        private String valueEn;

        public String getType() {
            return type;
        }
        public void setType(String type) {
            this.type = type;
        }
        public String getTypeDesc() {
            return typeDesc;
        }
        public void setTypeDesc(String typeDesc) {
            this.typeDesc = typeDesc;
        }
        public String getValue() {
            return value;
        }
        public void setValue(String value) {
            this.value = value;
        }

        public String getValueEn() {
            return valueEn;
        }
        public void setValueEn(String valueEn) {
            this.valueEn = valueEn;
        }
    }


    public String getKeyNo() {
        return keyNo;
    }

    public void setKeyNo(String keyNo) {
        this.keyNo = keyNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public List<Relation> getRole() {
        return role;
    }

    public void setRole(List<Relation> role) {
        this.role = role;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
}
