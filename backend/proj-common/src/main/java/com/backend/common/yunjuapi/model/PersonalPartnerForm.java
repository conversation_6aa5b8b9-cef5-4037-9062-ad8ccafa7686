package com.backend.common.yunjuapi.model;

import java.util.Objects;

/**
 * added for v1.5.3 KNZT-2401
 * 高管合作伙伴查询入参
 *
 * <AUTHOR>
 * @datetime 2024/3/7 20:12
 */
public class PersonalPartnerForm {
    // 查询合作伙伴参数
    private String id;
    // added for v1.5.6 KNZT-2598 查询合作伙伴详情另外的参数
    private String targetKeyNo;
    private String companyKeyNo;
    private String isValid;
    private String type;

    // 分页参数
    private String pageIndex;
    private String pageSize;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getTargetKeyNo() {
        return targetKeyNo;
    }

    public void setTargetKeyNo(String targetKeyNo) {
        this.targetKeyNo = targetKeyNo;
    }

    public String getCompanyKeyNo() {
        return companyKeyNo;
    }

    public void setCompanyKeyNo(String companyKeyNo) {
        this.companyKeyNo = companyKeyNo;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
