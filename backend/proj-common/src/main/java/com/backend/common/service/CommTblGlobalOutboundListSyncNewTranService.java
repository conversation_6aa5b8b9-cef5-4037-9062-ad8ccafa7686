package com.backend.common.service;

import com.backend.common.entity.TblGlobalOutboundListSyncTask;
import com.backend.common.mapper.TblGlobalOutboundListSyncDao;
import com.backend.common.modules.common.entity.TblGlobalOutboundListSync;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.proapi.ProOutboundSyncInterface;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.thread.OutboundCorpPersTaskRunnable;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompDataSaveEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.OutboundTaskStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.Dict;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Objects;

@Service
public class CommTblGlobalOutboundListSyncNewTranService extends CrudService<TblGlobalOutboundListSyncDao, TblGlobalOutboundListSync> {
    @Autowired
    private CommTblGlobalOutboundListSyncTaskService taskService;
    @Autowired
    private ThreadPoolService threadPoolService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommSysCompInfoExtService commSysCompInfoExtService;
    @Autowired
    private CommCompUserService commCompUserService;


    /**
     * updated for for v1.8.5 KNZT-3802
     *
     * @param order
     * @return
     */
    public void newTranSaveOutboundListSync(TblCompReportOrder order) {
        if (!SysConfigCacheService.isEnableOutboundSync()) {
           return;
        }
        if (!ReportTypeEnum.outboundAllowList().contains(order.getReportType())) {
            logger.info("outbound not save, reportType:{}", order.getReportType());
            return;
        }
        Company company = commSysCompanyService.get(order.getCompanyId());
        // updated for lvcy v2.0.6 KNZT-5689
        if (Objects.isNull(company) || CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            logger.info("outbound not save, companyId:{}", order.getCompanyId());
            return;
        }
        boolean testCompany = commCompUserService.isTestCompany(order.getCompanyId());
        if (testCompany) {
            logger.info("outbound not save, inner account, companyId:{}", order.getCompanyId());
            return;
        }

        boolean exist = taskService.existSuccessByKeyNo(order.getKeyNo(), order.getReportType());
        if (exist) {
            logger.info("outbound exist task record, companyId:{}", order.getCompanyId());
            return;
        }

        String name;
        if(Constants.Report.KEY_NO_TYPE_CORP.equals(order.getKeyNoType())) {
            name = StringUtils.isNotBlank(order.getCorpName()) ? order.getCorpName() : order.getCorpNameEn();
        } else {
            name = StringUtils.isNotBlank(order.getPersName()) ? order.getPersName() : order.getPersNameEn();
        }
        // 保存任务表，后续线程池、job执行
        TblGlobalOutboundListSyncTask task = new TblGlobalOutboundListSyncTask();
        if (CompDataSaveEnum.SAVE.getCode().equals(company.getDataSave())) {
            task.setOrderId(order.getId());
        }
        task.setKeyNo(order.getKeyNo());
        task.setReportType(order.getReportType());
        task.setName(name);
        task.setExecuteStatus(OutboundTaskStatusEnum.PENDING.getCode());
        task.setExecuteFailCount(0);
        taskService.save(task);
        // 事务提交之后 执行
        try {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    threadPoolService.execute("pool.outbound.corp-pers-task", new OutboundCorpPersTaskRunnable(task.getId()));
                }
            });
        } catch (Exception e) {
            logger.error("【Outbound saveCorpPersSyncTask】 error, orderId:{}", order.getId(), e);
        }
    }

}
