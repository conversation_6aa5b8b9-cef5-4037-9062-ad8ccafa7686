package com.backend.common.modules.benefit.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.backend.common.modules.benefit.condition.MapTrialListCondition;
import com.backend.common.modules.benefit.condition.TblBenefitDeliveryCondition;
import com.backend.common.modules.benefit.entity.TblBenefitDelivery;
import com.backend.common.modules.benefit.mapper.TblBenefitDeliveryDao;
import com.backend.common.modules.benefit.model.BenefitDeliveryRecordTO;
import com.backend.common.modules.report.condition.MapTrialStatisticsForm;
import com.backend.common.modules.report.model.PlatformMapTrialListTO;
import com.qcc.frame.commons.ienum.BenefitGroupEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryBenefitStatusEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.service.CrudService;

@Service
public class CommTblBenefitDeliveryService extends CrudService<TblBenefitDeliveryDao, TblBenefitDelivery> {

    
    public List<TblBenefitDelivery> pageByCreateDateWithCondition(int size, Date lastMinDate, TblBenefitDeliveryCondition condition) {
        return dao.pageByCreateDateWithCondition(size, lastMinDate, condition);
    }

    public void clearByCompanyId(String companyId) {
        TblBenefitDelivery delivery = new TblBenefitDelivery();
        delivery.setCompanyId(companyId);
        delivery.preUpdate();
        dao.clearByCompanyId(delivery);
    }

    /**
     * 根据公司ID获取权益交付列表
     * 
     * @param companyId 公司ID
     * @return 权益交付列表
     */
    public List<TblBenefitDelivery> getByCompanyId(String companyId) {
        return dao.getByCompanyId(companyId);
    }

    public List<TblBenefitDelivery> getByCompanyIdAndContractNo(String companyId, String contractNo) {
        return dao.getByCompanyIdAndContractNo(companyId, contractNo);
    }

    public List<TblBenefitDelivery> getMonitorByCompanyId(String companyId) {
        List<TblBenefitDelivery> benefitDeliveries = dao.getByCompanyId(companyId);
        return benefitDeliveries.stream()
                .filter(tblBenefitDelivery -> BenefitTypeEnum.getMonitorBenefitTypeList().contains(tblBenefitDelivery.getBenefitType()))
                .collect(Collectors.toList());
    }

    public TblBenefitDelivery getEarliestEffectMonitorByCompanyId(String companyId) {
        return dao.getEarliestEffectMonitorByCompanyId(companyId, BenefitGroupEnum.SEAT.getCode());
    }

    /**
     * 根据公司ID查询权益交付记录
     * 
     * @param companyId 公司ID
     * @return 权益交付记录列表
     */
    public List<BenefitDeliveryRecordTO> listDeliveryRecordsByCompanyId(String companyId) {
        List<BenefitDeliveryRecordTO> list = dao.listDeliveryRecordsByCompanyId(companyId);
        for (BenefitDeliveryRecordTO record : list) {
            record.setStatusDesc(DeliveryBenefitStatusEnum.getDescByCode(record.getStatus()));
            record.setBenefitTypeDesc(BenefitTypeEnum.getNameByCode(record.getBenefitType()));
        }
        return list;
    }

    public List<PlatformMapTrialListTO> pageMapTrialList(MapTrialListCondition condition) {
        return dao.pageMapTrialList(condition);
    }

    /**
     * 统计试用中的账号数量
     */
    public Integer countMapEffectTrialCompany(MapTrialStatisticsForm condition) {
        return dao.countMapEffectTrialCompany(condition);
    }

    /**
     * 统计累计试用账号数量
     */
    public Integer countMapTotalTrialCompany(MapTrialStatisticsForm condition) {
        return dao.countMapTotalTrialCompany(condition);
    }

    /**
     * 根据公司ID列表和权益类型列表批量查询权益交付记录
     * 
     * @param companyIds 公司ID列表
     * @param benefitTypes 权益类型列表
     * @return 权益交付记录列表
     */
    public List<TblBenefitDelivery> getByCompanyIdList(List<String> companyIds, List<String> benefitTypes) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return new ArrayList<>();
        }
        return dao.getByCompanyIdListAndBenefitTypes(companyIds, benefitTypes);
    }
}
