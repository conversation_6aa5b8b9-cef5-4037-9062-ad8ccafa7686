package com.backend.common.yunjuapi.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class CoutShiXin implements Serializable {
    private String id;
    private String name;
    private Long liandate;
    private Integer sourceid;
    private Integer follows;
    private Integer isperson;
    private Object createdate;
    private Object updatedate;
    private String anno;
    private String orgno;
    private String ownerName;
    private String executegov;
    private String province;
    private String executeunite;
    private String yiwu;
    private String executestatus;
    private String executestatusEn;
    private String actionremark;
    private String actionremarkEn;
    private Long publicdate;
    private Integer age;
    private String sexy;
    private String executeno;
    private String performedpart;
    private String unperformpart;
    private String judicialOpinionId;
    private String uniqueno;
    private List<CoutShiXinNameKeyNoColl> nameKeyNoCollection = new ArrayList<>();
    private String orgType;
    private String orgTypeName;
    private String riskLevel;
    private String riskLevelDesc;
    private String originOrgNo;

    private List<Object> riskExplain;
    private String actionTypeName;
    private String amount;
    private String caseSearchId;

    private String closeStatus;

    private List<CoutShiXinNameKeyNoColl> sqrInfo = new ArrayList<>();
}
