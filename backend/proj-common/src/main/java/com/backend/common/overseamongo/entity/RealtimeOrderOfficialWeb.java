package com.backend.common.overseamongo.entity;

import com.qcc.frame.jee.commons.annotation.MongoTable;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 字段说明：<a href="https://doc.greatld.com/pages/viewpage.action?pageId=88656843"/>
 */
@Getter
@Setter
@MongoTable("Qcc_Prod_RealtimeOrder_OfficialWeb")
public class RealtimeOrderOfficialWeb {

    private String _id;
    private String _CreateTime;
    private String OrderTime;
    private String NationCode;
    private String CompKeyno;
    private String EnvType;
    private Integer ApiCallback;
    private CompanyInfo CompanyInfo;
    private List<ShareCapital> ShareCapital;
    private List<Shareholder> Shareholders;
    private List<Shareholder> HistoricalShareholders;
    private List<Director> Directors;
    private List<Director> HistoricalDirectors;
    private List<UltimateHoldingCompany> UltimateHoldingCompany;
    private List<UltimateHoldingCompany> HistoricalUltimateHoldingCompany;

    @Getter
    @Setter
    public static class CompanyInfo {
        private String CompName;
        private String CompNameTC;
        private String CompNo;
        private String BusinessNo;
        private List<OtherNumber> OtherNumber;
        private String EntityType;
        private String EntityTypeStandard;
        private String EntityTypeSecond;
        private String EntityClass;
        private String EntityClassTc;
        private String IncorporationDate;
        private String PlaceOfIncorporation;
        private String Jurisdiction;
        private String CessationDate;
        private String CompStatus;
        private String CompStatusTc;
        private String CompStatusEnTranslation;
        private String CompStatusLabel;
        private String CompStatusSecond;
        private String EntityTypeTax;
        private String EntityTypeTaxTc;
        private List<OfficialStatusMo> PreviousCompStatus;
        private List<PreviousName> PreviousNames;
        private List<PreviousName> OtherNames;
        private List<CompAddress> CompAddress;
        private List<CompAddress> PreviousCompAddress;
        private List<Industry> Industry;
        private ContactInformation ContactInformation;
        private ReceiverShipInfo ReceiverShipInfo;
        private LiquidationInfo LiquidationInfo;
        private String PreNamesDescription;
        private String CompBasicDescription;
        private String ShareholdersDescription;
        private String TotalNumberOfShares;
        private String DirectorsDescription;
        private String LastUpdatedDate;
        private String ConstitutionFiled;
        private String ArFilingMonth;
        private String FraReportingMonth;
        private String AmountCapital;
        private String PaidInCapital;
        private String ShareValue;
        private String EquityStatusTc;
        private List<String> EquityStatus;
        private String RegistrationAuthority;
        private String IsSpecialVotingRights;
        private String IsSpecificIssuesVeto;
        private String IsProhibited;
        private String IsCloselyHeldCompany;
        private String SuspensionStartDate;
        private String SuspensionEndDate;
        private String SuspensionApprovingAuthority;
        private List<Representative> Representative;
        private StockInfo StockInfo;
        private IntellectualPropertyInfo IntellectualPropertyInfo;
        private AssociationInfo AssociationInfo;
        private List<BranchOffice> BranchOffices;
        private List<Factory> Factory;
        private List<History> History;
        private String ShareholderInfo;
        private String OutwardInvestment;
        private String DirectorsTermEndDate;
        private String DirectorsTermStartDate;
        private AccountsDate AccountsDate;
        private AnnualReturnDate AnnualReturnDate;
        private ConfirmStatementDate ConfirmStatementDate;
        private List<Charge> Charges;
        private List<Insolvency> Insolvency;
        private String ShareholdersUpdateDate;
    }

    @Getter
    @Setter
    public static class OtherNumber {
        private String Type;
        private String Number;
    }

    @Getter
    @Setter
    public static class OfficialStatusMo {
        private String CompStatus;
        private String CompStatusLabel;
        private String StartDate;
        private String EndDate;
    }

    @Getter
    @Setter
    public static class PreviousName {
        private String Type;
        private String CompName;
        private String StartDate;
        private String EndDate;
        private String SerialNo;
    }

    @Getter
    @Setter
    public static class CompAddress {
        private String Type;
        private String Address;
        private String StartDate;
        private String EndDate;
        private String Remark;
        private String AddressTc;
    }

    @Getter
    @Setter
    public static class Industry {
        private String Code;
        private String Desc;
    }

    @Getter
    @Setter
    public static class ContactInformation {
        private List<Phone> Phone;
        private List<Fax> Fax;
        private List<Email> Email;
        private List<Website> Website;
        private String OfficeAddress;
        private String DeliveryAddress;
        private String PostalAddress;
        private String InvoiceAddress;
        private String TradingArea;
    }

    @Getter
    @Setter
    public static class Phone {
        private String Type;
        private String Phone;
    }

    @Getter
    @Setter
    public static class Fax {
        private String Type;
        private String Fax;
    }

    @Getter
    @Setter
    public static class Email {
        private String Type;
        private String Email;
    }

    @Getter
    @Setter
    public static class Website {
        private String Type;
        private String Website;
    }

    @Getter
    @Setter
    public static class ReceiverShipInfo {
        private String Title;
        private String PartTitle;
        private String StartDate;
        private String Status;
        private String AppointedBy;
        private String Propertyin;
        private String Agreement;
        private List<Report> Reports;
        private List<ReceiverShip> ReceiverShipList;
    }

    @Getter
    @Setter
    public static class Report {
        private String Title;
        private String Date;
    }

    @Getter
    @Setter
    public static class ReceiverShip {
        private String Name;
        private String Organisation;
        private String Phone;
        private String Email;
        private String Address;
        private String AppointmentDate;
        private String Vacated;
    }

    @Getter
    @Setter
    public static class LiquidationInfo {
        private String Title;
        private String PartTitle;
        private String StartDate;
        private String Status;
        private String AppointedBy;
        private String Propertyin;
        private String Agreement;
        private List<Report> Reports;
        private List<Liquidation> LiquidationList;
    }

    @Getter
    @Setter
    public static class Liquidation {
        private String Name;
        private String Organisation;
        private String Phone;
        private String Email;
        private String Address;
        private String AppointmentDate;
        private String Vacated;
    }

    @Getter
    @Setter
    public static class StockInfo {
        private String LastUpdatedDate;
        private String Source;
        private String StockSymbol;
        private String StockSname;
        private String IndustryCode;
        private String Industry;
        private String StockType;
        private String StockTypeTC;
    }

    @Getter
    @Setter
    public static class IntellectualPropertyInfo {
        private String LastUpdatedDate;
        private String Source;
        private String Trademark;
    }

    @Getter
    @Setter
    public static class AssociationInfo {
        private String LastUpdatedDate;
        private String Source;
        private String Phone;
        private String Product;
        private String Website;
        private String CompName;
    }

    @Getter
    @Setter
    public static class BranchOffice {
        private String Keyno;
        private String CompNo;
        private String CompNameTC;
        private String CompStatus;
        private String IncorporationDate;
        private String LastUpdatedDate;
        private String CompAddress;
        private String Manager;
    }

    @Getter
    @Setter
    public static class Factory {
        private String CompNo;
        private String CompNameTC;
        private String CompStatus;
        private String IncorporationDate;
        private String LastUpdatedDate;
    }

    @Getter
    @Setter
    public static class History {
        private String CompNo;
        private String CompNameTC;
        private String PaidInCapital;
        private String ApproveDate;
        private String AmountCapital;
        private String AddressTc;
    }

    @Getter
    @Setter
    public static class AccountsDate {
        private String LastAccountsPeriodEndDate;
        private String LastAccountsPeriodStartDate;
        private String LastAccountsType;
        private String NextAccountsPeriodEndDate;
        private String NextAccountsPeriodStartDate;
        private String NextAccountsDueDate;
        private Boolean IsOverdue;
    }

    @Getter
    @Setter
    public static class AnnualReturnDate {
        private String LastARPeriodEndDate;
        private String NextARPeriodEndDate;
        private String NextARDueDate;
        private Boolean IsOverdue;
    }

    @Getter
    @Setter
    public static class ConfirmStatementDate {
        private String LastCSPeriodEndDate;
        private String NextCSPeriodEndDate;
        private String NextCSDueDate;
        private Boolean IsOverdue;
    }

    @Getter
    @Setter
    public static class Charge {
        private String Number;
        private String ChargeCode;
        private String Classification;
        private String ChargeNature;
        private String Status;
        private String DeliveredDate;
        private String CreatedDate;
        private String SatisfiedDate;
        private String ResolvedDate;
        private List<String> EntitledPersons;
        private String AmountSecured;
        private String ObligationsSecured;
        private ChargeDetail ChargeDetails;
    }

    @Getter
    @Setter
    public static class ChargeDetail {
        private String Type;
        private String Description;
        private String DescriptionFull;
        private Boolean IsFixedCharge;
        private Boolean IsFloatingCharge;
        private Boolean IsNegativePledge;
        private Boolean Iscaabt;
        private Boolean Isfcca;
    }

    @Getter
    @Setter
    public static class Insolvency {
        private String Number;
        private String Type;
        private List<Practitioner> Practitioners;
        private List<OfficialDate> Dates;
    }

    @Getter
    @Setter
    public static class Practitioner {
        private String Name;
        private String Address;
        private String AppointmentDate;
        private String CeasedDate;
        private String Role;
    }

    @Getter
    @Setter
    public static class OfficialDate {
        private String Type;
        private String Date;
    }

    @Getter
    @Setter
    public static class ShareCapital {
        private String ShareType;
        private String ShareTypeStandard;
        private String Currency;
        private String CurrencyNameEn;
        private String NumberOfShares;
        private String IssuedCapitalAmount;
        private String PrescribedParticulars;
    }

    @Getter
    @Setter
    public static class Shareholder {
        private String NumberOfShares;
        private String PercentOfClass;
        private List<ShareholderInfo> Shareholder;
    }

    @Getter
    @Setter
    public static class ShareholderInfo {
        private String EntityType;
        private String Name;
        private String NameEn;
        private String NameEnRelated;
        private String KeyNo;
        private String ShareType;
        private String ShareTypeStandard;
        private String IdentificationNoOrig;
        private String IdentificationNoOrigMask;
        private String IdentificationNumberType;
        private String Address;
        private String Country;
        private String NationCode;
        private String NationSname;
        private String NationSnameEn;
        private String EndDate;
    }

    @Getter
    @Setter
    public static class Director {
        private String Type;
        private String EntityType;
        private String Name;
        private String NameEn;
        private List<PreviousName> PreviousNames;
        private String NameEnRelated;
        private String KeyNo;
        private String FirstName;
        private String LastName;
        private String Position;
        private String Occupation;
        private String Address;
        private String ResidenceCountry;
        private String Country;
        private String NationCode;
        private String NationSname;
        private String NationSnameEn;
        private String AppointmentDate;
        private String StartDate;
        private String BirthDate;
        private String EndDate;
        private String RepresentedCompany;
        private String RepresentedCompanyEn;
        private String NumberOfShares;
        private String IdentificationNoOrig;
        private String IdentificationNoOrigMask;
        private String IdentificationNumberType;
        private String IdentificationType;
        private String RepresentedCompNo;
        private String RepresentedCompKeyno;
        private String RepresentedNoType;
    }

    @Getter
    @Setter
    public static class UltimateHoldingCompany {
        private String EntityType;
        private String Name;
        private String KeyNo;
        private String CompNo;
        private String CompType;
        private String BusinessNo;
        private String Country;
        private String ResidenceCountry;
        private String NationCode;
        private String NationSname;
        private String NationSnameEn;
        private String RegisteredAddress;
        private String NotifiedDate;
        private String CeasedDate;
        private String NameElements;
        private List<String> ControlNatures;
        private Boolean IsSanctioned;
        private String BirthDate;
        private String Address;
        private String IdentificationNoOrig;
        private String IdentificationNoOrigMask;
        private String IdentificationNumberType;
    }

    @Getter
    @Setter
    public static class Representative {
        private String Type;
        private String Name;
        private String NameEnRelated;
        private String ContributedCapital;
        private String LiabilityType;
        private String KeyNo;
        private String EntityType;
        private String NationCode;
        private String NationSname;
        private String NationSnameEn;
        private String IdentificationNoOrig;
        private String IdentificationNoOrigMask;
        private String IdentificationNumberType;
    }
}