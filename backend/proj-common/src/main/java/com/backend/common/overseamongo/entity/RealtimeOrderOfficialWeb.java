package com.backend.common.overseamongo.entity;

import com.qcc.frame.jee.commons.annotation.MongoTable;

import java.util.List;

/**
 * 字段说明：<a href="https://doc.greatld.com/pages/viewpage.action?pageId=88656843"/>
 */
@MongoTable("Qcc_Prod_RealtimeOrder_OfficialWeb")
public class RealtimeOrderOfficialWeb {

    private String _id;
    private Integer ApiCallback;
    private String _CreateTime;
    private String CompKeyno;
    private CompanyInfo CompanyInfo;
    private List<Director> Directors;
    private List<Shareholder> Shareholders;
    private List<UltimateHoldingCompany> UltimateHoldingCompany;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public Integer getApiCallback() {
        return ApiCallback;
    }

    public void setApiCallback(Integer apiCallback) {
        ApiCallback = apiCallback;
    }

    public String get_CreateTime() {
        return _CreateTime;
    }

    public void set_CreateTime(String _CreateTime) {
        this._CreateTime = _CreateTime;
    }

    public String getCompKeyno() {
        return CompKeyno;
    }

    public void setCompKeyno(String compKeyno) {
        CompKeyno = compKeyno;
    }

    public CompanyInfo getCompanyInfo() {
        return CompanyInfo;
    }

    public void setCompanyInfo(CompanyInfo companyInfo) {
        CompanyInfo = companyInfo;
    }

    public List<Director> getDirectors() {
        return Directors;
    }

    public void setDirectors(List<Director> directors) {
        Directors = directors;
    }

    public List<Shareholder> getShareholders() {
        return Shareholders;
    }

    public void setShareholders(List<Shareholder> shareholders) {
        Shareholders = shareholders;
    }

    public List<UltimateHoldingCompany> getUltimateHoldingCompany() {
        return UltimateHoldingCompany;
    }

    public void setUltimateHoldingCompany(List<UltimateHoldingCompany> ultimateHoldingCompany) {
        UltimateHoldingCompany = ultimateHoldingCompany;
    }

    public static final class CompanyInfo {
        private String CompName;
        private String CompNameTC;
        private String CompNo;
        private String BusinessNo;
        private List<OtherNumber> OtherNumber;
        private String EntityType;
        private String IncorporationDate;
        private String CompStatus;
        private String CompStatusLabel;
        private List<PreviousName> PreviousNames;
        private List<OtherName> OtherNames;
        private List<CompAddress> CompAddress;
        private List<Industry> Industry;
        private ContactInformation ContactInformation;
        private ReceiverShipInfo ReceiverShipInfo;
        private LiquidationInfo LiquidationInfo;
        private String PreNamesDescription;
        private String ShareholdersDescription;
        private String AmountCapital;
        private String PaidInCapital;
        private String ShareValue;
        private String TotalNumberOfShares;
        private String DirectorsDescription;
        private String DirectorsTermStartDate;
        private String DirectorsTermEndDate;
        private List<String> EquityStatus;
        private String LastUpdatedDate;
        private String ConstitutionFiled;
        private String ArFilingMonth;
        private String FraReportingMonth;
        private String RegistrationAuthority;
        private String IsSpecialVotingRights;
        private String IsSpecificIssuesVeto;
        private String IsProhibited;
        private String IsCloselyHeldCompany;
        private String SuspensionStartDate;
        private String SuspensionEndDate;
        private String SuspensionApprovingAuthority;
        private List<Representative> Representative;
        private StockInfo StockInfo;
        private IntellectualPropertyInfo IntellectualPropertyInfo;
        private List<History> History;

        public String getCompName() {
            return CompName;
        }

        public void setCompName(String compName) {
            CompName = compName;
        }

        public String getCompNameTC() {
            return CompNameTC;
        }

        public void setCompNameTC(String compNameTC) {
            CompNameTC = compNameTC;
        }

        public String getCompNo() {
            return CompNo;
        }

        public void setCompNo(String compNo) {
            CompNo = compNo;
        }

        public String getBusinessNo() {
            return BusinessNo;
        }

        public void setBusinessNo(String businessNo) {
            BusinessNo = businessNo;
        }

        public List<OtherNumber> getOtherNumber() {
            return OtherNumber;
        }

        public void setOtherNumber(List<OtherNumber> otherNumber) {
            OtherNumber = otherNumber;
        }

        public String getEntityType() {
            return EntityType;
        }

        public void setEntityType(String entityType) {
            EntityType = entityType;
        }

        public String getIncorporationDate() {
            return IncorporationDate;
        }

        public void setIncorporationDate(String incorporationDate) {
            IncorporationDate = incorporationDate;
        }

        public String getCompStatus() {
            return CompStatus;
        }

        public void setCompStatus(String compStatus) {
            CompStatus = compStatus;
        }

        public String getCompStatusLabel() {
            return CompStatusLabel;
        }

        public void setCompStatusLabel(String compStatusLabel) {
            CompStatusLabel = compStatusLabel;
        }

        public List<PreviousName> getPreviousNames() {
            return PreviousNames;
        }

        public void setPreviousNames(List<PreviousName> previousNames) {
            PreviousNames = previousNames;
        }

        public List<OtherName> getOtherNames() {
            return OtherNames;
        }

        public void setOtherNames(List<OtherName> otherNames) {
            OtherNames = otherNames;
        }

        public List<CompAddress> getCompAddress() {
            return CompAddress;
        }

        public void setCompAddress(List<CompAddress> compAddress) {
            CompAddress = compAddress;
        }

        public List<Industry> getIndustry() {
            return Industry;
        }

        public void setIndustry(List<Industry> industry) {
            Industry = industry;
        }

        public ContactInformation getContactInformation() {
            return ContactInformation;
        }

        public void setContactInformation(ContactInformation contactInformation) {
            ContactInformation = contactInformation;
        }

        public ReceiverShipInfo getReceiverShipInfo() {
            return ReceiverShipInfo;
        }

        public void setReceiverShipInfo(ReceiverShipInfo receiverShipInfo) {
            ReceiverShipInfo = receiverShipInfo;
        }

        public LiquidationInfo getLiquidationInfo() {
            return LiquidationInfo;
        }

        public void setLiquidationInfo(LiquidationInfo liquidationInfo) {
            LiquidationInfo = liquidationInfo;
        }

        public String getPreNamesDescription() {
            return PreNamesDescription;
        }

        public void setPreNamesDescription(String preNamesDescription) {
            PreNamesDescription = preNamesDescription;
        }

        public String getShareholdersDescription() {
            return ShareholdersDescription;
        }

        public void setShareholdersDescription(String shareholdersDescription) {
            ShareholdersDescription = shareholdersDescription;
        }

        public String getAmountCapital() {
            return AmountCapital;
        }

        public void setAmountCapital(String amountCapital) {
            AmountCapital = amountCapital;
        }

        public String getPaidInCapital() {
            return PaidInCapital;
        }

        public void setPaidInCapital(String paidInCapital) {
            PaidInCapital = paidInCapital;
        }

        public String getShareValue() {
            return ShareValue;
        }

        public void setShareValue(String shareValue) {
            ShareValue = shareValue;
        }

        public String getTotalNumberOfShares() {
            return TotalNumberOfShares;
        }

        public void setTotalNumberOfShares(String totalNumberOfShares) {
            TotalNumberOfShares = totalNumberOfShares;
        }

        public String getDirectorsDescription() {
            return DirectorsDescription;
        }

        public void setDirectorsDescription(String directorsDescription) {
            DirectorsDescription = directorsDescription;
        }

        public String getDirectorsTermStartDate() {
            return DirectorsTermStartDate;
        }

        public void setDirectorsTermStartDate(String directorsTermStartDate) {
            DirectorsTermStartDate = directorsTermStartDate;
        }

        public String getDirectorsTermEndDate() {
            return DirectorsTermEndDate;
        }

        public void setDirectorsTermEndDate(String directorsTermEndDate) {
            DirectorsTermEndDate = directorsTermEndDate;
        }

        public List<String> getEquityStatus() {
            return EquityStatus;
        }

        public void setEquityStatus(List<String> equityStatus) {
            EquityStatus = equityStatus;
        }

        public String getLastUpdatedDate() {
            return LastUpdatedDate;
        }

        public void setLastUpdatedDate(String lastUpdatedDate) {
            LastUpdatedDate = lastUpdatedDate;
        }

        public String getConstitutionFiled() {
            return ConstitutionFiled;
        }

        public void setConstitutionFiled(String constitutionFiled) {
            ConstitutionFiled = constitutionFiled;
        }

        public String getArFilingMonth() {
            return ArFilingMonth;
        }

        public void setArFilingMonth(String arFilingMonth) {
            ArFilingMonth = arFilingMonth;
        }

        public String getFraReportingMonth() {
            return FraReportingMonth;
        }

        public void setFraReportingMonth(String fraReportingMonth) {
            FraReportingMonth = fraReportingMonth;
        }

        public String getRegistrationAuthority() {
            return RegistrationAuthority;
        }

        public void setRegistrationAuthority(String registrationAuthority) {
            RegistrationAuthority = registrationAuthority;
        }

        public String getIsSpecialVotingRights() {
            return IsSpecialVotingRights;
        }

        public void setIsSpecialVotingRights(String isSpecialVotingRights) {
            IsSpecialVotingRights = isSpecialVotingRights;
        }

        public String getIsSpecificIssuesVeto() {
            return IsSpecificIssuesVeto;
        }

        public void setIsSpecificIssuesVeto(String isSpecificIssuesVeto) {
            IsSpecificIssuesVeto = isSpecificIssuesVeto;
        }

        public String getIsProhibited() {
            return IsProhibited;
        }

        public void setIsProhibited(String isProhibited) {
            IsProhibited = isProhibited;
        }

        public String getIsCloselyHeldCompany() {
            return IsCloselyHeldCompany;
        }

        public void setIsCloselyHeldCompany(String isCloselyHeldCompany) {
            IsCloselyHeldCompany = isCloselyHeldCompany;
        }

        public String getSuspensionStartDate() {
            return SuspensionStartDate;
        }

        public void setSuspensionStartDate(String suspensionStartDate) {
            SuspensionStartDate = suspensionStartDate;
        }

        public String getSuspensionEndDate() {
            return SuspensionEndDate;
        }

        public void setSuspensionEndDate(String suspensionEndDate) {
            SuspensionEndDate = suspensionEndDate;
        }

        public String getSuspensionApprovingAuthority() {
            return SuspensionApprovingAuthority;
        }

        public void setSuspensionApprovingAuthority(String suspensionApprovingAuthority) {
            SuspensionApprovingAuthority = suspensionApprovingAuthority;
        }

        public List<Representative> getRepresentative() {
            return Representative;
        }

        public void setRepresentative(List<Representative> representative) {
            Representative = representative;
        }

        public StockInfo getStockInfo() {
            return StockInfo;
        }

        public void setStockInfo(StockInfo stockInfo) {
            StockInfo = stockInfo;
        }

        public IntellectualPropertyInfo getIntellectualPropertyInfo() {
            return IntellectualPropertyInfo;
        }

        public void setIntellectualPropertyInfo(IntellectualPropertyInfo intellectualPropertyInfo) {
            IntellectualPropertyInfo = intellectualPropertyInfo;
        }

        public List<History> getHistory() {
            return History;
        }

        public void setHistory(List<History> history) {
            History = history;
        }
    }

    public static final class OtherNumber {
        private String Type;
        private String Number;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getNumber() {
            return Number;
        }

        public void setNumber(String number) {
            Number = number;
        }
    }

    public static final class PreviousName {
        private String CompName;
        private String StartDate;
        private String EndDate;

        public String getCompName() {
            return CompName;
        }

        public void setCompName(String compName) {
            CompName = compName;
        }

        public String getStartDate() {
            return StartDate;
        }

        public void setStartDate(String startDate) {
            StartDate = startDate;
        }

        public String getEndDate() {
            return EndDate;
        }

        public void setEndDate(String endDate) {
            EndDate = endDate;
        }
    }

    public static final class OtherName {
        private String Type;
        private String CompName;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getCompName() {
            return CompName;
        }

        public void setCompName(String compName) {
            CompName = compName;
        }
    }

    public static final class CompAddress {
        private String Type;
        private String Address;
        private String AddressTc;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String address) {
            Address = address;
        }

        public String getAddressTc() {
            return AddressTc;
        }

        public void setAddressTc(String addressTc) {
            AddressTc = addressTc;
        }
    }

    public static final class ContactInformation {
        private List<Phone> Phone;
        private List<Fax> Fax;
        private List<Email> Email;
        private List<Website> Website;
        private String OfficeAddress;
        private String DeliveryAddress;
        private String PostalAddress;
        private String InvoiceAddress;
        private String TradingArea;

        public List<Phone> getPhone() {
            return Phone;
        }

        public void setPhone(List<Phone> phone) {
            Phone = phone;
        }

        public List<Fax> getFax() {
            return Fax;
        }

        public void setFax(List<Fax> fax) {
            Fax = fax;
        }

        public List<Email> getEmail() {
            return Email;
        }

        public void setEmail(List<Email> email) {
            Email = email;
        }

        public List<Website> getWebsite() {
            return Website;
        }

        public void setWebsite(List<Website> website) {
            Website = website;
        }

        public String getOfficeAddress() {
            return OfficeAddress;
        }

        public void setOfficeAddress(String officeAddress) {
            OfficeAddress = officeAddress;
        }

        public String getDeliveryAddress() {
            return DeliveryAddress;
        }

        public void setDeliveryAddress(String deliveryAddress) {
            DeliveryAddress = deliveryAddress;
        }

        public String getPostalAddress() {
            return PostalAddress;
        }

        public void setPostalAddress(String postalAddress) {
            PostalAddress = postalAddress;
        }

        public String getInvoiceAddress() {
            return InvoiceAddress;
        }

        public void setInvoiceAddress(String invoiceAddress) {
            InvoiceAddress = invoiceAddress;
        }

        public String getTradingArea() {
            return TradingArea;
        }

        public void setTradingArea(String tradingArea) {
            TradingArea = tradingArea;
        }
    }

    public static final class Industry {
        private String Code;
        private String Desc;

        public String getCode() {
            return Code;
        }

        public void setCode(String code) {
            Code = code;
        }

        public String getDesc() {
            return Desc;
        }

        public void setDesc(String desc) {
            Desc = desc;
        }
    }

    public static final class Phone {
        private String Type;
        private String Phone;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getPhone() {
            return Phone;
        }

        public void setPhone(String phone) {
            Phone = phone;
        }
    }

    public static final class Fax {
        private String Type;
        private String Fax;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getFax() {
            return Fax;
        }

        public void setFax(String phone) {
            Fax = phone;
        }
    }

    public static final class Email {
        private String Type;
        private String Email;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getEmail() {
            return Email;
        }

        public void setEmail(String email) {
            Email = email;
        }
    }

    public static final class Website {
        private String Type;
        private String Website;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getWebsite() {
            return Website;
        }

        public void setWebsite(String website) {
            Website = website;
        }
    }

    public static final class ReceiverShipInfo {
        private String Title;
        private String PartTitle;
        private String StartDate;
        private String Status;
        private String Propertyin;
        private String Agreement;
        private String AppointedBy;
        private List<ReceiverShipList> ReceiverShipList;

        public String getTitle() {
            return Title;
        }

        public void setTitle(String title) {
            Title = title;
        }

        public String getPartTitle() {
            return PartTitle;
        }

        public void setPartTitle(String partTitle) {
            PartTitle = partTitle;
        }

        public String getStartDate() {
            return StartDate;
        }

        public void setStartDate(String startDate) {
            StartDate = startDate;
        }

        public String getStatus() {
            return Status;
        }

        public void setStatus(String status) {
            Status = status;
        }

        public String getPropertyin() {
            return Propertyin;
        }

        public void setPropertyin(String propertyin) {
            Propertyin = propertyin;
        }

        public String getAgreement() {
            return Agreement;
        }

        public void setAgreement(String agreement) {
            Agreement = agreement;
        }

        public String getAppointedBy() {
            return AppointedBy;
        }

        public void setAppointedBy(String appointedBy) {
            AppointedBy = appointedBy;
        }

        public List<ReceiverShipList> getReceiverShipList() {
            return ReceiverShipList;
        }

        public void setReceiverShipList(List<ReceiverShipList> receiverShipList) {
            ReceiverShipList = receiverShipList;
        }
    }

    public static final class ReceiverShipList {
        private String Name;
        private String Organisation;
        private String Phone;
        private String Email;
        private String Address;
        private String AppointmentDate;

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getOrganisation() {
            return Organisation;
        }

        public void setOrganisation(String organisation) {
            Organisation = organisation;
        }

        public String getPhone() {
            return Phone;
        }

        public void setPhone(String phone) {
            Phone = phone;
        }

        public String getEmail() {
            return Email;
        }

        public void setEmail(String email) {
            Email = email;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String address) {
            Address = address;
        }

        public String getAppointmentDate() {
            return AppointmentDate;
        }

        public void setAppointmentDate(String appointmentDate) {
            AppointmentDate = appointmentDate;
        }
    }

    public static final class LiquidationInfo {
        private String Title;
        private String PartTitle;
        private String StartDate;
        private String Status;
        private String AppointedBy;
        private List<LiquidationList> LiquidationList;

        public String getTitle() {
            return Title;
        }

        public void setTitle(String title) {
            Title = title;
        }

        public String getPartTitle() {
            return PartTitle;
        }

        public void setPartTitle(String partTitle) {
            PartTitle = partTitle;
        }

        public String getStartDate() {
            return StartDate;
        }

        public void setStartDate(String startDate) {
            StartDate = startDate;
        }

        public String getStatus() {
            return Status;
        }

        public void setStatus(String status) {
            Status = status;
        }

        public String getAppointedBy() {
            return AppointedBy;
        }

        public void setAppointedBy(String appointedBy) {
            AppointedBy = appointedBy;
        }

        public List<LiquidationList> getLiquidationList() {
            return LiquidationList;
        }

        public void setLiquidationList(List<LiquidationList> liquidationList) {
            LiquidationList = liquidationList;
        }
    }

    public static final class LiquidationList {
        private String Name;
        private String Organisation;
        private String Phone;
        private String Email;
        private String Address;
        private String AppointmentDate;

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getOrganisation() {
            return Organisation;
        }

        public void setOrganisation(String organisation) {
            Organisation = organisation;
        }

        public String getPhone() {
            return Phone;
        }

        public void setPhone(String phone) {
            Phone = phone;
        }

        public String getEmail() {
            return Email;
        }

        public void setEmail(String email) {
            Email = email;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String address) {
            Address = address;
        }

        public String getAppointmentDate() {
            return AppointmentDate;
        }

        public void setAppointmentDate(String appointmentDate) {
            AppointmentDate = appointmentDate;
        }
    }

    public static final class Representative {
        private String Type;
        private String Name;
        private String nameEnRelated;
        private String IdentificationNoOrig;
        private String IdentificationNumberType;
        private String ContributedCapital;
        private String LiabilityType;
        private String Keyno;
        private String EntityType;
        private String NationCode;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getNameEnRelated() {
            return nameEnRelated;
        }

        public void setNameEnRelated(String nameEnRelated) {
            this.nameEnRelated = nameEnRelated;
        }

        public String getIdentificationNoOrig() {
            return IdentificationNoOrig;
        }

        public void setIdentificationNoOrig(String identificationNoOrig) {
            IdentificationNoOrig = identificationNoOrig;
        }

        public String getIdentificationNumberType() {
            return IdentificationNumberType;
        }

        public void setIdentificationNumberType(String identificationNumberType) {
            IdentificationNumberType = identificationNumberType;
        }

        public String getContributedCapital() {
            return ContributedCapital;
        }

        public void setContributedCapital(String contributedCapital) {
            ContributedCapital = contributedCapital;
        }

        public String getLiabilityType() {
            return LiabilityType;
        }

        public void setLiabilityType(String liabilityType) {
            LiabilityType = liabilityType;
        }

        public String getKeyno() {
            return Keyno;
        }

        public void setKeyno(String keyno) {
            Keyno = keyno;
        }

        public String getEntityType() {
            return EntityType;
        }

        public void setEntityType(String entityType) {
            EntityType = entityType;
        }

        public String getNationCode() {
            return NationCode;
        }

        public void setNationCode(String nationCode) {
            NationCode = nationCode;
        }
    }

    public static final class StockInfo {
        private String StockSymbol;
        private String StockSname;
        private String StockTypeTc;
        private String StockType;
        private String IndustryCode;
        private String Industry;

        public String getStockSymbol() {
            return StockSymbol;
        }

        public void setStockSymbol(String stockSymbol) {
            StockSymbol = stockSymbol;
        }

        public String getStockSname() {
            return StockSname;
        }

        public void setStockSname(String stockSname) {
            StockSname = stockSname;
        }

        public String getStockTypeTc() {
            return StockTypeTc;
        }

        public void setStockTypeTc(String stockTypeTc) {
            StockTypeTc = stockTypeTc;
        }

        public String getStockType() {
            return StockType;
        }

        public void setStockType(String stockType) {
            StockType = stockType;
        }

        public String getIndustryCode() {
            return IndustryCode;
        }

        public void setIndustryCode(String industryCode) {
            IndustryCode = industryCode;
        }

        public String getIndustry() {
            return Industry;
        }

        public void setIndustry(String industry) {
            Industry = industry;
        }
    }

    public static final class IntellectualPropertyInfo {
        private String LastUpdatedDate;
        private String Source;
        private String Trademark;

        public String getLastUpdatedDate() {
            return LastUpdatedDate;
        }

        public void setLastUpdatedDate(String lastUpdatedDate) {
            LastUpdatedDate = lastUpdatedDate;
        }

        public String getSource() {
            return Source;
        }

        public void setSource(String source) {
            Source = source;
        }

        public String getTrademark() {
            return Trademark;
        }

        public void setTrademark(String trademark) {
            Trademark = trademark;
        }
    }

    public static final class History {
        private String ApproveDate;
        private String CompNo;
        private String CompNameTc;
        private String AmountCapital;
        private String PaidInCapital;
        private String AddressTc;

        public String getApproveDate() {
            return ApproveDate;
        }

        public void setApproveDate(String approveDate) {
            ApproveDate = approveDate;
        }

        public String getCompNo() {
            return CompNo;
        }

        public void setCompNo(String compNo) {
            CompNo = compNo;
        }

        public String getCompNameTc() {
            return CompNameTc;
        }

        public void setCompNameTc(String compNameTc) {
            CompNameTc = compNameTc;
        }

        public String getAmountCapital() {
            return AmountCapital;
        }

        public void setAmountCapital(String amountCapital) {
            AmountCapital = amountCapital;
        }

        public String getPaidInCapital() {
            return PaidInCapital;
        }

        public void setPaidInCapital(String paidInCapital) {
            PaidInCapital = paidInCapital;
        }

        public String getAddressTc() {
            return AddressTc;
        }

        public void setAddressTc(String addressTc) {
            AddressTc = addressTc;
        }
    }

    public static final class Director {
        private String Type;
        private String EntityType;
        private String KeyNo;
        private String Name;
        private String NameEn;
        private String IdentificationNoOrig;
        private String IdentificationNumberType;
        private String Address;
        private String Country;
        private String NationCode;
        private String Position;
        private String AppointmentDate;
        private String RepresentedCompany;
        private String RepresentedCompanyEn;
        private String RepresentedCompNo;
        private String RepresentedCompKeyno;

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public String getEntityType() {
            return EntityType;
        }

        public void setEntityType(String entityType) {
            EntityType = entityType;
        }

        public String getKeyNo() {
            return KeyNo;
        }

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getNameEn() {
            return NameEn;
        }

        public void setNameEn(String nameEn) {
            NameEn = nameEn;
        }

        public String getIdentificationNoOrig() {
            return IdentificationNoOrig;
        }

        public void setIdentificationNoOrig(String identificationNoOrig) {
            IdentificationNoOrig = identificationNoOrig;
        }

        public String getIdentificationNumberType() {
            return IdentificationNumberType;
        }

        public void setIdentificationNumberType(String identificationNumberType) {
            IdentificationNumberType = identificationNumberType;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String address) {
            Address = address;
        }

        public String getCountry() {
            return Country;
        }

        public void setCountry(String country) {
            Country = country;
        }

        public String getNationCode() {
            return NationCode;
        }

        public void setNationCode(String nationCode) {
            NationCode = nationCode;
        }

        public String getPosition() {
            return Position;
        }

        public void setPosition(String position) {
            Position = position;
        }

        public String getAppointmentDate() {
            return AppointmentDate;
        }

        public void setAppointmentDate(String appointmentDate) {
            AppointmentDate = appointmentDate;
        }

        public String getRepresentedCompany() {
            return RepresentedCompany;
        }

        public void setRepresentedCompany(String representedCompany) {
            RepresentedCompany = representedCompany;
        }

        public String getRepresentedCompanyEn() {
            return RepresentedCompanyEn;
        }

        public void setRepresentedCompanyEn(String representedCompanyEn) {
            RepresentedCompanyEn = representedCompanyEn;
        }

        public String getRepresentedCompNo() {
            return RepresentedCompNo;
        }

        public void setRepresentedCompNo(String representedCompNo) {
            RepresentedCompNo = representedCompNo;
        }

        public String getRepresentedCompKeyno() {
            return RepresentedCompKeyno;
        }

        public void setRepresentedCompKeyno(String representedCompKeyno) {
            RepresentedCompKeyno = representedCompKeyno;
        }
    }

    public static final class Shareholder {
        private String NumberOfShares;
        private String PercentOfClass;
        private List<ShareholderInfo> Shareholder;

        public String getNumberOfShares() {
            return NumberOfShares;
        }

        public void setNumberOfShares(String numberOfShares) {
            NumberOfShares = numberOfShares;
        }

        public String getPercentOfClass() {
            return PercentOfClass;
        }

        public void setPercentOfClass(String percentOfClass) {
            PercentOfClass = percentOfClass;
        }

        public List<ShareholderInfo> getShareholder() {
            return Shareholder;
        }

        public void setShareholder(List<ShareholderInfo> shareholder) {
            Shareholder = shareholder;
        }
    }

    public static final class ShareholderInfo {
        private String EntityType;
        private String Name;
        private String NameEn;
        private String KeyNo;
        private String IdentificationNoOrig;
        private String IdentificationNumberType;
        private String Address;
        private String NationCode;

        public String getEntityType() {
            return EntityType;
        }

        public void setEntityType(String entityType) {
            EntityType = entityType;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getNameEn() {
            return NameEn;
        }

        public void setNameEn(String nameEn) {
            NameEn = nameEn;
        }

        public String getKeyNo() {
            return KeyNo;
        }

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public String getIdentificationNoOrig() {
            return IdentificationNoOrig;
        }

        public void setIdentificationNoOrig(String identificationNoOrig) {
            IdentificationNoOrig = identificationNoOrig;
        }

        public String getIdentificationNumberType() {
            return IdentificationNumberType;
        }

        public void setIdentificationNumberType(String identificationNumberType) {
            IdentificationNumberType = identificationNumberType;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String address) {
            Address = address;
        }

        public String getNationCode() {
            return NationCode;
        }

        public void setNationCode(String nationCode) {
            NationCode = nationCode;
        }
    }

    public static final class UltimateHoldingCompany {
        private String EntityType;
        private String Name;
        private String KeyNo;
        private String CompNo;
        private String BusinessNo;
        private String Country;
        private String NationCode;
        private String RegisteredAddress;

        public String getEntityType() {
            return EntityType;
        }

        public void setEntityType(String entityType) {
            EntityType = entityType;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public String getKeyNo() {
            return KeyNo;
        }

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public String getCompNo() {
            return CompNo;
        }

        public void setCompNo(String compNo) {
            CompNo = compNo;
        }

        public String getBusinessNo() {
            return BusinessNo;
        }

        public void setBusinessNo(String businessNo) {
            BusinessNo = businessNo;
        }

        public String getCountry() {
            return Country;
        }

        public void setCountry(String country) {
            Country = country;
        }

        public String getNationCode() {
            return NationCode;
        }

        public void setNationCode(String nationCode) {
            NationCode = nationCode;
        }

        public String getRegisteredAddress() {
            return RegisteredAddress;
        }

        public void setRegisteredAddress(String registeredAddress) {
            RegisteredAddress = registeredAddress;
        }
    }

}
