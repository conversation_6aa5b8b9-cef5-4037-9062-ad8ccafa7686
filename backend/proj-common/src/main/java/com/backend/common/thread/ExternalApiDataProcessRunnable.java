package com.backend.common.thread;

import com.backend.common.modules.api.service.CommExternalApiBusinessService;
import com.backend.common.modules.api.service.CommExternalApiDataService;
import com.backend.common.openapi.model.ExternalApiOrderValidateResultTO;
import com.backend.common.service.SysDingMsgNewTranService;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import com.qcc.frame.jee.commons.thread.BaseRunnable;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;

/**
 * 异步处理
 */
public class ExternalApiDataProcessRunnable extends BaseRunnable {

    CommExternalApiBusinessService apiOrderService = SpringContextHolder.getBean(CommExternalApiBusinessService.class);
    CommExternalApiDataService apiDataService = SpringContextHolder.getBean(CommExternalApiDataService.class);

    SysDingMsgNewTranService sysDingMsgNewTranService = SpringContextHolder.getBean(SysDingMsgNewTranService.class);

    private final String orderRespId;
    private final String qccCode;
    private final String keyNo;
    private final String reportType;
    private final String orderNo;

    public ExternalApiDataProcessRunnable(String qccCode, String reportType, String orderRespId, String orderNo) {
        super();
        this.qccCode = qccCode;
        this.keyNo = null;
        this.reportType = reportType;
        this.orderRespId = orderRespId;
        this.orderNo = orderNo;
    }
    
    public ExternalApiDataProcessRunnable(String qccCode, String keyNo, String reportType, String orderRespId, String orderNo) {
        super();
        this.qccCode = qccCode;
        this.keyNo = keyNo;
        this.reportType = reportType;
        this.orderRespId = orderRespId;
        this.orderNo = orderNo;
    }

    @Override
    public void process() {
        logger.info("orderNo: {} process api data begin", orderNo);
        try {
            ExternalApiOrderValidateResultTO validateResultTO;
            if (ApiTypeEnum.CN_PERS_BASIC.getReportType().equals(reportType)) {
                validateResultTO = apiOrderService.checkOrderAfterSubmit(null, keyNo, true);
            } else {
                validateResultTO = apiOrderService.checkOrderAfterSubmit(qccCode);
            }
            apiOrderService.updateApiOrderInfoAfterCheck(orderRespId, reportType, validateResultTO);
            apiDataService.savaApiData2DB(orderRespId);
            logger.info("orderNo: {} process api data end", orderNo);
        } catch (Exception e) {
            logger.error("orderNo: {} process api data end error", orderNo, e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.API_DATA_PROCESSING_ERROR, orderNo);
            apiOrderService.refundOrderByOrderResp(orderRespId, orderNo);
        }
    }
}
