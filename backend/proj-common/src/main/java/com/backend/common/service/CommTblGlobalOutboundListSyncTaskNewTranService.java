package com.backend.common.service;

import com.backend.common.entity.TblGlobalOutboundCorpPersSync;
import com.backend.common.entity.TblGlobalOutboundListSyncTask;
import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.global.gateway.corp.model.PledgeOfEquityListTO;
import com.backend.common.global.gateway.person.PersonGatewayInterface;
import com.backend.common.global.gateway.person.model.PersonPartnerTO;
import com.backend.common.mapper.TblGlobalOutboundListSyncTaskDao;
import com.backend.common.modules.common.entity.TblGlobalOutboundListSync;
import com.backend.common.overseamongo.model.basic.CommEntityItem;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.ECILocalInterface;
import com.backend.common.yunjuapi.model.*;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.OutboundDataTypeEnum;
import com.qcc.frame.commons.ienum.OutboundKeyNoTypeEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.SysConfig;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CommTblGlobalOutboundListSyncTaskNewTranService extends CrudService<TblGlobalOutboundListSyncTaskDao, TblGlobalOutboundListSyncTask> {

    private final static Logger logger = LoggerFactory.getLogger(CommTblGlobalOutboundListSyncTaskNewTranService.class);

    @Autowired
    private CommTblGlobalOutboundCorpPersSyncService corpPersSyncService;
    @Autowired
    private CommTblGlobalOutboundListSyncService outboundListSyncService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;

    private final static int pageSize = 100;

    // updated for v1.6.5 KNZT-2810 方法重载，入参加入needCompanyKey 为false则代表使用默认key, true代表使用主账号绑定的key
    // updated for v1.8.5 KNZT-3802 增加维度统计，根据人名去重
    public void saveCorpPersSync(TblGlobalOutboundListSyncTask task, boolean needCompanyKey) throws MessageException {
        String keyNo = task.getKeyNo();
        String name = task.getName();
        Set<String> outboundDataTypeList = getConfigOutboundDataTypeList(); // 出境维度配置
        if (CollectionUtils.isEmpty(outboundDataTypeList)) {
            logger.info("【Outbound saveCorpPersSyncTask】 outboundDataTypeList is empty");
            return;
        }
        boolean exist = corpPersSyncService.existCorpByKeyNo(task.getReportType(), keyNo);
        if (exist) {
            logger.info("【Outbound saveCorpPersSyncTask】 record has done in first check, keyNo:{}", keyNo);
            return;
        }

        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getEnumByCode(task.getReportType());
        if (reportTypeEnum == null) {
            logger.error("reportType not exist");
            return;
        }
        List<TblGlobalOutboundCorpPersSync> corpPersResultList = Lists.newArrayList();
        AbstractReportResolver reportResolver = getInstanceReportResolver(reportTypeEnum, corpPersResultList, task, outboundDataTypeList, needCompanyKey);
        if (reportResolver != null) {
            reportResolver.resolve4Report();
        }
        if (CollectionUtils.isEmpty(corpPersResultList)) {
            logger.info("【Outbound saveCorpPersSyncTask】 corpPersSyncList data is empty, keyNo:{}", keyNo);
            return;
        }

        int errorRemindThreshold = getErrorRemindThreshold();
        Map<String, Long> dataType2CountMap = corpPersResultList.stream().collect(Collectors.groupingBy(TblGlobalOutboundCorpPersSync::getDataType, Collectors.counting()));
        dataType2CountMap.entrySet().stream().filter(entry -> entry.getValue() > errorRemindThreshold).forEach(entry -> {
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.OUTBOUND_DATA_TOO_MUCH, keyNo, name, reportTypeEnum.getDesc(), OutboundDataTypeEnum.getDesc(entry.getKey()), String.valueOf(entry.getValue()));
        });
        // 再次校验是否已经插入，避免定时任务和异步逻辑同时处理
        boolean doubleExist = corpPersSyncService.existCorpByKeyNo(task.getReportType(), keyNo);
        if (doubleExist) {
            logger.info("【Outbound saveCorpPersSyncTask】 has done in second check, keyNo:{}", keyNo);
            return;
        }

        corpPersSyncService.batchPartialInsert(corpPersResultList);

        List<String> persNameList = corpPersResultList.stream().map(TblGlobalOutboundCorpPersSync::getPersName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Set<String> existNameList = outboundListSyncService.getExistNameList(persNameList);

        // 插入企业关联人员的额度数据
        List<TblGlobalOutboundListSync> persOutboundList = corpPersResultList.stream()
                .filter(cp -> StringUtils.isNotBlank(cp.getPersKeyNo()) && StringUtils.isNotBlank(cp.getPersName()) && !existNameList.contains(cp.getPersName()))
                .map(cp -> {
                    TblGlobalOutboundListSync persOutbound = new TblGlobalOutboundListSync();
                    persOutbound.setKeyNo(cp.getPersKeyNo());
                    persOutbound.setKeyNoType(OutboundKeyNoTypeEnum.RELATED_PERS.getCode());
                    persOutbound.setName(cp.getPersName());
                    return persOutbound;
                })
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TblGlobalOutboundListSync::getName))), ArrayList::new));
        outboundListSyncService.batchInsertWithDate(persOutboundList, task.getCreateDate());
    }

    // added for v1.8.5 KNZT-3802
    private static Set<String> getConfigOutboundDataTypeList() {
        Set<String> outboundDataTypeList = new HashSet<>();
        List<SysConfig> outboundDataTypeConfigs = ConfigUtils.listConfigByType("outbound_data_type");
        if (CollectionUtils.isNotEmpty(outboundDataTypeConfigs)) {
            outboundDataTypeList.addAll(outboundDataTypeConfigs.stream().map(SysConfig::getConfigValue)
                    .flatMap(value -> Stream.of(StringUtils.split(value, ",")))
                    .collect(Collectors.toSet()));
        }
        return outboundDataTypeList;
    }

    // 某个维度出境人员数量超过此阈值告警
    private static int getErrorRemindThreshold() {
        String configValue = ConfigUtils.getConfigValueByTypeAndKey("error_remind_threshold", "error_remind_threshold", "1000");
        return Integer.parseInt(configValue);
    }

    // updated for v1.8.5 KNZT-3802
    public void saveCorpPersSync(TblGlobalOutboundListSyncTask task) throws MessageException {
        this.saveCorpPersSync(task, true);
    }

    private static AbstractReportResolver getInstanceReportResolver(ReportTypeEnum reportTypeEnum, List<TblGlobalOutboundCorpPersSync> corpPersResultList, TblGlobalOutboundListSyncTask task, Set<String> outboundDataTypeList, boolean needCompanyKey) {
        AbstractReportResolver reportResolver = null;
        switch (reportTypeEnum) {
            // added for v2.0.5 KNZT-5597 KYC Lite出境人员统计 只处理到法定代表人
            case LITE:
                reportResolver = new LiteReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break; 
            case BASIC:
                reportResolver = new BasicReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case UBO:
                reportResolver = new UBOReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case ADVANCED:
                reportResolver = new AdvancedReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case CORP_360:
                reportResolver = new AdvancedReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case CORP_360_DELAY:
                reportResolver = new AdvancedReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case PERS_BASIC:
                reportResolver = new PersBasicReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case INDIVIDUAL_360:
                reportResolver = new Individual360ReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            case MERCHANT:
                reportResolver = new UBOReportResolver(corpPersResultList, task.getReportType(), task.getKeyNo(), task.getName(), outboundDataTypeList, needCompanyKey);
                break;
            default:
                break;
        }
        return reportResolver;
    }

    // added for v1.8.5 KNZT-3802
    public abstract static class AbstractReportResolver {

        protected final List<TblGlobalOutboundCorpPersSync> corpPersResultList;

        protected final String reportType;

        protected final String mainKeyNo;

        protected final String mainName;

        protected final Set<String> configOutboundDataTypeList;

        protected final boolean needCompanyKey;

        public AbstractReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersResultList,
                                      String reportType,
                                      String mainKeyNo,
                                      String mainName,
                                      Set<String> configOutboundDataTypeList,
                                      boolean needCompanyKey) {
            this.corpPersResultList = corpPersResultList;
            this.reportType = reportType;
            this.mainKeyNo = mainKeyNo;
            this.mainName = mainName;
            this.configOutboundDataTypeList = configOutboundDataTypeList;
            this.needCompanyKey = needCompanyKey;
        }

        protected boolean ignoreThisDataType(OutboundDataTypeEnum dataTypeEnum) {
            return !configOutboundDataTypeList.contains(dataTypeEnum.getCode());
        }

        protected void addIfNecessary(OutboundDataTypeEnum dataTypeEnum, String keyNo, String name, String nameEn) {
            String nameValue = null; // 兼容人名只存在英文字段的情况
            if (isPerson(keyNo) && StringUtils.isNotBlank(nameValue = StringUtils.getNotBlankStr(name, nameEn))) {
                corpPersResultList.add(TblGlobalOutboundCorpPersSync.build(reportType, dataTypeEnum, mainKeyNo, mainName, keyNo, nameValue));
            }
        }

        protected boolean isPerson(String keyNo) {
            return StringUtils.isNotBlank(keyNo) && keyNo.startsWith(Constants.CorpTypePrefix.PERSON_PREFIX);
        }

        public abstract void resolve4Report() throws MessageException;
    }

    /**
     * added for v2.0.5 fengsw KNZT-5668
     * KYC LITE数据出境记录处理器
     */
    public static class LiteReportResolver extends AbstractReportResolver {

        // 为父子类共用, 避免重复获取；仅通过getOrInitCorpBasicInfo获取
        private CorpBasicDetail4UnitTO corpBasicInfoCache;

        public LiteReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        protected CorpBasicDetail4UnitTO getOrInitCorpBasicInfo() throws MessageException {
            if (corpBasicInfoCache == null) {
                corpBasicInfoCache = CompanyDetailsInterface.getCorpBasicInfo4Unit(mainKeyNo, null, needCompanyKey);
                if (corpBasicInfoCache == null) {
                    throw new MessageException("【Outbound saveCorpPersSyncTask】 corp info not found, keyNo: " + mainKeyNo);
                }
            }
            return corpBasicInfoCache;
        }

        // added for v2.0.5 fengsw KNZT-5597
        @Override
        public void resolve4Report() throws MessageException {
            addData4LegalRepresentative();
        }

        // added for v2.0.5 fengsw KNZT-5597
        // Legal Representative
        private void addData4LegalRepresentative() throws MessageException {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.LEGAL_REP;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            CorpBasicDetail4UnitTO corpBasicInfo = getOrInitCorpBasicInfo();
            if (Objects.nonNull(corpBasicInfo.getLegalRepresentativeInfo())
                    && CollectionUtils.isNotEmpty(corpBasicInfo.getLegalRepresentativeInfo().getItems())) {
                for (LegalRepresentativeInfoTO.Item item : corpBasicInfo.getLegalRepresentativeInfo().getItems()) {
                    addIfNecessary(dataTypeEnum, item.getKeyNo(), item.getName(), item.getNameEn());
                }
            }
        }
    }
    
    // added for v1.8.5 KNZT-3802
    // updated for v2.0.5 fengsw KNZT-5668
    public static class BasicReportResolver extends LiteReportResolver {

        // 为父子类共用, 避免重复获取；仅通过getOrInitCorpBasicInfo获取
        private CorpBasicDetail4UnitTO corpBasicInfoCache;

        public BasicReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        protected CorpBasicDetail4UnitTO getOrInitCorpBasicInfo() throws MessageException {
            if (corpBasicInfoCache == null) {
                corpBasicInfoCache = CompanyDetailsInterface.getCorpBasicInfo4Unit(mainKeyNo, null, needCompanyKey);
                if (corpBasicInfoCache == null) {
                    throw new MessageException("【Outbound saveCorpPersSyncTask】 corp info not found, keyNo: " + mainKeyNo);
                }
            }
            return corpBasicInfoCache;
        }

        // added for v1.8.5 KNZT-3802
        @Override
        public void resolve4Report() throws MessageException {
            super.resolve4Report();
            addData4Partner();
            addData4KeyPerson();
            addData4HeadquartersInChina();
        }
        
        // updated for v1.8.5 KNZT-3802
        // Shareholders
        private void addData4Partner() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.SHARE;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            CompanyPartnerResult partnerResult = null;
            do {
                partnerResult = ECILocalInterface.getPartnerWithGroup(mainKeyNo, "Priority", String.valueOf(pageIndex++), String.valueOf(pageSize), needCompanyKey);
                if (Objects.isNull(partnerResult) || CollectionUtils.isEmpty(partnerResult.getResult())) {
                    break;
                }
                for (CompanyParnter partner : partnerResult.getResult()) {
                    addIfNecessary(dataTypeEnum, partner.getKeyNo(), partner.getStockName(), null);
                }
            } while (true);
        }

        // updated for v1.8.5 KNZT-3802
        // Key Persons
        private void addData4KeyPerson() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.KEY_PERS;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            CorpEmployeeResult employeeResult = ECILocalInterface.getEmployeeList(mainKeyNo, "IpoEmployees", String.valueOf(pageIndex), String.valueOf(pageSize), needCompanyKey);
            if (Objects.nonNull(employeeResult) && CollectionUtils.isNotEmpty(employeeResult.getResult())) {
                while (Objects.nonNull(employeeResult) && CollectionUtils.isNotEmpty(employeeResult.getResult())) {
                    for (CorpEmployeeTO employee : employeeResult.getResult()) {
                        addIfNecessary(dataTypeEnum, employee.getKeyNo(), employee.getName(), employee.getEnglishName());
                    }
                    pageIndex++;
                    employeeResult = ECILocalInterface.getEmployeeList(mainKeyNo, "IpoEmployees", String.valueOf(pageIndex), String.valueOf(pageSize), needCompanyKey);
                }
            } else {
                employeeResult = ECILocalInterface.getEmployeeList(mainKeyNo, "Employees", String.valueOf(pageIndex), String.valueOf(pageSize), needCompanyKey);
                if (Objects.nonNull(employeeResult) && CollectionUtils.isNotEmpty(employeeResult.getResult())) {
                    while (Objects.nonNull(employeeResult) && CollectionUtils.isNotEmpty(employeeResult.getResult())) {
                        for (CorpEmployeeTO employee : employeeResult.getResult()) {
                            addIfNecessary(dataTypeEnum, employee.getKeyNo(), employee.getName(), employee.getEnglishName());
                        }
                        pageIndex++;
                        employeeResult = ECILocalInterface.getEmployeeList(mainKeyNo, "Employees", String.valueOf(pageIndex), String.valueOf(pageSize), needCompanyKey);
                    }
                }
            }
        }

        // added for v1.8.5 KNZT-3802
        // Headquarters in China(Legal Representative) 总公司(法定代表人)
        private void addData4HeadquartersInChina() throws MessageException {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HQ_CN;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            CorpBusinessParentInfoTO corpParentInfo = CompanyDetailsInterface.getCorpParentInfo(mainKeyNo, needCompanyKey);
            CorpParentOper corpParentOper = Optional.ofNullable(corpParentInfo).map(CorpBusinessParentInfoTO::getOper).orElse(null);
            if (Objects.nonNull(corpParentOper)) {
                addIfNecessary(dataTypeEnum, corpParentOper.getKeyNo(), corpParentOper.getName(), corpParentOper.getEnglishName());
            }
        }
    }

    // added for v1.8.5 KNZT-3802
    public static class UBOReportResolver extends BasicReportResolver {

        public UBOReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        // added for v1.8.5 KNZT-3802
        @Override
        public void resolve4Report() throws MessageException {
            super.resolve4Report();

            addData4UBO();
        }

        // UBO
        public void addData4UBO() throws MessageException {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.UBO;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            Map<String, String> persMap = corpPersResultList.stream().collect(Collectors.toMap(TblGlobalOutboundCorpPersSync::getPersName, TblGlobalOutboundCorpPersSync::getPersKeyNo, (v1, v2) -> v1));
            StockResult4UnitTO uboResult = CompanyDetailsInterface.getCorpUBO4Unit(mainKeyNo, needCompanyKey);
            if (Objects.isNull(uboResult)) {
                return;
            }
            if (StringUtils.isBlank(uboResult.getRemark())) {
                if (CollectionUtils.isNotEmpty(uboResult.getBreakThroughListReport())) {
                    for (StockDetail4UnitTO ubo : uboResult.getBreakThroughListReport()) {
                        addIfNecessary(dataTypeEnum, ubo.getKeyNo(), ubo.getName(), ubo.getNameEn());
                    }
                }
            } else {
                if (CollectionUtils.isNotEmpty(uboResult.getExecutives())) {
                    if (StringUtils.isNotBlank(uboResult.getOperName()) && StringUtils.isNotBlank(persMap.get(uboResult.getOperName()))) {
                        addIfNecessary(dataTypeEnum, persMap.get(uboResult.getOperName()), uboResult.getOperName(), uboResult.getOperNameEn());
                    }
                    for (Executive executive : uboResult.getExecutives()) {
                        if (StringUtils.isNotBlank(persMap.get(executive.getName()))) {
                            addIfNecessary(dataTypeEnum, persMap.get(executive.getName()), executive.getName(), executive.getNameEn());
                        }
                    }
                } else if (CollectionUtils.isNotEmpty(uboResult.getHehuoPersonList()) || CollectionUtils.isNotEmpty(uboResult.getOtherBeneList())) {
                    List<OtherBeneTO> otherBeneTOList = CollectionUtils.isNotEmpty(uboResult.getHehuoPersonList()) ?
                            uboResult.getHehuoPersonList() : uboResult.getOtherBeneList();
                    for (OtherBeneTO otherBeneTO : otherBeneTOList) {
                        addIfNecessary(dataTypeEnum, otherBeneTO.getKeyNo(), otherBeneTO.getName(), otherBeneTO.getNameEn());
                    }
                } else if (StringUtils.isNotBlank(uboResult.getOperName())) {
                    if (StringUtils.isNotBlank(persMap.get(uboResult.getOperName()))) {
                        addIfNecessary(dataTypeEnum, persMap.get(uboResult.getOperName()), uboResult.getOperName(), uboResult.getOperNameEn());
                    }
                }
            }
            // added for lvcy v2.0.6 KNZT-5663
            if (CollectionUtils.isNotEmpty(uboResult.getBreakThroughListReportBO())) {
                for (StockDetail4UnitTO bo : uboResult.getBreakThroughListReportBO()) {
                    addIfNecessary(dataTypeEnum, bo.getKeyNo(), bo.getName(), bo.getNameEn());
                }
            }
        }
    }

    // added for v1.8.5 KNZT-3802
    public static class AdvancedReportResolver extends UBOReportResolver {

        public AdvancedReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        // added for v1.8.5 KNZT-3802
        @Override
        public void resolve4Report() throws MessageException {
            super.resolve4Report();

            addData4HistoricalLegalRepresentative();
            addData4HistoricalShareholders();
            addData4HistoricalKeyPersons();
            addData4DishonestJudgementDebtors();
            addData4HistoricalDishonestJudgementDebtors();
            addData4RestrictionOfHighConsumption();
            addData4HistoricalRestrictionOfHighConsumption();
            addData4RestrictedDeparture();
            addData4EquityPledge();
        }

        // added for v1.8.5 KNZT-3802
        // Historical Legal Representative
        private void addData4HistoricalLegalRepresentative() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIST_LEGAL_REP;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            HistOperResult historyOperListResult = CompanyDetailsInterface.getHistoryOperList(mainKeyNo, needCompanyKey);
            if (Objects.nonNull(historyOperListResult) && CollectionUtils.isNotEmpty(historyOperListResult.getResultList())) {
                for (HistOper histOper : historyOperListResult.getResultList()) {
                    addIfNecessary(dataTypeEnum, histOper.getKeyNo(), histOper.getOperName(), histOper.getOperNameEn());
                }
            }
        }

        // added for v1.8.5 KNZT-3802
        // Historical shareholders
        private void addData4HistoricalShareholders() throws MessageException {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIST_SHARE;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            EquityShareChangesByDateResultTO equityShareChangesByDate = CorpGatewayInterface.getEquityShareChangesByDate(mainKeyNo);
            if (Objects.nonNull(equityShareChangesByDate) && CollectionUtils.isNotEmpty(equityShareChangesByDate.getChanges())) {
                for (EquityShareChangesByDateResultTO.ChangesTO changesTO : equityShareChangesByDate.getChanges()) {
                    if (CollectionUtils.isNotEmpty(changesTO.getShares())) {
                        for (EquityShareChangesByDateResultTO.SharesTO sharesTO : changesTO.getShares()) {
                            addIfNecessary(dataTypeEnum, sharesTO.getKeyNo(), sharesTO.getName(), sharesTO.getNameEn());
                        }
                    }
                }
            }
        }

        // added for v1.8.5 KNZT-3802
        // Historical key persons
        private void addData4HistoricalKeyPersons() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIST_KEY_PERS;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            HistEmployeeResult histEmployeeResult = null;
            do {
                histEmployeeResult = CompanyDetailsInterface.getHistoryEmployeeList(mainKeyNo, String.valueOf(pageIndex++), String.valueOf(pageSize), needCompanyKey);
                if (Objects.isNull(histEmployeeResult) || CollectionUtils.isEmpty(histEmployeeResult.getResultList())) {
                    break;
                }
                for (HistEmployee histEmployee : histEmployeeResult.getResultList()) {
                    addIfNecessary(dataTypeEnum, histEmployee.getKeyNo(), histEmployee.getEmployeeName(), histEmployee.getEmployeeEnName());
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // Dishonest Judgement Debtors(Applicant)
        private void addData4DishonestJudgementDebtors() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.DISH_JUDGE;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<CoutShiXin> coutShiXinResult = null;
            do {
                if (dataTypeEnum.getLimitRecords() > 0 && pageIndex > dataTypeEnum.getLimitRecords() / pageSize) {
                    break;
                }
                coutShiXinResult = CorpGatewayInterface.listShixin(mainKeyNo, "1", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(coutShiXinResult.getList())) {
                    break;
                }
                for (CoutShiXin coutShiXinItem : coutShiXinResult.getList()) {
                    if (CollectionUtils.isNotEmpty(coutShiXinItem.getSqrInfo())) {
                        for (CoutShiXinNameKeyNoColl coutShiXinNameKeyNoColl : coutShiXinItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, coutShiXinNameKeyNoColl.getKeyNo(), coutShiXinNameKeyNoColl.getName(), coutShiXinNameKeyNoColl.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // Historical Dishonest Judgement Debtors(Applicant)
        private void addData4HistoricalDishonestJudgementDebtors() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIST_DISH_JUDGE;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<CoutShiXin> coutShiXinResult = null;
            do {
                if (dataTypeEnum.getLimitRecords() > 0 && pageIndex > dataTypeEnum.getLimitRecords() / pageSize) {
                    break;
                }
                coutShiXinResult = CorpGatewayInterface.listShixin(mainKeyNo, "0", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(coutShiXinResult.getList())) {
                    break;
                }
                for (CoutShiXin coutShiXinItem : coutShiXinResult.getList()) {
                    if (CollectionUtils.isNotEmpty(coutShiXinItem.getSqrInfo())) {
                        for (CoutShiXinNameKeyNoColl coutShiXinNameKeyNoColl : coutShiXinItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, coutShiXinNameKeyNoColl.getKeyNo(), coutShiXinNameKeyNoColl.getName(), coutShiXinNameKeyNoColl.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // Restriction of High Consumption(Applicant、Associated Object)
        private void addData4RestrictionOfHighConsumption() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIGH_CONS_REST;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<PersonSumptuary> personSumptuaryResult = null;
            do {
                if (dataTypeEnum.getLimitRecords() > 0 && pageIndex > dataTypeEnum.getLimitRecords() / pageSize) {
                    break;
                }
                personSumptuaryResult = CorpGatewayInterface.listSumptuary(mainKeyNo, "1", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(personSumptuaryResult.getList())) {
                    break;
                }
                for (PersonSumptuary personSumptuaryItem : personSumptuaryResult.getList()) {
                    // Associated Object
                    addIfNecessary(dataTypeEnum, personSumptuaryItem.getRelatedKeyNo(), personSumptuaryItem.getRelatedName(), personSumptuaryItem.getRelatedEnName());
                    // Applicant
                    if (CollectionUtils.isNotEmpty(personSumptuaryItem.getSqrInfo())) {
                        for (SqrInfo sqrInfoItem : personSumptuaryItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, sqrInfoItem.getKeyNo(), sqrInfoItem.getName(), sqrInfoItem.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // Historical Restriction of High Consumption(Applicant、Associated Object)
        private void addData4HistoricalRestrictionOfHighConsumption() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIST_HIGH_CONS_REST;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<PersonSumptuary> personSumptuaryResult = null;
            do {
                if (dataTypeEnum.getLimitRecords() > 0 && pageIndex > dataTypeEnum.getLimitRecords() / pageSize) {
                    break;
                }
                personSumptuaryResult = CorpGatewayInterface.listSumptuary(mainKeyNo, "0", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(personSumptuaryResult.getList())) {
                    break;
                }
                for (PersonSumptuary personSumptuaryItem : personSumptuaryResult.getList()) {
                    // Associated Object
                    addIfNecessary(dataTypeEnum, personSumptuaryItem.getRelatedKeyNo(), personSumptuaryItem.getRelatedName(), personSumptuaryItem.getRelatedEnName());
                    // Applicant
                    if (CollectionUtils.isNotEmpty(personSumptuaryItem.getSqrInfo())) {
                        for (SqrInfo sqrInfoItem : personSumptuaryItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, sqrInfoItem.getKeyNo(), sqrInfoItem.getName(), sqrInfoItem.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // Restricted Departure(Object)
        private void addData4RestrictedDeparture() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.REST_DEP;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<LimitExit> limitExitResult = null;
            do {
                if (dataTypeEnum.getLimitRecords() > 0 && pageIndex > dataTypeEnum.getLimitRecords() / pageSize) {
                    break;
                }
                limitExitResult = CorpGatewayInterface.listLimitExit(mainKeyNo, String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (Objects.isNull(limitExitResult) || CollectionUtils.isEmpty(limitExitResult.getList())) {
                    break;
                }
                for (LimitExit limitExitItem : limitExitResult.getList()) {
                    if (CollectionUtils.isNotEmpty(limitExitItem.getLimitedPerson())) {
                        for (Oper limitedPerson : limitExitItem.getLimitedPerson()) {
                            addIfNecessary(dataTypeEnum, limitedPerson.getKeyNo(), limitedPerson.getName(), limitedPerson.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // added for v1.8.5 KNZT-3802
        // EquityPledge(Pledgor、Pledgee)
        private void addData4EquityPledge() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.EQ_PLED;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<CorpPledgeV2TO> result = null;
            do {
                result = CorpGatewayInterface.listEquityPledge(mainKeyNo, String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(result.getList())) {
                    break;
                }
                for (CorpPledgeV2TO corpPledgeV2TO : result.getList()) {
                    if (CollectionUtils.isNotEmpty(corpPledgeV2TO.getPledgorInfo())) {
                        for (CorpPledgeV2TO.CorpPledgePledgorInfo corpPledgePledgorInfo : corpPledgeV2TO.getPledgorInfo()) {
                            addIfNecessary(dataTypeEnum, corpPledgePledgorInfo.getKeyNo(), corpPledgePledgorInfo.getName(), corpPledgePledgorInfo.getNameEn());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(corpPledgeV2TO.getPledgeeInfo())) {
                        for (CorpPledgeV2TO.CorpPledgePledgeeInfo corpPledgePledgeeInfo : corpPledgeV2TO.getPledgeeInfo()) {
                            addIfNecessary(dataTypeEnum, corpPledgePledgeeInfo.getKeyNo(), corpPledgePledgeeInfo.getName(), corpPledgePledgeeInfo.getNameEn());
                        }
                    }
                }
            } while (true);
        }
    }



    // added for v1.8.5 KNZT-3802
    public static class PersBasicReportResolver extends AbstractReportResolver {

        public PersBasicReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        // added for v1.8.5 KNZT-3802
        @Override
        public void resolve4Report() throws MessageException {
            if (ignoreThisDataType(OutboundDataTypeEnum.EXECU)) {
                return;
            }
            addIfNecessary(OutboundDataTypeEnum.EXECU, mainKeyNo, mainName, null);
        }
    }

    public static class Individual360ReportResolver extends PersBasicReportResolver {

        public Individual360ReportResolver(List<TblGlobalOutboundCorpPersSync> corpPersSyncList, String reportType, String mainKeyNo, String mainName, Set<String> outboundDataTypeList, boolean needCompanyKey) {
            super(corpPersSyncList, reportType, mainKeyNo, mainName, outboundDataTypeList, needCompanyKey);
        }

        // added for v1.8.5 KNZT-3802
        @Override
        public void resolve4Report() throws MessageException {
            super.resolve4Report();
            
            addData4BusinessPartners();
            addData4JudgmentDebtor();
            addData4DishonestJudgmentDebtor();
            addData4RestrictionOnHighConsumption();
            addData4TravelRestrictionOrder();
            addData4EquityPledge();
        }

        // Business Partners - PersonGatewayInterface.listPersonPartners
        private void addData4BusinessPartners() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.BUS_PARTNER;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            PageDataBO<PersonPartnerTO> partnerResult = PersonGatewayInterface.listPersonPartners(mainKeyNo);
            if (CollectionUtils.isNotEmpty(partnerResult.getList())) {
                for (PersonPartnerTO partner : partnerResult.getList()) {
                    addIfNecessary(dataTypeEnum, partner.getPersKeyNo(), partner.getPersName(), partner.getPersNameEn());
                }
            }
        }

        // Judgment Debtor - CorpGatewayInterface.listZhixing
        private void addData4JudgmentDebtor() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.JUDG_DEBT;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<CoutZhiXing> zhiXingResult = null;
            do {
                zhiXingResult = CorpGatewayInterface.listZhixing(mainKeyNo, "1", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(zhiXingResult.getList())) {
                    break;
                }
                for (CoutZhiXing zhiXingItem : zhiXingResult.getList()) {
                    if (CollectionUtils.isNotEmpty(zhiXingItem.getNameKeyNoCollection())) {
                        for (CoutZhiXingNameKeyNoColl nameKeyNoColl : zhiXingItem.getNameKeyNoCollection()) {
                            addIfNecessary(dataTypeEnum, nameKeyNoColl.getKeyNo(), nameKeyNoColl.getName(), nameKeyNoColl.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // Dishonest Judgment Debtor - CorpGatewayInterface.listShixin
        private void addData4DishonestJudgmentDebtor() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.DISH_JUDGE;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<CoutShiXin> shiXinResult = null;
            do {
                shiXinResult = CorpGatewayInterface.listShixin(mainKeyNo, "1", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(shiXinResult.getList())) {
                    break;
                }
                for (CoutShiXin shiXinItem : shiXinResult.getList()) {
                    if (CollectionUtils.isNotEmpty(shiXinItem.getNameKeyNoCollection())) {
                        for (CoutShiXinNameKeyNoColl nameKeyNoColl : shiXinItem.getNameKeyNoCollection()) {
                            addIfNecessary(dataTypeEnum, nameKeyNoColl.getKeyNo(), nameKeyNoColl.getName(), nameKeyNoColl.getEnglishName());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(shiXinItem.getSqrInfo())) {
                        for (CoutShiXinNameKeyNoColl sqrInfoItem : shiXinItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, sqrInfoItem.getKeyNo(), sqrInfoItem.getName(), sqrInfoItem.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // Restriction on High-Consumption Spending - CorpGatewayInterface.listSumptuary
        private void addData4RestrictionOnHighConsumption() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.HIGH_CONS_REST;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<PersonSumptuary> sumptuaryResult = null;
            do {
                sumptuaryResult = CorpGatewayInterface.listSumptuary(mainKeyNo, "1", String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(sumptuaryResult.getList())) {
                    break;
                }
                for (PersonSumptuary sumptuaryItem : sumptuaryResult.getList()) {
                    // sqrInfo
                    if (CollectionUtils.isNotEmpty(sumptuaryItem.getSqrInfo())) {
                        for (SqrInfo sqrInfoItem : sumptuaryItem.getSqrInfo()) {
                            addIfNecessary(dataTypeEnum, sqrInfoItem.getKeyNo(), sqrInfoItem.getName(), sqrInfoItem.getEnglishName());
                        }
                    }
                    // relatedKeyNo, relatedName, relatedEnName
                    addIfNecessary(dataTypeEnum, sumptuaryItem.getRelatedKeyNo(), sumptuaryItem.getRelatedName(), sumptuaryItem.getRelatedEnName());
                    // xianGaoLingKeyNo, xianGaoLingObj, xianGaoLingObjEnName
                    addIfNecessary(dataTypeEnum, sumptuaryItem.getXianGaoLingKeyNo(), sumptuaryItem.getXianGaoLingObj(), sumptuaryItem.getXianGaoLingObjEnName());
                }
            } while (true);
        }

        // Travel Restriction Order - CorpGatewayInterface.listLimitExit
        private void addData4TravelRestrictionOrder() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.REST_DEP;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<LimitExit> limitExitResult = null;
            do {
                limitExitResult = CorpGatewayInterface.listLimitExit(mainKeyNo, String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(limitExitResult.getList())) {
                    break;
                }
                for (LimitExit limitExitItem : limitExitResult.getList()) {
                    // limitedPerson
                    if (CollectionUtils.isNotEmpty(limitExitItem.getLimitedPerson())) {
                        for (Oper limitedPerson : limitExitItem.getLimitedPerson()) {
                            addIfNecessary(dataTypeEnum, limitedPerson.getKeyNo(), limitedPerson.getName(), limitedPerson.getEnglishName());
                        }
                    }
                    // applayer
                    if (CollectionUtils.isNotEmpty(limitExitItem.getApplayer())) {
                        for (Oper applayer : limitExitItem.getApplayer()) {
                            addIfNecessary(dataTypeEnum, applayer.getKeyNo(), applayer.getName(), applayer.getEnglishName());
                        }
                    }
                }
            } while (true);
        }

        // Equity Pledge - CorpGatewayInterface.listPledgeOfEquity
        private void addData4EquityPledge() {
            OutboundDataTypeEnum dataTypeEnum = OutboundDataTypeEnum.EQ_PLED;
            if (ignoreThisDataType(dataTypeEnum)) {
                return;
            }
            int pageIndex = 1;
            PageDataBO<PledgeOfEquityListTO> pledgeResult = null;
            do {
                pledgeResult = CorpGatewayInterface.listPledgeOfEquity(mainKeyNo, String.valueOf(pageIndex++), String.valueOf(pageSize));
                if (CollectionUtils.isEmpty(pledgeResult.getList())) {
                    break;
                }
                for (PledgeOfEquityListTO pledgeItem : pledgeResult.getList()) {
                    // pledgorList
                    if (CollectionUtils.isNotEmpty(pledgeItem.getPledgorList())) {
                        for (CommEntityItem pledgor : pledgeItem.getPledgorList()) {
                            addIfNecessary(dataTypeEnum, pledgor.getKeyNo(), pledgor.getName(), pledgor.getNameEn());
                        }
                    }
                    // pledgeeList
                    if (CollectionUtils.isNotEmpty(pledgeItem.getPledgeeList())) {
                        for (CommEntityItem pledgee : pledgeItem.getPledgeeList()) {
                            addIfNecessary(dataTypeEnum, pledgee.getKeyNo(), pledgee.getName(), pledgee.getNameEn());
                        }
                    }
                }
            } while (true);
        }
    }

}
