package com.backend.common.openapi;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.backend.common.openapi.model.KzzContractPaymentSheet;
import com.backend.common.openapi.model.KzzInvoiceDetailTO;
import com.backend.common.openapi.model.KzzWorkFlowDetailTO;
import com.backend.common.openapi.model.KzzBenefitWorkFlowDetailTO;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.AmtStdEnum;
import com.qcc.frame.commons.ienum.BillingBankEnum;
import com.qcc.frame.commons.ienum.KzzCustomFieldKeyEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.CurrencyUnit;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.*;

/**
 * added for v1.8.8 KNZT-3324
 *
 * <AUTHOR>
 * @datetime 2024/6/26 18:01
 */
public class KzzApiInterface {
    protected static Logger logger = LoggerFactory.getLogger(KzzApiInterface.class);


    public static KzzContractDetailTO getContractDetail(String contractNo) throws MessageException {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }
        String respStr = get("/openapi/kzz/contract/v2/code/" + contractNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject contractJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(contractJsonObj)) {
                    continue;
                }
                KzzContractDetailTO contract = contractJsonObj.toJavaObject(KzzContractDetailTO.class);

                JSONObject customerJsonObj = contractJsonObj.getJSONObject("customer");
                if (Objects.nonNull(customerJsonObj)) {
                    String customerName = customerJsonObj.getString("name");
                    contract.setCustomerName(customerName);
                }
                JSONObject currencyEsModelJsonObj = contractJsonObj.getJSONObject("currencyEsModel");
                if (Objects.nonNull(currencyEsModelJsonObj)) {
                    String abbreviation = currencyEsModelJsonObj.getString("abbreviation");
                    contract.setAbbreviation(abbreviation);
                    String currencyId = currencyEsModelJsonObj.getString("currencyId");
                    contract.setCurrencyId(Long.parseLong(currencyId));
                }
                JSONObject quotationJsonObj = contractJsonObj.getJSONObject("quotation");
                if (Objects.nonNull(quotationJsonObj)) {
                    String quotationCode = quotationJsonObj.getString("code");
                    contract.setQuotationCode(quotationCode);
                }

                List<KzzContractDetailTO.ContractProduct> contractProducts = Lists.newArrayList();
                JSONArray contractProductJsonArray = contractJsonObj.getJSONArray("contractProducts");
                if (Objects.nonNull(contractProductJsonArray)) {
                    for (int j = 0; j < contractProductJsonArray.size(); j++) {
                        JSONObject contractProductJsonObj = contractProductJsonArray.getJSONObject(j);
                        if (Objects.nonNull(contractProductJsonObj)) {
                            KzzContractDetailTO.ContractProduct contractProduct = contractProductJsonObj.toJavaObject(KzzContractDetailTO.ContractProduct.class);
                            JSONObject productJsonObj = contractProductJsonObj.getJSONObject("product");
                            if (Objects.nonNull(productJsonObj)) {
                                contractProduct.setProductCode(productJsonObj.getString("code"));
                                contractProduct.setProductName(productJsonObj.getString("name"));
                            }
                            contractProducts.add(contractProduct);
                        }
                    }
                }
                contract.setContractProducts(contractProducts);

                JSONObject customShowFieldsJsonObj = contractJsonObj.getJSONObject("customShowFields");
                if (Objects.nonNull(customShowFieldsJsonObj)) {
                    JSONArray payTypeArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_PAY_TYPE.getKey());
                    if (Objects.nonNull(payTypeArray) && !payTypeArray.isEmpty()) {
                        contract.setPayType(payTypeArray.getString(0));
                    }
                    int serviceYear = customShowFieldsJsonObj.getIntValue(KzzCustomFieldKeyEnum.CONTRACT_SERVICE_YEAR.getKey());
                    contract.setServiceYear(serviceYear);
                    JSONArray invoiceMethodArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_INVOICE_METHOD.getKey());
                    if (Objects.nonNull(invoiceMethodArray) && !invoiceMethodArray.isEmpty()) {
                        contract.setInvoiceMethod(invoiceMethodArray.getString(0));
                    }
                    JSONArray signingPartyArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_SIGNING_PARTY.getKey());
                    if (Objects.nonNull(signingPartyArray) && !signingPartyArray.isEmpty()) {
                        contract.setSigningParty(signingPartyArray.getString(0));
                    }
                    String invoiceName = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_INVOICE_NAME.getKey());
                    contract.setInvoiceName(invoiceName);

                    JSONArray statementTypeArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_STATEMENT_TYPE.getKey());
                    if (Objects.nonNull(statementTypeArray) && !statementTypeArray.isEmpty()) {
                        contract.setStatementType(statementTypeArray.getString(0));
                    }
                    // 客户地址
                    String addressLine1 = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_ADDRESS_LINE1.getKey());
                    contract.setAddressLine1(addressLine1);

                    String addressLine2 = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_ADDRESS_LINE2.getKey());
                    contract.setAddressLine2(addressLine2);

                    String city = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_CITY.getKey());
                    contract.setCity(city);

                    String province = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_PROVINCE.getKey());
                    contract.setProvince(province);

                    JSONArray countryArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_COUNTRY.getKey());
                    if (Objects.nonNull(countryArray) && !countryArray.isEmpty()) {
                        contract.setCountry(countryArray.getString(0));
                    }

                    String zipCode = customShowFieldsJsonObj.getString(KzzCustomFieldKeyEnum.CONTRACT_ZIP_CODE.getKey());
                    contract.setZipCode(zipCode);

                    // 开票银行信息字段映射
                    JSONArray billingAccountArray = customShowFieldsJsonObj.getJSONArray(KzzCustomFieldKeyEnum.CONTRACT_BILLING_BANK_ACCOUNT.getKey());
                    if (Objects.nonNull(billingAccountArray) && !billingAccountArray.isEmpty()) {
                        String billingAccountNumber = billingAccountArray.getString(0);
                        BillingBankEnum billingBank = BillingBankEnum.getByAccountNumber(billingAccountNumber);
                        if (billingBank != null) {
                            contract.setAccountName(billingBank.getAccountName());
                            contract.setDepositBank(billingBank.getDepositBank());
                            contract.setAccountNumber(billingBank.getAccountNumber());
                            contract.setSwift(billingBank.getSwift());
                        }
                    }
                }
                // added for fengsw v2.2.7 KNZT-7687 获取合同的客户负责人id
                JSONArray userCustomerArray = contractJsonObj.getJSONArray("userCustomers");
                if (Objects.nonNull(userCustomerArray)) {
                    for (int j = 0; j < userCustomerArray.size(); j++) {
                        JSONObject jsonObject = userCustomerArray.getJSONObject(j);
                        if (Objects.nonNull(jsonObject)) {
                            int roleId = jsonObject.getIntValue("roleId");
                            if (roleId == 4) {
                                contract.setCustomerUserId(jsonObject.getLong("userId"));
                                break;
                            }
                        }
                    }
                }

                return contract;
            }
        }
        return null;
    }

    public static KzzWorkFlowDetailTO getWorkFlowDetail(String workFlowNo) throws MessageException {
        String respStr = get("/openapi/kzz/workflow/code/" + workFlowNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject workFlowJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(workFlowJsonObj)) {
                    continue;
                }
                String rootRouteBaseId = workFlowJsonObj.getString("rootRouteBaseId");
                if (!StringUtils.equalsAny(rootRouteBaseId, "3466", "4673")) {
                    logger.info("工单类型不符合要求, workFlowNo:{}, rootRouteBaseId:{}", workFlowNo, rootRouteBaseId);
                    continue;
                }
                KzzWorkFlowDetailTO workFlow = workFlowJsonObj.toJavaObject(KzzWorkFlowDetailTO.class);

                JSONObject contractJsonObj = workFlowJsonObj.getJSONObject("contract");
                if (Objects.nonNull(contractJsonObj)) {
                    String contractNo = contractJsonObj.getString("code");
                    workFlow.setContractNo(contractNo);
                }

                JSONObject createUserJsonObj = workFlowJsonObj.getJSONObject("createUser");
                if (Objects.nonNull(createUserJsonObj)) {
                    String name = createUserJsonObj.getString("name");
                    workFlow.setSubmitName(name);
                }
                JSONArray customFieldValues = workFlowJsonObj.getJSONArray("customFieldValues");
                for (Object customFieldValue : customFieldValues) {
                    JSONObject customShowFieldsJsonObj = (JSONObject) customFieldValue;
                    String fieldKey = customShowFieldsJsonObj.getString("kzzFieldKey");
                    if (KzzCustomFieldKeyEnum.WORKFLOW_BEGIN_DATE.matches(fieldKey)) {
                        Date date = customShowFieldsJsonObj.getDate("dateValue");
                        workFlow.setBeginDate(date);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_END_DATE.matches(fieldKey)) {
                        Date date = customShowFieldsJsonObj.getDate("dateValue");
                        workFlow.setEndDate(date);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_ADDRESS_LINE1.matches(fieldKey)) {
                        String addressLine1 = customShowFieldsJsonObj.getString("multiStrValue");
                        workFlow.setAddressLine1(addressLine1);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_ADDRESS_LINE2.matches(fieldKey)) {
                        String addressLine2 = customShowFieldsJsonObj.getString("multiStrValue");
                        workFlow.setAddressLine2(addressLine2);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_CITY.matches(fieldKey)) {
                        String city = customShowFieldsJsonObj.getString("strValue");
                        workFlow.setCity(city);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_PROVINCE.matches(fieldKey)) {
                        String province = customShowFieldsJsonObj.getString("strValue");
                        workFlow.setProvince(province);
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_COUNTRY.matches(fieldKey)) {
                        JSONArray countryArray = customShowFieldsJsonObj.getJSONArray("listValue");
                        if (Objects.nonNull(countryArray) && !countryArray.isEmpty())
                            workFlow.setCountry(countryArray.getString(0));
                    }
                    if (KzzCustomFieldKeyEnum.WORKFLOW_ZIP_CODE.matches(fieldKey)) {
                        String zipCode = customShowFieldsJsonObj.getString("strValue");
                        workFlow.setZipCode(zipCode);
                    }
                }
                return workFlow;
            }
        }
        return null;
    }

    /**
     * 获取回款单数据
     *
     * @param paymentSheetCode
     * @return
     * @throws MessageException
     */
    public static KzzContractPaymentSheet getPaymentSheet(String paymentSheetCode) throws MessageException {
        String respStr = get("/openapi/kzz/payment/sheet/v2/code/" + paymentSheetCode, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject paymentSheetJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(paymentSheetJsonObj)) {
                    continue;
                }
                return paymentSheetJsonObj.toJavaObject(KzzContractPaymentSheet.class);
            }
        }
        return null;
    }

    public static KzzContractDetailTO getContractDetailByWorkFlowNo(String workFlowNo) throws MessageException {
        KzzWorkFlowDetailTO workFlowDetail = KzzApiInterface.getWorkFlowDetail(workFlowNo);
        if (Objects.nonNull(workFlowDetail) && workFlowDetail.isPass()) {
            return KzzApiInterface.getContractDetail(workFlowDetail.getContractNo());
        }
        return null;
    }


    public static String get(String url, Map<String, String> paramMap) throws MessageException {
        try {
            String accessKey = Global.getConfig("kzz.api.access.key");
            String secretKey = Global.getConfig("kzz.api.secret.key");
            Map<String, String> headerMap = new HashMap<>();
            long timestamp = System.currentTimeMillis();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String sign = Base64.encodeBase64String(sha256_HMAC.doFinal((url + "\n" + "GET" + "\n" + timestamp).getBytes()));// 重点
            headerMap.put("x-kzz-sign", sign);
            headerMap.put("x-kzz-timestamp", String.valueOf(timestamp));
            headerMap.put("x-kzz-access-key", accessKey);
            logger.info("call kzz url:{}, param:{}", url, JSONUtils.toJSONString(paramMap));
            String config = Global.getConfig("kzz.api.domain");
            String resp = HttpUtils.get(config + url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_300000, headerMap);
            logger.info("call kzz url:{}, resp:{}", url, resp);
            return resp;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            logger.error("KzzApiInterface.get error", e);
        }
        return null;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 推送客找找创建合同信息
     *
     * @param url
     * @param requestJsonStr
     * @return
     * @throws MessageException
     */
    public static String postJsonRequest(String url, String requestJsonStr) throws MessageException {
        try {
            String accessKey = Global.getConfig("kzz.api.access.key");
            String secretKey = Global.getConfig("kzz.api.secret.key");
            Map<String, String> headerMap = new HashMap<>();
            long timestamp = System.currentTimeMillis();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String sign = Base64.encodeBase64String(sha256_HMAC.doFinal((url + "\n" + "POST" + "\n" + timestamp).getBytes()));
            headerMap.put("x-kzz-sign", sign);
            headerMap.put("x-kzz-timestamp", String.valueOf(timestamp));
            headerMap.put("x-kzz-access-key", accessKey);
            headerMap.put("content-type", "application/json");
            logger.info("call kzz url:{}, param:{}", url, requestJsonStr);
            String config = Global.getConfig("kzz.api.domain");
            String resp = HttpUtils.postJson(config + url, requestJsonStr, headerMap, HttpUtils.HTTP_CLIENT_TIMEOUT_65000);
            logger.info("call kzz url:{}, resp:{}", url, resp);
            return resp;
        } catch (Exception e) {
            logger.error("KzzApiInterface create contract error", e);
            throw new MessageException("KzzApiInterface create contract error", e.getMessage());
        }
    }

    /**
     * 获取额度划扣工单详情
     *
     * @param workFlowNo 工单编号
     * @return KzzBenefitWorkFlowDetailTO 额度划扣工单详情
     * @throws MessageException 消息异常
     */
    public static KzzBenefitWorkFlowDetailTO getBenefitWorkFlowDetail(String workFlowNo) throws MessageException {
        String respStr = get("/openapi/kzz/workflow/code/" + workFlowNo, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject workFlowJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(workFlowJsonObj)) {
                    continue;
                }

                KzzBenefitWorkFlowDetailTO benefitWorkFlow = workFlowJsonObj.toJavaObject(KzzBenefitWorkFlowDetailTO.class);

                // 设置关联合同号
                JSONObject contractJsonObj = workFlowJsonObj.getJSONObject("contract");
                if (Objects.nonNull(contractJsonObj)) {
                    String contractNo = contractJsonObj.getString("code");
                    benefitWorkFlow.setContractNo(contractNo);
                }

                // 解析自定义字段值
                JSONArray customFieldValues = workFlowJsonObj.getJSONArray("customFieldValues");
                if (Objects.nonNull(customFieldValues)) {
                    for (Object customFieldValue : customFieldValues) {
                        JSONObject customFieldJsonObj = (JSONObject) customFieldValue;
                        String fieldKey = customFieldJsonObj.getString("kzzFieldKey");

                        // 解析credits
                        if (KzzCustomFieldKeyEnum.BENEFIT_CREDITS.matches(fieldKey)) {
                            Integer initialCredits = customFieldJsonObj.getInteger("numberValue");
                            benefitWorkFlow.setConsumedCredits(initialCredits);
                        }

                        // 解析交付产品(产品编号)
                        if (KzzCustomFieldKeyEnum.BENEFIT_PRODUCT_CODE.matches(fieldKey)) {
                            String productCode = customFieldJsonObj.getString("strValue");
                            benefitWorkFlow.setProductCode(productCode);
                        }

                        // 解析开始时间
                        if (KzzCustomFieldKeyEnum.BENEFIT_BEGIN_DATE.matches(fieldKey)) {
                            Date beginDate = customFieldJsonObj.getDate("dateValue");
                            benefitWorkFlow.setBeginDate(beginDate);
                        }

                        // 解析交付数量
                        if (KzzCustomFieldKeyEnum.BENEFIT_DELIVERY_QUANTITY.matches(fieldKey)) {
                            Integer deliveryQuantity = customFieldJsonObj.getInteger("numberValue");
                            benefitWorkFlow.setDeliveryQuantity(deliveryQuantity);
                        }
                    }
                }

                return benefitWorkFlow;
            }
        }
        return null;
    }

    public static KzzInvoiceDetailTO getInvoiceDetail(String kzzInvoiceId) throws MessageException {
        String respStr = get("/openapi/kzz/invoice/v2/id/" + kzzInvoiceId, new HashMap<>());
        JSONObject jsonObj = JsonUtils.parseObject(respStr);
        if (Objects.nonNull(jsonObj)
                && StringUtils.equals("OK", jsonObj.getString("status"))
                && Objects.nonNull(jsonObj.getJSONArray("results"))) {
            JSONArray jsonArray = jsonObj.getJSONArray("results");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject invoiceJsonObj = jsonArray.getJSONObject(i);
                if (Objects.isNull(invoiceJsonObj)) {
                    continue;
                }

                KzzInvoiceDetailTO invoiceDetailTO = invoiceJsonObj.toJavaObject(KzzInvoiceDetailTO.class);
                JSONObject customerJsonObj = invoiceJsonObj.getJSONObject("customer");
                if (Objects.nonNull(customerJsonObj)) {
                    String customerName = customerJsonObj.getString("name");
                    invoiceDetailTO.setCustomerName(customerName);
                }

                JSONObject contractJsonObj = invoiceJsonObj.getJSONObject("contract");
                if (Objects.nonNull(contractJsonObj)) {
                    String contractNumber = contractJsonObj.getString("code");
                    invoiceDetailTO.setContractNumber(contractNumber);
                }

                // 解析自定义字段值
                JSONArray customFieldValues = invoiceJsonObj.getJSONArray("customFieldValues");
                if (Objects.nonNull(customFieldValues)) {
                    for (Object customFieldValue : customFieldValues) {
                        JSONObject customFieldJsonObj = (JSONObject) customFieldValue;
                        String fieldKey = customFieldJsonObj.getString("kzzFieldKey");
                        // 解析addressLine1
                        if (KzzCustomFieldKeyEnum.INVOICE_ADDRESS_LINE1.matches(fieldKey)) {
                            String addressLine1 = customFieldJsonObj.getString("multiStrValue");
                            invoiceDetailTO.setAddressLine1(addressLine1);
                        }
                        // 解析addressLine2
                        if (KzzCustomFieldKeyEnum.INVOICE_ADDRESS_LINE2.matches(fieldKey)) {
                            String addressLine2 = customFieldJsonObj.getString("multiStrValue");
                            invoiceDetailTO.setAddressLine2(addressLine2);
                        }
                        // 解析city
                        if (KzzCustomFieldKeyEnum.INVOICE_CITY.matches(fieldKey)) {
                            String city = customFieldJsonObj.getString("strValue");
                            invoiceDetailTO.setCity(city);
                        }
                        // 解析province
                        if (KzzCustomFieldKeyEnum.INVOICE_PROVINCE.matches(fieldKey)) {
                            String province = customFieldJsonObj.getString("strValue");
                            invoiceDetailTO.setProvince(province);
                        }
                        // 解析country
                        if (KzzCustomFieldKeyEnum.INVOICE_COUNTRY.matches(fieldKey)) {
                            JSONArray jsonArray1 = customFieldJsonObj.getJSONArray("listValue");
                            if (CollectionUtils.isNotEmpty(jsonArray1)) {
                                invoiceDetailTO.setCountry(jsonArray1.getString(0));
                            }
                        }
                    }
                }
                return invoiceDetailTO;
            }
        }
        return null;
    }

    /**
     * 从客找找获取所有货币对SGD的汇率信息
     * 分页循环查找所有汇率信息，构建成一个map<币种，对应税率>
     *
     * @return
     * @throws MessageException
     */
    public static Map<String, BigDecimal> fetchExchangeRatesToSGD() throws MessageException {
        Map<String, BigDecimal> exchangeRateMap = new HashMap<>();
        int pageSize = 100;
        int pageIndex = 1;

        // 构造基础参数
        TreeMap<String, Object> baseParam = new TreeMap<>();
        LocalDate currentDate = LocalDate.now();
        baseParam.put("pageSize", pageSize);
        baseParam.put("toCurrency", "SGD");
        baseParam.put("year", currentDate.getYear());
        baseParam.put("month", currentDate.getMonth().getValue());
        TreeMap<String, Object> param = new TreeMap<>(baseParam);
        param.put("pageIndex", pageIndex);

        int total = listExchangeRates4TargetCurrency(param, exchangeRateMap);
        int loopTimes = (total + pageSize - 1) / pageSize;
        if (loopTimes > 1) {
            pageIndex++;
            while (pageIndex <= loopTimes) {
                param = new TreeMap<>(baseParam); // 复用基础参数
                param.put("pageIndex", pageIndex);
                listExchangeRates4TargetCurrency(param, exchangeRateMap);
                pageIndex++;
            }
        }
        return exchangeRateMap;
    }

    /**
     * 指定输入货币，目标货币，计算汇率数据
     *
     * @param fromCurrency
     * @return
     * @throws MessageException
     */
    public static BigDecimal getExchangeRate4TargetCurrencyToSGD(String fromCurrency) throws MessageException {
        Map<String, BigDecimal> exchangeRateMap = new HashMap<>();
        if (AmtStdEnum.SGD.getCode().equals(fromCurrency)) { // 如果是新加坡元，直接返回1
            return BigDecimal.ONE;
        }
        int pageSize = 10;
        int pageIndex = 1;
        // 构造基础参数
        TreeMap<String, Object> baseParam = new TreeMap<>();
        LocalDate currentDate = LocalDate.now();
        baseParam.put("pageSize", pageSize);
        baseParam.put("toCurrency", "SGD");
        baseParam.put("year", currentDate.getYear());
        baseParam.put("month", currentDate.getMonth().getValue());
        TreeMap<String, Object> param = new TreeMap<>(baseParam);
        param.put("pageIndex", pageIndex);
        listExchangeRates4TargetCurrency(param, exchangeRateMap);
        if (!exchangeRateMap.isEmpty()) {
            return exchangeRateMap.get(fromCurrency);
        } else {
            return null;
        }
    }

    /**
     * 获取所有货币对SGD的汇率信息
     *
     * @return 以货币代码为key，汇率为value的Map
     * @throws MessageException 消息异常
     */
    private static int listExchangeRates4TargetCurrency(TreeMap<String, Object> paramMap, Map<String, BigDecimal> exchangeRateMap) throws MessageException {
        try {
            String responseStr = KzzApiInterface.postJsonRequest("/openapi/kzz/pairExchangeRate/search/", JSONObject.toJSONString(paramMap));
            JSONObject jsonObj = JsonUtils.parseObject(responseStr);
            MsgExceptionUtils.checkIsNull(jsonObj, "接口响应内容为空");
            MsgExceptionUtils.failBuild(!"OK".equals(jsonObj.getString("status")), jsonObj.getString("error"));

            JSONArray jsonArray = jsonObj.getJSONArray("results");
            MsgExceptionUtils.failBuild(jsonArray == null || jsonArray.isEmpty(), "接口响应内容为空");

            JSONObject dateObject = jsonArray.getJSONObject(0);
            JSONArray data = dateObject.getJSONArray("data");
            MsgExceptionUtils.failBuild(data == null || data.isEmpty(), "接口响应内容为空");

            // 遍历所有汇率数据，构建成Map
            for (int i = 0; i < data.size(); i++) {
                JSONObject rateObject = data.getJSONObject(i);
                String fromCurrency = rateObject.getString("fromCurrency");
                BigDecimal fquantity = rateObject.getBigDecimal("fquantity");
                BigDecimal tquantity = rateObject.getBigDecimal("tquantity");

                // 验证数据有效性
                if (StringUtils.isNotBlank(fromCurrency) &&
                        fquantity != null &&
                        tquantity != null &&
                        BigDecimal.ZERO.compareTo(fquantity) < 0) {

                    // 计算汇率: tquantity / fquantity，保留6位小数
                    BigDecimal exchangeRate = tquantity.divide(fquantity, 6, RoundingMode.HALF_UP);
                    exchangeRateMap.put(fromCurrency, exchangeRate);
                }
            }
            return dateObject.getInteger("total");
        } catch (Exception e) {
            logger.error("获取汇率信息失败: {}", e.getMessage(), e);
            throw new MessageException("获取汇率数据失败: " + e.getMessage());
        }
    }
}