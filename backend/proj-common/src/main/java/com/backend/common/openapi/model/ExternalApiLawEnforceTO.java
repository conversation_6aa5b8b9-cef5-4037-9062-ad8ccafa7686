package com.backend.common.openapi.model;

import com.backend.common.yunjuapi.model.*;
import com.google.common.collect.Lists;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 案件信息
 */
@Data
public class ExternalApiLawEnforceTO {
    private String caseNo;                            // Case number (50)
    private String documentNo;
    private List<TargetInfo> targetInfo;              // Law enforcement target
    private List<TargetInfo> associatedTargetInfo; // Law enforcement associated target
    private String caseValue;                         // Cash value involved in the case (50)
    private List<TargetInfo> applicantInfo;        // Case applicant
    private String caseType;                          // Subject to enforcement/Dishonest listing/Injunction (20)
    private String currency;                  // Currency code (5)
    private String caseFilingDate;                    // Case filing date (20)
    private String fulfillmentStatus;                 // case fulfillment status (50)
    private String details;                           // Case details (1000)
    private String caseStatus;                        // Case status (open, removed from source) (20)
    private String court;                             // Court (1000)
    private String publicationDate;                   // publish date (20)

    /**
     * 执行目标信息
     */
    @Data
    @NoArgsConstructor
    public static class TargetInfo {
        private String name;     // Script Name (500)
        private String nameEn;   // English Name (1000)

        public TargetInfo(String name, String nameEn) {
            this.name = name;
            this.nameEn = nameEn;
        }
    }

    public static List<ExternalApiLawEnforceTO> build4ShiXin(List<CoutShiXin> sourceList, String isValid) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<ExternalApiLawEnforceTO> resultList = new ArrayList<>();
        for (CoutShiXin coutShiXin : sourceList) {
            ExternalApiLawEnforceTO target = new ExternalApiLawEnforceTO();
            target.setCaseNo(coutShiXin.getAnno());
            target.setDocumentNo(coutShiXin.getExecuteno());
            target.setTargetInfo(covertTargetInfo4ShiXin(coutShiXin.getNameKeyNoCollection()));
            target.setCaseValue(coutShiXin.getAmount());
            target.setApplicantInfo(covertTargetInfo4ShiXin(coutShiXin.getSqrInfo()));
            target.setCaseType("dishonest debtor");
            if (StringUtils.isNotBlank(coutShiXin.getAmount())) {
                target.setCurrency("CNY");
            }
            String formatDateStr = DateUtils.formatDate(DateUtils.toDate(coutShiXin.getLiandate()));
            target.setCaseFilingDate(StringUtils.getValue(formatDateStr, null));
            target.setFulfillmentStatus(coutShiXin.getExecutestatusEn());
            target.setDetails(coutShiXin.getActionremarkEn());
            if ("1".equals(isValid)) {
                target.setCaseStatus("open");
            } else {
                target.setCaseStatus("removed from source");
            }
            target.setCourt(coutShiXin.getExecutegov());
            formatDateStr = DateUtils.formatDate(DateUtils.toDate(coutShiXin.getPublicdate()));
            target.setPublicationDate(StringUtils.getValue(formatDateStr, null));
            resultList.add(target);
        }
        return resultList;
    }

    private static List<ExternalApiLawEnforceTO.TargetInfo> covertTargetInfo4ShiXin(List<CoutShiXinNameKeyNoColl> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<ExternalApiLawEnforceTO.TargetInfo> resultList = new ArrayList<>();
        for (CoutShiXinNameKeyNoColl shiXinNameKeyNoColl : sourceList) {
            ExternalApiLawEnforceTO.TargetInfo targetInfo = new ExternalApiLawEnforceTO.TargetInfo();
            targetInfo.setName(StringUtils.getValue(shiXinNameKeyNoColl.getName(), null));
            targetInfo.setNameEn(StringUtils.getValue(shiXinNameKeyNoColl.getEnglishName(), null));
            resultList.add(targetInfo);
        }
        return resultList;
    }

    public static List<ExternalApiLawEnforceTO> build4ZhiXing(List<CoutZhiXing> sourceList, String isValid) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<ExternalApiLawEnforceTO> resultList = new ArrayList<>();
        for (CoutZhiXing coutZhiXing : sourceList) {
            ExternalApiLawEnforceTO target = new ExternalApiLawEnforceTO();
            target.setCaseNo(coutZhiXing.getAnno());
            String formatDateStr = DateUtils.formatDate(DateUtils.toDate(coutZhiXing.getLiandate()));
            target.setCaseFilingDate(StringUtils.getValue(formatDateStr, null));
            target.setTargetInfo(covertTargetInfo4ZhiXing(coutZhiXing.getNameKeyNoCollection()));
            target.setCaseValue(coutZhiXing.getBiaodi());
            if (StringUtils.isNotBlank(coutZhiXing.getBiaodi())) {
                target.setCurrency("CNY");
            }
            if ("1".equals(isValid)) {
                target.setCaseStatus("open");
            } else {
                target.setCaseStatus("removed from source");
            }
            target.setCaseType("judgment debtor");
            resultList.add(target);
        }
        return resultList;
    }

    private static List<TargetInfo> covertTargetInfo4ZhiXing(List<CoutZhiXingNameKeyNoColl> nameKeyNoCollection) {
        if (CollectionUtils.isEmpty(nameKeyNoCollection)) {
            return null;
        }
        List<TargetInfo> resultList = new ArrayList<>();
        for (CoutZhiXingNameKeyNoColl coutZhiXingNameKeyNoColl : nameKeyNoCollection) {
            TargetInfo targetInfo = new TargetInfo();
            targetInfo.setName(StringUtils.getValue(coutZhiXingNameKeyNoColl.getName(), null));
            targetInfo.setNameEn(StringUtils.getValue(coutZhiXingNameKeyNoColl.getEnglishName(), null));
            resultList.add(targetInfo);
        }
        return resultList;
    }

    public static List<ExternalApiLawEnforceTO> build4HighConsumption(List<PersonSumptuary> sourceList, String isValid) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<ExternalApiLawEnforceTO> resultList = new ArrayList<>();
        for (PersonSumptuary source : sourceList) {
            ExternalApiLawEnforceTO target = new ExternalApiLawEnforceTO();
            target.setCaseNo(source.getCaseNo());
            String formatDateStr = DateUtils.formatDate(DateUtils.toDate(source.getJudgeDate()));
            target.setCaseFilingDate(StringUtils.getValue(formatDateStr, null));
            target.setTargetInfo(Lists.newArrayList(new TargetInfo(source.getXianGaoLingObj(), source.getXianGaoLingObjEnName())));
            target.setAssociatedTargetInfo(Lists.newArrayList(new TargetInfo(source.getRelatedName(), source.getRelatedEnName())));
            target.setCaseValue(source.getAmount());
            if (StringUtils.isNotBlank(source.getAmount())) {
                target.setCurrency("CNY");
            }
            if ("1".equals(isValid)) {
                target.setCaseStatus("open");
            } else {
                target.setCaseStatus("removed from source");
            }
            target.setApplicantInfo(covertApplicant4HighConsumption(source.getSqrInfo()));
            target.setCaseType("consumption ban");
            resultList.add(target);
        }
        return resultList;
    }

    private static List<TargetInfo> covertApplicant4HighConsumption(List<SqrInfo> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<TargetInfo> resultList = new ArrayList<>();
        for (SqrInfo sqrInfo : sourceList) {
            TargetInfo targetInfo = new TargetInfo();
            targetInfo.setName(StringUtils.getValue(sqrInfo.getName(), null));
            targetInfo.setNameEn(StringUtils.getValue(sqrInfo.getEnglishName(), null));
            resultList.add(targetInfo);
        }
        return resultList;
    }

    public static List<ExternalApiLawEnforceTO> build4TravelBan(List<LimitExit> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        List<ExternalApiLawEnforceTO> resultList = new ArrayList<>();
        for (LimitExit source : sourceList) {
            ExternalApiLawEnforceTO target = new ExternalApiLawEnforceTO();
            target.setCaseNo(source.getCaseNo());
            target.setTargetInfo(convertTarget4TravelBan(source.getLimitedPerson()));
            target.setAssociatedTargetInfo(convertTarget4TravelBan(source.getExecutedPerson()));
            target.setCaseValue(source.getExecutedAmount());
            if (StringUtils.isNotBlank(source.getExecutedAmount())) {
                target.setCurrency("CNY");
            }
            String formatDateStr = DateUtils.formatDate(DateUtils.toDate(source.getPublishDate()));
            target.setCaseFilingDate(StringUtils.getValue(formatDateStr, null));
            target.setCaseStatus("open");
            target.setCaseType("travel ban");
            resultList.add(target);
        }
        return resultList;
    }

    private static List<TargetInfo> convertTarget4TravelBan(List<Oper> limitedPerson) {
        if (CollectionUtils.isEmpty(limitedPerson)) {
            return null;
        }
        List<TargetInfo> resultList = new ArrayList<>();
        for (Oper oper : limitedPerson) {
            TargetInfo targetInfo = new TargetInfo();
            targetInfo.setName(StringUtils.getValue(oper.getName(), null));
            targetInfo.setNameEn(StringUtils.getValue(oper.getEnglishName(), null));
            resultList.add(targetInfo);
        }
        return resultList;
    }
}
