package com.backend.common.modules.delivery.service;

import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.api.model.ApiChargeUnitSaveTO;
import com.backend.common.modules.api.service.CommTblCompApiChargeUnitService;
import com.backend.common.modules.api.service.CommTblCompApiOrderService;
import com.backend.common.modules.benefit.service.BenefitBusinessService;
import com.backend.common.modules.delivery.condition.ChargeRecordsCondition;
import com.backend.common.modules.delivery.entity.*;
import com.backend.common.modules.delivery.form.AllocateCompanyUnitForm;
import com.backend.common.modules.delivery.form.ContractDeliveryForm;
import com.backend.common.modules.delivery.form.ContractDeliveryGiveProdUnitForm;
import com.backend.common.modules.delivery.form.ContractDeliveryGiveProdUnitListForm;
import com.backend.common.modules.delivery.model.InvoiceAddressTO;
import com.backend.common.modules.delivery.model.InvoiceBankTO;
import com.backend.common.modules.delivery.model.ChargeRecordsInfoListTO;
import com.backend.common.modules.delivery.model.ContractDetailVO;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.util.OpLogUtils;
import com.backend.common.openapi.KzzApiInterface;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.backend.common.openapi.model.KzzWorkFlowDetailTO;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.SysCompRoleService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.*;
import com.qcc.frame.jee.commons.annotation.DingErrorMsg;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.*;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.form.CompanyAddressUpdateForm;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.service.RedisServUtils;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.stripe.model.PaymentIntent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * added for v1.8.8 KNZT-3324
 * 交付综合service
 *
 * <AUTHOR>
 * @datetime 2024/6/26 10:18
 */
@Service
public class DeliveryBusinessService {
    protected static Logger logger = LoggerFactory.getLogger(DeliveryBusinessService.class);

    @Autowired
    private CommTblReportChargeUnitService reportChargeUnitService;
    @Autowired
    private CommTblContractDeliveryService commTblContractDeliveryService;
    @Autowired
    private CommTblContractDeliveryProdService commTblContractDeliveryProdService;
    @Autowired
    private CommTblContractDeliveryChargeUnitService commTblContractDeliveryChargeUnitService;
    @Autowired
    private CommTblContractDeliveryProdAccService commTblContractDeliveryProdAccService;
    @Autowired
    private SysCompRoleService sysCompRoleService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private UserService userService;
    @Autowired
    private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
    @Autowired
    private SaasLoginService saasLoginService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private CommTblCompReportOrderService reportOrderService;
    @Autowired
    private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
    @Autowired
    private CommTblCompApiChargeUnitService apiChargeUnitService;
    @Autowired
    private CommTblContractDeliveryApiChargeUnitService contractDeliveryApiChargeUnitService;
    @Autowired
    private CommTblCompApiOrderService apiOrderService;
    @Autowired
    private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;
    @Autowired
    private RedisServUtils redisServUtils;
    @Autowired
    private DictService dictService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private BenefitBusinessService benefitBusinessService;
    @Autowired
    private CommCompUserService commCompUserService;
    @Autowired
    private InvoiceBusinessService invoiceBusinessService;
    @Autowired
    private RedisService redisService;

    public ContractDetailVO getContractDetail(String workFlowNo, String companyId) throws MessageException {
        KzzContractDetailTO contractDetail = null;
        String submitName = null;
        Date beginDateFromWorkFlow = null; // added for v2.1.8 fengsw KNZT-6933
        Date endDateFromWorkFlow = null;

        KzzWorkFlowDetailTO workFlowDetail = KzzApiInterface.getWorkFlowDetail(workFlowNo);
        if (Objects.nonNull(workFlowDetail) && workFlowDetail.isPass()) {
            contractDetail = KzzApiInterface.getContractDetail(workFlowDetail.getContractNo());
            submitName = workFlowDetail.getSubmitName();
            beginDateFromWorkFlow = workFlowDetail.getBeginDate();
            endDateFromWorkFlow = workFlowDetail.getEndDate();
        }

        if (Objects.isNull(workFlowDetail) || Objects.isNull(contractDetail)) {
            logger.error("workFlowNo:{} companyId:{} getContractDetail fail", workFlowNo, companyId);
            return null;
        }
        TblContractDelivery baseInfo = DeliveryBusinessService.transferContractBaseFromKzz(contractDetail);
        
        if(Objects.nonNull(beginDateFromWorkFlow)) {
            baseInfo.setEffectTime(beginDateFromWorkFlow);
        }
        if(Objects.nonNull(endDateFromWorkFlow)) {
            baseInfo.setOverTime(endDateFromWorkFlow);
        }

        List<TblContractDeliveryProd> productList = DeliveryBusinessService.transferContractProductFromKzz(contractDetail.getContractProducts());

        // 增加回款方式校验 added for lvcy v2.0.6 KNZT-5499
        boolean hasCreditCharge = productList.stream().anyMatch(k -> BigDecimal.ZERO.compareTo(k.getTotalUnit()) < 0);
        MsgExceptionUtils.failBuild(CompPayTypeEnum.AFTER.getCode().equals(baseInfo.getPayType()) && hasCreditCharge,
                "msg:后付费的合同，不允许对账号进行充值，请核对后重试");

        List<TblContractDelivery> contractDeliveryList = commTblContractDeliveryService.getByCompanyId(companyId);
        boolean hasNotSamePayType = contractDeliveryList.stream().anyMatch(k -> !StringUtils.equals(baseInfo.getPayType(), k.getPayType()));
        MsgExceptionUtils.failBuild(hasNotSamePayType, "msg:账号的回款方式不支持切换，请核对后重试");

        // updated for v1.9.8 KNZT-4538
        List<TblReportChargeUnit> defaultChargeUnits = reportChargeUnitService.listChargeUnitByCompany(Constants.ChargeUnit.DEFAULT_COMPANY_ID);
        List<TblContractDeliveryChargeUnit> contractDeliveryChargeUnitList = DeliveryBusinessService.transferContractChargeUnitFromKzz(contractDetail.getContractProducts(), defaultChargeUnits);
        List<TblReportChargeUnit> chargeUnitList = contractDeliveryChargeUnitList.stream().map(k -> {
            TblReportChargeUnit chargeUnit = new TblReportChargeUnit();
            BeanUtils.copyProperties(k, chargeUnit);
            return chargeUnit;
        }).collect(Collectors.toList());


        // added for v1.9.7 KNZT-4708
        List<TblContractDeliveryApiChargeUnit> apiChargeUnitSaveTOList = DeliveryBusinessService.transferApiChargeUnitFromKzz(contractDetail.getContractProducts());
        Map<String, TblContractDeliveryApiChargeUnit> apiChargeUnitSaveTOMap = apiChargeUnitSaveTOList.stream().collect(Collectors.toMap(TblContractDeliveryApiChargeUnit::getApiType, k -> k, (k1, k2) -> k1));
        List<TblCompApiChargeUnit> commApiChargeUnit = apiChargeUnitService.getByCompanyId(Constants.ChargeUnit.DEFAULT_COMPANY_ID);
        List<TblCompApiChargeUnit> apiChargeUnitList = commApiChargeUnit.stream()
                .filter(k -> apiChargeUnitSaveTOMap.containsKey(k.getApiType()))
                .peek(apiChargeUnit -> {
                    TblContractDeliveryApiChargeUnit saveTO = apiChargeUnitSaveTOMap.get(apiChargeUnit.getApiType());
                    if (Objects.nonNull(saveTO)) {
                        apiChargeUnit.setUnit(saveTO.getUnit());
                        apiChargeUnit.setEnabled(Constants.YES);
                    }
                })
                .collect(Collectors.toList());

        baseInfo.setEnableApi(CollectionUtils.isNotEmpty(apiChargeUnitSaveTOList));

        List<KzzContractDetailTO.Attachment> attachments = contractDetail.getAttachments();

        // 地址信息
        InvoiceAddressTO invoiceAddress = transferAddressFromWorkFlow(workFlowDetail);

        // 银行卡信息
        InvoiceBankTO bankTO = transferBankFromKzzContract(contractDetail);


        // 产品税点计算
        String taxRateStr = ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfigType.TAX_RATE, GlobalAreaEnum.SG.getNameCode(), "0");
        BigDecimal taxRate = NumberUtils.toDec(taxRateStr);
        List<ContractDetailVO.ProdVO> prodVOList = productList.stream()
                .map(product -> {
                    ContractDetailVO.ProdVO prodVO = new ContractDetailVO.ProdVO();
                    BeanUtils.copyProperties(product, prodVO);
                    if (invoiceAddress.isSingapore()) {
                        prodVO.setTaxAmount(prodVO.getTotalAmount().multiply(taxRate));
                        prodVO.setDiscountAmountWithTax(prodVO.getTotalAmount().add(prodVO.getTaxAmount()));
                    } else {
                        prodVO.setTaxAmount(BigDecimal.ZERO);
                        prodVO.setDiscountAmountWithTax(prodVO.getTotalAmount());
                    }
                    return prodVO;
                })
                .collect(Collectors.toList());
        return new ContractDetailVO(baseInfo, prodVOList, attachments, submitName,
                chargeUnitList, apiChargeUnitList, invoiceAddress, bankTO);
    }


    public Company delivery(ContractDeliveryForm form) throws MessageException {
        // region 1.合同信息校验
        form.checkParam();
        KzzContractDetailTO kzzContractDetail = KzzApiInterface.getContractDetail(form.getContractNo());
        MsgExceptionUtils.checkIsNull(kzzContractDetail, "msg:客找找合同不存在", form.getContractNo());
        MsgExceptionUtils.failBuild(kzzContractDetail.judgeInvalid(), "msg:客找找合同尚未审批通过", form.getContractNo());
        KzzWorkFlowDetailTO workFlowDetail = KzzApiInterface.getWorkFlowDetail(form.getWorkFlowNo());
        MsgExceptionUtils.checkIsNull(workFlowDetail, "msg:客找找工单不存在", form.getWorkFlowNo());
        MsgExceptionUtils.failBuild(!workFlowDetail.isPass(), "msg:客找找工单状态不为审批通过", form.getWorkFlowNo());
        MsgExceptionUtils.failBuild(!StringUtils.equals(form.getContractNo(), workFlowDetail.getContractNo()), "msg:客找找工单合同不一致", form.getContractNo());

        TblContractDelivery contractDelivery = DeliveryBusinessService.transferContractBaseFromKzz(kzzContractDetail);
        MsgExceptionUtils.checkIsNull(contractDelivery, "msg:合同不存在", form.getContractNo());
        MsgExceptionUtils.checkIsNull(contractDelivery.getAmountStd(), "msg:缺少合同币别信息", form.getContractNo());
        MsgExceptionUtils.checkIsNull(contractDelivery.getContractBeginDate(), "msg:缺少合同开始日期", form.getContractNo());
        MsgExceptionUtils.checkIsNull(contractDelivery.getContractEndDate(), "msg:缺少合同结束日期", form.getContractNo());

        List<TblContractDelivery> sameContractNoDeliveryList = commTblContractDeliveryService.getByContractNo(form.getContractNo());
        MsgExceptionUtils.failBuild(CompPayTypeEnum.PREPAID.getCode().equals(contractDelivery.getPayType()) && CollectionUtils.isNotEmpty(sameContractNoDeliveryList),
                "msg:该合同已交付过，请检查数据", form.getContractNo());

        List<TblReportChargeUnit> defaultChargeUnits = reportChargeUnitService.listChargeUnitByCompany(Constants.ChargeUnit.DEFAULT_COMPANY_ID);
        List<TblContractDeliveryApiChargeUnit> apiChargeUnitSaveTOList = DeliveryBusinessService.transferApiChargeUnitFromKzz(kzzContractDetail.getContractProducts());
        List<TblContractDeliveryChargeUnit> chargeUnits = DeliveryBusinessService.transferContractChargeUnitFromKzz(kzzContractDetail.getContractProducts(), defaultChargeUnits);
        MsgExceptionUtils.failBuild(CollectionUtils.isEmpty(chargeUnits) && CollectionUtils.isEmpty(apiChargeUnitSaveTOList), "msg:缺少合同产品信息");
        boolean noneEnabledChargeUnit = chargeUnits.stream().noneMatch(chargeUnit -> Constants.YES.equals(chargeUnit.getEnabled()));
        MsgExceptionUtils.failBuild(noneEnabledChargeUnit && CollectionUtils.isEmpty(apiChargeUnitSaveTOList), "msg:合同产品及单元额度,但没有产品对应的服务");
        boolean duplicateChargeUnit = chargeUnits.stream()
                .map(TblContractDeliveryChargeUnit::getReportType)
                .collect(Collectors.groupingBy(type -> type, Collectors.counting()))
                .values()
                .stream()
                .anyMatch(count -> count > 1);
        MsgExceptionUtils.failBuild(duplicateChargeUnit, "msg:合同产品服务重复");


        List<TblContractDeliveryProd> productList = DeliveryBusinessService.transferContractProductFromKzz(kzzContractDetail.getContractProducts());
        MsgExceptionUtils.checkIsNull(productList, "msg:缺少合同产品信息");
        for (TblContractDeliveryProd product : productList) {
            MsgExceptionUtils.checkIsNull(product.getFunctionTableId(), "msg:缺少合同产品信息必要字段");
            MsgExceptionUtils.checkIsNull(product.getTotalUnit(), "msg:缺少合同产品信息必要字段 总单元数量");
            MsgExceptionUtils.checkIsNull(product.getDiscountAmount(), "msg:缺少合同产品信息必要字段 折后单价");
            MsgExceptionUtils.checkIsNull(product.getDiscountRate(), "msg:缺少合同产品信息必要字段 折扣率");
        }


        // 合同日期相关校验
        LocalDate curDate = LocalDate.now();
        LocalDate beginDate = DateUtils.toLocalDate(form.getBeginDate());
        LocalDate endDate = DateUtils.toLocalDate(form.getEndDate());
        LocalDate contractBeginDate = DateUtils.toLocalDate(contractDelivery.getContractBeginDate());

//        MsgExceptionUtils.failBuild(beginDate.isBefore(curDate), "msg:交付开始日期不能早于今天");
        MsgExceptionUtils.failBuild(beginDate.isBefore(contractBeginDate), "msg:交付开始日期不能早于合同开始日期");
        MsgExceptionUtils.failBuild(beginDate.isAfter(endDate), "msg:交付开始日期不能晚于交付结束日期");
        MsgExceptionUtils.failBuild(curDate.isAfter(endDate), "msg:当前日期不能晚于交付结束日期");

        // 回款方式校验 added for lvcy v2.0.6 KNZT-5499
        boolean hasCreditCharge = productList.stream().anyMatch(k -> BigDecimal.ZERO.compareTo(k.getTotalUnit()) < 0);
        MsgExceptionUtils.failBuild(CompPayTypeEnum.AFTER.getCode().equals(contractDelivery.getPayType()) && hasCreditCharge,
                "msg:后付费的合同，不允许对账号进行充值，请核对后重试");

        // 后付费合同校验
        boolean hasEffectAfterContract = commTblContractDeliveryService.getByCompanyId(form.getCompanyId()).stream()
                .anyMatch(k -> DeliveryContracStatusEnum.EFFECT.getCode().equals(k.getStatus())
                        && CompPayTypeEnum.AFTER.getCode().equals(k.getPayType()));
        MsgExceptionUtils.failBuild(hasEffectAfterContract, "msg:该账号存在已生效的合同，请核对后重试");


        // 校验处理分配额度入参
        List<AllocateCompanyUnitForm> allocationList = checkAndCorrectAllocateParam(form, productList);
        // endregion

        for (AllocateCompanyUnitForm allocation : allocationList) {
            // 2.公司数据校验
            String companyId = allocation.getCompanyId();
            Company company = commSysCompanyService.get(companyId);
            MsgExceptionUtils.checkIsNull(company, "公司不存在", companyId);
            User user = userService.get(company.getMainUserId());
            MsgExceptionUtils.checkIsNull(user, "msg:主账号不存在");
            if (Boolean.TRUE.equals(form.getTrialToSign())) {
                MsgExceptionUtils.failBuild(!CompTypeEnum.TRIAL.getCode().equals(company.getType()), "msg:只允许试用账户转成签约账户");
            } else {
                MsgExceptionUtils.failBuild(!Objects.equals(CompTypeEnum.SIGN.getCode(), company.getType()), "msg:只允许签约账户交付");
            }
            // 校验其他交付记录
            List<TblContractDelivery> otherContractDeliveryList = commTblContractDeliveryService.getByCompanyId(companyId);
            if (CollectionUtils.isNotEmpty(otherContractDeliveryList)) {
                for (TblContractDelivery otherContractDelivery : otherContractDeliveryList) {
                    LocalDate otherBeginDate = DateUtils.toLocalDate(otherContractDelivery.getBeginDate());
                    LocalDate otherEndDate = DateUtils.toLocalDate(otherContractDelivery.getEndDate());
                    MsgExceptionUtils.failBuild(beginDate.isBefore(otherBeginDate), "msg:交付开始日期不能早于其他交付记录的开始日期");
                    MsgExceptionUtils.failBuild(endDate.isBefore(otherEndDate), "msg:交付结束日期不能早于其他交付记录的结束日期");
                }
            }
            // 回款方式校验 added for lvcy v2.0.6 KNZT-5499
            boolean hasNotSamePayType = otherContractDeliveryList.stream().anyMatch(k -> !StringUtils.equals(contractDelivery.getPayType(), k.getPayType()));
            MsgExceptionUtils.failBuild(hasNotSamePayType, "msg:账号的回款方式不支持切换，请核对后重试");

            // region 3.数据处理模块
            // 试用转签约，处理数据
            if (Boolean.TRUE.equals(form.getTrialToSign())) {
                handleTrialToSignCompany(companyId);
            }

            // 交付时需要保证 主账号处于有效状态
            if (Constants.Sys.USER_STATUS_INACTIVE.equals(user.getStatus())) {
                /* updated for v2.0.4 chenbl KNZT-5442
                user.setStatus(Constants.Sys.USER_STATUS_ACTIVE);
                // 首次试用充值时 账号邮件发送 告知客户账号、密码
                String password = UserService.encryptWithMd5(user.getLoginName());
                user.setPassword(UserService.entryptPassword(password));
                userService.save(user);
                commSysCompanyService.sendNotification(company, password);*/
                commSysCompanyService.sendActivateUserEmail4MainUser(company, true);//updated for v2.0.5 fengsw KNZT-5602 正式账号激活邮件需要增加密送人
            }

            // 保存交付合同基本信息
            TblContractDelivery insertContractDelivery = new TblContractDelivery();
            BeanUtils.copyProperties(contractDelivery, insertContractDelivery);
            insertContractDelivery.setStatus(DeliveryContracStatusEnum.PENDING.getCode());
            insertContractDelivery.setCompanyId(companyId);
            insertContractDelivery.setBeginDate(form.getBeginDate());
            insertContractDelivery.setEndDate(form.getEndDate());
            insertContractDelivery.setContractNo(form.getContractNo());
            insertContractDelivery.setWorkOrderNo(form.getWorkFlowNo());
            insertContractDelivery.setActualDiscountAmount(productList.get(0).getDiscountAmount());
            insertContractDelivery.setIsNewRecord(false);
            commTblContractDeliveryService.save(insertContractDelivery);
            String contractDeliveryId = insertContractDelivery.getId();

            // 保存产品信息
            productList = productList.stream().peek(product -> {
                product.setContractDeliveryId(contractDeliveryId);
                product.setTotalUnit(allocation.getAllocateUnit());
                product.setTotalAmount(allocation.getAllocateAmount());
            }).collect(Collectors.toList());
            commTblContractDeliveryProdService.batchInsert(productList);

            // 保存服务信息
            for (TblContractDeliveryChargeUnit chargeUnit : chargeUnits) {
                chargeUnit.setContractDeliveryId(contractDeliveryId);
                chargeUnit.setCompanyId(companyId);
            }
            commTblContractDeliveryChargeUnitService.batchInsert(chargeUnits);

            // 保存api服务信息 added for v1.9.7 KNZT-4708
            for (TblContractDeliveryApiChargeUnit contractDeliveryApiChargeUnit : apiChargeUnitSaveTOList) {
                contractDeliveryApiChargeUnit.setContractDeliveryId(contractDeliveryId);
                contractDeliveryApiChargeUnit.setCompanyId(companyId);
            }
            contractDeliveryApiChargeUnitService.batchInsert(apiChargeUnitSaveTOList);

            // 当前时间大于等于合同开始时间, 合同进行生效逻辑
            if (curDate.isAfter(beginDate) || curDate.isEqual(beginDate)) {
                contractEffect(insertContractDelivery);
            }
            // endregion
            // 保存跟踪记录 added for v1.9.0 KNZT-4093
            commCrmCompTrackingRecordService.save(companyId, form.getTrackingContent(), CompTrackingRecordSourceEnum.SIGN_DELIVERY.getCode());
            
            // 交付流程保存用户填写的地址信息
            CompanyAddressUpdateForm companyAddressUpdateForm = new CompanyAddressUpdateForm();
            companyAddressUpdateForm.setCompanyId(companyId);
            GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(workFlowDetail.getCountry());
            MsgExceptionUtils.checkIsNull(areaEnum, "msg:开票地址国家信息错误,无法匹配到对应的二字国家码");
            companyAddressUpdateForm.setCountry(areaEnum.getNameCode());
            companyAddressUpdateForm.setProvince(workFlowDetail.getProvince());
            companyAddressUpdateForm.setCity(workFlowDetail.getCity());
            companyAddressUpdateForm.setAddressLine1(workFlowDetail.getAddressLine1());
            companyAddressUpdateForm.setAddressLine2(workFlowDetail.getAddressLine2());
            companyAddressUpdateForm.setZipCode(workFlowDetail.getZipCode());
            commCompUserService.doUpdateCompanyAddress(companyAddressUpdateForm);

            // 保存银行卡信息
            InvoiceBankTO bankTO = transferBankFromKzzContract(kzzContractDetail);
            commSysCompanyService.saveBankInfo(companyId, bankTO);

        }

        return commSysCompanyService.get(allocationList.get(0).getCompanyId());
    }

    /**
     * 校验处理分配额度入参
     * added for lvcy v1.9.9 KNZT-5123
     *
     * @param form
     * @param productList
     * @return List<AllocateCompanyUnitForm>
     */
    private static List<AllocateCompanyUnitForm> checkAndCorrectAllocateParam(ContractDeliveryForm form, List<TblContractDeliveryProd> productList) throws MessageException {
        List<AllocateCompanyUnitForm> allocateList = form.getAllocationList();
        BigDecimal prodTotalUnit = productList.get(0).getTotalUnit();
        BigDecimal prodTotalAmount = productList.get(0).getTotalAmount();
        if (CollectionUtils.isEmpty(form.getAllocationList())) {
            AllocateCompanyUnitForm allocateCompanyUnitForm = new AllocateCompanyUnitForm();
            allocateCompanyUnitForm.setCompanyId(form.getCompanyId());
            allocateCompanyUnitForm.setAllocateUnit(prodTotalUnit);
            allocateList = Lists.newArrayList(allocateCompanyUnitForm);
            form.setAllocationList(allocateList);
        }
        BigDecimal totalUnit = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (int i = 0; i < allocateList.size(); i++) {
            AllocateCompanyUnitForm allocation = allocateList.get(i);
            BigDecimal allocateUnit = allocation.getAllocateUnit().setScale(2, RoundingMode.HALF_UP);
            totalUnit = totalUnit.add(allocateUnit);
            // 计算对应的分配金额
            BigDecimal allocateAmount;
            if (i == allocateList.size() - 1) {
                allocateAmount = prodTotalAmount.subtract(totalAmount);
            } else {
                BigDecimal allocateRate = allocateUnit.divide(prodTotalUnit, 6, RoundingMode.HALF_UP);
                allocateAmount = prodTotalAmount.multiply(allocateRate).setScale(2, RoundingMode.HALF_UP);
            }
            totalAmount = totalAmount.add(allocateAmount);

            allocation.setAllocateAmount(allocateAmount);
            allocation.setAllocateUnit(allocateUnit);
        }
        MsgExceptionUtils.failBuild(totalUnit.compareTo(prodTotalUnit) != 0, "msg:分配额度与产品总单元数量不一致");
        MsgExceptionUtils.failBuild(totalAmount.compareTo(prodTotalAmount) != 0, "msg:分配金额与产品总金额不一致");
        return allocateList;
    }

    /*public void transferRemainUnit(String outProdAccId, String inProdAccId) throws MessageException {
        // 业务校验
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(outProdAccId, inProdAccId), "err.param.invalid");
        MsgExceptionUtils.checkIsNull(inProdAccId, "err.param.invalid");
        MsgExceptionUtils.failBuild(StringUtils.equals(outProdAccId, inProdAccId),
                "msg:结转出的合同产品账户和结转入的合同产品账户不能是同一账户");
        TblContractDeliveryProdAcc outProdAcc = commTblContractDeliveryProdAccService.lockById(outProdAccId);
        MsgExceptionUtils.checkIsNull(outProdAcc, "msg:结转出的合同产品账户信息不存在");
        BigDecimal transferUnit = outProdAcc.calTotalRemainUnit();
        MsgExceptionUtils.failBuild(transferUnit.compareTo(BigDecimal.ZERO) <= 0,
                "msg:结转出的合同产品账户额度不能为0");
        MsgExceptionUtils.failBuild(!DeliveryContracProdAccTypeEnum.PREPAID.getCode().equals(outProdAcc.getType()),
                "msg:结转出的合同产品账户类型必须是预付费");
        TblContractDeliveryProdAcc inProdAcc = commTblContractDeliveryProdAccService.lockById(inProdAccId);
        MsgExceptionUtils.checkIsNull(inProdAcc, "msg:结转入的合同产品账户信息不存在");
        MsgExceptionUtils.failBuild(outProdAcc.getContractDeliveryId().equals(inProdAcc.getContractDeliveryId()),
                "msg:结转出的合同产品账户和结转入的合同产品账户不能是同一合同");
        MsgExceptionUtils.failBuild(!outProdAcc.getCompanyId().equals(inProdAcc.getCompanyId()),
                "msg:结转出的合同产品账户和结转入的合同产品账户必须是同一公司");
        MsgExceptionUtils.failBuild(!StringUtils.equals(outProdAcc.getFunctionTableId(), inProdAcc.getFunctionTableId()),
                "msg:结转出的合同产品账户和结转入的合同产品账户必须是同一产品");
        MsgExceptionUtils.failBuild(!DeliveryContracProdAccTypeEnum.PREPAID.getCode().equals(inProdAcc.getType()),
                "msg:结转入的合同产品账户类型必须是预付费");
        TblContractDelivery outContractDelivery = commTblContractDeliveryService.get(outProdAcc.getContractDeliveryId());
        MsgExceptionUtils.failBuild(!DeliveryContracStatusEnum.OVERDUE.getCode().equals(outContractDelivery.getStatus()),
                "msg:只有过期合同的产品账户才能做结转");
        TblContractDelivery inContractDelivery = commTblContractDeliveryService.get(inProdAcc.getContractDeliveryId());
        MsgExceptionUtils.failBuild(DeliveryContracStatusEnum.OVERDUE.getCode().equals(inContractDelivery.getStatus()),
                "msg:只能结转到不处于终态的合同产品账户");
        TblContractDeliveryProd inProd = commTblContractDeliveryProdService.getByContractDeliveryIdAndFuncTblId(inProdAcc.getContractDeliveryId(), inProdAcc.getFunctionTableId());
        MsgExceptionUtils.checkIsNull(inProd, "msg:结转入合同产品信息不存在");

        // 结转出的合同产品账户，生成结转出的流水
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction
                .init(outProdAcc.getCompanyId(), TransactionTypeEnum.TRANSFER_OUT.getCode())
                .injectRelId(inProdAcc.getId())
                .injectChangedUnit(transferUnit);
        transactionBusinessService.saveTransactionAndChangeAccUnit(transaction, outProdAcc);

        // 结转入的合同产品账户，增加额度，调整单价、折扣率
        BigDecimal afterTotalUnit = inProdAcc.getTotalUnit().add(transferUnit);
        logger.info("结转入prodAccId:{}, totalUnit {} -> {}", inProdAccId, inProdAcc.getTotalUnit(), afterTotalUnit);
        inProdAcc.setTotalUnit(afterTotalUnit);
        inProdAcc.setLastTransferInUnit(inProdAcc.getLastTransferInUnit().add(transferUnit));
        // 计算实际折扣金额 = 产品总金额 / 转入后的额度 （避免误差过大，单价保留四位小数）
        BigDecimal totalAmount = inProd.getTotalAmount();
        BigDecimal actualDiscountAmount = BigDecimal.ZERO.compareTo(afterTotalUnit) != 0 ?
                totalAmount.divide(afterTotalUnit, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        logger.info("结转入prodAccId:{}, actualDiscountAmount {} -> {}", inProdAccId, inProdAcc.getActualDiscountAmount(), actualDiscountAmount);
        inProdAcc.setActualDiscountAmount(actualDiscountAmount);
        // 计算实际折扣率 = 实际折扣金额 / 原单价
        BigDecimal oriAmount = inProd.getOriAmount();
        BigDecimal actualDiscountRate = NumberUtils.divide4Dig(actualDiscountAmount, oriAmount).multiply(BigDecimal.valueOf(100));
        logger.info("结转入prodAccId:{}, actualDiscountRate {} -> {}", inProdAccId, inProdAcc.getActualDiscountRate(), actualDiscountRate);
        inProdAcc.setActualDiscountRate(actualDiscountRate);
        commTblContractDeliveryProdAccService.save(inProdAcc);
        // 触发该笔流水的月账计算
        transactionBusinessService.processProdAccTransactionDaily(outProdAcc, LocalDate.now());
        transactionBusinessService.processProdAccTransactionMonthly(outProdAcc, YearMonth.now());
    }*/

    public void confirmRemainUnit(String prodAccId) throws MessageException {
        // 业务校验
        MsgExceptionUtils.checkIsNull(prodAccId, "err.param.invalid");
        TblContractDeliveryProdAcc prodAcc = commTblContractDeliveryProdAccService.lockById(prodAccId);
        MsgExceptionUtils.checkIsNull(prodAcc, "msg:结转出的合同产品账户信息不存在");
        BigDecimal remainUnit = prodAcc.calTotalRemainUnit();
        MsgExceptionUtils.failBuild(remainUnit.compareTo(BigDecimal.ZERO) <= 0,
                "msg:合同产品账户额度不能为0");
        MsgExceptionUtils.failBuild(!DeliveryContracProdAccTypeEnum.PREPAID.getCode().equals(prodAcc.getType()),
                "msg:结转出的合同产品账户类型必须是预付费");
        TblContractDelivery contractDelivery = commTblContractDeliveryService.get(prodAcc.getContractDeliveryId());
        MsgExceptionUtils.failBuild(!DeliveryContracStatusEnum.OVERDUE.getCode().equals(contractDelivery.getStatus()),
                "msg:结转出的合同产品账户类型必须是预付费");
        MsgExceptionUtils.failBuild(CompPayTypeEnum.AFTER.getCode().equals(contractDelivery.getPayType()), "msg:后付费公司不能确认收入");
        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.readOnlyGetCompFuncCount(prodAcc.getCompanyId());
        MsgExceptionUtils.checkIsNull(funcCount, "msg:公司额度信息不存在");

        // 确认剩余所有收入
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction
                .init(prodAcc.getCompanyId(), TransactionTypeEnum.CONFIRM.getCode(), funcCount.calRemainCount())
                .injectChangedUnit(remainUnit);
        transactionBusinessService.saveTransactionAndChangeAccUnit(transaction, prodAcc);

        // 触发该笔流水的月账计算
//        transactionBusinessService.processProdAccTransactionDaily(prodAcc, LocalDate.now());
        transactionBusinessService.processContractTransactionMonthly(contractDelivery.getContractNo(), contractDelivery.getActualDiscountAmount(), YearMonth.now());
    }

    // updated for v1.9.0 KNZT-4093
    public void giveProdUnit(ContractDeliveryGiveProdUnitListForm listForm) throws MessageException {
        MsgExceptionUtils.checkIsNull(listForm, "err.param.invalid");
        List<ContractDeliveryGiveProdUnitForm> giveProdUnitFormList = listForm.getGiveProdUnitFormList();
        for (ContractDeliveryGiveProdUnitForm form : giveProdUnitFormList) {
            this.giveProdUnit(form);
        }
        // 保存跟进记录 added for v1.9.0 KNZT-4093
        commCrmCompTrackingRecordService.save(giveProdUnitFormList.get(0).getCompanyId(),
                listForm.getTrackingContent(),
                CompTrackingRecordSourceEnum.SIGN_GIVEN.getCode());
    }

    // updated for v1.9.0 KNZT-4093
    public void giveProdUnit(ContractDeliveryGiveProdUnitForm form) throws MessageException {
        // 业务校验
        String companyId = form.getCompanyId();
        MsgExceptionUtils.checkIsNull(form.getBeginDate(), "err.param.invalid");
        MsgExceptionUtils.checkIsNull(form.getEndDate(), "err.param.invalid");
        MsgExceptionUtils.failBuild(Objects.isNull(form.getTotalUnit()) || form.getTotalUnit().compareTo(BigDecimal. ZERO) <= 0, "err.param.invalid");
        LocalDate curDate = LocalDate.now();
        LocalDate beginDate = DateUtils.toLocalDate(form.getBeginDate());
        LocalDate endDate = DateUtils.toLocalDate(form.getEndDate());
        MsgExceptionUtils.failBuild(beginDate.isBefore(curDate), "msg:开始日期不能早于今天");
        MsgExceptionUtils.failBuild(beginDate.isAfter(endDate), "msg:开始日期不能晚于交付结束日期");
        MsgExceptionUtils.failBuild(curDate.isAfter(endDate), "msg:当前日期不能晚于交付结束日期");
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");
        MsgExceptionUtils.failBuild(CompTypeEnum.TRIAL.getCode().equals(company.getType()), "msg:公司类型不能是试用客户");
        MsgExceptionUtils.failBuild(CompPayTypeEnum.AFTER.getCode().equals(company.getPayType()), "msg:后付费公司不能赠送产品");
        // 赠送产品账户
        String remark = StringUtils.left(form.getRemark(), 500);
        TblContractDeliveryProdAcc givenAcc = TblContractDeliveryProdAcc.build4NoDelivery(
                DeliveryContracProdAccTypeEnum.GIVEN.getCode(),
                companyId, form.getTotalUnit(),
                form.getBeginDate(), form.getEndDate(), remark);
        commTblContractDeliveryProdAccService.save(givenAcc);
        logger.info("giveProdUnit 创建赠送产品账户, givenAcc:{}", JSONObject.toJSONString(givenAcc));


        // 同步额度 updated for v1.9.8 KNZT-4538
        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(companyId);

        // 生成充值流水
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.buildTopUp(companyId, givenAcc, funcCount.calRemainCount());
        contractDeliveryTransactionService.save(transaction);
        logger.info("giveProdUnit 生成充值流水, companyId:{}, prodAccId:{}, changedUnit:{}", transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), transaction.getChangedUnit());
    }


    @DingErrorMsg(referenceNoPrefix = "contractDelivery-")
    public void contractEffect(TblContractDelivery contractDelivery) throws MessageException {
        MsgExceptionUtils.checkIsNull(contractDelivery, "合同交付信息不存在");
        String contractDeliveryId = contractDelivery.getId();
        logger.info("ContractDelivery effect begin, contractDeliveryId:{}", contractDeliveryId);
        LocalDate curDate = LocalDate.now();
        LocalDate beginDate = DateUtils.toLocalDate(contractDelivery.getBeginDate());
        MsgExceptionUtils.failBuild(curDate.isBefore(beginDate), "合同未到生效时间");
        String companyId = contractDelivery.getCompanyId();
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "公司信息不存在");
        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount, "缺少额度记录");


        // 流转合同状态
        contractDelivery.setStatus(DeliveryContracStatusEnum.EFFECT.getCode());
        contractDelivery.setEffectTime(DateUtils.getCurrentDate());
        commTblContractDeliveryService.save(contractDelivery);

        // 切换价格
        List<TblContractDeliveryChargeUnit> chargeUnitList = commTblContractDeliveryChargeUnitService.getByContractDeliveryId(companyId, contractDeliveryId);
        List<CompChargeUnitTO> compChargeUnitTOList = chargeUnitList.stream().map(k -> {
            CompChargeUnitTO compChargeUnitTO = new CompChargeUnitTO();
            BeanUtils.copyProperties(k, compChargeUnitTO);
            compChargeUnitTO.setContractDeliveryId(contractDeliveryId);
            return compChargeUnitTO;
        }).collect(Collectors.toList());
        boolean changedChargeUnit = reportChargeUnitService.saveOrUpdateSysCompChargeUnitList(companyId, compChargeUnitTOList);

        // 切换api价格 added for v1.9.7 KNZT-4708
        List<TblContractDeliveryApiChargeUnit> contractDeliveryApiChargeUnitList = contractDeliveryApiChargeUnitService.getByContractDeliveryId(contractDeliveryId);
        List<ApiChargeUnitSaveTO> apiChargeUnitSaveTOList = ApiChargeUnitSaveTO.build(contractDeliveryApiChargeUnitList);
        apiChargeUnitService.batchSave(companyId, contractDeliveryId, apiChargeUnitSaveTOList);
        // added for v1.9.7 KNZT-4910 是否开启api 存在变更的话，也需要踢出用户
        boolean enableApi = CollectionUtils.isNotEmpty(apiChargeUnitSaveTOList);
        boolean enableApiChanged = !Objects.equals(company.getEnableApi(), enableApi);
        company.setEnableApi(enableApi);
        // added for v2.2.8 fengsw KNZT-7656 生成mock key、secretKey
        if (enableApi) {
            commSysCompanyService.generateApiMockKeyAndSecretKey(companyId);
        }
        company.setPayType(contractDelivery.getPayType());
        if (StringUtils.isBlank(company.getSecretKey())) {
            company.setSecretKey(IdGenUtil.generateSecretKey());
        }
        commSysCompanyService.save(company);

        // 保存账户信息
        TblContractDeliveryProd prod = commTblContractDeliveryProdService.getFuncProd(contractDeliveryId);
        MsgExceptionUtils.checkIsNull(prod, "交付产品信息不存在");
        if (CompPayTypeEnum.PREPAID.getCode().equals(contractDelivery.getPayType())) {
            TblContractDeliveryProdAcc acc = TblContractDeliveryProdAcc.buildFromProd(
                    DeliveryContracProdAccTypeEnum.PREPAID.getCode(), prod, contractDelivery, companyId);
            commTblContractDeliveryProdAccService.save(acc);
            // 处理额度信息 updated for v1.9.8 KNZT-4538
            BigDecimal afterTotalCount = funcCount.getTotalCount().add(prod.getTotalUnit());
            funcCount = commSysCompInfoFuncCountService.updateFuncTotalCount(funcCount, afterTotalCount);
            // 创建充值流水
            TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.buildTopUp(companyId, acc, funcCount.calRemainCount());
            contractDeliveryTransactionService.save(transaction);
            logger.info("contractEffect 生成充值流水, companyId:{}, prodAccId:{}, changedUnit:{}", transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), transaction.getChangedUnit());
        } else if (CompPayTypeEnum.AFTER.getCode().equals(contractDelivery.getPayType())) {
            TblContractDeliveryProdAcc acc = TblContractDeliveryProdAcc.buildFromProd(
                    DeliveryContracProdAccTypeEnum.AFTER_INF.getCode(), prod, contractDelivery, companyId);
            commTblContractDeliveryProdAccService.save(acc);
            // 处理额度信息
            BigDecimal afterTotalCount = funcCount.getTotalCount().add(Constants.MAX_UNIT);
            commSysCompInfoFuncCountService.updateFuncTotalCount(funcCount, afterTotalCount);
        }

        // 角色；计费模式；存储模式；修改时，需要踢出用户
        if (changedChargeUnit || enableApiChanged) {
            saasLoginService.logoutUserByCompanyId(company.getId(), company.getName());
        }

        // 增加发送邮件延时任务
        redisService.addDelayedCommTask(CommDelayedTaskTypeEnum.DELIVERY_EMAIL, company.getId(), 2 * 60 * 60);
        logger.info("ContractDelivery effect end, contractDeliveryId:{}", contractDeliveryId);
    }

    public void contractExpiry(TblContractDelivery contractDelivery) throws MessageException {
        String companyId = contractDelivery.getCompanyId();
        // 流转合同状态
        contractDelivery.setStatus(DeliveryContracStatusEnum.OVERDUE.getCode());
        contractDelivery.setOverTime(DateUtils.getCurrentDate());
        commTblContractDeliveryService.save(contractDelivery);
        if (CompPayTypeEnum.AFTER.getCode().equals(contractDelivery.getPayType())) {
            Company company = commSysCompanyService.get(companyId);
            MsgExceptionUtils.checkIsNull(company);
            BigDecimal taxRate = commCompUserService.getTaxRate(companyId);
            BigDecimal exchangeRate = KzzApiInterface.getExchangeRate4TargetCurrencyToSGD(contractDelivery.getAmountStd());
            TblCompStatementMonthly invoice = invoiceBusinessService.saveOrPreviewInvoice4After(contractDelivery, company, YearMonth.now(), taxRate, exchangeRate, false);
            logger.info("ContractDelivery expiry generate invoice success, companyId:{}, amountWithoutTax:{}",
                    companyId, invoice.getAmountUsed());
            SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(companyId);
            logger.info("ContractDelivery expiry update funcCount success, companyId:{}, totalCount:{}, consumedCount:{}",
                    companyId, funcCount.getTotalCount(), funcCount.getConsumedCount());
        }
    }


    public static TblContractDelivery transferContractBaseFromKzz(KzzContractDetailTO contractDetail) {
        if (Objects.isNull(contractDetail)) {
            return null;
        }
        TblContractDelivery contractDelivery = new TblContractDelivery();
        contractDelivery.setContractBeginDate(contractDetail.getBeginDate());
        contractDelivery.setContractEndDate(contractDetail.getExpiredDate());
        contractDelivery.setContractNo(contractDetail.getCode());
        contractDelivery.setCustomerName(contractDetail.getCustomerName());
        contractDelivery.setAmountStd(contractDetail.getAbbreviation());
        if (Objects.nonNull(contractDelivery.getContractBeginDate()) && Objects.nonNull(contractDelivery.getContractEndDate())) {
            int contractTerm = calculateServicePeriods(contractDelivery.getContractBeginDate(), contractDelivery.getContractEndDate());
            contractDelivery.setContractTerm(contractTerm);
        }
        String payTypeCode = CompPayTypeEnum.getCodeByDescCn(contractDetail.getPayType());
        contractDelivery.setPayType(payTypeCode);
        return contractDelivery;
    }

    public static List<TblContractDeliveryProd> transferContractProductFromKzz(List<KzzContractDetailTO.ContractProduct> contractProductList) {
        if (CollectionUtils.isEmpty(contractProductList)) {
            return Lists.newArrayList();
        }
        Map<String, String> configMap = ConfigUtils.getConfigMap("kzz_match_func");
        List<TblContractDeliveryProd> prodList = contractProductList.stream()
                .filter(k -> configMap.containsKey(k.getProductCode()))
                .map(contractProduct -> {
                    TblContractDeliveryProd deliveryProd = new TblContractDeliveryProd();
                    deliveryProd.setProdName(contractProduct.getProductName());
                    BigDecimal totalUnit = Objects.isNull(contractProduct.getQuantity()) ? BigDecimal.ZERO : BigDecimal.valueOf(contractProduct.getQuantity());
                    deliveryProd.setTotalUnit(totalUnit);
                    deliveryProd.setTotalAmount(contractProduct.getTotalPrice());
                    deliveryProd.setOriAmount(contractProduct.getPrice());
                    deliveryProd.setDiscountRate(contractProduct.getDiscountRate());
                    deliveryProd.setDiscountAmount(contractProduct.getDiscountPrice());
                    deliveryProd.setFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
                    if (StringUtils.equalsAny(contractProduct.getProductCode(), "KYC001", "KYC002")) {
                        deliveryProd.setProdName("国际业务企业查询充值");
                    }
                    return deliveryProd;
                })
                .collect(Collectors.toList());
        // 兼容流程中合同仍然有境内境外产品的情况，作合并处理
        if (CollectionUtils.isNotEmpty(prodList) && prodList.size() > 1) {
            TblContractDeliveryProd prod = prodList.get(0);
            prodList.stream().map(TblContractDeliveryProd::getTotalUnit).reduce(BigDecimal::add).ifPresent(prod::setTotalUnit);
            prodList.stream().map(TblContractDeliveryProd::getTotalAmount).reduce(BigDecimal::add).ifPresent(prod::setTotalAmount);
            return Lists.newArrayList(prod);
        } else {
            return prodList;
        }
    }

    /**
     * 创建充值合同
     * added for lvcy v2.1.7 KNZT-6487
     *
     * @param paymentIntent
     * @throws MessageException
     */
    public TblContractDeliveryProdAcc createTopUpContract(PaymentIntent paymentIntent) throws MessageException {
        logger.info("createTopUpContract 创建充值合同, paymentIntent:{}", JSONObject.toJSONString(paymentIntent));
        Map<String, String> metadata = paymentIntent.getMetadata();
        BigDecimal paymentAmount = BigDecimal.valueOf(paymentIntent.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        String amountWithoutTaxStr = metadata.getOrDefault(Constants.StripeParam.AMOUNT_WITHOUT_TAX, paymentAmount.toString());
        String topUpNo = metadata.getOrDefault(Constants.StripeParam.TOP_UP_NO, commTblContractDeliveryService.generateTopUpNo());
        String prodAccType = metadata.getOrDefault(Constants.StripeParam.PROD_ACC_TYPE, DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode());
        String creditCountStr = metadata.getOrDefault(Constants.StripeParam.CREDIT_COUNT, amountWithoutTaxStr);  // 如果订单没有credits，优先采用税前支付金额，再没有采用税后支付金额
        String companyId = metadata.get(Constants.StripeParam.COMPANY_ID);
        String yearStr = metadata.getOrDefault(Constants.StripeParam.YEARS, "1");
        String taxRateStr = metadata.getOrDefault(Constants.StripeParam.TAX_RATE, "0");

        BigDecimal creditCount = new BigDecimal(creditCountStr);
        int years = NumberUtils.getInt(yearStr, 1);
        BigDecimal amountWithoutTax = new BigDecimal(amountWithoutTaxStr);
        BigDecimal taxRate = new BigDecimal(taxRateStr);

        // 创建合同
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "公司信息不存在");
        TblContractDelivery contractDelivery = new TblContractDelivery();
        contractDelivery.setCompanyId(companyId);
        contractDelivery.setContractNo(topUpNo);
        contractDelivery.setCustomerName(company.getName());
        contractDelivery.setContractTerm(years * 12);
        Date curDate = DateUtils.getCurDate();
        contractDelivery.setBeginDate(curDate);
        contractDelivery.setEffectTime(curDate);
        contractDelivery.setContractBeginDate(curDate);
        Date endDate = DateUtils.addDays(curDate, years * 365);
        contractDelivery.setEndDate(endDate);
        contractDelivery.setContractEndDate(endDate);
        contractDelivery.setStatus(DeliveryContracStatusEnum.EFFECT.getCode());
        contractDelivery.setPayType(CompPayTypeEnum.REAL_TIME.getCode());
        contractDelivery.setAmountStd(AmtStdEnum.USD.getCode());
        BigDecimal actualDiscountAmount = NumberUtils.divide4Dig(amountWithoutTax, creditCount);
        contractDelivery.setActualDiscountAmount(actualDiscountAmount);
        contractDelivery.setEnableApi(false);
        contractDelivery.setPayRelId(paymentIntent.getId());
        commTblContractDeliveryService.save(contractDelivery);

        // 创建产品
        TblContractDeliveryProd prod = new TblContractDeliveryProd();
        prod.setCompanyId(companyId);
        prod.setContractDeliveryId(contractDelivery.getId());
        prod.setFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
        prod.setDiscountRate(BigDecimal.valueOf(100));
        prod.setOriAmount(actualDiscountAmount);
        prod.setDiscountAmount(actualDiscountAmount);
        prod.setProdName(prodAccType);
        prod.setTotalUnit(creditCount);
        prod.setTotalAmount(amountWithoutTax);
        commTblContractDeliveryProdService.save(prod);

        // 创建产额度账户
        TblContractDeliveryProdAcc prodAcc = TblContractDeliveryProdAcc.buildFromProd(prodAccType, prod, contractDelivery, companyId);
        commTblContractDeliveryProdAccService.save(prodAcc);

        // 同步额度
        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.updateFuncCountByValidProdAcc(companyId);
        logger.info("createTopUpContract 同步额度, companyId:{}", companyId);

        // 创建充值流水
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.buildTopUp(companyId, prodAcc, funcCount.calRemainCount());
        contractDeliveryTransactionService.save(transaction);
        logger.info("createTopUpContract 生成订单流水, companyId:{}, prodAccId:{}, changedUnit:{}", transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), transaction.getChangedUnit());
        // 保存充值成功操作日志
        if (DeliveryContracProdAccTypeEnum.TOP_UP.getCode().equals(prodAcc.getType())) {
            OpLogUtils.saveOpLog4PaymentWebhook(OpIpLogTypeEnum.TOP_UP, paymentIntent.getId(), prodAcc.getId());
        }
        checkChargeAmountThenReminder(companyId, amountWithoutTax);

        // 发送invoice
        try {
            BigDecimal exchangeRate = KzzApiInterface.getExchangeRate4TargetCurrencyToSGD(contractDelivery.getAmountStd());
            MsgExceptionUtils.checkIsNull(exchangeRate, "exchangeRate is null");
            invoiceBusinessService.saveInvoice4Payment(topUpNo, prodAcc, company, amountWithoutTax, paymentAmount, taxRate, exchangeRate);
        } catch (Exception e) {
            logger.error("createTopUpContract 发送invoice失败, companyId:{}, topUpNo:{}, prodAccId:{}, amountWithoutTax:{}, paymentAmount:{}, taxRate:{}, exchangeRate:{}", companyId, topUpNo, prodAcc.getId(), amountWithoutTax, paymentAmount, taxRate, BigDecimal.ONE, e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.STRIPE_INVOICE_SEND_ERROR, companyId, topUpNo);
        }
        return prodAcc;
    }

    /**
     * 校验充值金额是否超过系统配置
     * @param companyId
     * @param paymentAmount
     */
    private void checkChargeAmountThenReminder(String companyId, BigDecimal paymentAmount) {
        String chargeAmount = dictService.getDictValueByTypeLabel(Constants.DictType.TOP_UP_AMOUNT_LIMIT, "system_config", "1000");
        if (new BigDecimal(chargeAmount).compareTo(paymentAmount) <= 0) {
            Company company = commSysCompanyService.getCompany(companyId);
            sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.STRIPE_CHARGE_REMINDER, company.getName(), NumberUtils.formatRptUnit(paymentAmount));
        }
    }

    public static List<TblContractDeliveryChargeUnit> transferContractChargeUnitFromKzz(List<KzzContractDetailTO.ContractProduct> contractProductList,
                                                                   List<TblReportChargeUnit> defaultChargeUnits) {
        if (CollectionUtils.isEmpty(contractProductList)) {
            return Lists.newArrayList();
        }
        Map<String, String> configMap = ConfigUtils.getConfigMap("kzz_match_report_type");

        Map<String, TblReportChargeUnit> defaultChargeUnitMap = defaultChargeUnits.stream().collect(Collectors.toMap(TblReportChargeUnit::getReportType, v -> v, (k1, k2) -> k1));

        List<TblContractDeliveryChargeUnit> chargeUnitList = Lists.newArrayList();
        for (KzzContractDetailTO.ContractProduct contractProduct : contractProductList) {
            String reportType = configMap.get(contractProduct.getProductCode());
            if (StringUtils.isBlank(reportType)) {
                continue;
            }
            TblReportChargeUnit defaultChargeUnit = defaultChargeUnitMap.get(reportType);
            if (Objects.nonNull(defaultChargeUnit)) {
                TblContractDeliveryChargeUnit chargeUnit = new TblContractDeliveryChargeUnit();
                BeanUtils.copyProperties(defaultChargeUnit, chargeUnit);
                chargeUnit.setUnit(contractProduct.getDiscountPrice());
                chargeUnit.setEnabled(Constants.YES);
                chargeUnitList.add(chargeUnit);
            }
        }
        return chargeUnitList;
    }

    /**
     * 根据客找找产品映射api产品
     * added for v1.9.7 KNZT-4708
     *
     * @param contractProductList
     * @return List<ApiChargeUnitSaveTO>
     */
    public static List<TblContractDeliveryApiChargeUnit> transferApiChargeUnitFromKzz(List<KzzContractDetailTO.ContractProduct> contractProductList) {
        if (CollectionUtils.isEmpty(contractProductList)) {
            return Lists.newArrayList();
        }
        Map<String, String> configMap = ConfigUtils.getConfigMap("kzz_match_api_type");

        return contractProductList.stream()
                .map(contractProduct -> {
                    String apiType = configMap.get(contractProduct.getProductCode());
                    if (StringUtils.isBlank(apiType) || Objects.isNull(ApiTypeEnum.getEnumByCode(apiType))) {
                        return null;
                    }
                    TblContractDeliveryApiChargeUnit saveTO = new TblContractDeliveryApiChargeUnit();
                    saveTO.setApiType(apiType);
                    saveTO.setUnit(contractProduct.getDiscountPrice());
                    return saveTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private static InvoiceBankTO transferBankFromKzzContract(KzzContractDetailTO contractDetail) {
        if (StringUtils.andBlank(contractDetail.getDepositBank(), contractDetail.getAccountName(), contractDetail.getAccountNumber(), contractDetail.getSwift())) {
            return null;
        }
        InvoiceBankTO bankTO = new InvoiceBankTO();
        bankTO.setDepositBank(contractDetail.getDepositBank());
        bankTO.setAccountName(contractDetail.getAccountName());
        bankTO.setAccountNumber(contractDetail.getAccountNumber());
        bankTO.setSwift(contractDetail.getSwift());
        return bankTO;
    }

    private static InvoiceAddressTO transferAddressFromWorkFlow(KzzWorkFlowDetailTO workFlowDetail) {
        InvoiceAddressTO invoiceAddressTO = new InvoiceAddressTO();
        invoiceAddressTO.setCountry(workFlowDetail.getCountry());
        invoiceAddressTO.setProvince(workFlowDetail.getProvince());
        invoiceAddressTO.setCity(workFlowDetail.getCity());
        invoiceAddressTO.setAddressLine1(workFlowDetail.getAddressLine1());
        invoiceAddressTO.setAddressLine2(workFlowDetail.getAddressLine2());
        invoiceAddressTO.setZipCode(workFlowDetail.getZipCode());
        return invoiceAddressTO;
    }


    /**
     * added for v1.8.8 KNZT-3324
     * 1，计算结果参考Excel工时ROUND(DATEDIF(‘开始日期’,‘结束尾期’,"D")/365*12,0)
     * 2，对于服务开始结束日期是同一天的（批量数据一次性交付），服务期限取值最小值为1；
     * 3，如果期次间隔计算为0，服务期限取值最小值为1；
     *
     * @param serviceStartTime
     * @param serviceEndTime
     * @return
     */
    private static int calculateServicePeriods(Date serviceStartTime, Date serviceEndTime) {
        if (serviceStartTime == null || serviceEndTime == null || (serviceStartTime.compareTo(serviceEndTime) == 0)) return 1;
        int periodsOfMonth = DateUtils.getPeriodsOfMonth(serviceStartTime, serviceEndTime);
        return periodsOfMonth == 0 ? 1 : periodsOfMonth;
    }

    /**
     * added for v1.8.7 KNZT-3881【优化】【收入确认】增加合同交付记录查询功能
     * @param companyId
     * @return
     */
    public List<ChargeRecordsInfoListTO> listDeliveryRecordsByCompanyId(String companyId){
        return commTblContractDeliveryService.listDeliveryRecordsByCompanyId(companyId);
    }

    /**
     * 分页查询交付记录
     * @param condition 查询条件
     * @return 分页结果
     */
    public Page<ChargeRecordsInfoListTO> pageDeliveryRecords(ChargeRecordsCondition condition){
        return commTblContractDeliveryService.pageDeliveryRecords(condition);
    }


    private void handleTrialToSignCompany(String companyId) {
        // 切换公司类型
        commSysCompanyService.updateCompanyType(companyId, CompTypeEnum.SIGN.getCode());
        // 清除试用数据
        reportOrderService.deleteByCompanyId(companyId);
        commSysCompInfoFuncCountService.clearFuncCount(companyId);
        // 清除api订单数据
        apiOrderService.deleteByCompanyId(companyId);
        // 处理试用监控记录
        benefitBusinessService.invalidMonitorBenefitByCompanyId(companyId);
    }


    /**
     * 处理线上直销订单
     * @param order 订单列表
     */
    public void patchTopUpConsumedOrder(TblCompReportOrder order) throws MessageException {
        List<TblContractDeliveryTransaction> transactionList = contractDeliveryTransactionService.getByRelId(order.getId(), TransactionTypeEnum.ORDER.getCode());
        boolean isUpdated = transactionList.stream().noneMatch(k -> StringUtils.equals(k.getContractDeliveryId(), "SELF_ACCOUNT_CONTRACT_DELIVERY_ID"));
        if (isUpdated) {
            logger.info("patchSelfOrderTopUp 订单已处理，跳过， orderId:{}", order.getId());
            return;
        }
        if (CollectionUtils.isEmpty(transactionList)) {
            logger.error("patchSelfOrderTopUp 订单不存在，跳过， orderId:{}", order.getId());
            return;
        }

        Date date = order.getPayResponseTime();

        String companyId = order.getCompanyId();
        // 创建合同
        Company company = commSysCompanyService.get(companyId);

        TblContractDelivery contractDelivery = new TblContractDelivery();
        contractDelivery.setCompanyId(order.getCompanyId());
        String contractNo = redisServUtils.generateNumber("KYCTOP", "kyc_top",
                DateUtils.formatDate(date, DateUtils.DATE_FORMAT_YMDHMS));
        contractDelivery.setContractNo(contractNo);
        contractDelivery.setCustomerName(Objects.nonNull(company) ? company.getName() : order.getLoginName());
        contractDelivery.setContractTerm(12);
        contractDelivery.setBeginDate(date);
        contractDelivery.setEffectTime(date);
        contractDelivery.setContractBeginDate(date);
        Date endDate = DateUtils.addDays(date, 365);
        contractDelivery.setEndDate(endDate);
        contractDelivery.setContractEndDate(endDate);
        contractDelivery.setStatus(DeliveryContracStatusEnum.EFFECT.getCode());
        contractDelivery.setPayType(CompPayTypeEnum.REAL_TIME.getCode());
        contractDelivery.setAmountStd(AmtStdEnum.USD.getCode());
        contractDelivery.setActualDiscountAmount(BigDecimal.valueOf(1));
        contractDelivery.setEnableApi(false);
        contractDelivery.preInsert();
        contractDelivery.setCreateDate(transactionList.get(0).getCreateDate());
        commTblContractDeliveryService.insert(contractDelivery);
        logger.info("patchSelfOrderTopUp 生成合同, companyId:{}, contractNo:{}, contractDeliveryId:{}, orderId:{}", companyId, contractNo, contractDelivery.getId(), order.getId());

        // 创建产品
        TblContractDeliveryProd prod = new TblContractDeliveryProd();
        prod.setCompanyId(companyId);
        prod.setContractDeliveryId(contractDelivery.getId());
        prod.setFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
        prod.setDiscountRate(BigDecimal.valueOf(100));
        prod.setOriAmount(BigDecimal.valueOf(1));
        prod.setDiscountAmount(BigDecimal.valueOf(1));
        prod.setProdName(DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode());
        prod.setTotalUnit(order.getTotalUnit());
        prod.setTotalAmount(order.getTotalUnit());
        commTblContractDeliveryProdService.save(prod);
        logger.info("patchSelfOrderTopUp 生成产品, companyId:{}, prodId:{}, prodTotalUnit:{}, orderId:{}", prod.getCompanyId(), prod.getId(), prod.getTotalUnit(), order.getId());

        // 创建产额度账户
        TblContractDeliveryProdAcc prodAcc = TblContractDeliveryProdAcc.buildFromProd(DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode(), prod, contractDelivery, companyId);
        prodAcc.setTotalConsumedUnit(order.getTotalUnit());
        commTblContractDeliveryProdAccService.save(prodAcc);
        logger.info("patchSelfOrderTopUp 生成产额度账户, companyId:{}, prodAccId:{}, prodAccTotalUnit:{}, orderId:{}", prodAcc.getCompanyId(), prodAcc.getId(), prodAcc.getTotalUnit(), order.getId());

        // 创建充值流水
        TblContractDeliveryTransaction topUpTransaction = TblContractDeliveryTransaction.buildTopUp(companyId, prodAcc, prodAcc.getTotalUnit());
        topUpTransaction.injectDateInt(prodAcc.getBeginDate());
        topUpTransaction.preInsert();
        topUpTransaction.setCreateDate(order.getPayResponseTime());
        contractDeliveryTransactionService.insert(topUpTransaction);
        logger.info("patchSelfOrderTopUp 生成充值流水, companyId:{}, prodAccId:{}, changedUnit:{}, orderId:{}", topUpTransaction.getCompanyId(), topUpTransaction.getContractDeliveryProdAccId(), topUpTransaction.getChangedUnit(), order.getId());

        // 更新消耗流水
        TblContractDeliveryTransaction orderTransaction = transactionList.get(0);
        orderTransaction.setContractDeliveryId(prodAcc.getContractDeliveryId());
        orderTransaction.setContractDeliveryProdAccId(prodAcc.getId());
        orderTransaction.setBeforeRemainUnit(prodAcc.getTotalUnit());
        orderTransaction.setChargeUnitContractDeliveryId(null);
        orderTransaction.setCompanyRemainUnit(BigDecimal.ZERO);
        contractDeliveryTransactionService.save(orderTransaction);
        logger.info("patchSelfOrderTopUp 更新消耗流水, companyId:{}, prodAccId:{}, changedUnit:{}, orderId:{}", orderTransaction.getCompanyId(), orderTransaction.getContractDeliveryProdAccId(), orderTransaction.getChangedUnit(), order.getId());

        // 生成月账
        LocalDate beginDate = DateUtils.toLocalDate(prodAcc.getBeginDate());
        YearMonth beginYM = YearMonth.from(beginDate);
        YearMonth endYM = YearMonth.now();
        for (YearMonth ym = beginYM; ym.isBefore(endYM) || ym.equals(endYM); ym = ym.plusMonths(1)) {
            transactionBusinessService.processContractTransactionMonthly(contractNo, contractDelivery.getActualDiscountAmount(), ym);
            logger.info("patchSelfOrderTopUp 生成月账, companyId:{}, contractNo:{}, ym:{}, orderId:{}", companyId, contractNo, ym, order.getId());
        }
    }
}
