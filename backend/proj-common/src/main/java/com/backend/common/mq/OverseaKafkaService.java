package com.backend.common.mq;

import com.alibaba.fastjson.JSON;
import com.backend.common.modules.report.model.QccOvsBasicInfoCommTO;
import com.backend.common.modules.setting.model.HkSearcherInfo;
import com.backend.common.modules.setting.model.HkSearcherInfo4Api;
import com.backend.common.overseamongo.entity.QccOvsBasic;
import com.backend.common.overseamongo.entity.QccOvsBasicInfo;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.service.CommSysCompanyService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.model.mq.BuyAuReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuyHkIrdReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuyHkReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuyMyReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuyNzReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuySgFinReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuySgReportMessage;
import com.qcc.frame.jee.commons.model.mq.BuyTwReportMessage;
import com.qcc.frame.jee.commons.model.mq.HkIrdGoodsMessage;
import com.qcc.frame.jee.commons.model.mq.HkIrdSearchMessage;
import com.qcc.frame.jee.commons.model.mq.RefreshCompanyMessage;
import com.qcc.frame.jee.commons.model.mq.RefreshHkReportListMessage;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.model.LimitConfig;
import com.qcc.frame.jee.modules.sys.service.RateLimitUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class OverseaKafkaService {

    private final static Logger logger = LoggerFactory.getLogger(OverseaKafkaService.class);
    @Resource
    private KafkaTemplate<String, String> overseaSpiderKafkaTemplate;
    @Autowired
    private OvsQccOvsBasicService qccOvsBasicService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private RateLimitUtils rateLimitUtils;

    public void sendMessage4RefreshHkReportList(String keyNo) throws MessageException {
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(qccOvsBasic), logger, "QccOvsBasic is null, keyNo: " + keyNo);
        String topic = Global.getConfig("kafka.oversea.spider.hk.report.refresh.topic");
        String message = buildMessage4RefreshHkReportList(qccOvsBasic);
        overseaSpiderKafkaTemplate.send(topic, message);
        logger.info("sendMessage4RefreshHkReportList, message:{}", message);
    }

    private String buildMessage4RefreshHkReportList(QccOvsBasic qccOvsBasic) {
        RefreshHkReportListMessage message = new RefreshHkReportListMessage();
        message.setUuid(IdGenUtil.uuid());
        message.setKeyNo(qccOvsBasic.getKeyNo());
        message.setBusinessNumber(qccOvsBasic.getBasicInfo().getCompNo());
        message.setDocumentType("All");
        message.setGetAllDocument(true);
        message.setUserID(UserUtils.getUserId());
        message.setSource("KYC");
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        return JSON.toJSONString(message);
    }

    public void sendMessage4BuyHkReport(String reference, String keyNo, String documentNumber) throws MessageException {
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(qccOvsBasic), logger, "QccOvsBasic is null, keyNo: " + keyNo);
        HkSearcherInfo4Api hkSearchInfo4Api = commSysCompanyService.getHkSearchInfo4Api(UserUtils.getUserCompanyId());
        String topic = Global.getConfig("kafka.oversea.spider.hk.report.buy.topic");
        String message = buildMessage4BuyHkReport(reference, qccOvsBasic, documentNumber, hkSearchInfo4Api);
        overseaSpiderKafkaTemplate.send(topic, message);
        logger.info("sendMessage4BuyHkReport, message:{}", message);
    }

    private String buildMessage4BuyHkReport(String reference, QccOvsBasic qccOvsBasic, String documentNumber, HkSearcherInfo4Api hkSearchInfo4Api) {
        BuyHkReportMessage message = new BuyHkReportMessage();
        message.setCountry("Hong Kong");
        message.setKeyNo(qccOvsBasic.getKeyNo());
        List<String> otherCompNo = qccOvsBasic.getBasicInfo().getOtherCompNo();
        message.setEntityNumber(CollectionUtils.isNotEmpty(otherCompNo) ? otherCompNo.get(0) : null);
        message.setBusinessNumber(qccOvsBasic.getBasicInfo().getCompNo());
        message.setDocumentNumber(documentNumber);
        message.setType("Basic|0,CapitalStructure|0,Shareholder|1,Director|0");
        message.setOrderSource("12");
        message.setDataSource("E-Services");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(reference);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        message.setLoginOption(hkSearchInfo4Api.getLoginOption());
        message.setLoginInputFull(hkSearchInfo4Api.getLoginInputFull());
        return JSON.toJSONString(message);
    }

    public void sendMessage4BuyMyReport(String reference, String keyNo, String compNo, String regNo) throws MessageException {
        BuyMyReportMessage message = new BuyMyReportMessage();
        message.setCountry("Malaysia");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setOldCompanyNo(regNo);
        message.setType("EntityProfileReport");
        message.setOrderSource("3");
        message.setDataSource("Handshakes");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(reference);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.my.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuyMyReport, message:{}", messageJsonStr);
    }

    public void sendMessage4BuyNzReport(String apiOrderNo, String keyNo, String compNo) {
        BuyNzReportMessage message = new BuyNzReportMessage();
        message.setRegion("NewZealand");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setType("EntityProfileReport");
        message.setOrderSource("3");
        message.setDataSource("CompaniesRegister");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.nz.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuyNzReport, message:{}", messageJsonStr);
    }

    public void sendMessage4BuyTwReport(String apiOrderNo, String keyNo) throws MessageException {
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(qccOvsBasic), logger, "QccOvsBasic is null, keyNo: " + keyNo);
        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        String compNo = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo);
        String compClassDetail = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompClassDetail);
        MsgExceptionUtils.failBuildAndLogError(StringUtils.isBlank(compClassDetail), logger, "compClassDetail is blank, keyNo: " + keyNo);
        if (!StringUtils.equalsAny(compClassDetail, "Company", "Business")) {
            logger.error("compClassDetail is not Company or Business, keyNo: " + keyNo);
            throw new MessageException("err.access");
        }
        BuyTwReportMessage message = new BuyTwReportMessage();
        message.setRegion("TaiWan");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setCompanyType(StringUtils.equals(compClassDetail, "Company") ? "公司" : "商业");
        message.setType("EntityProfileReport");
        message.setOrderSource("3");
        message.setDataSource("FindBiz");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.tw.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuyTwReport, message:{}", messageJsonStr);
    }

    public void sendMessage4HkIrdSearch(String taskId, String searchType, String companyName, String businessNo, String branchNo) throws MessageException {
        HkIrdSearchMessage message = new HkIrdSearchMessage();
        message.setUuid(taskId);
        message.setSearchType(searchType);
        message.setCompanyName(companyName);
        message.setBusinessNo(businessNo);
        message.setBranchNo(branchNo);
        message.setUserID(UserUtils.getUserId());
        message.setSource("KYC");
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.hk.ird.search.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4HkIrdSearch, message:{}", messageJsonStr);
    }

    public void sendMessage4HkIrdGoods(String keyNo, String businessNo, String branchNo) throws MessageException {
        HkIrdGoodsMessage message = new HkIrdGoodsMessage();
        message.setUuid(IdGenUtil.uuid());
        message.setKeyNo(keyNo);
        message.setBusinessNo(businessNo);
        message.setBranchNo(branchNo);
        message.setUserID(UserUtils.getUserId());
        message.setSource("KYC");
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.hk.ird.goods.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4HkIrdGoods, message:{}", messageJsonStr);
    }

    public void sendMessage4BuyHkIrdReport(String apiOrderNo, String keyNo, String reportType) throws MessageException {
        QccOvsBasicInfoCommTO qccOvsBasicInfo = qccOvsBasicService.getQccOvsBasicInfoCommTO(keyNo);
        String type = null;
        if (ReportTypeEnum.HK_BR_EXTRACT.getCode().equals(reportType)) {
            type = "ElectronicExtract";
        } else if (ReportTypeEnum.HK_DUP_CERT.getCode().equals(reportType)) {
            type = "BusinessRegistrationCertificate";
        }
        MsgExceptionUtils.checkIsNull(type);
        HkSearcherInfo hkSearcherInfo = commSysCompanyService.getHkSearcherInfo(UserUtils.getUserCompanyId());
        BuyHkIrdReportMessage message = new BuyHkIrdReportMessage();
        message.setRegion("HongKong");
        message.setKeyNo(keyNo);
        message.setBusinessNo(qccOvsBasicInfo.getCompNo());
        message.setBranchNo(MappingUtils.getValue(qccOvsBasicInfo, QccOvsBasicInfoCommTO::getBranchNo, "000"));
        message.setType(type);
        message.setOrderSource("3");
        message.setDataSource("Ird");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        message.setLoginInput(buildLoginInput(hkSearcherInfo));
        String topic = Global.getConfig("kafka.oversea.spider.hk.ird.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuyHkIrdReport, message:{}", messageJsonStr);
    }

    /**
     * 组装IRD查册人信息, 格式如下:
     * ["申请人姓名", "证件号码", "发出文件的政府部门的名称", "签发地点"]
     *
     * @param hkSearcherInfo
     * @return
     */
    public static List<String> buildLoginInput(HkSearcherInfo hkSearcherInfo) {
        String fullName = Lists.newArrayList(MappingUtils.getValue(hkSearcherInfo, HkSearcherInfo::getEnglishOtherNames),
                        MappingUtils.getValue(hkSearcherInfo, HkSearcherInfo::getEnglishSurname))
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(" "));
        return Lists.newArrayList(fullName,
                        MappingUtils.getValue(hkSearcherInfo, HkSearcherInfo::getIdDocNum, ""),
                        MappingUtils.getValue(hkSearcherInfo, HkSearcherInfo::getIssuingAuthority, ""),
                        MappingUtils.getValue(hkSearcherInfo, HkSearcherInfo::getIssuingPlace, ""))
                .stream().map(StringUtils::zhToTraditional).collect(Collectors.toList());
    }

    public void sendMessage4BuyAuReport(String apiOrderNo, String keyNo, String compNo) {
        BuyAuReportMessage message = new BuyAuReportMessage();
        message.setRegion("Australia");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setType("CurrentCompanyInfo");
        message.setOrderSource(3);
        message.setDataSource("Asic");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.au.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuyAuReport, message:{}", messageJsonStr);
    }

    public void sendMessage4BuySgReport(String apiOrderNo, String keyNo) throws MessageException {
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(qccOvsBasic), logger, "QccOvsBasic is null, keyNo: " + keyNo);
        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        String compNo = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo);
        BuySgReportMessage message = new BuySgReportMessage();
        message.setRegion("Singapore");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setType("EntityProfileReport");
        message.setOrderSource(3);
        message.setDataSource("CRSG");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.sg.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuySgReport, message:{}", messageJsonStr);
    }

    public void sendMessage4BuySgFinReport(String apiOrderNo, String keyNo, String reportType, String finYear) throws MessageException {
        if (!ReportTypeEnum.SG_FIN_YEARS.getCode().equals(reportType)
                && !ReportTypeEnum.SG_FIN.getCode().equals(reportType)) {
            logger.error("不支持的报告类型, reportType: " + reportType);
            return;
        }
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        MsgExceptionUtils.failBuildAndLogError(Objects.isNull(qccOvsBasic), logger, "QccOvsBasic is null, keyNo: " + keyNo);
        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        String compNo = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo);
        BuySgFinReportMessage message = new BuySgFinReportMessage();
        message.setCountry("Singapore");
        message.setKeyNo(keyNo);
        message.setCompanyNo(compNo);
        message.setUen(compNo);
        message.setType("FinancialProfileReport");
        if (ReportTypeEnum.SG_FIN_YEARS.getCode().equals(reportType)) {
            message.setFinancialYearType("5");
        } else {
            message.setFinancialYearType("1");
            message.setFinancialYear(finYear);
        }
        message.setOrderSource(3);
        message.setDataSource("CRSG");
        message.setUserID(UserUtils.getUserId());
        message.setOrderReference(apiOrderNo);
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        String topic = Global.getConfig("kafka.oversea.spider.sg.fin.report.buy.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4BuySgFinReport, message:{}", messageJsonStr);
    }

    public void sendMessage4RefreshCompany(String keyNo) throws MessageException {
        boolean limited = rateLimitUtils.checkCombinedLimits(LimitConfig
                .forApi("sendMessage4RefreshCompany", keyNo)
                .setDailyLimit(1));
        if (limited) {
            logger.info("company has been refreshed today, keyNo: " + keyNo);
            return;
        }
        QccOvsBasic qccOvsBasic = qccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        if (Objects.isNull(qccOvsBasic)) {
            logger.warn("qccOvsBasic is null, keyNo: " + keyNo);
            return;
        }
        String nationCode = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getNationCode);
        GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getByNameCode(nationCode);
        if (Objects.isNull(globalAreaEnum)) {
            logger.warn("globalAreaEnum is null, nationCode: " + nationCode);
            return;
        }
        List<GlobalAreaEnum> supportedGlobalAreaEnums = Lists.newArrayList(
                GlobalAreaEnum.HK, GlobalAreaEnum.MY, GlobalAreaEnum.SG,
                GlobalAreaEnum.TW, GlobalAreaEnum.NZ, GlobalAreaEnum.AU, GlobalAreaEnum.GB);
        if (!supportedGlobalAreaEnums.contains(globalAreaEnum)) {
            logger.warn("globalAreaEnum is not supported, globalAreaEnum: " + globalAreaEnum);
            return;
        }
        RefreshCompanyMessage message = buildMessage4RefreshCompany(qccOvsBasic);
        String topic = Global.getConfig("kafka.oversea.spider.refresh.company.topic");
        String messageJsonStr = JSON.toJSONString(message);
        overseaSpiderKafkaTemplate.send(topic, messageJsonStr);
        logger.info("sendMessage4RefreshCompany, message:{}", messageJsonStr);
    }

    private static RefreshCompanyMessage buildMessage4RefreshCompany(QccOvsBasic qccOvsBasic) throws MessageException {
        RefreshCompanyMessage message = new RefreshCompanyMessage();
        String nationCode = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getNationCode);
        GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getByNameCode(nationCode);
        if (Objects.isNull(globalAreaEnum)) {
            throw new MessageException("globalAreaEnum is null, nationCode: " + nationCode);
        }
        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        switch (globalAreaEnum) {
            case HK:
                message.setRegion("HongKong");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getRegNo));
                message.setBusinessNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            case MY:
                message.setRegion("Malaysia");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                message.setOldCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getRegNo));
                break;
            case SG:
                message.setRegion("Singapore");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            case TW:
                message.setRegion("TaiWan");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            case NZ:
                message.setRegion("NewZealand");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            case AU:
                message.setRegion("Australia");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            case GB:
                message.setRegion("Britain");
                message.setCompanyNo(MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getCompNo));
                break;
            default:
                throw new MessageException("globalAreaEnum is not supported, nationCode: " + nationCode);
        }
        message.setKeyNo(MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getKeyNo));
        message.setOrderSource(3);
        message.setUserID(UserUtils.getUserId());
        message.setEnvType(Global.isProjectEnv(Constants.ProjectEnv.PRD) ? 3 : 1);
        message.setRequestTime(DateUtils.getDateTime());
        return message;
    }
}
