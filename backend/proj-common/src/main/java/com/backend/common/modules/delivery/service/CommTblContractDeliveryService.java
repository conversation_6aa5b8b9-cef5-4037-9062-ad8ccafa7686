package com.backend.common.modules.delivery.service;

import com.backend.common.modules.delivery.condition.ChargeRecordsCondition;
import com.backend.common.modules.delivery.condition.TblContractDeliveryCondition;
import com.backend.common.modules.delivery.model.ChargeRecordsInfoListTO;
import com.backend.common.modules.delivery.model.ChargeRecordsInfoListV2TO;
import com.backend.common.modules.delivery.model.Contract4BillTO;
import com.backend.common.modules.setting.condition.CompanyChargeHistoryCondition;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.modules.sys.service.RedisServUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.mapper.TblContractDeliveryDao;
import com.qcc.frame.commons.ienum.DeliveryContracStatusEnum;
import com.qcc.frame.jee.commons.service.CrudService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * added for v1.8.8 KNZT-3324
 * <AUTHOR>
 * @datetime 2024/6/26 10:18
 */
@Service
public class CommTblContractDeliveryService extends CrudService<TblContractDeliveryDao, TblContractDelivery> {

    @Autowired
    private RedisServUtils redisServUtils;

    public List<TblContractDelivery> getByContractNo(String contractNo) {
        return dao.getByContractNo(contractNo);
    }

    public List<TblContractDelivery> getByCompanyId(String companyId) {
        return dao.getByCompanyId(companyId);
    }


    public List<TblContractDelivery> pageByCreateDateInStatus(int size, List<String> statusList, Date lastMinDate) {
        return dao.pageKAByCreateDateInStatus(size, statusList, lastMinDate, false);
    }

    public List<TblContractDelivery> pageKaByCreateDateInStatus(int size, List<String> statusList, Date lastMinDate) {
        return dao.pageKAByCreateDateInStatus(size, statusList, lastMinDate, true);
    }

    // added for v1.8.7 KNZT-3881【优化】【收入确认】增加合同交付记录查询功能
    public List<ChargeRecordsInfoListTO> listDeliveryRecordsByCompanyId(String companyId) {
        return dao.listDeliveryRecordsByCompanyId(companyId);
    }
    
    // 分页查询交付记录
    public Page<ChargeRecordsInfoListTO> pageDeliveryRecords(ChargeRecordsCondition condition) {
        condition.getPage().setList(dao.pageDeliveryRecords(condition));
        return condition.getPage();
    }

    /**
     * 查询合同，按合同编号分组
     * added for lvcy v2.0.8 KNZT-5828
     * @return List<Contract4BillTO>
     */
    public List<Contract4BillTO> listGroupByContractNo() {
        return dao.listGroupByContractNo();
    }

    // added for lvcy v2.0.8 KNZT-5828
    public List<TblContractDelivery> getByContractNoList(List<String> contractNoList) {
        if (CollectionUtils.isEmpty(contractNoList)) {
            return Lists.newArrayList();
        }
        return dao.getByContractNoList(contractNoList);
    }


    // added for lvcy v2.0.8 KNZT-5811
    public List<TblContractDelivery> getByCompanyIdList(List<String> companyIdList) {
        if (CollectionUtils.isEmpty(companyIdList)) {
            return Lists.newArrayList();
        }
        return dao.getByCompanyIdList(companyIdList);
    }

    /**
     * 生成充值编号
     * 生成规则：KYCTOP + 年月日时分秒14位（yyyyMMddHHmmss）+ 订单数3位（001）+ 检验位
     * added for lvcy v2.1.7 KNZT-6487
     * @return 合同编号（充值编号）
     */
    public String generateTopUpNo() {
        return redisServUtils.generateNumber("KYCTOP", "kyc_top");
    }
    public List<ChargeRecordsInfoListV2TO> pageChargeRecords(CompanyChargeHistoryCondition condition) {
        return dao.pageChargeRecords(condition);
    }

    public void insert(TblContractDelivery entity) {
        dao.insert(entity);
    }
    
    /**
     * 查询状态为EFFECT但已过期的合同
     * 
     * @param today 当前日期
     * @return 已过期但状态仍为EFFECT的合同列表
     */
    public List<String> getExpiredEffectiveContractIds(LocalDate today) {
        Date todayDate = DateUtils.toDate(today);
        TblContractDeliveryCondition condition = new TblContractDeliveryCondition();
        condition.setStatus(DeliveryContracStatusEnum.EFFECT.getCode());
        condition.setEndDateLessThanOrEqual(todayDate);
        return dao.getIdListByCondition(condition);
    }
    
    /**
     * 查询状态为PENDING但生效时间已到的合同
     * 
     * @param today 当前日期
     * @return 应该生效但状态仍为PENDING的合同列表
     */
    public List<String> getPendingContractsReadyToEffect(LocalDate today) {
        Date todayDate = DateUtils.toDate(today);
        TblContractDeliveryCondition condition = new TblContractDeliveryCondition();
        condition.setStatus(DeliveryContracStatusEnum.PENDING.getCode());
        condition.setBeginDateLessThanOrEqual(todayDate);
        return dao.getIdListByCondition(condition);
    }
    
    /**
     * 查询即将过期的合同（距离过期还有30/15/5天的合同）
     * updated for v2.3.9 fengsw KNZT-8773
     * 
     * @param today   当前日期
     * @param payTypeList 支付类型列表（区分后付费、预付费、实时支付类型）
     * @return 即将过期的合同列表
     */
    public List<String> getIdByNearExpiryDates(LocalDate today, List<String> payTypeList) {
        LocalDate date30 = today.plusDays(30);
        LocalDate date15 = today.plusDays(15);
        LocalDate date5 = today.plusDays(5);
        
        // 使用字符串格式化日期，避免精度问题
        Date date30Str = DateUtils.toDate(date30);
        Date date15Str = DateUtils.toDate(date15);
        Date date5Str = DateUtils.toDate(date5);
        
        List<Date> expiryDates = Arrays.asList(date30Str, date15Str, date5Str);
        
        return dao.getIdByExpiryDates(DeliveryContracStatusEnum.EFFECT.getCode(), expiryDates, payTypeList);
    }

    public List<TblContractDelivery> batchGetByIds(List<String> ids) {
        return dao.batchGetByIds(ids);
    }


    /**
     * 按创建时间分批获取合同数据，使用条件对象过滤
     *  added for lvcy v2.1.7 KNZT-6487
     * @param size 每批数量
     * @param lastMinDate 上次查询的最后一条记录的创建时间
     * @param condition 过滤条件
     * @return 符合条件的合同列表
     */
    public List<TblContractDelivery> pageByCreateDateWithCondition(int size, Date lastMinDate, TblContractDeliveryCondition condition) {
        return dao.pageByCreateDateWithCondition(size, lastMinDate, condition);
    }

    /**
     * 根据companyIds 获取数据，根据companyId分组，获取每个companyId的最新一条数据
     * 
     * @param companyIds 公司ID列表
     * @return 公司金额标准Map
     */
    public Map<String, String> getCompanyAmountStdMap(List<String> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return Maps.newHashMap();
        }
        List<TblContractDelivery> list = dao.getCompanyAmountStd(companyIds);
        return list.stream().collect(Collectors.toMap(TblContractDelivery::getCompanyId, TblContractDelivery::getAmountStd));
    }

    public TblContractDelivery getLatestDataByCompanyId(String companyId) {
        return dao.getLatestDeliveryByCompanyId(companyId);
    }

    public TblContractDelivery getByPayRelId(String payRelId) {
        return dao.getByPayRelId(payRelId);
    }
}
