package com.backend.common.modules.report.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.convertapi.WebConvertInterface;
import com.backend.common.entity.SysTemplate;
import com.backend.common.entity.mapping.CompanyConsumedInfoTO;
import com.backend.common.form.ding.msg.DingMsgSendDTO;
import com.backend.common.global.gateway.verify.VerifyInterface;
import com.backend.common.global.gateway.verify.model.VerifyResp;
import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.common.model.DictItem;
import com.backend.common.modules.common.service.CommTblCompOpIpLogService;
import com.backend.common.modules.common.service.CommTblUserQuotaLimitService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.model.TblGlobalIndustryBaseTO;
import com.backend.common.modules.industry.model.TblGlobalIndustryQueryResultTO;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.person_vrfy.form.EncryptedForm;
import com.backend.common.modules.report.condition.TblCompReportOrderCondition;
import com.backend.common.modules.report.condition.TblCompReportOrderFinTaxCondition;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblCompReportOrderFinTax;
import com.backend.common.modules.report.entity.TblCompReportOrderHkDoc;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.entity.TblWebConverter;
import com.backend.common.modules.report.entity.mapping.ReportOrder4Loop;
import com.backend.common.modules.report.entity.mapping.ReportOrderUnitInfo;
import com.backend.common.modules.report.form.AcquireLegalDocumentCallbackForm;
import com.backend.common.modules.report.form.ClientSecretForm;
import com.backend.common.modules.report.form.GeneralInfo4BatchOrderForm;
import com.backend.common.modules.report.form.InfoBeforeSubmitForm;
import com.backend.common.modules.report.form.ReportOrder4ManagCondition;
import com.backend.common.modules.report.form.ReportOrderInitForm;
import com.backend.common.modules.report.form.TblCompReportOrderDuplicateForm;
import com.backend.common.modules.report.mapper.TblCompReportOrderDao;
import com.backend.common.modules.report.model.CheckCanOrderResult;
import com.backend.common.modules.report.model.CorpHKScanningCount4CoverTO;
import com.backend.common.modules.report.model.CorpHkInfoTO;
import com.backend.common.modules.report.model.CorpInfoTO;
import com.backend.common.modules.report.model.CorpScanningCount4CoverTO;
import com.backend.common.modules.report.model.HKInfoBeforeSubmitTO;
import com.backend.common.modules.report.model.HongKongAnnouncementTO;
import com.backend.common.modules.report.model.InfoBeforeSubmitTO;
import com.backend.common.modules.report.model.Order4CartResult;
import com.backend.common.modules.report.model.Order4CartValidateResult;
import com.backend.common.modules.report.model.OrderDistributionResultTO;
import com.backend.common.modules.report.model.OrderFinTaxSimpleTO;
import com.backend.common.modules.report.model.OrderGeneraInfo4BatchOrderTO;
import com.backend.common.modules.report.model.OrderGeneraInfoTO;
import com.backend.common.modules.report.model.OrderInitInfoTO;
import com.backend.common.modules.report.model.OrderPollingResultTO;
import com.backend.common.modules.report.model.OrderSaveResult;
import com.backend.common.modules.report.model.OrderSubmitTO;
import com.backend.common.modules.report.model.OrderValidateResult;
import com.backend.common.modules.report.model.PersInfoTO;
import com.backend.common.modules.report.model.PersScanningCount4CoverTO;
import com.backend.common.modules.report.model.PluginInfoTO;
import com.backend.common.modules.report.model.RemainingUnitCheckResultTO;
import com.backend.common.modules.report.model.ReportOrderInfoOfBillTO;
import com.backend.common.modules.report.model.ReportOrderSearchDictOfBillTO;
import com.backend.common.modules.report.model.management.ReportOrder4ManagListTO;
import com.backend.common.modules.report.model.order.CompReportOrderGroupTO;
import com.backend.common.modules.report.model.sg.SgBasicTO;
import com.backend.common.modules.report.service.report.order.custom.SgReportOrderCustomService;
import com.backend.common.modules.report_data.corp.oversea.base.ReportDataContextService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.modules.report_data.model.ReportDataResultTO;
import com.backend.common.modules.rule.model.UserAccessScopeTO;
import com.backend.common.modules.rule.service.RuleBusinessService;
import com.backend.common.modules.scan.entity.TblCompReportOrderScan;
import com.backend.common.modules.scan.form.ScanOrderSubmitForm;
import com.backend.common.modules.scan.service.CommTblCompReportOrderScanService;
import com.backend.common.modules.scan.service.CommTblCompScanListRuleService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.modules.setting.model.HkSearcherInfo;
import com.backend.common.modules.trans.base.form.TransWrapper;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.modules.util.OpLogUtils;
import com.backend.common.mq.OverseaKafkaService;
import com.backend.common.openapi.model.ExternalApiIndustryInfoTO;
import com.backend.common.openapi.model.OrderRecordsTO;
import com.backend.common.oversea.model.HkReportResultTO;
import com.backend.common.oversea.model.HongKongAnnouncementsTO;
import com.backend.common.overseamongo.entity.QccOvsBasic;
import com.backend.common.overseamongo.entity.QccOvsBasicInfo;
import com.backend.common.overseamongo.entity.QccProdHkTaxReport;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.overseamongo.service.QccProdHkTaxReportService;
import com.backend.common.proapi.ProOutboundSyncInterface;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.CommSysTemplateService;
import com.backend.common.service.CommTblGlobalOutboundListSyncNewTranService;
import com.backend.common.service.CommTblGlobalOutboundListSyncService;
import com.backend.common.service.SysCompRoleService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.oversea.HkReportResultService;
import com.backend.common.service.oversea.OverseaHongKongAnnouncementsService;
import com.backend.common.stripeapi.StripePaymentInterface;
import com.backend.common.thread.ExecuteAfterSubmitOrderRunnable;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.CompanyReportInternalInterface;
import com.backend.common.yunjuapi.CorpHKDimensionCountTO;
import com.backend.common.yunjuapi.ECILocalInterface;
import com.backend.common.yunjuapi.FuzzySearchInterface;
import com.backend.common.yunjuapi.GlobalCompanyDetailsInterface;
import com.backend.common.yunjuapi.IntranetInterface;
import com.backend.common.yunjuapi.QccEntDetailInfoTO;
import com.backend.common.yunjuapi.SgCompanyDetailsInterface;
import com.backend.common.yunjuapi.YunjuReportInterface;
import com.backend.common.yunjuapi.model.ApiGlobalCorpDetailTO;
import com.backend.common.yunjuapi.model.CorpBasicDetail4UnitTO;
import com.backend.common.yunjuapi.model.CorpBusinessParentInfoTO;
import com.backend.common.yunjuapi.model.CorpLogoInfoTO;
import com.backend.common.yunjuapi.model.CorpParentOper;
import com.backend.common.yunjuapi.model.CorpScanningCountTO;
import com.backend.common.yunjuapi.model.GetDetailOfSimpleTO;
import com.backend.common.yunjuapi.model.HkCorpBasicDetailTO;
import com.backend.common.yunjuapi.model.HkCorpIdentityInfoProcessor;
import com.backend.common.yunjuapi.model.IndustryInfoTO;
import com.backend.common.yunjuapi.model.KeyNoResult;
import com.backend.common.yunjuapi.model.OverseaMaintainStatusTO;
import com.backend.common.yunjuapi.model.PersonJobResult;
import com.backend.common.yunjuapi.model.PersonScanningCountTO;
import com.backend.common.yunjuapi.model.ReportUrlTO;
import com.backend.common.yunjuapi.model.hk.IdentificationObj;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.qcc.frame.commons.ienum.CompDataSaveEnum;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.FintaxAuthStatusEnum;
import com.qcc.frame.commons.ienum.FintaxDataStatusEnum;
import com.qcc.frame.commons.ienum.ForeignCorpStatusEnum;
import com.qcc.frame.commons.ienum.GenderEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.GlobalProductTypeOpenApiCodeEnum;
import com.qcc.frame.commons.ienum.HKOrderErrorEnum;
import com.qcc.frame.commons.ienum.HkCompanyTypeEnum;
import com.qcc.frame.commons.ienum.HkStatusEnum;
import com.qcc.frame.commons.ienum.IndustryCountryTypeEnum;
import com.qcc.frame.commons.ienum.KycOrderTypeEnum;
import com.qcc.frame.commons.ienum.OpIpLogTypeEnum;
import com.qcc.frame.commons.ienum.OrderHkDocRelTypeEnum;
import com.qcc.frame.commons.OrderExtConstants;
import com.qcc.frame.commons.ienum.OrderRelTypeEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.PayStatusEnum;
import com.qcc.frame.commons.ienum.PayTypeEnum;
import com.qcc.frame.commons.ienum.QccEntCodeTypeEnum;
import com.qcc.frame.commons.ienum.RegistrationStatusEnum;
import com.qcc.frame.commons.ienum.ReportGroupMappingEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.ScanListEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.commons.ienum.UnitGroupEnum;
import com.qcc.frame.commons.ienum.ding.DingMsgLevelEnum;
import com.qcc.frame.jee.commons.annotation.DingErrorMsg;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.model.json.BaseJsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.utils.BeanUtil;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.FileUtils;
import com.qcc.frame.jee.commons.utils.FreemarkerUtils;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.PdfUtils;
import com.qcc.frame.jee.commons.utils.PinyinUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.WebContextHolder;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.SysConfig;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.ApiUserLoginService;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.RedisServUtils;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.service.SysConfigService;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.stripe.model.PaymentIntent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CommTblCompReportOrderService extends CrudService<TblCompReportOrderDao, TblCompReportOrder> {

    private static final Logger logger = LoggerFactory.getLogger(CommTblCompReportOrderService.class);
    @Autowired
    private CommTblReportChargeUnitService commTblReportChargeUnitService;
    @Autowired
    private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
    @Autowired
    private ApiUserLoginService apiUserLoginService;
    @Autowired
    private TranslaterService translaterService;
    @Autowired
    private CommTblGlobalOutboundListSyncService commTblGlobalOutboundListSyncService;
    @Autowired
    private SgReportOrderCustomService sgReportOrderCustomService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommCompUserService commCompUserService;
    @Autowired
    private SysCompRoleService sysCompRoleService;
    @Autowired
    private DictService dictService;
    @Autowired
    private ThreadPoolService threadPoolService;
    @Autowired
    private QccMailSenderService qccMailSenderService;
    @Autowired
    private CommSysTemplateService commSysTemplateService;
    @Autowired
    private CommTblCompReportOrderService self;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private CommTblCompReportOrderRelService commTblCompReportOrderRelService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private CommTblContractDeliveryProdAccService prodAccService;
    @Autowired
    private CommTblCompOpIpLogService commTblCompOpIpLogService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CommTblCompReportCartService commTblCompReportCartService;
    @Autowired
    private CommTblCompReportOrderFinTaxService finTaxService;
    @Autowired
    private CommTblCompReportOrderExtService commTblCompReportOrderExtService;
    @Autowired
    private CommTblWebConverterService commTblWebConverterService;
    @Autowired
    private CommTblCompReportOrderScanService commTblCompReportOrderScanService;
    @Autowired
    private CommTblCompScanListRuleService commTblCompScanListRuleService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private CommTblGlobalIndustryService commTblGlobalIndustryService;
    @Autowired
    private OvsQccOvsBasicService qccOvsBasicService;
    @Autowired
    private CommTblCompReportOrderMapService commTblCompReportOrderMapService;
    @Autowired
    private RedisServUtils redisServUtils;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private HkReportResultService hkReportResultService;
    @Autowired
    private OverseaKafkaService overseaKafkaService;
    @Autowired
    private CommTblCompReportOrderHkDocService hkDocService;
    @Autowired
    private OverseaHongKongAnnouncementsService overseaHongKongAnnouncementsService;
    @Autowired
    private CommTblUserQuotaLimitService commTblUserQuotaLimitService;
    @Autowired
    private ReportDataContextService reportDataContextService;
    @Autowired
    private UserService userService;
    @Autowired
    private RuleBusinessService ruleBusinessService;
    @Autowired
    private QccProdHkTaxReportService qccProdHkTaxReportService;
    @Autowired
    private CommTblGlobalOutboundListSyncNewTranService commTblGlobalOutboundListSyncNewTranService;
    @Autowired
    private CommonTransService commonTransService;
    @Autowired
    private CommTblCompReportOrderExtService orderExtService;

    public BigDecimal sumOrderTotalUnitByCompanyId(String companyId, List<String> unitGroupList) {
        BigDecimal result = dao.sumOrderTotalUnitByCompanyId(companyId, unitGroupList, false);
        return result != null ? result : BigDecimal.ZERO;
    }

    // added for lvcy v2.0.6 KNZT-5707
    public BigDecimal sumInvalidOrderTotalUnit(String companyId) {
        BigDecimal result = dao.sumOrderTotalUnitByCompanyId(companyId, null, true);
        return result != null ? result : BigDecimal.ZERO;
    }

    //added v6.7.1 KNZT-38 报告列表的消耗额度，从本地统计，该方法只给平台管理员用
    public BigDecimal sumOrderTotalUnit() {
        BigDecimal result = dao.sumOrderTotalUnit();
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 获取订单数据(用于需要轮询的订单)
     * updated for v2.0.6 chenbl KNZT-5506
     * updated for lvcy v2.1.5 KNZT-6377 去除订单数据获取成功后，生成报告的逻辑
     * @param orderId
     * @return
     */
    public ReportDataResultTO<?> getOrderDataByLoopRequest(String orderId) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();
        ReportOrder4Loop order4Loop = dao.getOrder4LoopById(orderId, companyId);
        return getApiOrderDataFull(order4Loop);
    }

    /**
     * added for v2.0.6 chenbl KNZT-5506
     * api订单数据获取成功后，需要做的操作
     * @param order4Loop
     * @throws MessageException
     */
    private void doAfterApiOrderDataSuccess(TblCompReportOrder order4Loop) throws MessageException {
        if (!OrderStatusEnum.canUpdateTo(OrderStatusEnum.getByCode(order4Loop.getRptStatus()), OrderStatusEnum.PENDING)) {
            logger.info("订单状态无法流转到P, 不进行处理, orderId: {}", order4Loop.getId());
            return;
        }
        TblCompReportOrder order = get(order4Loop.getId());
        if (order == null) {
            return;
        }
        order.transitionStatus(OrderStatusEnum.PENDING);
        this.save(order);
        commTblCompReportOrderService.createReport(order);
        // added for v2.1.2 chenbl KNZT-6194
        if (ReportTypeEnum.HK_BAS_AR.getCode().equals(order4Loop.getReportType())) {
            HongKongAnnouncementsTO hongKongAnnouncementsTO = hkReportResultService.getHkAnnouncementByApiOrderNo(order4Loop.getApiOrderNo());
            if (Objects.isNull(hongKongAnnouncementsTO)) {
                logger.error("HongKongAnnouncementsTO is null, apiOrderNo:{}", order4Loop.getApiOrderNo());
                return;
            }
            dao.updateExtraInfo1ById(order4Loop.getId(), hongKongAnnouncementsTO.getDocumentType(), UserUtils.getUserId());
            hkDocService.save(order, OrderHkDocRelTypeEnum.ORDER.getCode(), hongKongAnnouncementsTO);
        } else if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(order4Loop.getReportType())) {
            TblCompReportOrderHkDoc hkDoc = hkDocService.getByRelIdAndType(order.getId(), OrderHkDocRelTypeEnum.ORDER.getCode());
            if (Objects.isNull(hkDoc)) {
                logger.error("handleHkDocCallback overseaReportCallback hkDoc is not exist, orderId:{}", order.getId());
                return;
            }
            HongKongAnnouncementsTO hkAnnouncements = overseaHongKongAnnouncementsService.getByKeyNoAndDocNumber(hkDoc.getKeyNo(), hkDoc.getDocNumber());
            if (Objects.isNull(hkAnnouncements)) {
                logger.error("handleHkDocCallback overseaReportCallback hkAnnouncements is not exist, keyNo:{}", hkDoc.getKeyNo());
                return;
            }
            String documentFile = hkAnnouncements.getDocumentFile();
            if (StringUtils.isBlank(documentFile)) {
                logger.error("handleHkDocCallback overseaReportCallback hkAnnouncements documentFile is blank, apiOrderNo:{}", order4Loop.getApiOrderNo());
                return;
            }
            order.setUrl(documentFile);
            order.transitionStatus(OrderStatusEnum.SUCCESS);
            this.save(order);

            hkDoc.setDocUrl(documentFile);
            hkDocService.save(hkDoc);
            logger.info("overseaReportCallback update hk doc, orderId:{}", order.getId());
        } else if (ReportTypeEnum.MAP_NETWORK_HK.getCode().equals(order4Loop.getReportType())
                || ReportTypeEnum.MAP_OWNERSHIP_HK.getCode().equals(order4Loop.getReportType())) {
            order.transitionStatus(OrderStatusEnum.SUCCESS);
            this.save(order);
        }
        logger.info("overseaReportCallback success, orderId:{}", order.getId());
    }

    /**
     * 不可暴露给用户接口，根据订单号获取API报告数据
     * added for v2.0.5 chenbl KNZT-5506
     * @param orderNo
     * @return
     * @throws MessageException
     */
    public ReportDataResultTO<?> getApiOrderDataByOrderNo(String orderNo) throws MessageException {
        TblCompReportOrder reportOrder = dao.getByOrderNo(orderNo);
        if (reportOrder == null) {
            return new ReportDataResultTO<>();
        }
        ReportOrder4Loop order4Loop = ReportOrder4Loop.buildFromReportOrder(reportOrder);
        AtomicReference<ReportDataResultTO<?>> atomicResult = new AtomicReference<>();
        apiUserLoginService.loginCompany(order4Loop.getCompanyId(), () -> {
            atomicResult.set(getApiOrderDataFull(order4Loop));
            return null;
        });
        return atomicResult.get();
    }

    /**
     * added for v2.0.5 chenbl KNZT-5506
     * 获取API订单数据(填充后)
     * @param order4Loop
     * @return
     */
    private ReportDataResultTO<?> getApiOrderDataFull(ReportOrder4Loop order4Loop) {
        ReportDataResultTO<?> result = new ReportDataResultTO<>();
        if (order4Loop == null) {
            return result;
        }
        boolean supported = reportDataContextService.isSupported(order4Loop.getReportType(), order4Loop.getDataVersion());
        if (!supported) {
            return result;
        }
        return reportDataContextService.getService(order4Loop.getReportType(), order4Loop.getDataVersion())
                .getResult(ReportDataGetResultForm.build(order4Loop.getId()));
    }

    /**
     * 解密并设置对应字段值
     *
      * @param processors
     * @param userKeyByCompanyId
     */
    private void processIdentificationData(List<? extends HkCorpIdentityInfoProcessor> processors, String userKeyByCompanyId) {
        if (CollectionUtils.isNotEmpty(processors)) {
            for (HkCorpIdentityInfoProcessor processor : processors) {
                List<IdentificationObj> identificationObjs = GlobalCompanyDetailsInterface.decryptFromIdentificationString(processor.getIdentificationString(), userKeyByCompanyId);
                if (CollectionUtils.isNotEmpty(identificationObjs)) {
                    GlobalCompanyDetailsInterface.desensitize(identificationObjs);
                    processor.setHkId(getIdentificationNumberByType(identificationObjs, "1"));
                    processor.setCnId(getIdentificationNumberByType(identificationObjs, "2"));
                    IdentificationObj passportObj = getIdentificationObjByType(identificationObjs, "3");
                    if (passportObj != null) {
                        processor.setPassportNo(passportObj.getIdentificationNumber());
                        processor.setPassportCountry(passportObj.getPassportCountry());
                    }
                }
            }
        }
    }

    /**
     * 根据类型获取身份证号
     */
    private String getIdentificationNumberByType(List<IdentificationObj> identificationObjs, String type) {
        if(CollectionUtils.isEmpty(identificationObjs)) return null;
        return identificationObjs.stream()
                .filter(identificationObj -> type.equals(identificationObj.getIdentificationType()))
                .findFirst()
                .map(IdentificationObj::getIdentificationNumber)
                .orElse(null);
    }

    /**
     * 根据类型获取IdentificationObj对象
     */
    private IdentificationObj getIdentificationObjByType(List<IdentificationObj> identificationObjs, String type) {
        if(CollectionUtils.isEmpty(identificationObjs)) return null;
        return identificationObjs.stream()
                .filter(identificationObj -> type.equals(identificationObj.getIdentificationType()))
                .findFirst()
                .orElse(null);
    }

    // updated for v1.9.9 chenbl KNZT-5071 入参仅使用dataToken, 其他字段后端获取
    public OrderInitInfoTO getInitOrderInfo(ReportOrderInitForm form) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();

        OrderInitInfoTO result = new OrderInitInfoTO();
        String dataToken = form.getDataToken();
        String orderIdOfDataToken = getOrderIdAndCheckDataToken(dataToken);
        if (StringUtils.isBlank(orderIdOfDataToken)) {
            throw new MessageException("err.access");
        }
        TblCompReportOrder order = dao.getByIdAndCompany(orderIdOfDataToken, companyId);
        if(Objects.isNull(order)){
            throw new MessageException("err.access");
        }
        result.setReportType(order.getReportType());
        result.setReportGroup(order.getReportGroup());
        result.setDataToken(dataToken);
        result.setOrderCreateDate(order.getCreateDate());
        result.setOrderBeginDate(order.getBeginDate()); // added for v2.0.3 chenbl KNZT-5271
        CorpInfoTO corpInfoTO = new CorpInfoTO();
        String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(order.getReportGroup());
        if (Constants.Report.REPORT_GROUP_SG.equals(order.getReportGroup())) {//added v1.3.9 KNZT-2170
            sgReportOrderCustomService.populateInitOrderInfo(result, dataToken, order, true);
        } else if(StringUtils.equals(keyNoType, Constants.Report.KEY_NO_TYPE_CORP)) {
            corpInfoTO.setCorpKeyNo(order.getKeyNo());
            corpInfoTO.setCorpName(order.getCorpName());
            corpInfoTO.setCorpNameEn(StringUtils.upperCase(order.getCorpNameEn()));
            corpInfoTO.setCorpNumber(order.getCorpNumber());
            corpInfoTO.setCorpStatus(order.getCorpStatus());
            String searchKey = StringUtils.getNotBlankStr(corpInfoTO.getCorpKeyNo(), corpInfoTO.getCorpNumber(), corpInfoTO.getCorpName(), corpInfoTO.getCorpNameEn());

            String includeExtraFields = ReportTypeEnum.withBasicList().contains(result.getReportType()) ?
                    "PartnerList,PubPartnerList,EmployeeList,PubEmployeeList" : null;
            CorpBasicDetail4UnitTO basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4Unit(searchKey, includeExtraFields);
            populateOrderByCorpDetail(corpInfoTO, basicDetailTO);
            result.setCorpInfo(corpInfoTO);

            if(Constants.Report.REPORT_GROUP_CORP_G.equals(order.getReportGroup())) {
                populateCorpInfoTOBySearchKey(corpInfoTO, searchKey, basicDetailTO, true);
            } else if(Constants.Report.REPORT_GROUP_KYB_HK.equals(order.getReportGroup())) {
                HkCorpBasicDetailTO detail = qccOvsBasicService.getHkCorpBasicDetailTO(order.getKeyNo());
                CorpHkInfoTO corpHkInfoTO = new CorpHkInfoTO();
                corpHkInfoTO.setOrderId4Hk(dataToken); // added for KNZT-3139
                if(order != null) {
                    corpHkInfoTO.setOrderStatus(order.getRptStatus());
                    corpHkInfoTO.setDetail(detail);
//                    corpHkInfoTO.setExtraInfo1(order.getExtraInfo1()); removed for v2.1.2 chenbl KNZT-6194
                    // added for v2.0.6 fengsw KNZT-5381 补充qccCode
                    corpHkInfoTO.setQccCode(Objects.nonNull(basicDetailTO) ? basicDetailTO.getQccCode() : null);
                    // added for v2.1.4 fengsw KNZT-6360 leiCode
                    QccEntDetailInfoTO entDetailInfoTO = CompanyDetailsInterface.getQccDetailByEntCode(order.getKeyNo(), QccEntCodeTypeEnum.KEY_NO.getCode());
                    corpHkInfoTO.setLeiCode(Objects.nonNull(entDetailInfoTO) ? entDetailInfoTO.getLeiCode() : null);
                    corpInfoTO.setCorpHkInfoTO(corpHkInfoTO);
                    // added for v1.7.8 KNZT-3320 提前返回最新周年申报表时间
                    HongKongAnnouncementTO hkLatestAnnual = IntranetInterface.getHkLatestAnnual(order.getKeyNo());
                    if (Objects.nonNull(hkLatestAnnual) && StringUtils.isNotBlank(hkLatestAnnual.getDocumentDate())) {
                        String documentDate = hkLatestAnnual.getDocumentDate().replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
                        corpHkInfoTO.setLatestAnnualDate(documentDate);
                    }
                }
                // updated for v1.2.4 KNZT-1181
                this.setCorpOrPersonScanningCountInfoForCover(result, "hkCorp");
                if(detail!= null) {
                    // added for KNZT-1314日期格式化
                    if (StringUtils.isNotBlank(detail.getUpdateDate()) && NumberUtils.isLong(detail.getUpdateDate())) {
                        detail.setUpdateDate(DateUtils.toDateStr(new Long(detail.getUpdateDate()), DateUtils.DATE_FORMAT));
                    }
                    corpInfoTO.setListingStatus4Hk(detail.isListingFlag());
                    if (!detail.isListingFlag()) {
                        detail.setContactAddress(detail.getAddress());
                    }
                }
            }
        }
        // removed for v1.7.0 KNZT-3065
        else {
            PersInfoTO persInfoTO = new PersInfoTO();
            persInfoTO.setPersName(order.getPersName());
            persInfoTO.setPersNameEn(order.getPersNameEn());
            persInfoTO.setPersKeyNo(order.getKeyNo());
            result.setPersInfoTO(persInfoTO);
            // update for KNZT-924 人员核验完成的订单，不用再解锁，直接查看详情
            if(Constants.Report.REPORT_GROUP_PERS_G.equals(order.getReportGroup()) || Constants.Report.REPORT_GROUP_PERS_NOT_RPT.equals(order.getReportGroup())) {
                    persInfoTO.setOrderId4PersLclLang(dataToken);
                    if(Constants.Report.REPORT_TYPE_PERS_360.equals(order.getReportType())) {
                        PluginInfoTO pluginInfoTO = new PluginInfoTO();
                        pluginInfoTO.setKeyNo(order.getKeyNo());
                        persInfoTO.setPluginInfoTO(pluginInfoTO);
                    }
            }
        }

        List<TblReportChargeUnit> reportList = commTblReportChargeUnitService.listChargeUnit(companyId, order.getReportGroup());
        if(CollectionUtils.isNotEmpty(reportList)) {
            result.setReportList(reportList);
        }
        result.setUserInfo(this.getUserInfo(order.getUserId()));
        return result;
    }

    private OrderInitInfoTO.UserInfo getUserInfo(String userId) {
        OrderInitInfoTO.UserInfo userInfo = new OrderInitInfoTO.UserInfo();
        if (StringUtils.isNotBlank(userId)) {
            User user = commCompUserService.get(userId);
            if (user != null) {
                Company company = commSysCompanyService.get(user.getCompanyId());
                if (company != null) {
                    userInfo.setLoginId(StringUtils.left(user.getCompanyId(), 13) + "-" + StringUtils.left(user.getId(), 20));
                    userInfo.setCategory(company.getCategory());
                }
            }
        }
        return userInfo;
    }

    /**
     * updated for v2.0.6 chenbl KNZT-5506
     * added for v1.7.2 KNZT-3104
     */
    public OrderInitInfoTO getInitOrderInfoNotNeedOrder(ReportOrderInitForm form, TblCompReportOrder order) throws MessageException {
        if (order == null) {
            return null;
        }
        OrderInitInfoTO result = new OrderInitInfoTO();
        CorpInfoTO corpInfoTO = new CorpInfoTO();
        result.setOrderCreateDate(order.getCreateDate());
        result.setOrderBeginDate(order.getBeginDate());
        // 用户唯一标识(和doLogin接口返回的loginId规则一致)
        result.setLoginId(StringUtils.left(order.getCompanyId(), 13) + "-" + StringUtils.left(order.getUserId(), 20));
        if (UnitGroupEnum.SG_UNIT.getGroup().equals(order.getUnitGroup())) {
            sgReportOrderCustomService.populateInitOrderInfo(result, null, order, false);
        } else {
            CorpBasicDetail4UnitTO basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4Unit(order.getKeyNo(), "PartnerList,PubPartnerList,EmployeeList,PubEmployeeList", false);
            if (basicDetailTO != null) {
                corpInfoTO.setCorpKeyNo(order.getKeyNo());
                corpInfoTO.setCorpName(basicDetailTO.getCompanyName());
                TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN.getNameCode());
                transWrapper.addEntryOf(corpInfoTO,
                                CorpInfoTO::setCorpName, CorpInfoTO::setCorpNameEn,
                                CorpInfoTO::getCorpName, CorpInfoTO::getCorpNameEn)
                        .withEntity(TransWrapper.ENTRY_TYPE_CORP, CorpInfoTO::getCorpKeyNo);
                commonTransService.enPostProcessor(transWrapper);
                corpInfoTO.setCorpNumber(basicDetailTO.getCreditCode());
            }
            populateOrderByCorpDetail(corpInfoTO, basicDetailTO);
            result.setCorpInfo(corpInfoTO);
            populateCorpInfoTOBySearchKey(corpInfoTO, order.getKeyNo(), basicDetailTO, false);
            // added for v2.0.9 fengsw KNZT-5608 香港报告数据接口返回基本详情
            if (Constants.Report.REPORT_GROUP_KYB_HK.equals(order.getReportGroup())) {
                HkCorpBasicDetailTO detail = qccOvsBasicService.getHkCorpBasicDetailTO(order.getKeyNo());
                CorpHkInfoTO corpHkInfoTO = new CorpHkInfoTO();
                corpHkInfoTO.setDetail(detail);
//                corpHkInfoTO.setExtraInfo1(order.getExtraInfo1()); removed for v2.1.2 chenbl KNZT-6194
                corpHkInfoTO.setQccCode(Objects.nonNull(basicDetailTO) ? basicDetailTO.getQccCode() : null);
                corpHkInfoTO.setLeiCode(Objects.nonNull(basicDetailTO) ? basicDetailTO.getLeiCode() : null);
                corpInfoTO.setCorpHkInfoTO(corpHkInfoTO);
            }
        }
        result.setUserInfo(this.getUserInfo(order.getUserId()));
        return result;
    }

    // updated for v1.7.2 KNZT-3104
    // updated for v1.7.2 KNZT-3231
    public void populateCorpInfoTOBySearchKey(CorpInfoTO corpInfoTO, String searchKey, CorpBasicDetail4UnitTO basicDetailTO, boolean needCompanyKey){
        if(basicDetailTO == null) {
            logger.error("basicDetailTO isnull");
            basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4Unit(searchKey, "PartnerList,PubPartnerList,EmployeeList,PubEmployeeList", needCompanyKey);
        }
        //注只要initOrder接口传corpShortStatusCn
        if (Objects.nonNull(basicDetailTO)) {
            // updated for v1.7.2 KNZT-3104
            basicDetailTO.setRegistrationStatus(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(basicDetailTO.getRegistrationStatusCn()));
            corpInfoTO.setCorpStatus(basicDetailTO.getRegistrationStatus());
            if(StringUtils.isNotBlank(basicDetailTO.getRegisteredAddress())) {
                String enTxt = translaterService.getEnglishText(basicDetailTO.getRegisteredAddress(), corpInfoTO.getCorpKeyNo(), corpInfoTO.getCorpName(), SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                if(StringUtils.isNotBlank(enTxt) && !StringUtils.equals(enTxt, basicDetailTO.getRegisteredAddress())) {
                    basicDetailTO.setRegisteredAddressEn(enTxt);
                }
            }
            //  注册地址和经营地址一样，就不走翻译接口，直接复制英文名
            if (StringUtils.isNotBlank(basicDetailTO.getAnnualAddress())) {
                if (StringUtils.equals(basicDetailTO.getAnnualAddress(), basicDetailTO.getRegisteredAddress())) {
                    basicDetailTO.setAnnualAddressEn(basicDetailTO.getRegisteredAddressEn());
                } else {
                    // 地址信息不一致，则翻译
                    String enTxt = translaterService.getEnglishText(basicDetailTO.getAnnualAddress(), corpInfoTO.getCorpKeyNo(), corpInfoTO.getCorpName(), SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS);
                    if (StringUtils.isNotBlank(enTxt) && !StringUtils.equals(enTxt, basicDetailTO.getAnnualAddress())) {
                        basicDetailTO.setAnnualAddressEn(enTxt);
                    }
                }
            }
            // updated for v1.7.1 KNZT-3079
            /* else{
                //经营地址信息不存在，则取注册地址信息
                basicDetailTO.setAnnualAddress(basicDetailTO.getRegisteredAddress());
                basicDetailTO.setAnnualAddressEn(basicDetailTO.getRegisteredAddressEn());
            }*/
            corpInfoTO.setListingStatus(basicDetailTO.isListingStatus());
            // updated for v1.3.3 KNZT-1735 工商快照从scanning计数维度接口取
            // added for KNZT-1411 大陆企业解锁之后 获取工商快照图片信息
//            String snapshotImageUrl = CompanyDetailsInterface.getCorpSnapShot4Global(corpInfoTO.getCorpKeyNo());
//            basicDetailTO.setSnapshotImageUrl(snapshotImageUrl);
            // added for KNZT-1416 总公司信息 uppdated for KNZT-1735 如果是分支机构则，给出总公司信息
            if(basicDetailTO.getIsBranch() == 1){
                // updated for v1.7.2 KNZT-3231
                CorpBusinessParentInfoTO corpParentInfo = CompanyDetailsInterface.getCorpParentInfo(corpInfoTO.getCorpKeyNo(), needCompanyKey);
                if (Objects.nonNull(corpParentInfo)) {
                    // 信用代码：creditCode 注册号：regNo 登记状态：enStatus
                    TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN.getNameCode());
                    transWrapper.addEntryOf(corpParentInfo,
                                    CorpBusinessParentInfoTO::setName, CorpBusinessParentInfoTO::setEnglishName,
                                    CorpBusinessParentInfoTO::getName, CorpBusinessParentInfoTO::getEnglishName)
                            .withEntity(CorpBusinessParentInfoTO::getKeyNo, true);
                    if (Objects.nonNull(corpParentInfo.getOper())) {
                        CorpParentOper oper = corpParentInfo.getOper();
                        transWrapper.addEntryOf(oper,
                                        CorpParentOper::setName, CorpParentOper::setEnglishName,
                                        CorpParentOper::getName, CorpParentOper::getEnglishName)
                                .withEntity(CorpParentOper::getKeyNo, true);
                        corpParentInfo.setEnStatus(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(corpParentInfo.getShortStatus()));
                    }
                    commonTransService.enPostProcessor(transWrapper);
                }
                basicDetailTO.setCorpParentInfo(corpParentInfo);
            }
            //added for v1.8.8 KNZT-4024 查询企业行业信息 处理映射
            if (Objects.nonNull(basicDetailTO.getIndustryInfo())) {
                IndustryInfoTO industryInfoTO = basicDetailTO.getIndustryInfo();
                // updated for v2.1.3 fengsw KNZT-6226 企业基本信息维度 中国大陆行业、映射联合国行业处理。必须保证中国大陆行业信息存在，再处理联合国行业
                String industryCode = StringUtils.getNotBlankStr(basicDetailTO.getIndustryInfo().getSmallIndustryCode(), basicDetailTO.getIndustryInfo().getMiddleIndustryCode(), basicDetailTO.getIndustryInfo().getSubIndustryCode(), basicDetailTO.getIndustryInfo().getIndustryCode());
                if (StringUtils.isNotBlank(industryCode)) {
                    List<TblGlobalIndustryQueryResultTO> globalIndustryList = commTblGlobalIndustryService.listMappingIndustryInfoByCnIndCodeWithTypeList(industryCode, IndustryCountryTypeEnum.basicCountryCodeList());
                    if (CollectionUtils.isNotEmpty(globalIndustryList)) {
                        // updated for v2.3.4 fegnsw KNZT-8340
//                        List<TblGlobalIndustryQueryResultTO> targetList = getMappingIndustryList(globalIndustryList);
                        if (CollectionUtils.isNotEmpty(globalIndustryList)) {
                            TblGlobalIndustryQueryResultTO tblGlobalIndustryQueryResultTO = globalIndustryList.stream().filter(k -> IndustryCountryTypeEnum.CN.getCountryCode().equals(k.getCountryType())).findFirst().orElse(null);
                            if (Objects.nonNull(tblGlobalIndustryQueryResultTO)) {
                                industryInfoTO.setIndustry(tblGlobalIndustryQueryResultTO.getInd1Name());
                                industryInfoTO.setIndustryEn(tblGlobalIndustryQueryResultTO.getInd1NameEn());
                                industryInfoTO.setSubIndustry(tblGlobalIndustryQueryResultTO.getInd2Name());
                                industryInfoTO.setSubIndustryEn(tblGlobalIndustryQueryResultTO.getInd2NameEn());
                                industryInfoTO.setMiddleIndustry(tblGlobalIndustryQueryResultTO.getInd3Name());
                                industryInfoTO.setMiddleIndustryEn(tblGlobalIndustryQueryResultTO.getInd3NameEn());
                                industryInfoTO.setSmallIndustry(tblGlobalIndustryQueryResultTO.getInd4Name());
                                industryInfoTO.setSmallIndustryEn(tblGlobalIndustryQueryResultTO.getInd4NameEn());
                                tblGlobalIndustryQueryResultTO = globalIndustryList.stream().filter(k -> IndustryCountryTypeEnum.ISIC.getCountryCode().equals(k.getCountryType())).findFirst().orElse(null);
                                if (Objects.nonNull(tblGlobalIndustryQueryResultTO)) {
                                    industryInfoTO.setIntlIndustry(tblGlobalIndustryQueryResultTO.getInd1Name());
                                    industryInfoTO.setIntlIndustryEn(tblGlobalIndustryQueryResultTO.getInd1NameEn());
                                    industryInfoTO.setIntlIndustryCode(tblGlobalIndustryQueryResultTO.getInd1Code());
                                    industryInfoTO.setIntlSubIndustry(tblGlobalIndustryQueryResultTO.getInd2Name());
                                    industryInfoTO.setIntlSubIndustryEn(tblGlobalIndustryQueryResultTO.getInd2NameEn());
                                    industryInfoTO.setIntlSubIndustryCode(tblGlobalIndustryQueryResultTO.getInd2Code());
                                    industryInfoTO.setIntlMiddleIndustry(tblGlobalIndustryQueryResultTO.getInd3Name());
                                    industryInfoTO.setIntlMiddleIndustryEn(tblGlobalIndustryQueryResultTO.getInd3NameEn());
                                    industryInfoTO.setIntlMiddleIndustryCode(tblGlobalIndustryQueryResultTO.getInd3Code());
                                    industryInfoTO.setIntlSmallIndustry(tblGlobalIndustryQueryResultTO.getInd4Name());
                                    industryInfoTO.setIntlSmallIndustryEn(tblGlobalIndustryQueryResultTO.getInd4NameEn());
                                    industryInfoTO.setIntlSmallIndustryCode(tblGlobalIndustryQueryResultTO.getInd4Code());
                                }
                            }
                        }
                    }
                }
            }
            // added for v1.6.3 KNZT-2826 加密
/*            if (CollectionUtils.isNotEmpty(basicDetailTO.getPartnerList())) {
                for (CorpPartnerTO partnerTO : basicDetailTO.getPartnerList()) {
                    partnerTO.setKeyNo(CommTblCompReportOrderService.generateKeyNoEncrypted(
                            corpOrderId, corpInfoTO.getCorpKeyNo(), partnerTO.getKeyNo()));
                }
            }
            if (CollectionUtils.isNotEmpty(basicDetailTO.getPubPartnerList())) {
                for (CorpPartnerTO partnerTO : basicDetailTO.getPubPartnerList()) {
                    partnerTO.setKeyNo(CommTblCompReportOrderService.generateKeyNoEncrypted(
                            corpOrderId, corpInfoTO.getCorpKeyNo(), partnerTO.getKeyNo()));
                }
            }*/
        }
        corpInfoTO.setBuyBasicDetailTO(basicDetailTO);
        // added for v1.3.3 KNZT-1735 【优化】【国际版】大陆公司：UBO信息和实际控制人单独加载
//        StockResult4UnitTO corpUBO4Unit = new StockResult4UnitTO();
//        if (StringUtils.equalsIgnoreCase("注销",corpShortStatusCn)) {
//            corpUBO4Unit.setRemarkEn(UboRemarkEnum.ZHU_XIAO.getUboRemarkEn());
//            corpUBO4Unit.setTitleType("ZX");
//        }else {
//            corpUBO4Unit = CompanyDetailsInterface.getCorpUBO4Unit(searchKey);
//        }
//        corpInfoTO.setUboDetailTO(corpUBO4Unit);

        // removed for v1.1.6 KNZT-832
//        EnLabelTOResult rygLabel = DdRygLabelService.getRygLabel(corpInfoTO.getCorpKeyNo());
//        corpInfoTO.setRygLabel(rygLabel);
//
//        EnLabelTOResult shellLabel = ShellLabelService.getShellCompanyLabel(corpInfoTO.getCorpKeyNo());
//        corpInfoTO.setShellLabel(shellLabel);
        // added for v1.1.6 KNZT-820
        // added for v1.3.3 KNZT-1735 【优化】【国际版】大陆公司：UBO信息和实际控制人单独加载
//        List<ActualControllerTO> actualControllerList = RelationInterface.getActualControllerV3(corpInfoTO.getCorpKeyNo());
//        corpInfoTO.setActualControllerList(actualControllerList);
    }

    public void populateOrderByCorpDetail(CorpInfoTO target, CorpBasicDetail4UnitTO source) {
        if(source != null) {
            if(StringUtils.isBlank(target.getCorpNumber())) {
                target.setCorpNumber(source.getCreditCode());
            }
            target.setImageUrl(source.getImageUrl());//added for v1.0.6 KNZT-323
//            target.setCorpDistrict(source.getAddress());
//            target.setCorpDateOfReg(source.getStartDate());
            target.setCorpJurisdiction(FuzzySearchInterface.getJurisdictionByKeyNo(target.getCorpKeyNo())); // updated for v6.7.2 KNZT-84
            target.setShardHolderTitleType(source.getShardHolderTitleType());
            // added for v1.2.9 KNZT-1462
            target.setOperType(source.getOperType());
            target.setOperTypeEn(source.getOperTypeEn());
            // added for v1.5.0 KNZT-1523 从接口获取企业名称
            target.setCorpName(source.getCompanyNameInLocalLanguage());
            target.setCorpNameEn(StringUtils.upperCase(source.getCompanyName()));
        }
    }

    public void populateOrderInfoByCorpDetail(OrderSubmitTO target, CheckCanOrderResult checkCanOrderResult, TblReportChargeUnit rptUnit) throws MessageException {
        target.setReportType(rptUnit.getReportType());
        target.setReportGroup(rptUnit.getReportGroup());
        if (UnitGroupEnum.SG_UNIT.getGroup().equals(rptUnit.getUnitGroup())) {
            SgBasicTO sgBasicTO = Objects.isNull(checkCanOrderResult.getSgDetail()) ?
                    sgReportOrderCustomService.getBasic(target.getCorpKeyNo()) : checkCanOrderResult.getSgDetail();
            target.setCorpNumber(MappingUtils.getValue(sgBasicTO, SgBasicTO::getCompNo));
            target.setCorpStatusEn(MappingUtils.getValue(sgBasicTO, SgBasicTO::getCompStatusLabel));
            target.setCorpDistrict(MappingUtils.getValue(sgBasicTO, SgBasicTO::getContactAddress));
            target.setCorpDateOfReg(MappingUtils.getValue(sgBasicTO, SgBasicTO::getCompStartDate));
            target.setCorpJurisdiction(Constants.JURISDICTION.SG);
        } else if (UnitGroupEnum.HK_UNIT.getGroup().equals(rptUnit.getUnitGroup())) { // // updated for lvcy v2.0.2 KNZT-5255
            HkCorpBasicDetailTO hkDetail = Objects.isNull(checkCanOrderResult.getHkDetail()) ?
                    qccOvsBasicService.getHkCorpBasicDetailTO(target.getCorpKeyNo()) : checkCanOrderResult.getHkDetail();

            if (Objects.nonNull(hkDetail)) {
                if (StringUtils.isBlank(target.getCorpNumber())) {
                    target.setCorpNumber(hkDetail.getCompanyNumber());
                    HkStatusEnum statusEnumByStatus = HkStatusEnum.getStatusEnumByStatus(hkDetail.getStatus());
                    if (statusEnumByStatus != null) {
                        target.setCorpShortStatusCn(statusEnumByStatus.getStatusCn());
                        target.setCorpStatusEn(statusEnumByStatus.getStatusEn());
                    } else {
                        target.setCorpShortStatusCn(hkDetail.getStatus());
                    }
                    target.setCorpDistrict(hkDetail.getContactAddress());
                    target.setCorpDateOfReg(hkDetail.getRegistrationDate());
                    target.setCorpJurisdiction(Constants.JURISDICTION.HK); // updated for v6.7.2 KNZT-84
                }
            }
        } else {
            String searchKey = StringUtils.getNotBlankStr(target.getCorpKeyNo(), target.getCorpNumber(), target.getCorpName(), target.getCorpNameEn());
            ApiGlobalCorpDetailTO source = Objects.isNull(checkCanOrderResult.getCnDetail()) ?
                    CompanyDetailsInterface.getECIInfoVerifyInfo4Global(searchKey) : checkCanOrderResult.getCnDetail();
            if(source != null) {
                if(StringUtils.isBlank(target.getCorpNumber())) {
                    target.setCorpNumber(source.getCreditCode());
                }
                /**
                // updated for v1.8.7 KNZT-3987 后端获取状态
//                target.setCorpStatus(orderTO.getCorpStatusEn()); // updated for v1.8.6 KNZT-3927
//                if(StringUtils.isBlank(target.getCorpStatusCheck())) {
//                    target.setCorpStatusCheck(source.getStatus());
//                }
                // updated for v1.9.1 KNZT-4295 香港状态用status
                if (Constants.Report.REPORT_GROUP_KYB_HK.equals(target.getReportGroup())) {
                    HkStatusEnum statusEnumByStatus = HkStatusEnum.getStatusEnumByStatus(source.getStatus());
                    if (statusEnumByStatus != null) {
                        target.setCorpShortStatusCn(statusEnumByStatus.getStatusCn());
                        target.setCorpStatusEn(statusEnumByStatus.getStatusEn());
                    } else {
                        target.setCorpShortStatusCn(source.getStatus());
                    }
                } else {
                    target.setCorpShortStatusCn(source.getShortStatus());
                    target.setCorpStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(target.getCorpShortStatusCn()));
                }  removed for v2.0.2 KNZT-5255*/
                target.setCorpShortStatusCn(source.getShortStatus());
                target.setCorpStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(target.getCorpShortStatusCn()));
                target.setCorpDistrict(source.getAddress());
                target.setCorpDateOfReg(source.getStartDate());
                target.setCorpJurisdiction(Constants.JURISDICTION.CHINA_MAINLAND); // updated for v6.7.2 KNZT-84
            }
        }
    }


    /**
     * 批量校验购物车订单数据
     * added for lvcy v1.9.9 KNZT-4980
     *
     * @param orderSubmitTOList
     * @return Order4CartValidateResult
     */
    public Order4CartValidateResult validateOrderInfo4Cart(List<OrderSubmitTO> orderSubmitTOList) throws MessageException {
        try {
            List<TblReportChargeUnit> chargeUnitList = commTblReportChargeUnitService.listEnableChargeUnitByCompany(UserUtils.getUserCompanyId());
            Map<String, TblReportChargeUnit> chargeUnitMap = chargeUnitList.stream().collect(Collectors.toMap(TblReportChargeUnit::getReportType, Function.identity(), (v1, v2) -> v1));

            boolean notContainsReportType = orderSubmitTOList.stream().anyMatch(orderTO -> !chargeUnitMap.containsKey(orderTO.getReportType()));
            MsgExceptionUtils.failBuild(notContainsReportType, "err.account.no.service");

            // 校验公司注册处是否维护
            boolean existHkOrder = orderSubmitTOList.stream().anyMatch(k -> ReportTypeEnum.hkCorpList().contains(k.getReportType()));
            if (existHkOrder) {
                // 校验查册人信息完整 added for v2.0.0 chenbl KNZT-5094
                boolean validateHkSearcherInfo = commSysCompanyService.validateHkSearcherInfo(UserUtils.getUserCompanyId());
                if (!validateHkSearcherInfo) {
                    throw new MessageException(Constants.MessageExceptionKey.HK_SEARCHER_INFO_MISSING);
                }
                OverseaMaintainStatusTO overseaTainStatus4Hk = GlobalCompanyDetailsInterface.getOverseaTainStatus4Hk();
                Boolean maintain = Optional.ofNullable(overseaTainStatus4Hk).map(OverseaMaintainStatusTO::getMainTain).orElse(true);
                MsgExceptionUtils.failBuild(maintain, "err.hk.maintenance.cart");
            }

            // 兜底校验是否可以下单
            for (OrderSubmitTO orderSubmitTO : orderSubmitTOList) {
                checkCanOrder(orderSubmitTO.getCorpKeyNo(), chargeUnitMap.get(orderSubmitTO.getReportType()), false);
            }


            return new Order4CartValidateResult(chargeUnitMap);
        } catch (MessageException e) {
            if (StringUtils.equals("err.hk.maintenance", e.getMessage())) {
                throw new MessageException("err.hk.maintenance.cart");
            }
            throw e;
        }
    }

    /**
     * 校验扫描订单信息
     * added for lvcy v2.0.9 KNZT-5362
     * @param form
     * @return
     * @throws MessageException
     */
    @Deprecated // 待旧版下线后可移除
    public OrderValidateResult validateScanOrderInfo(ScanOrderSubmitForm form) throws MessageException {
        TblReportChargeUnit rptUnit = commTblReportChargeUnitService.getChargeUnitByUniqueKey(UserUtils.getUserCompanyId(),
            ReportGroupMappingEnum.SCAN_G.getRptGroup(), ReportTypeEnum.SCAN.getCode());
        MsgExceptionUtils.failBuild(Objects.isNull(rptUnit) || Constants.NO.equals(rptUnit.getEnabled()), "err.account.no.service");

        Company company = commSysCompanyService.get(UserUtils.getUserCompanyId());

        // 非C端客户校验额度
        if (!CompTypeEnum.isSelfPay(company.getType())) {
            checkFuncCount(rptUnit.getUnit(), false);
        }

        if (StringUtils.isNotBlank(form.getEntityCountryCode())) {
            GlobalAreaEnum areaEnum = GlobalAreaEnum.getByNameCode(form.getEntityCountryCode());
            MsgExceptionUtils.checkIsNull(areaEnum, "err.param.invalid");
        }

        // 人员参数校验
        if (Constants.Report.KEY_NO_TYPE_PERS.equals(form.getEntityType())) {
            // 校验姓名必填规则:要么填First Name + Last Name,要么填Script Name
            boolean hasFirstLastName = StringUtils.isNotBlank(form.getPersFirstName()) && StringUtils.isNotBlank(form.getPersLastName());
            boolean hasScriptName = StringUtils.isNotBlank(form.getPersScriptName());
            MsgExceptionUtils.failBuild(!hasFirstLastName && !hasScriptName,
                "msg:Please provide either First Name and Last Name, or Script Name");

            // 校验名字长度和格式
            if (StringUtils.isNotBlank(form.getPersFirstName())) {
                MsgExceptionUtils.failBuild(form.getPersFirstName().length() > 200, "msg:First Name cannot exceed 200 characters");
            }
            if (StringUtils.isNotBlank(form.getPersMiddleName())) {
                MsgExceptionUtils.failBuild(form.getPersMiddleName().length() > 200, "msg:Middle Name cannot exceed 200 characters");
            }
            if (StringUtils.isNotBlank(form.getPersLastName())) {
                MsgExceptionUtils.failBuild(form.getPersLastName().length() > 200, "msg:Last Name cannot exceed 200 characters");
            }
            if (StringUtils.isNotBlank(form.getPersScriptName())) {
                MsgExceptionUtils.failBuild(form.getPersScriptName().length() > 200, "msg:Script Name cannot exceed 200 characters");
            }

            // 校验生日格式
            if (Objects.nonNull(form.getPersBirthday())) {
                validateBirthday(form.getPersBirthday());
            }

            // 校验性别 Gender
            if (StringUtils.isNotBlank(form.getPersGender())) {
                GenderEnum genderEnum = GenderEnum.getByCode(form.getPersGender());
                MsgExceptionUtils.checkIsNull(genderEnum, "msg:Invalid gender value. Must be M or F");
            }

            // 校验个人ID信息 (National ID, Passport, etc.)
            if (StringUtils.isNotBlank(form.getPersNo())) {
                MsgExceptionUtils.failBuild(form.getPersNo().length() > 200,
                    "msg:ID No. cannot exceed 200 characters");
            }

            // 校验关联组织名称
            if (StringUtils.isNotBlank(form.getPersOrgScriptName())) {
                MsgExceptionUtils.failBuild(form.getPersOrgScriptName().length() > 200,
                    "Related Organization Name cannot exceed 200 characters");
            }

            // 校验关联组织注册号
            if (StringUtils.isNotBlank(form.getPersOrgNo())) {
                MsgExceptionUtils.failBuild(form.getPersOrgNo().length() > 200,
                    "msg:Registration Number of Associated Organization cannot exceed 200 characters");
            }
        } else if (Constants.Report.KEY_NO_TYPE_CORP.equals(form.getEntityType())){ // 企业参数校验
            // 企业名称必填一个
            MsgExceptionUtils.checkIsNull(StringUtils.isBlank(form.getOrgName()) && StringUtils.isBlank(form.getOrgScriptName()), "Organization name is required");
            // 企业参数校验
            if (StringUtils.isNotBlank(form.getOrgName())) {
                MsgExceptionUtils.failBuild(form.getOrgName().length() > 200, "msg:Organization name cannot exceed 200 characters");
            }
            if (StringUtils.isNotBlank(form.getOrgScriptName())) {
                MsgExceptionUtils.failBuild(form.getOrgScriptName().length() > 200, "msg:Organization Script Name cannot exceed 200 characters");
            }
            // 校验注册号
            if (StringUtils.isNotBlank(form.getOrgNo())) {
                MsgExceptionUtils.failBuild(form.getOrgNo().length() > 200, "msg:Registration Number cannot exceed 200 characters");
            }
        } else {
            throw new MessageException("err.param.invalid");
        }

        return OrderValidateResult.build(rptUnit);
    }

    /**
     * 校验生日格式
     * 1. 年份格式 YYYY
     * 2. 完整日期格式 YYYYMMDD
     * added for lvcy v2.0.9 KNZT-5362
     *
     * @param birthday
     * @throws MessageException
     */
    @Deprecated // 待旧版下线后可移除
    private void validateBirthday(Integer birthday) throws MessageException {
        String birthdayStr = String.valueOf(birthday);

        // 校验年份格式 YYYY
        if (birthdayStr.length() == 4) {
            int year = Integer.parseInt(birthdayStr);
            MsgExceptionUtils.failBuild(year > Calendar.getInstance().get(Calendar.YEAR), "msg:Please enter the correct format");
            return;
        }

        // 校验完整日期格式 YYYYMMDD
        if (birthdayStr.length() == 8) {
            try {
                int year = Integer.parseInt(birthdayStr.substring(0,4));
                int month = Integer.parseInt(birthdayStr.substring(4,6));
                int day = Integer.parseInt(birthdayStr.substring(6));

                Calendar cal = Calendar.getInstance();
                cal.setLenient(false);
                cal.set(year, month-1, day);
                cal.getTime(); // 会抛出异常如果日期无效
                return;
            } catch (Exception e) {
                logger.info("birthday format failed, str:{}, ex:{}", birthday, e);
                throw new MessageException("msg:Please enter the correct format");
            }
        }

        throw new MessageException("msg:Please enter the correct format");
    }

    /**
     * 校验香港、新加坡企业能否下单
     * 香港：1.校验公司注册处是否维护；2.校验公司类型不是有限合伙基金、不是有限责任合伙、不是开放型公司基金；3.校验是否能下单ar
     * 新加坡：1.校验公司不能购买；2.校验公司信息未纰漏
     * @param corpKeyNo
     * @param rptUnit
     * @return
     */
    public CheckCanOrderResult checkCanOrder(String corpKeyNo, TblReportChargeUnit rptUnit, boolean needCheckHkMaintainOrSearcherInfo) throws MessageException {
        // updated for v1.9.5 KNZT-4519
        if (UnitGroupEnum.HK_UNIT.getGroup().equals(rptUnit.getUnitGroup())) {
            // removed for lvcy v1.9.9 KNZT-4980
            /*HKInfoBeforeSubmitTO hkInfoBeforeSubmit = commTblCompReportOrderService.getHKInfoBeforeSubmit(corpKeyNo);
            Boolean canOrder = Optional.ofNullable(hkInfoBeforeSubmit).map(HKInfoBeforeSubmitTO::getCanOrder).orElse(false);
            if (!canOrder) {
                throw new MessageException("err.hk.maintenance");
            }
            if (ReportTypeEnum.HK_BAS_AR.getCode().equals(rptUnit.getReportType())) { // added for v1.7.8 KNZT-3493
                Boolean canOrderBasicAr = Optional.ofNullable(hkInfoBeforeSubmit).map(HKInfoBeforeSubmitTO::getCanOrderBasicAr).orElse(false);
                if (!canOrderBasicAr) {
                    throw new MessageException("err.access");
                }
            }*/
            // 校验香港查册人信息完整 added for v2.0.0 chenbl KNZT-5094
            if (needCheckHkMaintainOrSearcherInfo) {
                boolean validateHkSearcherInfo = commSysCompanyService.validateHkSearcherInfo(UserUtils.getUserCompanyId());
                if (!validateHkSearcherInfo) {
                    throw new MessageException(Constants.MessageExceptionKey.HK_SEARCHER_INFO_MISSING);
                }
            }
            // update for lvcy v1.9.9 KNZT-4980
            HkCorpBasicDetailTO detail = qccOvsBasicService.getHkCorpBasicDetailTO(corpKeyNo);
            MsgExceptionUtils.checkIsNull(detail);

            // 校验公司注册处是否维护
            if (needCheckHkMaintainOrSearcherInfo) {
                OverseaMaintainStatusTO overseaTainStatus4Hk = GlobalCompanyDetailsInterface.getOverseaTainStatus4Hk();
                Boolean maintain = Optional.ofNullable(overseaTainStatus4Hk).map(OverseaMaintainStatusTO::getMainTain).orElse(true);
                MsgExceptionUtils.failBuild(maintain, "err.hk.maintenance");
            }

            // 公司是否支持下单
            JsonResultList<HongKongAnnouncementTO> hkAnnouncementPage = IntranetInterface.getHkAnnouncementPage(corpKeyNo, "1", "1000");
            List<HongKongAnnouncementTO> hkAnnouncements = hkAnnouncementPage.getResultList();
            boolean companyCanOrder = companyCanOrder(hkAnnouncements, detail);
            MsgExceptionUtils.failBuild(!companyCanOrder, "err.hk.maintenance");

            // 校验是否可以下单basic+ar
            if (ReportTypeEnum.HK_BAS_AR.getCode().equals(rptUnit.getReportType())) { // added for v1.7.8 KNZT-3493
                Set<String> anTypeList = IntranetInterface.listAnnualOrApplicationType(hkAnnouncements);
                logger.info(String.format("keyNo:%s, 公告类型有：%s", corpKeyNo, StringUtils.join(anTypeList, ",")));
                // 不是【注册非香港公司】
                MsgExceptionUtils.checkIsNull(detail.getCompanyTypeEn());
                MsgExceptionUtils.failBuild(HkCompanyTypeEnum.COMPANY_TYPE7.getCompanyTypeEn().equals(detail.getCompanyTypeEn()));
                // 存在周年申报表
                MsgExceptionUtils.checkIsNull(anTypeList);
            }
            return new CheckCanOrderResult(detail);
        } else if (Constants.Report.REPORT_TYPE_CORP_SG_BAS_DTL.equals(rptUnit.getReportType())) {//added v1.3.9 KNZT-2177
            SgBasicTO sgBasic = sgReportOrderCustomService.getBasic(corpKeyNo);
            if (sgBasic == null || sgBasic.getCanBuyInGeneral() == null || !sgBasic.getCanBuyInGeneral()) {
                throw new MessageException("err.sg.forbid");
            }
            return new CheckCanOrderResult(sgBasic);
        } else if (UnitGroupEnum.CN_UNIT.getGroup().equals(rptUnit.getUnitGroup()) && Constants.Report.REPORT_GROUP_CORP_G.equals(rptUnit.getReportGroup())) {
            // 增加财税报告校验 added for lvcy v2.0.2 KNZT-5131
            ApiGlobalCorpDetailTO source = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(corpKeyNo);
            MsgExceptionUtils.checkIsNull(source);
            if (ReportTypeEnum.FIN_TAX.getCode().equals(rptUnit.getReportType())) {
                MsgExceptionUtils.failBuild(!StringUtils.equals(source.getEntType(), "General"), "err.fin.tax.forbid");
                MsgExceptionUtils.checkIsNull(source.getCreditCode(), "err.fin.tax.forbid");
                boolean existProcessingFinTaxOrder = this.existProcessingFinTaxOrder(UserUtils.getUserCompanyId(), corpKeyNo);
                MsgExceptionUtils.failBuild(existProcessingFinTaxOrder, "err.fin.tax.processing");
            }
            return new CheckCanOrderResult(source);
        }
        return new CheckCanOrderResult();
    }


    public CheckCanOrderResult checkCanOrder(String corpKeyNo, TblReportChargeUnit rptUnit) throws MessageException {
        return this.checkCanOrder(corpKeyNo, rptUnit, true);
    }

    /**
     * 是否存在流程中的财税报告订单
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param companyId
     * @param keyNo
     * @return boolean
     */
    private boolean existProcessingFinTaxOrder(String companyId, String keyNo) {
        TblCompReportOrderCondition condition = new TblCompReportOrderCondition();
        condition.setKeyNo(keyNo);
        condition.setReportTypeList(Lists.newArrayList(ReportTypeEnum.FIN_TAX.getCode()));
        condition.setRptStatusList(OrderStatusEnum.getProcessingStatus());
        condition.getPage().setPageSize(1);
        List<TblCompReportOrder> orderList = dao.listReportByCondition(companyId, condition);
        return CollectionUtils.isNotEmpty(orderList);
    }

    /**
     * 校验订单提交参数
     * added for lvcy v1.9.9 KNZT-4980
     *
     * @param orderTO
     * @return
     */
    private void checkOrderSubmitParam(OrderSubmitTO orderTO) throws MessageException {
        String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(orderTO.getReportGroup());
        if (Constants.Report.KEY_NO_TYPE_CORP.equals(keyNoType)) {
            MsgExceptionUtils.checkIsNull(orderTO.getCorpKeyNo());
        } else {
            MsgExceptionUtils.checkIsNull(orderTO.getPersKeyNo());
            MsgExceptionUtils.checkIsNull(orderTO.getPersName());
        }
    }

    /**
     * 校验额度
     * added for v1.9.3 KNZT-4193
     * updated for v1.9.8 KNZT-4538
     * updated for v2.0.2 chenbl KNZT-5294
     *
     * @param totalUnit4Orders
     * @param needCheck4ActualCost 是否需要校验实际成本
     * @return boolean
     */
    public SysCompInfoFuncCount checkFuncCount(BigDecimal totalUnit4Orders, boolean needCheck4ActualCost) throws MessageException {
        return this.checkFuncCount(totalUnit4Orders, null, needCheck4ActualCost);
    }

    /**
     * updated for v1.9.7 4708
     * updated for v1.9.8 KNZT-4538
     * updated for v2.0.2 chenbl KNZT-5294
     * 系统内部调用 companyId不用传递
     * 对外接口服务时，companyId必传
     *
     * @param totalUnit4Orders
     * @param companyId
     * @param needCheck4ActualCost 是否需要校验实际成本
     * @throws MessageException
     */
    public SysCompInfoFuncCount checkFuncCount(BigDecimal totalUnit4Orders, String companyId, boolean needCheck4ActualCost) throws MessageException {
        if (StringUtils.isBlank(companyId)) {
            companyId = UserUtils.getUserCompanyId();
        }

        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount, "err.amount.insufficient");

        if (BigDecimal.ZERO.compareTo(totalUnit4Orders) >= 0) {
            return funcCount;
        }
        // updated for v1.9.8 KNZT-4538
        // 香港服务的额度校验放开，交由开放平台香港接口下单校验
        BigDecimal remainingBalance = funcCount.calRemainCount();
        MsgExceptionUtils.failBuild(totalUnit4Orders.compareTo(remainingBalance) > 0, "err.amount.insufficient");
        // removed for v1.9.8 KNZT-4538
        /*
        // added for v1.8.8 KNZT-3324 签约账户增加产品账户额度校验
        if (CompTypeEnum.SIGN.getCode().equals(companyType)) {
            BigDecimal accRemainingBalance = prodAccService.summarizeProdAccRemainingUnit(companyId, functionTableId);
            remainingBalance = remainingBalance.compareTo(accRemainingBalance) > 0 ? accRemainingBalance : remainingBalance;
        }
        */
        if (needCheck4ActualCost) {
            boolean check4ActualCost = check4ActualCost(companyId);// 限制成本下单次数校验 added for v1.9.8 KNZT-4538
            if (!check4ActualCost) {
                logger.warn("成本下单次数不足, companyId: " + companyId);
                throw new MessageException("err.amount.insufficient");
            }
        }

        // 用户额度超出限额校验
        String userId = UserUtils.getUserId();
        if (StringUtils.isNotBlank(userId)) {
            commTblUserQuotaLimitService.checkQuotaLimit(companyId, userId, totalUnit4Orders);
        }
        return funcCount;
    }

    /**
     * 判断是否需要校验成本下单次数
     * reportChargeUnit和apiChargeUnits传一个即可
     *
     * @param companyId
     * @param reportChargeUnit
     * @param apiChargeUnits
     * @return
     * @throws MessageException
     */
    public boolean needCheck4ActualCost(String companyId, TblReportChargeUnit reportChargeUnit, List<TblCompApiChargeUnit> apiChargeUnits) throws MessageException {
        Company company = commSysCompanyService.get(companyId);
        if (company == null) {
            logger.error("company is null, companyId: " + companyId);
            throw new MessageException("err.access");
        }
        if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            return reportChargeUnit != null && ReportTypeEnum.actualCostTypeList().contains(reportChargeUnit.getReportType());
        }
        return false;
    }

    // added for v1.9.8 KNZT-4538
    // updated for v2.0.2 chenbl KNZT-5294
    // 试用账号校验境外下单次数
    private boolean check4ActualCost(String companyId) throws MessageException {
        String overseasOrderLimitStr = dictService.getDictValueByTypeLabel(companyId, Constants.DictType.OVERSEAS_ORDER_LIMIT, "0");
        try {
            int overseasOrderLimit = Integer.parseInt(overseasOrderLimitStr);
            TblCompReportOrderCondition condition = new TblCompReportOrderCondition();
            condition.setReportTypeList(ReportTypeEnum.actualCostTypeList());
            condition.setExcludeInvalid(1);
            int count = readOnlyCountByCompanyId(companyId, condition);
            if (count >= overseasOrderLimit) {
                return false;
            }
        } catch (Exception e) {
            logger.error(String.format("字典项配置错误, type: %s, label: %s", Constants.DictType.OVERSEAS_ORDER_LIMIT, companyId));
            throw new MessageException("err.access");
        }
        return true;
    }



    /**
     * 创建扫描订单支付意向
     * added for lvcy v2.0.9 KNZT-5362
     * @param form
     * @return
     */
    public OrderSaveResult createScanOrderPaymentIntent(ScanOrderSubmitForm form) throws MessageException {
        OrderValidateResult validateResult = validateScanOrderInfo(form);
        PaymentIntent paymentIntent = null;
        try {
            TblCompReportOrder order = saveScanOrder(form, validateResult, PayTypeEnum.ONLINE);
            paymentIntent = StripePaymentInterface.createPaymentIntent4Order(order, order.getTotalUnit(), contractDeliveryService.generateTopUpNo());
            MsgExceptionUtils.checkIsNull(paymentIntent, "err.access");
            order.setPayRelId(paymentIntent.getId());
            order.setPayStatus(PayStatusEnum.UNPAID.getCode());
            this.save(order);
            // stripe 超时取消
            redisService.addDelayedCommTask(CommDelayedTaskTypeEnum.PAYMENT_INTENT_CANCEL, paymentIntent.getId(), 30 * 60);
            // 保存操作IP日志 added for v1.9.6 KNZT-4642
//            commTblCompOpIpLogService.saveIpLog(OpIpLogTypeEnum.ORDER.getCode(), order.getId());
            return OrderSaveResult.build4OrderPaymentIntent(order.getId(), paymentIntent.getClientSecret(), paymentIntent.getId());
        } catch (Exception e) {
            logger.error("saveOrderAndCreatePaymentIntent error, ", e);
            if (Objects.nonNull(paymentIntent)) {
                paymentIntent = StripePaymentInterface.cancelPaymentIntent(paymentIntent);
                logger.info("PaymentIntent cancel,paymentIntent:{}", JSONObject.toJSONString(paymentIntent));
            }
            throw e;
        }
    }


    /**
     * 保存,提交扫描订单
     * added for lvcy v2.0.9 KNZT-5362
     * @param form
     * @return
     */
    public OrderSaveResult saveAndSubmitScanOrder(ScanOrderSubmitForm form) throws MessageException {
        OrderValidateResult validateResult = validateScanOrderInfo(form);
        TblCompReportOrder order = saveScanOrder(form, validateResult, PayTypeEnum.UNIT);
        // 扣费
        transactionBusinessService.payOrderAndCreateTransaction(order, validateResult.getReportChargeUnit().getContractDeliveryId());
        OrderSaveResult orderSaveResult = executeAfterOrderPaid(order);
        // 保存操作IP日志 added for v1.9.6 KNZT-4642
//        commTblCompOpIpLogService.saveIpLog(OpIpLogTypeEnum.ORDER.getCode(), orderSaveResult.getOrderId());
        return orderSaveResult;
    }



    /**
     * 提交订单，生成报告，相关记录落存
     * added for v1.9.3 KNZT-4193
     *
     * @param order
     * @return OrderSaveResult
     */
    public OrderSaveResult executeAfterOrderPaid(TblCompReportOrder order) throws MessageException {
        logger.info("executeAfterOrderPaid begin, orderId:{}", order.getId());

        String reportType = order.getReportType();
        if (ReportTypeEnum.FIN_TAX.getCode().equals(reportType)) {
            order.transitionStatus(OrderStatusEnum.ACTION_PENDING);
            finTaxService.save(TblCompReportOrderFinTax.build(order)); // 保存财税报告记录
        } else if (ReportTypeEnum.getMapList().contains(reportType)) {
            boolean needBuy = ReportTypeEnum.apiOrderTypeList().contains(reportType);
            order.transitionStatus(needBuy ? OrderStatusEnum.DATA_PENDING : OrderStatusEnum.SUCCESS);
        } else if (ReportTypeEnum.getVerifyList().contains(reportType)) {
            verify(order);
            order.transitionStatus(OrderStatusEnum.DATA_PENDING);
        } else {
            order.transitionStatus(OrderStatusEnum.DATA_PENDING);
        }
        this.save(order);

        // 付款成功后要执行的步骤
        if (ReportTypeEnum.PERS_360.getCode().equals(reportType)) {
            ProOutboundSyncInterface.saveOutBoundInfo(order.getKeyNoType(), order.getKeyNo(), order.getPersName(), order.getPersNameEn());
        } else if (ReportTypeEnum.CORP_360.getCode().equals(reportType)) {
            ProOutboundSyncInterface.saveOutBoundInfo(order.getKeyNoType(), order.getKeyNo(), order.getCorpName(), order.getCorpNameEn());
        } else if (ReportTypeEnum.CORP_360_DELAY.getCode().equals(reportType)) {
            // 72小时——96小时之间, 取一个随机数 延迟
            int randomSec = new Random().nextInt(24 * 60 * 60) + 72 * 60 * 60;
            logger.info("CORP_360_DELAY , orderId:{}, randomSec:{}", order.getId(), randomSec);
            redisService.addDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.CORP_360_DELAY.getCode(), order.getId()), randomSec);
        }

        // 出镜记录,统计额度
        commTblGlobalOutboundListSyncNewTranService.newTranSaveOutboundListSync(order);


        OrderSaveResult result = new OrderSaveResult();
        result.setOrderId(order.getId());
        result.setDataToken(generateDataToken(order.getId(), order.getKeyNo()));

        // 添加订单状态检查延时任务
        addStatusCheckDelayedTaskIfNecessary(order);

        // 保存操作IP日志 updated for v2.1.8 chenbl KNZT-6936
        if (PayTypeEnum.getContainsOnlineList().contains(order.getPayType())) {
            OpLogUtils.saveOpLog4PaymentWebhook(OpIpLogTypeEnum.ORDER, order.getPayRelId(), order.getId());
        } else {
            OpLogUtils.saveOpLog(order.getCompanyId(), order.getUserId(), OpIpLogTypeEnum.ORDER, order.getId());
        }

        // 事务提交后异步执行
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                threadPoolService.execute("pool.report.status.update", 1, new ExecuteAfterSubmitOrderRunnable(order.getId()));
            }
        });
        logger.info("executeAfterOrderPaid end, orderId:{}", order.getId());
        return result;
    }

    // added for v1.9.8 KNZT-4976 添加订单状态检查延时任务
    private void addStatusCheckDelayedTaskIfNecessary(TblCompReportOrder order) {
        Map<String, Integer> reportType2StatusCheckDelayedSecMap = ReportTypeEnum.getReportType2StatusCheckDelayedSecMap();
        if (reportType2StatusCheckDelayedSecMap.containsKey(order.getReportType())) {
            redisService.addDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.ORDER_STATUS_CHECK.getCode(), order.getId()), reportType2StatusCheckDelayedSecMap.get(order.getReportType()));
        }

        if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType())) {
            String validHourStr = ConfigUtils.getConfigValueByTypeAndKey("fin_tax", "order_valid_hour", "168");
            int validHour = NumberUtils.getInt(validHourStr, 168);
            redisService.addDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.FIN_TAX_REFUND_ORDER_CHECK.getCode(), order.getId()), validHour * 60 * 60);
        }
    }


    public TblCompReportOrder buildOrderFromParam(OrderSubmitTO orderTO, TblReportChargeUnit rptUnit) {
        // 构建订单信息
        String userId = UserUtils.getUserId();

        TblCompReportOrder rptOrder = new TblCompReportOrder();
        rptOrder.setOrderNo(generateOrderNo(rptUnit.getReportType()));// updated for v2.1.6 fengsw KNZT-6513
        rptOrder.setLoginName(UserUtils.getUserLoginName());
        String keyNoType = ReportGroupMappingEnum.getKeyNoTypeByReportGroup(rptUnit.getReportGroup());
        if (StringUtils.isBlank(keyNoType)) {
            keyNoType = StringUtils.isNotBlank(orderTO.getPersKeyNo()) ? "P" : "C";
        }
        rptOrder.setKeyNoType(keyNoType);
        if (Constants.Report.KEY_NO_TYPE_CORP.equals(rptOrder.getKeyNoType())) {
            rptOrder.setKeyNo(orderTO.getCorpKeyNo());
        } else {
            rptOrder.setKeyNo(orderTO.getPersKeyNo());
            rptOrder.setPersName(orderTO.getPersName());
            rptOrder.setPersNameEn(orderTO.getPersNameEn());
        }
        rptOrder.setUserId(userId);
        rptOrder.setCompanyId(UserUtils.getUserCompanyId());
        rptOrder.setReportGroup(rptUnit.getReportGroup());
        rptOrder.setReportType(rptUnit.getReportType());
        rptOrder.setReportName(rptUnit.getReportName());
        rptOrder.setRptStatus(OrderStatusEnum.NONE.getCode());
        if (Boolean.TRUE.equals(orderTO.getUseBenefit())) {
            rptOrder.setUnit(BigDecimal.ZERO);
            rptOrder.setTotalUnit(BigDecimal.ZERO);
        } else {
            rptOrder.setUnit(rptUnit.getUnit());
            rptOrder.setTotalUnit(rptUnit.getUnit());
        }
        rptOrder.setCorpKeyNo(orderTO.getCorpKeyNo());
        rptOrder.setCorpName(orderTO.getCorpName());
        rptOrder.setCorpNameEn(orderTO.getCorpNameEn());
        rptOrder.setCorpNumber(orderTO.getCorpNumber());
        // updated for v1.8.7 KNZT-3987 后端获取状态
        rptOrder.setUnitGroup(rptUnit.getUnitGroup());
        rptOrder.setCorpJurisdiction(UnitGroupEnum.getNameByUnitGroup(rptOrder.getUnitGroup()));
        rptOrder.setDataResource(Constants.OrderDataResource.GLOBAL);

        rptOrder.setCorpDistrict(orderTO.getCorpDistrict());
        rptOrder.setCorpDateOfReg(orderTO.getCorpDateOfReg());
        rptOrder.setCorpStatus(orderTO.getCorpStatusEn());
        rptOrder.setCorpStatusCheck(orderTO.getCorpShortStatusCn());
        if (UserUtils.isSelf()) {
            rptOrder.setPayType(PayTypeEnum.ONLINE.getCode());
        } else {
            rptOrder.setPayType(PayTypeEnum.UNIT.getCode());
        }
        rptOrder.setPayStatus(PayStatusEnum.UNPAID.getCode());
        if (ReportTypeEnum.getMapList().contains(rptOrder.getReportType())) {
            if (StringUtils.isNotBlank(orderTO.getMapNo()) && StringUtils.isNotBlank(orderTO.getMapNodeNo())) {
                rptOrder.setExtraInfo1(orderTO.getMapNo() + ":" + orderTO.getMapNodeNo());
            }
        } else if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(rptOrder.getReportType())) {
            rptOrder.setExtraInfo1(orderTO.getHkDocNumber());
        } else if (ReportTypeEnum.MY_BASIC.getCode().equals(rptOrder.getReportType())) {
            rptOrder.setExtraInfo1(orderTO.getRegNo());
        } else if (ReportTypeEnum.SG_FIN.getCode().equals(rptOrder.getReportType())) {
            rptOrder.setExtraInfo1(orderTO.getSgFinYear());
        } else if (ReportTypeEnum.SG_BAS.getCode().equals(rptOrder.getReportType())) {
            rptOrder.setDataVersion(Constants.Order.SG_DATA_VERSION);
        }
        return rptOrder;
    }

    /**
     * 保存扫描订单
     * added for lvcy v2.0.9 KNZT-5362
     *
     * @param form
     * @param validateResult
     * @param payType
     * @return
     */
    @Deprecated // 待旧版本下线后可移除
    private TblCompReportOrder saveScanOrder(ScanOrderSubmitForm form, OrderValidateResult validateResult, PayTypeEnum payType) {
        TblReportChargeUnit reportChargeUnit = validateResult.getReportChargeUnit();
        TblCompReportOrder order = new TblCompReportOrder();
        order.setReportType(reportChargeUnit.getReportType());
        order.setReportGroup(reportChargeUnit.getReportGroup());
        order.setReportName(reportChargeUnit.getReportName());
        order.setUnit(reportChargeUnit.getUnit());
        order.setTotalUnit(reportChargeUnit.getUnit());
        order.setLoginName(UserUtils.getUserLoginName());
        order.setUserId(UserUtils.getUserId());
        order.setCompanyId(UserUtils.getUserCompanyId());
        order.setOrderNo(generateOrderNo(order.getReportType()));// updated for v2.1.6 fengsw KNZT-6513
        order.setKeyNoType(form.getEntityType());
        order.setDataResource(Constants.OrderDataResource.GLOBAL);
        order.setRptStatus(OrderStatusEnum.NONE.getCode());
        order.setPayType(payType.getCode());

        if (Constants.Report.KEY_NO_TYPE_PERS.equals(form.getEntityType())) {
            order.setPersName(form.getPersScriptName());
            // 拼接英文名,过滤空值并用空格连接
            String persNameEn = Stream.of(form.getPersFirstName(), form.getPersMiddleName(), form.getPersLastName())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(" "));
            order.setPersNameEn(persNameEn);
        } else {
            order.setCorpName(form.getOrgScriptName());
            order.setCorpNameEn(form.getOrgName());
        }
        this.save(order);

        TblCompReportOrderScan scan = new TblCompReportOrderScan();
        BeanUtils.copyProperties(form, scan);
        scan.setReportOrderNo(order.getOrderNo());
        scan.setCompanyId(UserUtils.getUserCompanyId());
        scan.setUserId(UserUtils.getUserId());


        List<ScanListEnum> scanListEnumList = commTblCompScanListRuleService.getCompScanListEnumList(UserUtils.getUserCompanyId());
        scan.setScanGroupCodeList(ScanListEnum.generateScanListStr(scanListEnumList));

        commTblCompReportOrderScanService.save(scan);
        return order;
    }

    /**
     * added for v1.5.8 KNZT-KNZT-2616 去除企业、人员记录
     *
     * @param dataSave
     * @param order
     * @return
     */
    private static TblCompReportOrder removeCorpPersonField(String dataSave, TblCompReportOrder order) {
        if (StringUtils.equals(CompDataSaveEnum.NO.getCode(), dataSave)) {
            TblCompReportOrder paramOrder = new TblCompReportOrder();
            BeanUtils.copyProperties(order, paramOrder);
            order.setCorpKeyNo(null);
            order.setCorpName(null);
            order.setCorpNameEn(null);
            order.setCorpNumber(null);
            order.setCorpStatusCheck(null);
            order.setKeyNo(null);
            order.setPersId(null);
            order.setPersName(null);
            order.setPersNameEn(null);
            order.setCorpNumber(null);
            order.setCorpStatus(null);
            order.setCorpDistrict(null);
            order.setCorpDateOfReg(null);
            order.setCorpJurisdiction(null);
            order.setRegNo(null);
            return paramOrder;
        } else {
            return order;
        }
    }

    // added for v1.8.0 KNZT-3524
    public List<CompChargeUnitTO> getChargeUnits4OrderUpgrade(String orderId) throws MessageException {
        Date now = new Date();
        TblCompReportOrder order = super.get(orderId);
        List<CompChargeUnitTO> compChargeUnits = commTblReportChargeUnitService.getCompChargeUnits(UserUtils.getUserCompanyId()).stream()
                .filter(k -> !ReportTypeEnum.subOrderReportTypeList().contains(k.getReportType()))
                .collect(Collectors.toList()); // updated for v1.9.0 KNZT-4095
        CommTblReportChargeUnitService.removeCreditIfNecessary4UnitTO(compChargeUnits); // added for v1.8.2 KNZT-3680
        return doGetChargeUnits4OrderUpgrade(order, now, compChargeUnits);
    }

    // added for v1.8.0 KNZT-3524
    // 注意不要修改compChargeUnits
    private List<CompChargeUnitTO> doGetChargeUnits4OrderUpgrade(TblCompReportOrder order, Date now, List<CompChargeUnitTO> compChargeUnitsUnmodifiable) throws MessageException {
        MsgExceptionUtils.failBuild(OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus()), "err.try.again");
        // 订单升级超出时效
        boolean overdue = DateUtils.addHours(order.getCreateDate(), 24).before(now);
        if (overdue) {
            throw new MessageException("err.upgrade.over.time");
        }
        return compChargeUnitsUnmodifiable.stream().map(to -> {
            CompChargeUnitTO newTo = new CompChargeUnitTO();
            BeanUtil.copyProperties(newTo, to);
            newTo.setActiveFlag("0");
            if (ReportTypeEnum.FIN_TAX.getCode().equals(newTo.getReportType()) && StringUtils.equals(newTo.getActiveFlag(), "0")) {
                newTo.setDisabledTips("The Financial & Tax report of this entity you currently selected is not available. Please select other types of reports.");
            }
            newTo.setUnit4Upgrade(newTo.getUnit().subtract(order.getTotalUnit()));
            return newTo;
        }).collect(Collectors.toList());
    }


    /**
     * added for v1.8.0 KNZT-3524 订单退款
     *
     * @param order
     * @return
     */
    public void refundOrder(TblCompReportOrder order) throws MessageException {
        // 校验是否可以退款
        order.transitionStatus(OrderStatusEnum.REFUNDED);
        this.save(order);
        // added for v1.8.8 KNZT-3324 生成流水
        transactionBusinessService.createTransaction4Refund(order.getId(), order.getCompanyId(), order.getTotalUnit(), TransactionTypeEnum.REFUND_ORDER, TransactionTypeEnum.ORDER.getCode());
    }

    /**
     * 财税报告订单退款
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param order
     * @param finTax
     * @return
     */
    public void refundFinTaxOrder(TblCompReportOrder order, TblCompReportOrderFinTax finTax) throws MessageException {
        MsgExceptionUtils.failBuild(FintaxDataStatusEnum.INVALID.getCode().equals(finTax.getDataStatus()),"fin tax order data status error");
        MsgExceptionUtils.failBuild(FintaxAuthStatusEnum.INVALID.getCode().equals(finTax.getAuthStatus()), "fin tax order auth status error");
        finTax.setDataStatus(FintaxDataStatusEnum.INVALID.getCode());
        finTax.setAuthStatus(FintaxAuthStatusEnum.INVALID.getCode());
        finTaxService.save(finTax);
        refundOrder(order);
        sendRefundEmail(order);

/*        // 查找关联订单 added for lvcy v2.0.6 KNZT-5664
        List<String> sonOrderIdList = commTblCompReportOrderRelService.getRelOrderIdList(order.getId(), OrderRelTypeEnum.PARENT_SON, order.getCompanyId());
        List<TblCompReportOrder> sonOrderList = this.batchGet(sonOrderIdList);
        for (TblCompReportOrder sonOrder : sonOrderList) {
            this.refundOrder(sonOrder);
        }*/
    }



    public Page<TblCompReportOrder> findListByCondition(TblCompReportOrderCondition condition) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();
        /*if(Constants.Report.PAGE_REPORT_TAB_CORP.equals(condition.getReportTab())) {
//            condition.setReportTypeList(Lists.newArrayList(Constants.Report.REPORT_GROUP_CORP_G, Constants.Report.REPORT_GROUP_KYB_HK));
            condition.setReportTypeList(Lists.newArrayList(Constants.Report.REPORT_TYPE_CORP_GLOBAL_HK, Constants.Report.REPORT_TYPE_CORP_GLOBAL_CUSDD, Constants.Report.REPORT_TYPE_CORP_GLOBAL_PROFILE));
        } else if(Constants.Report.PAGE_REPORT_TAB_PERS.equals(condition.getReportTab())) {
//            condition.setReportGroupList(Lists.newArrayList(Constants.Report.REPORT_GROUP_KYC, Constants.Report.REPORT_GROUP_PERS_NOT_RPT));
            condition.setReportTypeList(Lists.newArrayList(Constants.Report.REPORT_TYPE_PERS_NOT_RPT, Constants.Report.REPORT_TYPE_PERS_GLOBAL_NPTN));
        } else */if(Constants.Report.PAGE_REPORT_TAB_CORP_VIEW.equals(condition.getReportTab())) {
            // updated for v1.5.2 KNZT-2320 收口统一管理企业类reportType
            condition.setReportTypeList(ReportTypeEnum.corpList());
        } else if(Constants.Report.PAGE_REPORT_TAB_PERS_VIEW.equals(condition.getReportTab())) {
            // updated for v1.5.2 KNZT-2320 收口统一管理人员类reportType
            condition.setReportTypeList(ReportTypeEnum.persTypeList());
        } else if(StringUtils.isNotBlank(condition.getReportType())) {
            condition.setReportTypeList(Lists.newArrayList(condition.getReportType()));
        } else {
            logger.error("invalid param ReportTab:" + condition.getReportTab());
            return null;
        }
        // updated for v1.5.2 KNZT-2320 区分企业类和人员类查询，企业需要group by
        List<TblCompReportOrder> list;
        if (Constants.Report.PAGE_REPORT_TAB_CORP_VIEW.equals(condition.getReportTab())) {
            list = dao.listDistinctCorpReportByCondition(companyId, condition);
        } else {
            list = dao.listReportByCondition(companyId, condition);
        }
        removeSensitiveIdInfo(list);
        condition.getPage().setList(list);
        return condition.getPage();
    }

    /**
     * added for v1.5.1 KNZT-2242
     * updated for v1.5.5 KNZT-2520
     * updated for v1.8.2 KNZT-3722 优化service字典从chargeUnit的数据判断
     * 对账单列表字典
     *
     *
     * @param condition 入参条件
     * @return ReportOrderSearchDictOfBillTO
     */
    public ReportOrderSearchDictOfBillTO getReportOrderSearchDictOfBill(TblCompReportOrderCondition condition) throws MessageException {
        String companyId = UserUtils.getUserCompanyId();

        check4Permission(condition); // added for v2.1.8 chenbl KNZT-6891
        ReportOrderSearchDictOfBillTO dict = new ReportOrderSearchDictOfBillTO();
        // 处理用户Users筛选项，仅当非个人时渲染
//        if (condition.getIsPersonal() == 0) {
//            List<User> userList = commCompUserService.findAllUsersForCompany(companyId);
//            List<DictItem> loginNameItemList = userList.stream()
//                    .map(User::getLoginName)
//                    .sorted()
//                    .map(e -> new DictItem(e, e))
//                    .collect(Collectors.toList());
//            loginNameItemList.add(new DictItem(Constants.OrderDataResource.API, Constants.OrderDataResource.API));
//            dict.setLoginNameList(loginNameItemList);
//        }
        // updated for v1.8.2 KNZT-3722 渲染service字典
        List<TblReportChargeUnit> chargeUnitList = commTblReportChargeUnitService.listEnableChargeUnitByCompany(companyId);
        List<TblReportChargeUnit> chargeUnits = chargeUnitList.stream()
                .filter(k -> ReportTypeEnum.getKYCReportList().contains(k.getReportType()))
                .collect(Collectors.toList());
        List<DictItem> unitGroupItemList = this.chargeUnitToItemList(chargeUnits);
        dict.setUnitGroupList(unitGroupItemList);

        List<DictItem> verifyItemList = chargeUnitList.stream()
                .filter(k -> ReportTypeEnum.getVerifyList().contains(k.getReportType()))
                .map(e -> new DictItem(e.getReportType(), e.getReportName()))
                .collect(Collectors.toList());
        dict.setVerifyList(verifyItemList);

        // added for v2.1.8 chenbl KNZT-6891
        List<DictItem> rptStatusGroupItemList = new ArrayList<>();
        List<CompReportOrderGroupTO> compReportOrderGroupTOS = dao.listReportGroupByCondition(companyId, condition);
        if (CollectionUtils.isNotEmpty(compReportOrderGroupTOS)) {
            Set<String> rptStatusSet = compReportOrderGroupTOS.stream().map(CompReportOrderGroupTO::getRptStatus).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(rptStatusSet)) {
                List<OrderStatusEnum> rptStatusEnumSorted = rptStatusSet.stream().map(OrderStatusEnum::getByCode).filter(Objects::nonNull)
                        .sorted(Comparator.comparingInt(Enum::ordinal))
                        .collect(Collectors.toList());
                boolean hasDataPending = false;
                for (OrderStatusEnum orderStatusEnum : rptStatusEnumSorted) {
                    // 特殊逻辑：DATA_PENDING和PENDING前端只展示一个PENDING，查询时需要穿两个
                    if (orderStatusEnum == OrderStatusEnum.DATA_PENDING) {
                        hasDataPending = true;
                        rptStatusGroupItemList.add(new DictItem(OrderStatusEnum.PENDING.getCode(), OrderStatusEnum.PENDING.getDescEn()));
                        continue;
                    }
                    if (hasDataPending && orderStatusEnum == OrderStatusEnum.PENDING) {
                        continue;
                    }
                    rptStatusGroupItemList.add(new DictItem(orderStatusEnum.getCode(), orderStatusEnum.getDescEn()));
                }
            }
        }
        dict.setRptStatusList(rptStatusGroupItemList);
        return dict;
    }

    /**
     * 根据订单列表tab找到对应的reportTypeList，过滤chargeUnits，生成订单列表service字典
     * added for v1.8.2 KNZT-3722
     * updated for lvcy v2.0.9 KNZT-5362
     *
     * @param chargeUnits
     * @return List<Item>
     */
    public List<DictItem> chargeUnitToItemList(List<TblReportChargeUnit> chargeUnits) {
        List<DictItem> unitGroupItemList = Lists.newArrayList();

        Map<String, List<TblReportChargeUnit>> unitGroupChargeUnitMap = chargeUnits.stream()
                .collect(Collectors.groupingBy(TblReportChargeUnit::getUnitGroup, Collectors.toList()));
        for (UnitGroupEnum unitGroup : UnitGroupEnum.values()) {
            List<TblReportChargeUnit> chargeUnitList = unitGroupChargeUnitMap.get(unitGroup.getGroup());
            if (CollectionUtils.isEmpty(chargeUnitList)) {
                continue;
            }
            List<DictItem> servicItemList = chargeUnitList.stream().map(chargeUnit -> new DictItem(chargeUnit.getReportType(), chargeUnit.getReportName()))
                    .collect(Collectors.toList());
            unitGroupItemList.add(new DictItem(
                    unitGroup.getGroup(), unitGroup.getName(), servicItemList));
        }
        // 判断是否有扫描产品, 渲染字典
        chargeUnits.stream()
                .filter(k -> ReportTypeEnum.SCAN.getCode().equals(k.getReportType()))
                .findFirst()
                .ifPresent(k -> {
                    DictItem scanDict = new DictItem(ReportTypeEnum.SCAN.getCode(), ReportTypeEnum.SCAN.getDesc());
                    DictItem subScanDict = new DictItem(ReportTypeEnum.SCAN.getCode(), ReportTypeEnum.SCAN.getDesc());
                    scanDict.setSubList(Lists.newArrayList(subScanDict));
                    unitGroupItemList.add(scanDict);
                });
        return unitGroupItemList;
    }

    /**
     * 对账单列表查询
     * added for v1.5.1 KNZT-2242
     * updated for lvcy v2.0.9 KNZT-5362
     *
     * @param condition 入参条件
     * @return Page<ReportOrderInfoOfBillTO>
     */
    public Page<ReportOrderInfoOfBillTO> listReportOrderOfBill(TblCompReportOrderCondition condition) throws MessageException {
        check4Permission(condition); // added for v2.1.2 chenbl KNZT-6158
        String companyId = UserUtils.getUserCompanyId();
        condition.setReportTypeList(condition.getServiceList());
        // 特殊处理API下单逻辑
        if (CollectionUtils.isNotEmpty(condition.getLoginNameList())) {
            condition.setIsContainApi(condition.getLoginNameList().contains(Constants.OrderDataResource.API) ? 1 : 0);
        }
        // added for v1.8.5 KNZT-3818
        if (StringUtils.isNotBlank(condition.getSearchKey())) {
            condition.setSearchKeys4db(StringUtils.generateChineseVariants(condition.getSearchKey()));
        }
        List<TblCompReportOrder> list = dao.listReportByCondition(companyId, condition);
        Page<ReportOrderInfoOfBillTO> rtnPage = new Page<>();
        BeanUtils.copyProperties(condition.getPage(), rtnPage);
        // updated for v1.8.0 KNZT-3524
        List<ReportOrderInfoOfBillTO> orderTos = ReportOrderInfoOfBillTO.build(list);
        fillOrderTos(orderTos);
        rtnPage.setList(orderTos);
        return rtnPage;
    }

    private void check4Permission(TblCompReportOrderCondition condition) throws MessageException {
        UserAccessScopeTO userAccessScope = ruleBusinessService.getUserAccessScope(UserUtils.getUserId());
        if (Objects.nonNull(condition.getListType())) {
            condition.setVisible(Constants.YES);
            condition.setAccessUserIdList(userAccessScope.getUserIdList());

            if (StringUtils.equals("PERSONAL", condition.getSharedType())) {
                condition.setLoginNameList(Lists.newArrayList(UserUtils.getUserLoginName()));
            } else if (StringUtils.equals("OTHER_SHARED", condition.getSharedType())) {
                condition.setExcludeLoginNameList(Lists.newArrayList(UserUtils.getUserLoginName()));
            }

            // KYC_HISTORY 查询时，需要包含verifyList;
            List<String> intersection = Lists.newArrayList(ReportTypeEnum.getKYCReportList());
            if (StringUtils.equals("KYC_HISTORY", condition.getListType())) {
                intersection.addAll(ReportTypeEnum.getVerifyList());
            }
            List<String> serviceListOfParam = condition.getServiceList();
            if (CollectionUtils.isNotEmpty(serviceListOfParam)) {
                intersection.retainAll(serviceListOfParam);
            }
            condition.setServiceList(intersection);

            if (StringUtils.equals("REPORT_HISTORY", condition.getListType())) {
                condition.setRptStatusList(OrderStatusEnum.getSuccessOrProcessingStatus());
            }
        }
    }

    /**
     * updated for v2.1.6 chenbl KNZT-6512
     * updated for v2.0.2 chenbl KNZT-5271
     * @param tos
     */
    public void fillOrderTos(List<ReportOrderInfoOfBillTO> tos){
        if(CollectionUtils.isEmpty(tos)){
            return;
        }
        boolean configAnonymousOrder = commSysCompanyService.getConfigAnonymousOrder();

        List<String> hkDocIdList = tos.stream().filter(item -> ReportTypeEnum.HK_DOCUMENT.getCode().equals(item.getReportType())).map(ReportOrderInfoOfBillTO::getId).collect(Collectors.toList());
        Map<String, TblCompReportOrderHkDoc> hkDocMap = hkDocService.batchGetHkDocMap(hkDocIdList, OrderHkDocRelTypeEnum.ORDER.getCode());

        Date now = new Date();
        int report360ViewValidDays = getReport360ViewValidDays(); // added for v1.8.6 KNZT-3873
        int localReportViewValidDays2c = getLocalReportViewValidDays2c();
        fillDocumentInfo4ApiOrder(tos); // added for v2.1.6 chenbl KNZT-6560
        for (ReportOrderInfoOfBillTO orderTo : tos) {
            List<ReportOrderInfoOfBillTO.Action> actions = new ArrayList<>();
            CollectionUtils.addIgnoreNull(actions, buildActionDownloadIfNecessary(orderTo));
            CollectionUtils.addIgnoreNull(actions, buildActionViewIfNecessary(orderTo, now, report360ViewValidDays, localReportViewValidDays2c));
            CollectionUtils.addIgnoreNull(actions, buildActionPayIfNecessary(orderTo));
            CollectionUtils.addIgnoreNull(actions, buildActionPasswordIfNecessary(orderTo));
            CollectionUtils.addIgnoreNull(actions, buildActionAuthorizeIfNecessary(orderTo));
            CollectionUtils.addIgnoreNull(actions, buildActionDeleteIfNecessary(orderTo));
            orderTo.setActions(actions);
            orderTo.setRptStatusDesc(OrderStatusEnum.getDescEnByCode(orderTo.getRptStatus()));
            orderTo.setIsSelf(UserUtils.getUserLoginName().equals(orderTo.getLoginName()) ? 1 : 0);
            orderTo.setCorpStatus(null);
            orderTo.setCorpStatusCheck(null);

            // 核验订单，只有本人可以查看
            if (ReportTypeEnum.getVerifyList().contains(orderTo.getReportType()) && !StringUtils.equals(UserUtils.getUserLoginName(), orderTo.getLoginName())) {
                orderTo.setUrl(null);
            }

            // 香港文档订单，设置香港文档全称
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(orderTo.getReportType())) {
                TblCompReportOrderHkDoc hkDoc = hkDocMap.get(orderTo.getId());
                if (Objects.nonNull(hkDoc)) {
                    orderTo.setDocNumber(hkDoc.getDocNumber());
                    orderTo.setDocTitle(hkDoc.getDocTitle());
                    orderTo.setDocTitleEn(hkDoc.getDocTitleEn());
                    orderTo.setDocYear(hkDoc.getDocYear());
                    orderTo.setDocDate(hkDoc.getDocDate());
                }
            }

            // 根据匿名订单配置是否展示邮箱：匿名订单时不显示用户信息
            if (configAnonymousOrder) {
                orderTo.setLoginName(null);
            }
        }
    }

    // added for v2.1.6 chenbl KNZT-6560
    public void fillDocumentInfo4ApiOrder(List<ReportOrderInfoOfBillTO> tos) {
        if (CollectionUtils.isEmpty(tos)) {
            return;
        }
        List<ReportOrderInfoOfBillTO> hkBasArTos = tos.stream().filter(to -> ReportTypeEnum.HK_BAS_AR.getCode().equals(to.getReportType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hkBasArTos)) {
            List<String> apiOrderNos = MappingUtils.getList(hkBasArTos, ReportOrderInfoOfBillTO::getApiOrderNo);
            if (CollectionUtils.isNotEmpty(apiOrderNos)) {
                Map<String, HkReportResultTO> apiOrderNo2HkReportResultTOMap = hkReportResultService.getApiOrderNo2HkReportResultTOMap(apiOrderNos);
                for (ReportOrderInfoOfBillTO hkBasArTo : hkBasArTos) {
                    HkReportResultTO hkReportResultTO = apiOrderNo2HkReportResultTOMap.get(hkBasArTo.getApiOrderNo());
                    if (hkReportResultTO != null) {
                        hkBasArTo.setDocumentType(hkReportResultTO.getDocumentType());
                        hkBasArTo.setDocumentFile(hkReportResultTO.getDocumentFile());
                    }
                }
            }
        }

        List<ReportOrderInfoOfBillTO> hkIrdTos = tos.stream().filter(to -> ReportTypeEnum.hkIrdCorpList().contains(to.getReportType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hkIrdTos)) {
            Set<String> apiOrderNos = MappingUtils.getSet(hkIrdTos, ReportOrderInfoOfBillTO::getApiOrderNo);
            if (CollectionUtils.isNotEmpty(apiOrderNos)) {
                List<QccProdHkTaxReport> qccProdHkTaxReports = qccProdHkTaxReportService.findByIds(apiOrderNos);
                Map<String, QccProdHkTaxReport> apiOrderNo2ResultMap = MappingUtils.getMap(qccProdHkTaxReports, QccProdHkTaxReport::get_id, Function.identity());
                for (ReportOrderInfoOfBillTO to : hkIrdTos) {
                    QccProdHkTaxReport qccProdHkTaxReport = apiOrderNo2ResultMap.get(to.getApiOrderNo());
                    if (qccProdHkTaxReport != null) {
                        to.setDocumentType(to.getReportType());
                        to.setDocumentFile(qccProdHkTaxReport.getReportUrl());
                    }
                }
            }
        }
    }

    // added for v1.9.3 KNZT-4382 actions实现调整
    public static ReportOrderInfoOfBillTO.Action buildActionDownloadIfNecessary(ReportOrderInfoOfBillTO order) {
        boolean enabled = false;
        if (OrderStatusEnum.getDataSuccessStatus().contains(order.getRptStatus())
                && (StringUtils.isNotBlank(order.getDocumentFile()) || StringUtils.isNotBlank(order.getUrl()))
                // 核验订单，只有本人可以下载
                && (!ReportTypeEnum.getVerifyList().contains(order.getReportType()) || order.getLoginName().equals(UserUtils.getUserLoginName()))) {
            enabled = true;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.DOWNLOAD, enabled, null) : null;
    }

    // added for v1.9.3 KNZT-4382 actions实现调整
    private static ReportOrderInfoOfBillTO.Action buildActionViewIfNecessary(ReportOrderInfoOfBillTO order, Date now, int report360ViewValidDays, int localReportViewValidDays2c) {
        boolean enabled = false;
        String disabledTips = null;
        // 非(数据处理中或者数据处理成功)
        if (!OrderStatusEnum.getSuccessOrProcessingStatus().contains(order.getRptStatus())) {
            return null;
        }
        boolean dataSuccess = OrderStatusEnum.getDataSuccessStatus().contains(order.getRptStatus());
        if (ReportTypeEnum.local360ReportList().contains(order.getReportType())) {
            enabled = dataSuccess && local360ReportCanView(order, now, report360ViewValidDays);
            disabledTips = getViewDisabledTips(report360ViewValidDays); // updated for v1.9.4 KNZT-4575
        } else if (ReportTypeEnum.localChromeReportList().contains(order.getReportType())) {
            if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType())) { // updated for v2.0.2 chenbl KNZT-5271
                // 财税不可view, do nothing
            } else {
                if (UserUtils.isSelf()) {
                    enabled = dataSuccess && localReportCanView2c(order, now, localReportViewValidDays2c);
                    disabledTips = getViewDisabledTips(localReportViewValidDays2c); // updated for v1.9.4 KNZT-4575
                }
            }
        } else if (ReportTypeEnum.apiOrderTypeList().contains(order.getReportType())) { // added for v1.9.4 KNZT-4487
            // apiOrderTypeList中包含一部分类型 不需要view按钮
            enabled = !ReportTypeEnum.apiOrderTypeWithoutViewList().contains(order.getReportType());
        } else if (ReportTypeEnum.SCAN.getCode().equals(order.getReportType())) { // added for v2.0.9 chenbl KNZT-5362
            enabled = dataSuccess;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.VIEW, enabled, disabledTips) : null;
    }

    // added for v1.9.4 KNZT-4575
    private static String getViewDisabledTips(int validDays) {
        String daySuffix = validDays == 1 ? "day" : "days";
        return String.format("Live view is available for only %s %s after this service is first unlocked. After that, you can download the report.", validDays, daySuffix);
    }

    // added for v1.9.3 KNZT-4382 actions实现调整
    private static ReportOrderInfoOfBillTO.Action buildActionPayIfNecessary(TblCompReportOrder order) {
        boolean enabled = false;
        String userId = UserUtils.getUserId();
        if (OrderStatusEnum.NONE.getCode().equals(order.getRptStatus())
                && PayTypeEnum.getContainsOnlineList().contains(order.getPayType())
                && PayStatusEnum.UNPAID.getCode().equals(order.getPayStatus())
                && StringUtils.equals(userId, order.getUserId())) {
            enabled = true;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.PAY, enabled, null) : null;
    }

    // added for v2.0.2 chenbl KNZT-5271
    private static ReportOrderInfoOfBillTO.Action buildActionPasswordIfNecessary(TblCompReportOrder order) {
        boolean enabled = false;
        if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType()) && OrderStatusEnum.SUCCESS.getCode().equals(order.getRptStatus())) {
            enabled = true;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.PASSWORD, enabled, null) : null;
    }

    private static ReportOrderInfoOfBillTO.Action buildActionAuthorizeIfNecessary(TblCompReportOrder order) {
        boolean enabled = false;
        if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType()) && OrderStatusEnum.ACTION_PENDING.getCode().equals(order.getRptStatus())) {
            enabled = true;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.AUTHORIZE, enabled, null) : null;
    }

    private static ReportOrderInfoOfBillTO.Action buildActionDeleteIfNecessary(TblCompReportOrder order) {
        boolean enabled = false;
        String userId = UserUtils.getUserId();
        if (OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus())
                && !Constants.NO.equals(order.getVisible())
                && StringUtils.equals(userId, order.getUserId())) {
            enabled = true;
        }
        return enabled ? ReportOrderInfoOfBillTO.Action.build(ReportOrderInfoOfBillTO.ActionTypeEnum.DELETE, enabled, null) : null;
    }

    /**
     * added for v1.8.6 KNZT-3873
     * 获取360报告可查看有效天数
      * @return
     */
    public static int getReport360ViewValidDays() {
        String report360ViewValidDaysVal = ConfigUtils.getConfigValueByTypeAndKey("report_360_view_valid_days", "report_360_view_valid_days", "5");
        return Integer.parseInt(report360ViewValidDaysVal);
    }

    // added for v1.8.6 KNZT-3873
    public static boolean local360ReportCanView(TblCompReportOrder order, Date now, int report360ViewValidDays) {
        // createDate + validDays >= now
        return !DateUtils.addDays(order.getBeginDate(), report360ViewValidDays).before(now);
    }

    // added for v1.9.3 KNZT-4382 获取大陆报告可查看有效天数
    public static int getLocalReportViewValidDays2c() {
        String localReportViewValidDays2c = ConfigUtils.getConfigValueByTypeAndKey("local_report_view_valid_days2c", "local_report_view_valid_days2c", "1");
        return Integer.parseInt(localReportViewValidDays2c);
    }

    // added for v1.9.3 KNZT-4382
    public static boolean localReportCanView2c(TblCompReportOrder order, Date now, int localReportViewValidDays2c) {
        // createDate + validDays >= now
        return !DateUtils.addDays(order.getCreateDate(), localReportViewValidDays2c).before(now);
    }

    // added for v2.0.2 chenbl KNZT-5271 获取财税报告可查看有效小时数
    public static int getFinTaxReportViewValidHours() {
        String finTaxReportViewValidHours = ConfigUtils.getConfigValueByTypeAndKey("fin_tax_report_view_valid_hours", "fin_tax_report_view_valid_hours", "120");
        return Integer.parseInt(finTaxReportViewValidHours);
    }

    // added for v2.0.2 chenbl KNZT-5271 获取财税报告可查看有效小时数
    public static boolean finTaxReportCanView(TblCompReportOrder order, Date now, int finTaxReportViewValidHours) {
        if (Objects.isNull(order.getBeginDate())) {
            return false;
        }
        // beginDate + validHours >= now
        return !DateUtils.addHours(order.getBeginDate(), finTaxReportViewValidHours).before(now);
    }

    /**
     * added for KNZT-1503 获取企业订单地区信息
     *
     * @return
     */
    public List<String> listCorpOrderDistrictByCompanyId() {
        String companyId = UserUtils.getUserCompanyId();
        return dao.listCorpOrderDistrictByCompanyId(companyId);
    }

    public TblCompReportOrder getByOrderNo(String orderNo) {
        return dao.getByOrderNo(orderNo);
    }

    public TblCompReportOrder getByOrderNoAndCompanyId(String companyId, String orderNo) {
        return dao.getByOrderNoAndCompanyId(companyId, orderNo);
    }

    // added for v1.9.6 KNZT-4762
    public TblCompReportOrder getByApiOrderNo(String apiOrderNo) {
        return dao.getByApiOrderNo(apiOrderNo);
    }

    public void update(TblCompReportOrder tblCompReportOrder) {
        dao.update(tblCompReportOrder);
    }

    /**
     * 检查报告服务的订单状态并同步
     * updated for v1.7.3 KNZT-3104 更换交互接口，去除reportOrder的rptStatus的判断
     * updated for v1.7.3 KNZT-3264 增加发起失败情况下，重新发起的补偿
     * @param rptOrder
     * @throws MessageException
     */
    @DingErrorMsg(referenceNoPrefix = "createReport-")
    public void checkAndUpdateOrderStatusInfo4Callback(TblCompReportOrder rptOrder) throws MessageException {
        if (Objects.nonNull(rptOrder) && StringUtils.isNotBlank(rptOrder.getCompanyId())
                && StringUtils.isNotBlank(rptOrder.getKeyNo())) {
            apiUserLoginService.loginUser(rptOrder.getUserId(), () -> {
                // updated for v1.9.6 KNZT-4762 增加香港报告补偿
                checkAndUpdate4Report(rptOrder);
                checkAndUpdate4ApiOrderDataStatus(rptOrder);
                return null;
            });
        }
    }

    /**
     * 处理报告生成回调
     * updated for v1.9.6 KNZT-4762
     * @param rptOrder
     * @return 是否更新url
     * @throws MessageException
     */
    public boolean checkAndUpdate4Report(TblCompReportOrder rptOrder) throws MessageException {
        if (!ReportTypeEnum.local360ReportList().contains(rptOrder.getReportType())) {
            return false;
        }
        String paramOrderNo = CompanyReportInternalInterface.generateOrderNo4LocalReport(rptOrder);
        ReportUrlTO urlTO = CompanyReportInternalInterface.getReportUrlTO(paramOrderNo);
        if (Objects.nonNull(urlTO) && StringUtils.isNotBlank(urlTO.getReportUrl())) {
            String url = urlTO.getReportPdfUrl();
            rptOrder.setUrl(url.startsWith(Constants.HttpType.HTTPS) ? url : url.replaceFirst(Constants.HttpType.HTTP, Constants.HttpType.HTTPS));
            rptOrder.transitionStatus(OrderStatusEnum.SUCCESS);
            if (ReportTypeEnum.CORP_360_DELAY.getCode().equals(rptOrder.getReportType())) {
                sendCorp360DelaySuccessEmail(rptOrder);
            }
            this.save(rptOrder);
            return true;
        }
        logger.error("360 report failed, orderId:{}", rptOrder.getId());
        return false;
    }

    /**
     * updated for v2.0.6 chenbl KNZT-5506
     * 检查更新api订单数据状态
     * added for v1.9.6 KNZT-4762 增加香港报告补偿
     * @param rptOrder
     * @return 订单数据状态，true已完成，false未完成
     * @throws MessageException
     */
    public boolean checkAndUpdate4ApiOrderDataStatus(TblCompReportOrder rptOrder) throws MessageException {
        if (!ReportTypeEnum.apiOrderTypeList().contains(rptOrder.getReportType())) { // updated for v2.0.0 chenbl KNZT-5055
            return true;
        }
        boolean supported = reportDataContextService.isSupported(rptOrder.getReportType(), rptOrder.getDataVersion());
        if (!supported) {
            logger.error("report type not support, reportType:{}", rptOrder.getReportType());
            return false;
        }
        boolean dataSuccess = reportDataContextService.getService(rptOrder.getReportType(), rptOrder.getDataVersion())
                .isDataSuccess(ReportDataGetResultForm.build(rptOrder.getId()));
        if (dataSuccess) {
            doAfterApiOrderDataSuccess(rptOrder);
            return true;
        } else {
            logger.info("checkAndUpdate4ApiOrderDataStatus failed, orderId:{}, apiOrderNo:{}, reportType:{}",
                    rptOrder.getId(), rptOrder.getApiOrderNo(), rptOrder.getReportType());
            return false;
        }
    }

    /**
     * added for v1.7.3 KNZT-3264
     * updated for v1.8.0 KNZT-3613 收口创建报告方法
     *
     * @param order
     * @return
     */
    public void createReport(TblCompReportOrder order) throws MessageException {
        String orderNo = order.getOrderNo();
        String reportType = order.getReportType();
        String keyNo = order.getKeyNo();

        if (StringUtils.isBlank(orderNo)) { // updated for v2.0.9 chenbl KNZT-5362
            logger.info("createReport not support, orderNo:{}, reportType:{}, keyNo:{}", orderNo, reportType, keyNo);
            return;
        }

        logger.info("createReport, orderId:{}, orderNo:{}, reportType:{}, keyNo:{}", order.getId(), orderNo, reportType, keyNo);
        if (ReportTypeEnum.chromeReportList().contains(reportType)) {
            User user = UserUtils.getUser();
            if (StringUtils.isBlank(user.getId())) {
                user = commCompUserService.get(order.getUserId());
            }
            String picId = Objects.nonNull(user) ? StringUtils.substringAfter(user.getEncryptUserId(), ",") : "";
            Map<String, String> orderExtMap = orderExtService.getKeyValueMap(order.getId());
            WebConvertInterface.convertCorpReport(order, orderExtMap, picId);
        } else if (ReportTypeEnum.local360ReportList().contains(reportType)) {
            String paramOrderNo = CompanyReportInternalInterface.generateOrderNo4LocalReport(order);
            CompanyReportInternalInterface.createReport(paramOrderNo, reportType, keyNo);
        } else if (ReportTypeEnum.spiderReportList().contains(reportType)) {
            commTblCompReportOrderService.createLegalDocumentReport(order);
        } else {
            logger.info("createReport current report not support, orderNo:{}, reportType:{}, keyNo:{}", orderNo, reportType, keyNo);
        }
    }

    /**
     * added for v1.7.6 KNZT-3306
     * 创建legal document 报告
     *
     * @param order
     * @return
     */
    @DingErrorMsg(referenceNoPrefix = "createLegalDocumentReport-")
    public void createLegalDocumentReport(TblCompReportOrder order) throws MessageException {
        String orderNo = order.getOrderNo();
        String reportType = order.getReportType();
        String keyNo = order.getKeyNo();
        if (!ReportTypeEnum.spiderReportList().contains(reportType) || StringUtils.isBlank(keyNo) || StringUtils.isBlank(orderNo)) {
            logger.info("createLegalDocumentReport current report not support, orderNo:{}, reportType:{}, keyNo:{}", orderNo, reportType, keyNo);
            return;
        }
        boolean needCompanyKey = Objects.nonNull(UserUtils.getUser().getCompany());
        ApiGlobalCorpDetailTO corpInfo = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(order.getKeyNo());
        String regNo = null;
        String province = null;
        if (Objects.nonNull(corpInfo)) {
            regNo = corpInfo.getNo();
            province = corpInfo.getProvince();
        }
        String type = ReportTypeEnum.LEGAL.getCode().equals(reportType) ? "1" : "2";
        YunjuReportInterface.acquireLegalDocument(
                order.getOrderNo(), order.getKeyNo(), order.getCorpName(),
                regNo, order.getCorpNumber(), province, type);
    }

    /**
     * added for  v1.7.6 KNZT-3306
     * 处理legal document 回调
     *
     * @param callback
     * @return
     */
    @DingErrorMsg(referenceNoPrefix = "callbackLegalDocumentReport-")
    public void handleLegalDocumentCallback(AcquireLegalDocumentCallbackForm callback) throws MessageException {
        logger.info("Legal document callback begin, callback:{}", JSONObject.toJSONString(callback));
        if (StringUtils.isBlank(callback.getDataId())) {
            logger.error("Legal document callback params is invalid");
            return;
        }
        TblCompReportOrder order = getByOrderNo(callback.getDataId());
        if (Objects.isNull(order) || !ReportTypeEnum.spiderReportList().contains(order.getReportType())) {
            logger.error("Legal document order is not exist");
            return;
        }
        if (OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus())) {
            logger.info("order has been refunded or canceled");
            return;
        }
        String userId = order.getUserId();
        User user = commCompUserService.get(userId);
        String url;
        if (Integer.valueOf(1).equals(callback.getType())) {
            url = handleDocumentPdf(callback.getReportUrl());
        } else {
            url = callback.getDetailImgUrl();
        }

        if (callback.isSuccess() && StringUtils.isNotBlank(url)) {
            order.setUrl(url.startsWith(Constants.HttpType.HTTPS) ? url : url.replaceFirst(Constants.HttpType.HTTP, Constants.HttpType.HTTPS));
            order.transitionStatus(OrderStatusEnum.SUCCESS);
            this.save(order);
            // 发送邮件
            if (Objects.nonNull(user) && StringUtils.isNotBlank(user.getLoginName())) {
                SysTemplate template = commSysTemplateService.getByTemplateName("u_email_legal_document_done");
                if(Objects.isNull(template)) {
                    logger.error("Legal document's template is not exist");
                    return;
                }
                String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), new HashMap<>());
                qccMailSenderService.send(user.getLoginName(), template.getTemplateSubject(), emailContent);
            }
        } else {
            logger.error("Legal document callback failed, callback:{}", JSONObject.toJSONString(callback));
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(callback.getMessage(), AlarmTypeEnum.SPIDER_REPORT_FAIL, callback.getDataId(), order.getReportType());
            // 退款 added for v1.8.0 KNZT-3600
            refundOrder(order);
            // 发送退款邮件 added for v1.8.0 KNZT-3591
            sendRefundMail(order, user);
        }
    }

    /**
     * added for lvcy v2.1.1 KNZT-5988
     * 处理工商报告网站回调
     * @param callback
     * @throws MessageException
     */
    @DingErrorMsg(referenceNoPrefix = "handleLegalDocumentWebsiteCallback-")
    public void handleLegalDocumentWebsiteCallback(AcquireLegalDocumentCallbackForm callback) throws MessageException {
        logger.info("Legal document website callback begin, callback:{}", JSONObject.toJSONString(callback));
        MsgExceptionUtils.checkIsNull(callback.getIsException(), "isException is null");
        MsgExceptionUtils.failBuild(!StringUtils.equalsAny(callback.getIsException(), "0", "1"), "isException is not 0 or 1");

        SysConfig config = sysConfigService.getConfigByTypeAndKey(Constants.SysConfigType.SERVICE_ACTIVE, ReportTypeEnum.LEGAL.getCode());
        if (Objects.isNull(config)) {
            logger.error("Legal document's config is not exist");
            return;
        }
        if (StringUtils.equals(callback.getIsException(), "0") && StringUtils.equals(config.getConfigValue(), "0")) {
            config.setConfigValue("1");
            sysConfigService.saveConfig(config);
            logger.info("Legal document's service_active config value is set to 1-active");
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.LEGAL_DOCUMENT_WEBSITE_STATUS_CHANGE, "正常");
        } else if (StringUtils.equals(callback.getIsException(), "1") && StringUtils.equals(config.getConfigValue(), "1")) {
            config.setConfigValue("0");
            sysConfigService.saveConfig(config);
            logger.info("Legal document's service_active config value is set to 0-inactive");
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.LEGAL_DOCUMENT_WEBSITE_STATUS_CHANGE, "异常");
        } else {
            logger.warn("Legal document's service_active config value is not changed");
        }
    }

    /**
     * 处理original document 文件
     * added for lvcy v2.0.6 KNZT-5664
     *
     * @param oriObsUrl
     * @return String
     */
    private String handleDocumentPdf(String oriObsUrl) {
        File oriPdf = null;
        File outFile = null;
        try {
            String encodeSuffixUrl = oriObsUrl.substring(oriObsUrl.indexOf("LegalDocument"));
            String ossObjectName = URLDecoder.decode(encodeSuffixUrl, "UTF-8");
            oriPdf = HuaweiObsServUtils.getInstance().getObjectFile(ossObjectName, ".pdf");
            String fileName = ossObjectName.replace("LegalDocument", "OriginalDocument");
            fileName = ossObjectName.replace(".pdf", "");
            outFile = File.createTempFile(fileName, ".pdf");
            return PdfUtils.blockOutByKeywordAndUpload(oriPdf, outFile,"申请人邮箱", 1);
        } catch (Exception e) {
            logger.error("handleDocumentPdf failed, oriObsUrl:{}", oriObsUrl);
            return oriObsUrl;
        } finally {
            if (Objects.nonNull(oriPdf)) {
                FileUtils.deleteFile(oriPdf.getAbsolutePath());
            }
            if (Objects.nonNull(outFile)) {
                FileUtils.deleteFile(outFile.getAbsolutePath());
            }
        }
    }

    private void sendRefundMail(TblCompReportOrder order, User user) throws MessageException {
        if (Objects.isNull(user) || StringUtils.isBlank(user.getLoginName())) {
            return;
        }
        SysTemplate template = commSysTemplateService.getByTemplateName("u_email_legal_document_refund");
        if(Objects.isNull(template)) {
            logger.error("Refund's template is not exist");
            return;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>() {
            {
                put("serviceName", ReportTypeEnum.getDesc(order.getReportType()));
                put("orderNo", order.getOrderNo());
                put("date", DateUtils.formatDateTime(order.getCreateDate()));
            }
        };
        String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
        qccMailSenderService.send(user.getLoginName(), template.getTemplateSubject(), emailContent);
    }

    public static void resetStatusAndMsg4Order(MessageException e, BaseJsonResult result) {
        // updated for v1.9.5 KNZT-4519
        if(StringUtils.equalsAny(e.getMessage(), "err.duplicate.item", "warn.hk.comp.unavailable", "err.upgrade.done", "err.upgrade.over.time", "err.hk.maintenance", "err.hk.maintenance.cart", "err.duplicate.items.cart", "err.cart.items.over.limit", "err.fin.tax.processing")) {
            result.setStatus(Constants.Result.FRONT_YELLOW_WARNING);
        } else if("err.amount.insufficient".equals(e.getMessage())) {
            result.setStatus(Constants.Result.EXCEED_MAX);
            // added for v2.0.7 chenbl KNZT-5772
            if (CompPayTypeEnum.AFTER.getCode().equals(Optional.of(UserUtils.getUser()).map(User::getCompany).map(Company::getPayType).orElse(null))) {
                e = new MessageException("err.company.service.end", e);
            }
        } else if (Constants.MessageExceptionKey.HK_SEARCHER_INFO_MISSING.equals(e.getMessage())) { // added for v2.0.0 chenbl KNZT-5094
            result.setStatus(Constants.Result.ORDER_HK_SEARCHER_INFO_MISSING);
        } else if (StringUtils.isBlank(result.getStatus())){
            result.setStatus(Constants.Result.FALSE_STR);
        }
        result.setMsg(I18NUtil.getMessage(e));
    }



    /**
     * updated for v1.7.3 KNZT-3104 查询订单
     *
     * @param beginTime
     * @param endTime
     * @return List<TblCompReportOrder>
     */
    public List<TblCompReportOrder> listUndoneReportByDateRange(String beginTime, String endTime, List<String> reportTypeList) {
        return dao.listUndoneReportByDateRange(beginTime, endTime, reportTypeList );
    }

    /**
     * added for v2.0.6 chenbl KNZT-5506
     *
     * @param beginTime
     * @param endTime
     * @return List<TblCompReportOrder>
     */
    public List<TblCompReportOrder> listOrder4ApiDataStatusCheck(String beginTime, String endTime, List<String> reportTypeList) {
        return dao.listOrder4ApiDataStatusCheck(beginTime, endTime, reportTypeList, OrderStatusEnum.DATA_PENDING.getCode());
    }

    // removed for v2.0.3 chenbl KNZT-5080
    // 查询是否有未完成的订单 added for v1.9.6 KNZT-4646
//    public boolean existUndoneReportByDateRange(String beginTime, String endTime, List<String> reportTypeList) {
//        return dao.existUndoneReportByDateRange(beginTime, endTime, reportTypeList) > 0;
//    }

    public String getOrderIdByKeyNo(String companyId, String keyNo) {
        return dao.getOrderIdByKeyNo(companyId, keyNo);
    }

    /**
     * added for KNZT-131 人员核验报告身份证号掩码脱敏
     *
     * @param list
     */
    public void removeSensitiveIdInfo(List<TblCompReportOrder> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                if (Constants.Report.KEY_NO_TYPE_PERS.equals(item.getKeyNoType()) && StringUtils.isNotBlank(item.getPersId())) {
                    item.setPersId(StringUtils.maskIdCard(item.getPersId()));
                }
            });
        }
    }

    /**
     * added for v1.8.2 KNZT-3680
     * 若公司配置了子账号隐藏价格，则需要将价格置空
     *
     * @param list
     */
    public static void removeCreditIfNecessary(List<? extends TblCompReportOrder> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (UserUtils.hideCredit()) {
            list.forEach(item -> {
                item.setTotalUnit(null);
                item.setUnit(null);
            });
        }
    }

    public String existPersonReportByParams(String companyId, String persId, String persName, List<String> rptStatusList){
        return dao.existPersonReportByParams(companyId, persId, persName, rptStatusList);
    }

    /**
     * added for v1.0.6 KNZT-336
     * update for v1.5.8 KNZT-2626 兼容id传token的情况
     * @param companyId
     * @param id
     * @return
     */
    public String getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(String companyId, String id) throws MessageException {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        if (id.contains(Constants.TokenSplit.DATA)) {
            return getKeyNoAndCheckDataToken(id);
        } else {
            return dao.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(companyId, id);
        }
    }

    /**
     * added for KNZT-3139 数据维度校验、token有效期校验
     *
     * @param dataToken
     * @param companyId
     * @param reportTypes
     * @return String
     */
    public String getKeyNoByDataToken(String dataToken, String companyId, List<String> reportTypes) throws MessageException {
        String orderId = getOrderIdAndCheckDataToken(dataToken);
        TblCompReportOrder order = dao.getByIdAndCompany(orderId, companyId);
        if (Objects.isNull(order)) {
            return null;
        }
        if (CollectionUtils.isEmpty(reportTypes)) {
            return null;
        }
        boolean matchReportType = reportTypes.stream().anyMatch(reportType -> StringUtils.equals(reportType, order.getReportType()));
        String keyNo = StringUtils.isBlank(order.getKeyNo()) ? getKeyNoAndCheckDataToken(dataToken) : order.getKeyNo();
        return matchReportType ? keyNo : null;
    }

    /**
     * added for v1.5.8 KNZT-2626
     * 通过加密token 获取keyNo
     *
     * @param dataToken
     * @return String
     */
    public String getKeyNoAndCheckDataToken(String dataToken) throws MessageException {
        if (StringUtils.isBlank(dataToken)) {
            throw new MessageException("err.data.token.tip");
        }
        String[] splits = StringUtils.split(dataToken, Constants.TokenSplit.DATA);
        if (splits == null || splits.length != 4) {
            logger.error("token is invalid, dataToken={}", dataToken);
            throw new MessageException("err.data.token.tip");
        }
        String orderId = splits[0];
        String keyNo = splits[1];
        String timeMillisStr = splits[2];
        String token = splits[3];
        if (validateDataToken(orderId, keyNo, timeMillisStr, token)) {
            return keyNo;
        } else {
            logger.error("token is invalid, dataToken={}", dataToken);
            throw new MessageException("err.data.token.tip");
        }
    }

    /**
     * added for v1.5.8 KNZT-2626
     * 通过加密token 获取orderId
     *
     * @param dataToken
     * @return String
     */
    public String getOrderIdAndCheckDataToken(String dataToken) throws MessageException {
        if (StringUtils.isBlank(dataToken)) {
            throw new MessageException("err.data.token.tip");
        }
        String[] splits = StringUtils.split(dataToken, Constants.TokenSplit.DATA);
        if (splits == null || splits.length != 4) {
            logger.error("token is invalid, dataToken={}", dataToken);
            throw new MessageException("err.data.token.tip");
        }
        String orderId = splits[0];
        String keyNo = splits[1];
        String timeMillisStr = splits[2];
        String token = splits[3];
        if (validateDataToken(orderId, keyNo, timeMillisStr, token)) {
            return orderId;
        } else {
            logger.error("token is invalid, dataToken={}", dataToken);
            throw new MessageException("err.data.token.tip");
        }
    }

    /**
     * added for KNZT-605
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    public List<String> listKeyNoByDateRange(String beginTime, String endTime) {
        return dao.listKeyNoByDateRange(beginTime, endTime);
    }

    /**
     * added for 判断该公司下是否有指定group的订单
     *
     * @param companyId
     * @param reportGroupList
     * @return String
     */
    public String existReportByCompanyAndGroup(String companyId, List<String> reportGroupList) {
        return dao.existReportByCompanyAndGroup(companyId, reportGroupList);
    }


    /**
     * added for KNZT-939
     *
     * @param orderInitInfoTO
     * @param mode
     * @return
     */
    private void setCorpOrPersonScanningCountInfoForCover(OrderInitInfoTO orderInitInfoTO ,String mode) throws MessageException{
        if(StringUtils.isNotBlank(mode) && orderInitInfoTO != null){
            if(StringUtils.equals(mode,"corp") && orderInitInfoTO.getCorpInfo() != null && StringUtils.isNotBlank(orderInitInfoTO.getCorpInfo().getCorpKeyNo())){
                //企业使用
                CorpScanningCountTO corpScanningCountTO = CompanyDetailsInterface.getCorpScanning(orderInitInfoTO.getCorpInfo().getCorpKeyNo());
                if(corpScanningCountTO != null){
                    CorpScanningCount4CoverTO corpScanningCount4CoverTO = new CorpScanningCount4CoverTO();
                    corpScanningCount4CoverTO.setHasDishonestData(corpScanningCountTO.getDishonestCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasJudgmentDebtorData(corpScanningCountTO.getJudgmentDebtorCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasSeriousIllegalData(corpScanningCountTO.getSeriousIllegalCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasAbnormalOperationsData(corpScanningCountTO.getAbnormalOperationsCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasAdverseMediaData(corpScanningCountTO.getAdverseMediaCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasExitLimitData(corpScanningCountTO.getExitLimitCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasSumptuaryData(corpScanningCountTO.getSumptuaryCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasAdminPenaltyData(corpScanningCountTO.getAdminPenaltyCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasJudgementData(corpScanningCountTO.getJudgementCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasBranchData(corpScanningCountTO.getBranchCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasPartnerData(corpScanningCountTO.getPartnerCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasIpoPartnerData(corpScanningCountTO.getIpoPartnerCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasEmployeeData(corpScanningCountTO.getEmployeeCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasIpoEmployeeData(corpScanningCountTO.getIpoEmployeeCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasActualControllerData(corpScanningCountTO.getActualControllerCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasSnapshotImageData(StringUtils.isNotBlank(corpScanningCountTO.getCompanyImageUrl()) ? 1 : 0);
                    // added for KNZT-1416 总公司信息动态蒙层
                    corpScanningCount4CoverTO.setHasParentCorpData(corpScanningCountTO.getParentCorpCount() > 0 ? 1 : 0);
                    corpScanningCount4CoverTO.setIsBranch(corpScanningCountTO.getIsBranch() > 0 ? 1 : 0);
                    orderInitInfoTO.setCorpScanningCount4CoverTO(corpScanningCount4CoverTO);
                    corpScanningCount4CoverTO.setHasHoldCompData(corpScanningCountTO.getHCCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasInvester(corpScanningCountTO.getInvesterCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasIndirectInvester(corpScanningCountTO.getIndirectInvesterCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasHistoryInvester(corpScanningCountTO.getHistoryInvesterCount()>0?1:0);
                    corpScanningCount4CoverTO.setHasHistoryPartner(corpScanningCountTO.getHistoryPartner2Count()>0?1:0);
                    corpScanningCount4CoverTO.setHasHistoryEmployee(corpScanningCountTO.getHistoryEmployee2Count()>0?1:0);
                    // // added for KNZT-1735 历史法人维度
                    corpScanningCount4CoverTO.setHasHistoryOperData(corpScanningCountTO.getHistoryOperCount() > 0 ? 1 : 0);
                }
            }else if(StringUtils.equals(mode,"person")&& orderInitInfoTO.getPersInfoTO() != null && StringUtils.isNotBlank(orderInitInfoTO.getPersInfoTO().getPersKeyNo())){
                //非企业使用
                PersonScanningCountTO personScanningCountTO = CompanyDetailsInterface.getPersonScanningCount(orderInitInfoTO.getPersInfoTO().getPersKeyNo(), "selfRisk,personInfo");
                if(personScanningCountTO != null){
                    PersScanningCount4CoverTO persScanningCount4CoverTO = new PersScanningCount4CoverTO();
                    persScanningCount4CoverTO.setHasAdministrativePenaltyData(personScanningCountTO.getAdministrativePenaltyCount()>0?1:0);
                    persScanningCount4CoverTO.setHasDishonestJudgmentDebtorsData(personScanningCountTO.getDishonestJudgmentDebtorsCount()>0?1:0);
                    persScanningCount4CoverTO.setHasJudgmentDebtorsData(personScanningCountTO.getJudgmentDebtorsCount()>0?1:0);
                    persScanningCount4CoverTO.setHasRestrictionofHighConsumptionData(personScanningCountTO.getRestrictionofHighConsumptionCount()>0?1:0);
                    persScanningCount4CoverTO.setHasCriminalCasesData(personScanningCountTO.getCriminalCasesCount()>0?1:0);
                    persScanningCount4CoverTO.setHasRestrictedDepartureData(personScanningCountTO.getRestrictedDepartureCount()>0?1:0);
                    persScanningCount4CoverTO.setHasTerminationofExecutionData(personScanningCountTO.getTerminationofExecutionCount()>0?1:0);
                    persScanningCount4CoverTO.setHasInquiryandEvaluationData(personScanningCountTO.getInquiryandEvaluationCount()>0?1:0);
                    persScanningCount4CoverTO.setHasFreezingofEquityData(personScanningCountTO.getFreezingofEquityCount()>0?1:0);
                    persScanningCount4CoverTO.setHasLoanContractDisputesDefendantData(personScanningCountTO.getLoanContractDisputesDefendantCount()>0?1:0);
                    persScanningCount4CoverTO.setHasPropertyPreservationCasesDefendantData(personScanningCountTO.getPropertyPreservationCasesDefendantCount()>0?1:0);
                    persScanningCount4CoverTO.setHasCooperationDisputesDefendantData(personScanningCountTO.getCooperationDisputesDefendantCount()>0?1:0);
                    persScanningCount4CoverTO.setHasEquityDisputesDefendantData(personScanningCountTO.getEquityDisputesDefendantCount()>0?1:0);
                    persScanningCount4CoverTO.setHasOtherDisputesDefendantData(personScanningCountTO.getOtherDisputesDefendantCount()>0?1:0);
                    persScanningCount4CoverTO.setHasLoanContractDisputesPlaintiffData(personScanningCountTO.getLoanContractDisputesPlaintiffCount()>0?1:0);
                    persScanningCount4CoverTO.setHasPropertyPreservationCasesPlaintiffData(personScanningCountTO.getPropertyPreservationCasesPlaintiffCount()>0?1:0);
                    persScanningCount4CoverTO.setHasCooperationDisputesPlaintiffData(personScanningCountTO.getCooperationDisputesPlaintiffCount()>0?1:0);
                    persScanningCount4CoverTO.setHasEquityDisputesPlaintiffData(personScanningCountTO.getEquityDisputesPlaintiffCount()>0?1:0);
                    persScanningCount4CoverTO.setHasOtherDisputesPlaintiffData(personScanningCountTO.getOtherDisputesPlaintiffCount()>0?1:0);
                    persScanningCount4CoverTO.setHasFinancialSupervisorsData(personScanningCountTO.getFinancialSupervisorsCount()>0?1:0);
                    persScanningCount4CoverTO.setHasHandlingofRegulatoryViolationsData(personScanningCountTO.getHandlingofRegulatoryViolationsCount()>0?1:0);
                    persScanningCount4CoverTO.setHasEquityPledgeData(personScanningCountTO.getEquityPledgeCount()>0?1:0);
                    persScanningCount4CoverTO.setHasPledgeofEquityData(personScanningCountTO.getPledgeofEquityCount()>0?1:0);
                    persScanningCount4CoverTO.setHasGuaranteesInformationData(personScanningCountTO.getGuaranteesInformationCount()>0?1:0);
                    persScanningCount4CoverTO.setHasAlterationofLegalRepresentativeData(personScanningCountTO.getAlterationofLegalRepresentativeCount()>0?1:0);
                    persScanningCount4CoverTO.setHasAlterationofActualControllerData(personScanningCountTO.getAlterationofActualControllerCount()>0?1:0);
                    persScanningCount4CoverTO.setHasAlterationofBeneficialOwnerData(personScanningCountTO.getAlterationofBeneficialOwnerCount()>0?1:0);
                    persScanningCount4CoverTO.setHasAlterationofKeyPersonsData(personScanningCountTO.getAlterationofKeyPersonsCount()>0?1:0);
                    persScanningCount4CoverTO.setHasAlterationofExternalCapitalInvestmentData(personScanningCountTO.getAlterationofExternalCapitalInvestmentCount()>0?1:0);
                    persScanningCount4CoverTO.setHasLegalData(personScanningCountTO.getLegalCount()>0?1:0);
                    persScanningCount4CoverTO.setHasInvestmentData(personScanningCountTO.getInvestmentCount()>0?1:0);
                    persScanningCount4CoverTO.setHasPositionData(personScanningCountTO.getPositionCount()>0?1:0);
                    persScanningCount4CoverTO.setHasHisLegalData(personScanningCountTO.getHisLegalCount()>0?1:0);
                    persScanningCount4CoverTO.setHasHisInvestmentData(personScanningCountTO.getHisInvestmentCount()>0?1:0);
                    persScanningCount4CoverTO.setHasHisPositionData(personScanningCountTO.getHisPositionCount()>0?1:0);
                    orderInitInfoTO.setPersScanningCount4CoverTO(persScanningCount4CoverTO);
                }
            } else if (StringUtils.equals(mode, "hkCorp") && orderInitInfoTO.getCorpInfo() != null && StringUtils.isNotBlank(orderInitInfoTO.getCorpInfo().getCorpKeyNo())) {
                //香港企业使用
                CorpHKDimensionCountTO corpHKDimensionCountTO = CompanyDetailsInterface.getHKCorpScanningCount(orderInitInfoTO.getCorpInfo().getCorpKeyNo());
                if (corpHKDimensionCountTO != null) {
                    CorpHKScanningCount4CoverTO corpHKScanningCount4CoverTO = new CorpHKScanningCount4CoverTO();
                    corpHKScanningCount4CoverTO.setSecretaryCount(corpHKDimensionCountTO.getSecretaryCount() > 0 ? 1 : 0);
                    corpHKScanningCount4CoverTO.setDirectorsCount(corpHKDimensionCountTO.getDirectorsCount() > 0 ? 1 : 0);
                    corpHKScanningCount4CoverTO.setShareCapitalStructureCount(corpHKDimensionCountTO.getShareCapitalStructureCount() > 0 ? 1 : 0);
                    corpHKScanningCount4CoverTO.setTotalShareCapitalCount(corpHKDimensionCountTO.getTotalShareCapitalCount() > 0 ? 1 : 0);
                    orderInitInfoTO.setCorpHKScanningCount4CoverTO(corpHKScanningCount4CoverTO);
                }
            }
        }
    }

    public KeyNoResult getKeyResult(String id, String keyNoEncrypted) throws MessageException {
        if (StringUtils.isNotBlank(keyNoEncrypted)) {
            String[] splits = StringUtils.split(keyNoEncrypted, Constants.TokenSplit.GRAPH);
            if (splits == null || splits.length != 3) {
                logger.error("token is invalid");
                return new KeyNoResult();
            }
            String keyNo = splits[0];
            String orderId = splits[1];
            String token = splits[2];
            String keyNoByOrderId = getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), orderId);
            if (validateSha256Token(keyNoByOrderId, keyNo, token)) {
                return new KeyNoResult(keyNo, orderId, keyNoByOrderId);
            } else {
                logger.error("token is invalid");
                return new KeyNoResult();
            }
        } else {
            String keyNo = getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), id);
            return new KeyNoResult(keyNo, id, keyNo);
        }
    }

    /**
     * 生成加密的keyNo
     *
     * @param orderId 不为空
     * @param keyNoByOrderId 不为空
     * @param keyNo 不为空
     * @return
     */
    public static String generateKeyNoEncrypted(String orderId, String keyNoByOrderId, String keyNo) {
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(keyNoByOrderId) || StringUtils.isBlank(keyNo)) {
            return null;
        }
        String generatedToken = generateTokenForStock(keyNoByOrderId, keyNo);
        return keyNo + Constants.TokenSplit.GRAPH + orderId + Constants.TokenSplit.GRAPH + generatedToken;
    }

    private static boolean validateSha256Token(String keyNoByOrderId, String keyNo, String token) {
        return StringUtils.validateSha256Token(keyNoByOrderId + keyNo, token);
    }

    private static String generateTokenForStock(String keyNoByOrderId, String keyNo) {
        if(StringUtils.isBlank(keyNoByOrderId) || StringUtils.isBlank(keyNo)){
            return null;
        }
        return StringUtils.getSha256Token4Validate(keyNoByOrderId + keyNo);
    }


    /**
     * added for v1.5.8 KNZT-2626
     * 生成orderId+keyNo+时间戳的token
     *
     * @param orderId
     * @param keyNo
     * @return String
     */
    public static String generateDataToken(String orderId, String keyNo) {
        if (StringUtils.isBlank(keyNo)) {
            return orderId;
        }

        long currentTimeMillis = System.currentTimeMillis();
        String token = StringUtils.getSha256Token4Validate(orderId + keyNo + currentTimeMillis);
        return orderId + Constants.TokenSplit.DATA + keyNo + Constants.TokenSplit.DATA + currentTimeMillis + Constants.TokenSplit.DATA + token;
    }

    /**
     * added for v1.5.8 KNZT-2626
     * 校验token是否有效
     *
     * @param orderId
     * @param keyNo
     * @param timeMillisStr
     * @param token
     * @return boolean
     */
    private static boolean validateDataToken(String orderId, String keyNo, String timeMillisStr, String token) throws MessageException {
        long timeMillis = Long.parseLong(timeMillisStr);
        long currentTimeMillis = System.currentTimeMillis();
        long diffHours = (currentTimeMillis - timeMillis) / (60*60*1000);
        if (diffHours >= 24) {
            throw new MessageException("err.data.token.tip");
        } else {
            return StringUtils.validateSha256Token(orderId + keyNo + timeMillisStr, token);
        }
    }

    /**
     * 开放平台保存订单信息
     *
     * @param orderTO
     * @return
     * @throws MessageException
     */
    @DingErrorMsg(referenceNoPrefix = "saveOrder4OpenApi-")
    public void saveOrder4OpenApi(OrderRecordsTO orderTO) throws MessageException {
        if (orderTO == null) {
            throw new MessageException("msg:No order detail");
        }
        logger.info("save openApi interface record:{}", JSON.toJSONString(orderTO));
        if (StringUtils.isAnyBlank(orderTO.getCompanyId(), orderTO.getApiCode())) {
            logger.error(String.format("必要参数companyId:%s,apiCode:%s,数据缺失", orderTO.getCompanyId(), orderTO.getApiCode()));
            throw new MessageException("err.param.invalid");
        }
        String companyId = orderTO.getCompanyId();
        Company company = commSysCompanyService.getCompany(companyId);
        if (company == null) {
            logger.error("company doesn't exists:" + orderTO.getCompanyId());
            throw new MessageException("err.param.invalid");
        }
        GlobalProductTypeOpenApiCodeEnum apiCodeEnum = GlobalProductTypeOpenApiCodeEnum.getByApiCode(orderTO.getApiCode());
        if (Objects.isNull(apiCodeEnum)) {
            logger.error(String.format("apiCode:%s,对应的服务产品不存在", orderTO.getApiCode()));
            throw new MessageException("err.param.invalid");
        }

        // updated for v1.5.8 KNZT-2587
        TblReportChargeUnit rptUnit = commTblReportChargeUnitService.getChargeUnitByUniqueKey(orderTO.getCompanyId(), apiCodeEnum.getReportGroup(), apiCodeEnum.getReportType());
        if (rptUnit == null) {
            logger.error("invalid param charge unit");
            throw new MessageException("err.param.invalid");
        }

        SysCompInfoFuncCount funcCount = commSysCompInfoFuncCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount, "err.amount.insufficient");

        TblCompReportOrder rptOrder = new TblCompReportOrder();
        rptOrder.setKeyNoType(ReportGroupMappingEnum.getKeyNoTypeByReportGroup(rptUnit.getReportGroup()));
        if (StringUtils.isBlank(orderTO.getKeyNo()) && Constants.Report.KEY_NO_TYPE_CORP.equals(rptOrder.getKeyNoType())) {
            logger.warn("CorpKeyNo is required");
            throw new MessageException("err.param.invalid");
        }

        rptOrder.setCompanyId(companyId);
        rptOrder.setId(IdGenUtil.uuid());
        rptOrder.setUserId(company.getMainUserId());
        rptOrder.setKeyNo(orderTO.getKeyNo());
        rptOrder.setCorpKeyNo(orderTO.getKeyNo());
        rptOrder.setCorpName(orderTO.getCorpName());
        rptOrder.setCorpNameEn(orderTO.getCorpNameEn());
        //added by v1.3.7 KNZT-1963 修复香港企业名称为空的问题
        if(StringUtils.isBlank(orderTO.getCorpName()) && StringUtils.isNotBlank(orderTO.getCorpNameEn())){
            rptOrder.setCorpName(orderTO.getCorpNameEn());
            rptOrder.setCorpNameEn(null);
        }
        rptOrder.setCorpNumber(orderTO.getRegNo());
        rptOrder.setCorpStatusCheck(orderTO.getCorpShortStatusCn());
        rptOrder.setApiOrderNo(orderTO.getApiOrderNo());
        if(StringUtils.equals(rptUnit.getReportGroup(),Constants.Report.REPORT_GROUP_KYB_HK)){
            // removed for v2.1.2 chenbl KNZT-6194
            // added for KNZT-4838 香港订单为 basic + ar时，传入标记，区分报表取自周年申报还是法团成立表的数据
//            if (GlobalProductTypeOpenApiCodeEnum.CORP_HK_BAS_AR.getApiCode().equals(orderTO.getApiCode())) {
//                rptOrder.setExtraInfo1(orderTO.getArType());
//            }
            rptOrder.setCorpStatus(HkStatusEnum.getStatusEnByStatusCn(orderTO.getCorpShortStatusCn()));
        }else if(StringUtils.equals(rptUnit.getReportGroup(),Constants.Report.REPORT_GROUP_SG)){
            rptOrder.setCorpStatus(orderTO.getCorpStatusEn());
        }else{
            //除香港与新加坡之外
            rptOrder.setCorpStatus(HkStatusEnum.getStatusEnByStatusCn(orderTO.getCorpShortStatusCn()));
        }
        rptOrder.setCorpDistrict(orderTO.getAddress());
        rptOrder.setCorpDateOfReg(orderTO.getStartDate());
        if(StringUtils.equals(rptUnit.getReportGroup(),Constants.Report.REPORT_GROUP_SG)){
            rptOrder.setCorpJurisdiction(Constants.JURISDICTION.SG);
        }else{
            rptOrder.setCorpJurisdiction(FuzzySearchInterface.getJurisdictionByKeyNo(orderTO.getKeyNo()));
        }
        rptOrder.setDataResource(Constants.OrderDataResource.API);
        rptOrder.setBeginDate(new Date());
//        rptOrder.setEndDate(new Date()); // 异步订单，等实际完成再回填，有延时任务和定时任务自动刷新
        rptOrder.setReportGroup(rptUnit.getReportGroup());
        rptOrder.setReportType(rptUnit.getReportType());
        rptOrder.setReportName(rptUnit.getReportName());
        rptOrder.setRptStatus(OrderStatusEnum.NONE.getCode());
        rptOrder.setUnit(rptUnit.getUnit());
        rptOrder.setTotalUnit(rptUnit.getUnit());
        rptOrder.setUnitGroup(rptUnit.getUnitGroup());
        rptOrder.setOrderNo(generateOrderNo(rptUnit.getReportType())); // updated for v2.1.6 fengsw KNZT-6513
        rptOrder.setLoginName(Objects.nonNull(company.getMainUser()) ? company.getMainUser().getLoginName() : null);
        rptOrder.setCreateDate(new Date());
        rptOrder.setUpdateDate(new Date());
        rptOrder.setUpdateBy(company.getMainUser());
        rptOrder.setCreateBy(company.getMainUser());
        rptOrder.setPayType(PayTypeEnum.UNIT.getCode());
        // added for v1.5.8 KNZT-KNZT-2616 去除企业、人员记录
        removeCorpPersonField(company.getDataSave(), rptOrder);
        dao.insert(rptOrder);
        logger.info("save openApi interface record end");
        transactionBusinessService.payOrderAndCreateTransaction(rptOrder, rptUnit.getContractDeliveryId()); // added for v1.8.8 KNZT-3324

        // 添加订单状态检查延时任务 added for v1.9.8 KNZT-4976
        addStatusCheckDelayedTaskIfNecessary(rptOrder);
    }

    // added for v1.8.0 KNZT-3595
    // updated for v1.8.7 KNZT-3972
    // updated for v2.1.3 chenbl KNZT-6295
    @Deprecated
    public InfoBeforeSubmitTO getInfoBeforeSubmit(InfoBeforeSubmitForm form) throws MessageException {
        InfoBeforeSubmitTO rtn = new InfoBeforeSubmitTO();
        UnitGroupEnum unitGroupEnum = UnitGroupEnum.getByGroup(form.getUnitGroup());
        if (unitGroupEnum == null) {
            unitGroupEnum = UnitGroupEnum.CN_UNIT;
        }
        String corpKeyNo = null;
        if (Constants.Report.KEY_NO_TYPE_PERS.equals(form.getKeyNoType())) {
            corpKeyNo = form.getCorpKeyNo();
            GetDetailOfSimpleTO detailOfSimple = ECILocalInterface.getDetailOfSimple(form.getKeyNo());
            if (detailOfSimple != null) {
                if (PinyinUtils.notConvert2Pinyin(detailOfSimple.getName())) {
                    rtn.setPersNameEn(detailOfSimple.getName());
                } else {
                    rtn.setPersName(detailOfSimple.getName());
                    rtn.setPersNameEn(PinyinUtils.chineseNameToPinyin(detailOfSimple.getName()));
                }
            }
            PersonJobResult personJobResult = CompanyDetailsInterface.listPersonJob4Global(form.getKeyNo(), null, null, null, null, null, null,
                    null, null, null, null, null, null, null, null, "onlyQueryCount");
            if (personJobResult != null) {
                rtn.setRelatedCorpCount(personJobResult.getTotalCount());
            }
        } else {
            corpKeyNo = form.getKeyNo();
        }
        if (unitGroupEnum == UnitGroupEnum.CN_UNIT) {
            if (StringUtils.isNotBlank(corpKeyNo)) {
                ApiGlobalCorpDetailTO apiGlobalCorpDetailTO = CompanyDetailsInterface.getECIInfoVerifyInfo4Global(corpKeyNo);
                if (apiGlobalCorpDetailTO != null) {
                    rtn.setCorpName(apiGlobalCorpDetailTO.getCorpName());
                    rtn.setEnglishName(apiGlobalCorpDetailTO.getEnglishName());
                    rtn.setAddress(apiGlobalCorpDetailTO.getAddress());
                    rtn.setAddressEn(translaterService.getEnglishText(rtn.getAddress(), corpKeyNo, rtn.getCorpName(), SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS));
                    rtn.setCreditCode(apiGlobalCorpDetailTO.getCreditCode());
                    rtn.setOrgNo(apiGlobalCorpDetailTO.getOrgNo());
                    rtn.setStartDate(apiGlobalCorpDetailTO.getStartDate());
                    rtn.setJurisdiction(apiGlobalCorpDetailTO.getJurisdiction());
                    rtn.setImageUrl(apiGlobalCorpDetailTO.getImageUrl());
                    rtn.setRegNo(apiGlobalCorpDetailTO.getNo());
                    String shortStatusEnByShortStatusCn = RegistrationStatusEnum.getShortStatusEnByShortStatusCn(apiGlobalCorpDetailTO.getShortStatus());
                    rtn.setShortStatus(ForeignCorpStatusEnum.getForeignStatus(shortStatusEnByShortStatusCn, null));
                }
            }
        } else if (unitGroupEnum == UnitGroupEnum.SG_UNIT) {
            SgBasicTO basic = sgReportOrderCustomService.getBasic(corpKeyNo);
            if (basic != null) {
                rtn.setCorpName(basic.getCompName());
                rtn.setAddress(basic.getContactAddress());
                rtn.setStartDate(basic.getCompStartDate());
                rtn.setJurisdiction(UnitGroupEnum.SG_UNIT.getName());
                rtn.setRegNo(basic.getCompNo());
                rtn.setShortStatus(basic.getCompStatusLabel());
            }
        }
        return rtn;
    }

    // updated for v1.8.8 KNZT-3979
    public HKInfoBeforeSubmitTO getHKInfoBeforeSubmit(String keyNo) throws MessageException {
        HKInfoBeforeSubmitTO rtn = new HKInfoBeforeSubmitTO();
        // 校验香港查册人信息完整 added for v2.0.0 chenbl KNZT-5094
        boolean validateHkSearcherInfo = commSysCompanyService.validateHkSearcherInfo(UserUtils.getUserCompanyId());
        if (!validateHkSearcherInfo) {
            throw new MessageException(Constants.MessageExceptionKey.HK_SEARCHER_INFO_MISSING);
        }
        HkCorpBasicDetailTO detail = qccOvsBasicService.getHkCorpBasicDetailTO(keyNo);
        if (detail == null) {
            return rtn;
        }
        BeanUtils.copyProperties(detail, rtn);
        rtn.setStatusEn(ForeignCorpStatusEnum.getForeignStatus(rtn.getStatusEn(), null));
        rtn.setJurisdiction(UnitGroupEnum.HK_UNIT.getName());
        rtn.setImageUrl(SgCompanyDetailsInterface.getGlobalCorpImageUrlInfo(keyNo));// added for v2.1.4 fengsw KNZT-6416 imageUrl
        rtn.setListingFlag(detail.isListingFlag());
        // updated for v2.0.0 chenbl KNZT-5258
        if (StringUtils.isContainChinese(rtn.getAddress())) {
            rtn.setAddress(translaterService.getEnglishText(rtn.getAddress(), "", "", SysConstants.TRANSLATE_HIST_FUNCTION_ADDRESS));
        }
        // added for v1.9.5 KNZT-4519
        OverseaMaintainStatusTO overseaTainStatus4Hk = GlobalCompanyDetailsInterface.getOverseaTainStatus4Hk();
        Boolean maintain = Optional.ofNullable(overseaTainStatus4Hk).map(OverseaMaintainStatusTO::getMainTain).orElse(true);
        if (maintain) {
            rtn.setCanOrder(false);
            rtn.setOrderErrorCode(HKOrderErrorEnum.OFFICIAL_SITE_MAINTENANCE.getCode());
            rtn.setOrderErrorMsg(I18NUtil.getMessage("err.hk.maintenance"));
            rtn.setMaintenanceEndTime(Optional.ofNullable(overseaTainStatus4Hk).map(OverseaMaintainStatusTO::getMaintainEndTime).orElse(null));
            return rtn;
        }

        JsonResultList<HongKongAnnouncementTO> hkAnnouncementPage = IntranetInterface.getHkAnnouncementPage(keyNo, "1", "1000");
        List<HongKongAnnouncementTO> hkAnnouncements = hkAnnouncementPage.getResultList();
        boolean companyCanOrder = companyCanOrder(hkAnnouncements, detail);
        rtn.setCanOrder(companyCanOrder);
        if (!companyCanOrder) {
            rtn.setOrderErrorCode(HKOrderErrorEnum.COMPANY_NOT_SUPPORTED.getCode());
            rtn.setOrderErrorMsg(HKOrderErrorEnum.COMPANY_NOT_SUPPORTED.getDesc());
            return rtn;
        }
        // added for v1.7.8 KNZT-3493
        String companyTypeEn = detail.getCompanyTypeEn();
        Set<String> anTypeList = IntranetInterface.listAnnualOrApplicationType(hkAnnouncements);
        logger.info(String.format("keyNo:%s, 公告类型有：%s", keyNo, StringUtils.join(anTypeList, ",")));
        if (StringUtils.isNotBlank(companyTypeEn) && !HkCompanyTypeEnum.COMPANY_TYPE7.getCompanyTypeEn().equals(companyTypeEn) && CollectionUtils.isNotEmpty(anTypeList)) {
            // 不是【注册非香港公司】并且存在周年申报表才可以下单
            rtn.setCanOrderBasicAr(true);
            rtn.setAnTypeList(new ArrayList<>(anTypeList));
        } else {
            rtn.setCanOrderBasicAr(false);
        }
        return rtn;
    }

    // added for v1.8.8 KNZT-3979
    public static boolean companyCanOrder(List<HongKongAnnouncementTO> hkAnnouncements, HkCorpBasicDetailTO detail) {
        boolean canOrder = false;
        String corpName = StringUtils.getNotBlankStr(detail.getCompanyNameChn(), detail.getCompanyTypeEn());
        logger.info(String.format("corpName:%s, 公告列表条数：%s", corpName, (CollectionUtils.isEmpty(hkAnnouncements) ? 0 : hkAnnouncements.size())));
        if (CollectionUtils.isNotEmpty(hkAnnouncements)) {
            // 存在公告列表
            boolean existHkAnnouncement4canOrder = hkAnnouncements.stream().anyMatch(hongKongAnnouncementTO -> Constants.HkAnnouncement.status.AVAILABLE.equals(hongKongAnnouncementTO.getDocumentStatus())
                    && StringUtils.equalsAny(hongKongAnnouncementTO.getDocumentType(), Constants.HkAnnouncement.ANNUAL_TYPE, Constants.HkAnnouncement.APPLICATION, Constants.HkAnnouncement.APPLICATION_F, Constants.HkAnnouncement.ANNUAL_RETURN_F));
            logger.info(String.format("corpName:%s, 公告列表筛选结果：%s", corpName, existHkAnnouncement4canOrder));
            if (existHkAnnouncement4canOrder) {
                // 存在状态可供查阅，并且类型在范围内的
                if (StringUtils.isNotBlank(detail.getCompanyNumber())) {
                    // 商业登记号码不为空
                    HongKongAnnouncementTO lastHongKongAnnouncement = hkAnnouncements.get(0);
                    logger.info(String.format("corpName:%s, 公告列表筛选最新documentNumber：%s", corpName, lastHongKongAnnouncement.getDocumentNumber()));
                    if (StringUtils.isNotBlank(lastHongKongAnnouncement.getDocumentNumber())) {
                        // 最新公告中的公司编号不为空
                        boolean companyType4CanOrder = !StringUtils.equalsAny(detail.getCompanyType(), HkCompanyTypeEnum.COMPANY_TYPE6.getCompanyTypeCn(), HkCompanyTypeEnum.COMPANY_TYPE16.getCompanyTypeCn(),
                                HkCompanyTypeEnum.COMPANY_TYPE12.getCompanyTypeCn(), HkCompanyTypeEnum.COMPANY_TYPE22.getCompanyTypeCn(), HkCompanyTypeEnum.COMPANY_TYPE25.getCompanyTypeCn(),
                                HkCompanyTypeEnum.COMPANY_TYPE15.getCompanyTypeCn());
                        if (companyType4CanOrder) {
                            // 公司类型不是有限合伙基金并且不是有限责任合伙并且不是开放式基金型公司
                            canOrder = true;
                        }
                    }
                }
            }
        }
        return canOrder;
    }

    public static boolean companyCanOrder4Tw(QccOvsBasic qccOvsBasic) {
        QccOvsBasicInfo basicInfo = MappingUtils.getValue(qccOvsBasic, QccOvsBasic::getBasicInfo);
        String generalStr = MappingUtils.getValue(basicInfo, QccOvsBasicInfo::getGeneral);
        boolean canBuy = false;
        if (StringUtils.isNotBlank(generalStr)) {
            try {
                JSONObject generalJo = JsonUtils.parseObject(generalStr);
                if (generalJo != null) {
                    Boolean isCanBuy = generalJo.getBoolean("IsCanBuy");
                    canBuy = isCanBuy != null && isCanBuy;
                }
            } catch (Exception e) {
                logger.warn("General Value parse exception, general value: " + generalStr);
            }
        }
        return canBuy;
    }

/*    *//**
     * added for v1.5.0 KNZT-1523 根据订单日期分页批量
     *
     * @param pageIndex 偏移量
     * @param size 数量
     * @param lastMinDate 最小的订单日期
     * @return List<TblCompReportOrder>
     *//*
    public List<TblCompReportOrder> pageCorpReportOrderByBeginDate(int pageIndex, int size, Date lastMinDate) {
        pageIndex = pageIndex <= 0 ? 1 : pageIndex;
        int offset = (pageIndex - 1) * size;
        return dao.pageCorpReportOrderByBeginDate(offset, size, lastMinDate);
    }


    *//**
     * added for v1.5.0 KNZT-1523 根据id更新企业名称
     *
     * @param id
     * @param corpName
     * @param corpNameEn
     * @return int
     *//*
    public int updateCorpNameById(String id, String corpName, String corpNameEn) {
        return dao.updateCorpNameById(id, corpName, corpNameEn);
    }*/

    /**
     * added for v1.5.1 KNZT-2315 根据订单
     *
     * @param id 订单id
     * @return TblCompReportOrder
     */
    public TblCompReportOrder getByIdAndCompany(String id, String companyId) {
        return dao.getByIdAndCompany(id, companyId);
    }

    public String existReportByCompanyId(String companyId) {
        return dao.existReportByCompanyId(companyId);
    }

    /**
     * added for v1.6.0 KNZT-2730
     * updated for v1.8.0 KNZT-3524
     * 根据reportType计算订单数量
     *
     * @param companyId
     * @param loginName
     * @return Integer
     */
    public List<ReportOrderUnitInfo> getReportOrderUnitInfo(String companyId, String loginName, Date minOrderCreatedDate, Date maxOrderCreatedDate, boolean excludeInvalid) {
        return dao.getReportOrderUnitInfo(companyId, loginName, minOrderCreatedDate, maxOrderCreatedDate, excludeInvalid);
    }



    /**
     * added for v1.6.0 KNZT-2560
     * 根据companyId 获取订单
     *
     * @return List<TblCompReportOrder>
     */
    public List<TblCompReportOrder> listByCompany(String companyId) {
        return dao.listByCompany(companyId);
    }

    /**
     * added for v1.7.1 KNZT-3149【DATA PATCH】匹配原服务 Snapshot 20美金 至 KYC Basic， 匹配360 Company Profile 至 KYC Advanced （OCBC应该没有数据），做好相应的衔接沟通
     * 线上数据patch，仅本次上线使用
     */
    public void handleOrderInfoByCompanyId() {
        // 逻辑删除所有账号的人员核验、高管订单信息
        dao.logicDeletePersOrderData();
    }


    /**
     * added for v1.7.3 KNZT-3104 批量补偿生产订单报告
     * @param idList
     */
    public void compensateCorpReport(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<TblCompReportOrder> orderList = dao.batchGet(idList);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (TblCompReportOrder order : orderList) {
            try {
                this.createReport(order);
            } catch (Exception e) {
                logger.error("compensateCorpReport error, orderId:{}, error : ", order.getId(), e);
            }
        }
    }


    /**
     * added for v1.7.3 KNZT-3104 轮询获取报告
     *
     * @param orderId
     * @return String
     */
    public String getReportUrlByLoop(String orderId) throws MessageException {
        TblCompReportOrder order = dao.get(orderId);
        if (Objects.isNull(order)) {
            return null;
        }
        return order.getUrl();
    }

    /**
     * 获取订单轮询结果
     * 包含订单ID、订单编号、报告生成时间、支付响应时间、URL
     *
     * @param orderId 订单ID
     * @return OrderPollingResultTO 订单轮询结果
     */
    public OrderPollingResultTO getOrderPollingResult(String orderId) {
        TblCompReportOrder order = dao.get(orderId);
        if (Objects.isNull(order)) {
            return null;
        }

        OrderPollingResultTO result = new OrderPollingResultTO();
        result.setOrderId(order.getId());
        result.setOrderNo(order.getOrderNo());
        result.setReportGenerateTime(order.getEndDate());
        result.setPayResponseTime(order.getPayResponseTime());
        result.setUrl(order.getUrl());
        result.setStatus(order.getRptStatus());
        return result;
    }


    /**
     * added for v1.8.2 KNZT-3688 提供高管snapshot报告数据接口
     *
     * @param form
     * @return
     * @throws MessageException
     */
    public OrderInitInfoTO getInitOrderInfoForPers(ReportOrderInitForm form) throws MessageException {
        if (StringUtils.isBlank(form.getPersKeyNo())) {
            logger.warn("invalid param PersKeyNo is required");
            throw new MessageException("err.access");
        }
        TblCompReportOrder tblCompReportOrder = this.getLatestPersOrderInfoByKeyNo(form.getPersKeyNo());
        if (Objects.isNull(tblCompReportOrder) || (StringUtils.isBlank(tblCompReportOrder.getPersName()) && StringUtils.isBlank(tblCompReportOrder.getPersNameEn()))) {
            logger.warn("PersName or PersNameEn is missed");
            throw new MessageException("err.access");
        }
        OrderInitInfoTO result = new OrderInitInfoTO();
        PersInfoTO persInfoTO = new PersInfoTO();
        persInfoTO.setPersKeyNo(form.getPersKeyNo());
        persInfoTO.setPersName(tblCompReportOrder.getPersName());
        if(!StringUtils.equals(tblCompReportOrder.getPersName(), tblCompReportOrder.getPersNameEn())) {
            persInfoTO.setPersNameEn(tblCompReportOrder.getPersNameEn());
        }
        result.setPersInfoTO(persInfoTO);
        result.setUserInfo(this.getUserInfo(tblCompReportOrder.getUserId()));
        return result;
    }

    /**
     * added for v1.8.2 KNZT-3688 提供高管snapshot报告数据接口
     *
     * @param persKeyNo
     * @return
     */
    public TblCompReportOrder getLatestPersOrderInfoByKeyNo(String persKeyNo) {
        return dao.getLatestPersOrderInfoByKeyNo(persKeyNo);
    }

    // added for v1.8.6 KNZT-3873
    // updated for v1.9.4 KNZT-4193
    public String generateTokenByOrderId(String orderId) throws MessageException {
        TblCompReportOrder order = dao.getByIdAndCompany(orderId, UserUtils.getUserCompanyId());
        if (order == null) {
            return null;
        }
        // 补充财税判断逻辑所需参数 updated for v2.0.2 chenbl KNZT-5271
        ReportOrderInfoOfBillTO orderTo = ReportOrderInfoOfBillTO.build(order);
        if (ReportTypeEnum.FIN_TAX.getCode().equals(orderTo.getReportType())) {
            TblCompReportOrderFinTaxCondition finTaxCondition = new TblCompReportOrderFinTaxCondition();
            finTaxCondition.setOrderNoList(Lists.newArrayList(orderTo.getOrderNo()));
            List<OrderFinTaxSimpleTO> finTaxSimpleList = finTaxService.getOrderFinTaxSimpleList(UserUtils.getUserCompanyId(), finTaxCondition);
            if (CollectionUtils.isNotEmpty(finTaxSimpleList)) {
                OrderFinTaxSimpleTO finTaxSimpleTO = finTaxSimpleList.get(0);
                orderTo.setAuthStatus(finTaxSimpleTO.getAuthStatus());
                orderTo.setDataStatus(finTaxSimpleTO.getDataStatus());
            }
        }
        // updated for v1.9.3 KNZT-4382
        ReportOrderInfoOfBillTO.Action viewAction = buildActionViewIfNecessary(orderTo, new Date(), getReport360ViewValidDays(), getLocalReportViewValidDays2c());
        if (viewAction == null) {
            logger.info("generateTokenByOrderId viewAction is null, orderId:{}", orderId);
            return null;
        } else {
            if (viewAction.isEnabled()) {
                return generateDataToken(order.getId(), order.getKeyNo());
            } else {
                logger.info("generateTokenByOrderId not allowed, orderId:{}", orderId);
                return null;
            }
        }
    }

    /**
     * added for v1.8.8 KNZT-4033
     * 清除公司下所有的订单数据
     *
     * @param companyId
     * @return
     */
    public void deleteByCompanyId(String companyId) {
        TblCompReportOrder entity = new TblCompReportOrder();
        entity.setCompanyId(companyId);
        entity.preUpdate();
        dao.deleteByCompanyId(entity);
    }

    /**
     * added for v1.9.1 KNZT-3900
     * 开放平台用户，提供境外接口产品服务时，查询额度是否足够
     * @param recordsTO
     * @return
     * @throws MessageException
     */
    public RemainingUnitCheckResultTO checkRemainingUnitForOpenApiByCompanyId(OrderRecordsTO recordsTO) throws MessageException {
        if (StringUtils.isAnyBlank(recordsTO.getCompanyId(), recordsTO.getApiCode())) {
            logger.error(String.format("必要参数companyId:%s,apiCode:%s,数据缺失", recordsTO.getCompanyId(), recordsTO.getApiCode()));
            throw new MessageException("err.param.invalid");
        }
        Company company = commSysCompanyService.getCompany(recordsTO.getCompanyId());
        if (Objects.isNull(company)) {
            logger.error(String.format("公司:%s,不存在", recordsTO.getCompanyId()));
            throw new MessageException("err.param.invalid");
        }

        GlobalProductTypeOpenApiCodeEnum apiCodeEnum = GlobalProductTypeOpenApiCodeEnum.getByApiCode(recordsTO.getApiCode());
        if (Objects.isNull(apiCodeEnum)) {
            logger.error(String.format("apiCode:%s,对应的服务产品不存在", recordsTO.getApiCode()));
            throw new MessageException("err.param.invalid");
        }

        TblReportChargeUnit rptUnit = commTblReportChargeUnitService.getChargeUnitByUniqueKey(recordsTO.getCompanyId(), apiCodeEnum.getReportGroup(), apiCodeEnum.getReportType());
        if (rptUnit == null || StringUtils.equals(Constants.NO, rptUnit.getEnabled())) {
            logger.error("invalid param charge unit info, group:{}, type:{}:", apiCodeEnum.getReportGroup(), apiCodeEnum.getReportType());
            throw new MessageException("err.account.no.service");
        }
        SysCompInfoFuncCount funcCountWithConsume = commSysCompInfoFuncCountService.lockByCompanyId(recordsTO.getCompanyId());
        BigDecimal remainingBalance = BigDecimal.ZERO;
        if (funcCountWithConsume != null) {
            remainingBalance = funcCountWithConsume.getTotalCount().subtract(funcCountWithConsume.getConsumedCount());
        }
        /* removed for v1.9.8 KNZT-4538
        Integer companyType = company.getType();
        if (CompTypeEnum.SIGN.getCode().equals(companyType)) {
            BigDecimal accRemainingBalance = prodAccService.summarizeProdAccRemainingUnit(recordsTO.getCompanyId(), functionTableId);
            remainingBalance = remainingBalance.compareTo(accRemainingBalance) > 0 ? accRemainingBalance : remainingBalance;
        }
        */
        boolean flag = rptUnit.getUnit().compareTo(remainingBalance) <= 0;
        // added for v1.9.8 KNZT-4538
        if (flag) {
            // updated for v2.0.2 chenbl KNZT-5294
            if (needCheck4ActualCost(company.getId(), rptUnit, null)) {
                flag = check4ActualCost(company.getId());
            }
        }
        return new RemainingUnitCheckResultTO(flag, remainingBalance);
    }

    /**
     * 根据payRelId获取订单数据
     * added for v1.9.3 KNZT-4193
     *
     * @param payRelId
     * @return
     */
    public List<TblCompReportOrder> getByPayRelId(String payRelId) {
        return dao.getByPayRelId(payRelId);
    }

    /**
     * 根据payRelId获取订单数据
     * added for v1.9.9 chenbl KNZT-5067
     *
     * @param companyId
     * @param payRelId
     * @return
     */
    public List<TblCompReportOrder> listByPayRelId(String companyId, String payRelId) {
        return dao.listByPayRelId(companyId, payRelId);
    }

    /**
     * 查询出在指定时间周期创建的未支付订单
     * added for v1.9.3 KNZT-4193
     * @param startDate
     * @param endDate
     * @return
     */
    public List<TblCompReportOrder> listUnpaidOrderInBeginDateRange(Date startDate, Date endDate) {
        TblCompReportOrderCondition condition = new TblCompReportOrderCondition();
        condition.setMinOrderCreatedDate(startDate);
        condition.setMaxOrderCreatedDate(endDate);
        condition.setPayStatusList(Lists.newArrayList(PayStatusEnum.UNPAID.getCode()));
        condition.setPayTypeList(PayTypeEnum.getContainsOnlineList());
        return dao.listReportByCondition(null, condition);
    }


    /**
     * 根据订单轮询
     * 24h内的订单可以生成token
     * added for v1.9.3 KNZT-4193
     *
     * @param orderId
     * @return String
     */
    @Deprecated // TODO lvcy
    public String generateLocalOrderDataToken(String orderId) throws MessageException {
        TblCompReportOrder order = this.getByIdAndCompany(orderId, UserUtils.getUserCompanyId());
        if (Objects.isNull(order)) {
            logger.info("generateLocalOrderDataToken order is not exist, orderId:{}", orderId);
            return null;
        }
        return generateLocalOrderDataToken(order);
    }

    /**
     * 根据订单轮询
     * 24h内的订单可以生成token
     * added for v1.9.3 KNZT-4193
     *
     * @param order
     * @return String
     */
    private String generateLocalOrderDataToken(TblCompReportOrder order) {
        String orderId = order.getId();
        if (!CompTypeEnum.isSelfPay(UserUtils.getUserCommInfo().getType())) {
            logger.info("generateLocalOrderDataToken user is not client, orderId:{}", orderId);
            return null;
        }
        if (!PayStatusEnum.PAID.getCode().equals(order.getPayStatus()) || Objects.isNull(order.getPayResponseTime())) {
            logger.info("generateLocalOrderDataToken order is not paid, orderId:{}", orderId);
            return null;
        }
        // 判断订单的beginDate是否超过24h
        Date submitOrderDate = order.getPayResponseTime();
        int localReportViewValidDays2c = getLocalReportViewValidDays2c();
        long tokenExpiryTime = submitOrderDate.getTime() + (long) localReportViewValidDays2c * 24 * 60 * 60 * 1000;
        if (tokenExpiryTime > System.currentTimeMillis()) {
            return generateDataToken(orderId, order.getKeyNo());
        }
        return null;
    }

    /**
     * 获取支付凭证
     * added for v1.9.3 KNZT-4193
     *
     * @param form
     * @return String
     */
    public String getPaymentClientSecret(ClientSecretForm form) throws MessageException {
        TblCompReportOrder order;
        if (StringUtils.isNotBlank(form.getPayRelId())) {
            List<TblCompReportOrder> orderList = this.getByPayRelId(form.getPayRelId());
            order = CollectionUtils.isNotEmpty(orderList) ? orderList.get(0) : null;
        } else if (StringUtils.isNotBlank(form.getOrderId())) {
            order = this.get(form.getOrderId());
        } else {
            return null;
        }
        // 判断订单的beginDate是否超过24h
        if (Objects.isNull(order) || !PayStatusEnum.UNPAID.getCode().equals(order.getPayStatus()) || Objects.isNull(order.getPayRelId())) {
            logger.info("getPaymentClientSecret failed, order:{}", form.getOrderId());
            return null;
        }
        PaymentIntent paymentIntent = StripePaymentInterface.retrievePaymentIntent(order.getPayRelId());
        return Objects.nonNull(paymentIntent) ? paymentIntent.getClientSecret() : null;
    }


    /**
     * 根据条件查询订单数量
     * added for v1.9.3 KNZT-4193
     * @param companyId
     * @return
     */
    public int readOnlyCountByCompanyId(String companyId, TblCompReportOrderCondition condition) {
        if (StringUtils.isEmpty(companyId)) {
            return 0;
        }
        return dao.countByCondition(companyId, condition);
    }

    /**
     * 获取订单概要信息
     * added for v1.9.3 KNZT-4193
     *
     * @param orderId
     * @return OrderGeneraInfoTO
     */
    public OrderGeneraInfoTO getGeneralInfo(String orderId) {
        String userCompanyId = UserUtils.getUserCompanyId();
        TblCompReportOrder order = dao.getByIdAndCompany(orderId, userCompanyId);
        if (Objects.isNull(order)) {
            return null;
        }
        OrderGeneraInfoTO orderGeneraInfoTO = OrderGeneraInfoTO.build(order);
        List<String> subOrderIdList = commTblCompReportOrderRelService.getRelOrderIdList(orderId, OrderRelTypeEnum.PARENT_SON, userCompanyId);
        if (CollectionUtils.isNotEmpty(subOrderIdList)) {
            List<TblCompReportOrder> subOrderList = dao.batchGet(subOrderIdList);
            orderGeneraInfoTO.setSubOrderList(OrderGeneraInfoTO.build(subOrderList));
        }
        return orderGeneraInfoTO;
    }

    // added for v1.9.9 chenbl KNZT-5067
    public OrderGeneraInfo4BatchOrderTO getGeneralInfos4BatchOrder(GeneralInfo4BatchOrderForm form) throws MessageException {
        OrderGeneraInfo4BatchOrderTO rtn = new OrderGeneraInfo4BatchOrderTO();
        String userCompanyId = UserUtils.getUserCompanyId();
        String payRelId = getPayRelIdByForm(form, userCompanyId);
        if (StringUtils.isBlank(payRelId)) {
            return rtn;
        }
        List<TblCompReportOrder> orders = dao.listByPayRelId(userCompanyId, payRelId);
        if (CollectionUtils.isEmpty(orders)) {
            return rtn;
        }
        List<OrderGeneraInfoTO> orderInfos = OrderGeneraInfoTO.build(orders);
        rtn.setOrderInfos(orderInfos);
        rtn.setTotalUnit(orders.stream().map(TblCompReportOrder::getTotalUnit).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 填充imageUrl added for v2.1.4 fengsw KNZT-6416 香港企业也要返回logo信息
        List<String> keyNos = orderInfos.stream().filter(orderGeneraInfoTO -> ReportTypeEnum.localCorpList().contains(orderGeneraInfoTO.getReportType()) || ReportTypeEnum.hkCorpList().contains(orderGeneraInfoTO.getReportType()))
                .map(OrderGeneraInfoTO::getKeyNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keyNos)) {
            List<CorpLogoInfoTO> corpImageUrlByKeyNos = CompanyDetailsInterface.getCorpImageUrlByKeyNos(keyNos);
            if (CollectionUtils.isNotEmpty(corpImageUrlByKeyNos)) {
                Map<String, CorpLogoInfoTO> keyno2InfoMap = corpImageUrlByKeyNos.stream().collect(Collectors.toMap(CorpLogoInfoTO::getKeyNo, Function.identity(), (k1, k2) -> k1));
                orderInfos.forEach(orderGeneraInfoTO -> {
                    CorpLogoInfoTO corpLogoInfoTO = keyno2InfoMap.get(orderGeneraInfoTO.getKeyNo());
                    if (Objects.nonNull(corpLogoInfoTO)) {
                        orderGeneraInfoTO.setImageUrl(corpLogoInfoTO.getImageUrl());
                    }
                });
            }
        }

        PaymentIntent paymentIntent = StripePaymentInterface.retrievePaymentIntent(payRelId);
        if (Objects.nonNull(paymentIntent)) {
            BigDecimal creditsByOnline = StripePaymentInterface.getPaymentAmountWithoutTax(paymentIntent);
            BigDecimal creditsToChargeByOnlineWithTax = StripePaymentInterface.getPaymentAmountWitTax(paymentIntent);
            rtn.setCreditsToChargeByOnline(creditsByOnline);
            rtn.setCreditsToChargeByOnlineWithTax(creditsToChargeByOnlineWithTax);
            rtn.setCreditsToChargeByProdAcc(rtn.getTotalUnit().subtract(creditsByOnline));
        }
        return rtn;
    }

    // added for v1.9.9 chenbl KNZT-5067
    private String getPayRelIdByForm(GeneralInfo4BatchOrderForm form, String userCompanyId) throws MessageException {
        String payRelId = form.getPayRelId();
        if (StringUtils.isBlank(payRelId)) {
            String orderId = form.getOrderId();
            if (StringUtils.isBlank(orderId)) {
                if (StringUtils.isNotBlank(form.getDataToken())) {
                    orderId = getOrderIdAndCheckDataToken(form.getDataToken());
                }
            }
            if (StringUtils.isNotBlank(orderId)) {
                TblCompReportOrder order = dao.getByIdAndCompany(orderId, userCompanyId);
                if (Objects.nonNull(order)) {
                    payRelId = order.getPayRelId();
                }
            }
        }
        return payRelId;
    }

    /**
     * added for v1.9.5 KNZT-4117
     * updated for v2.0.0 lvcy KNZT-5079 service层切片查询
     *
     * @param orderIdList
     * @return List<TblCompReportOrder>
     */
    public List<TblCompReportOrder> batchGet(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        List<TblCompReportOrder> result = Lists.newArrayList();
        for (List<String> partOrderIdList : Lists.partition(orderIdList, 500)) {
            List<TblCompReportOrder> orderList = dao.batchGet(partOrderIdList);
            if (CollectionUtils.isNotEmpty(orderList)) {
                result.addAll(orderList);
            }
        }
        return result;
    }

    // added for v1.9.6 KNZT-4642
    public Page<ReportOrder4ManagListTO> listReportOrder4Management(ReportOrder4ManagCondition condition) {
        Page<ReportOrder4ManagListTO> rtnPage = new Page<>();
        List<ReportOrder4ManagListTO> tos = dao.listReportOrder4Management(condition);
        BeanUtil.copyProperties(rtnPage, condition.getPage());

        List<String> hkDocIdList = tos.stream().filter(item -> ReportTypeEnum.HK_DOCUMENT.getCode().equals(item.getReportType())).map(ReportOrder4ManagListTO::getId).collect(Collectors.toList());
        Map<String, TblCompReportOrderHkDoc> hkDocMap = hkDocService.batchGetHkDocMap(hkDocIdList, OrderHkDocRelTypeEnum.ORDER.getCode());
        List<String> apiOrderTypeList = ReportTypeEnum.apiOrderTypeList();
        for (ReportOrder4ManagListTO to : tos) {
            to.generateTargetName();
            to.generateTotalUnitFormat();
            to.generatePaySuccessTime();
            to.setUnitGroupDesc(UnitGroupEnum.getNameCnByUnitGroup(to.getUnitGroup()));
            to.setService(ReportTypeEnum.getDesc(to.getReportType()));
            to.setPayTypeDesc(PayTypeEnum.getDescCnByCode(to.getPayType()));
            to.setPayStatusDesc(PayStatusEnum.getDescCnByCode(to.getPayStatus()));
            to.setRptStatusDesc(OrderStatusEnum.getDescCnByCode(to.getRptStatus()));
            to.setPayWayDesc("Stripe"); // 写死仅做展示用
            to.setCanCorrect(OrderStatusEnum.getUnSuccessFinalStatus().contains(to.getRptStatus()) || CompTypeEnum.TRIAL.getCode().equals(to.getCompType()) ?
                    Constants.NO : Constants.YES);
            to.setCanProxy(apiOrderTypeList.contains(to.getReportType()) ? Constants.YES : Constants.NO);
            if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(to.getReportType())) {
                TblCompReportOrderHkDoc hkDoc = hkDocMap.get(to.getId());
                if (Objects.nonNull(hkDoc)) {
                    to.setDocWholeName(hkDoc.generateWholeName());
                }
            }
        }
        rtnPage.setList(tos);
        return rtnPage;
    }

    /**
     * 境外企业向服务商购买数据
     * added for lvcy v1.9.9 KNZT-4980 抽象成方法，逻辑迁移至异步执行
     *
     * @param order
     * @return
     */
    public void buyGlobalReport(TblCompReportOrder order) throws MessageException {
        if (StringUtils.isNotBlank(order.getApiOrderNo())) {
            logger.info("BuyGlobalReport order exist apiOrderNo:{}, orderId:{}", order.getApiOrderNo(), order.getId());
            return;
        }
        // 补充查册人 updated for v2.1.0 chenbl KNZT-5238
        if (ReportTypeEnum.hkCorpList().contains(order.getReportType())) {
            HkSearcherInfo hkSearcherInfo = commSysCompanyService.getHkSearcherInfo(order.getCompanyId());
            if (ReportTypeEnum.HK_BAS.getCode().equals(order.getReportType())) {
                String corpNameHk = StringUtils.getNotBlankStr(order.getCorpNumber(), order.getCorpName(), order.getCorpNameEn());
                String apiOrderNo = GlobalCompanyDetailsInterface.createOrder4HKData(corpNameHk, hkSearcherInfo);
                if (StringUtils.isBlank(apiOrderNo)) {
                    throw new MessageException("err.access");
                }
                order.setApiOrderNo(apiOrderNo);
                logger.info("BuyGlobalReport createOrder4HKData, apiOrderNo:{}, orderId:{}", apiOrderNo, order.getId());
            } else if (ReportTypeEnum.MAP_NETWORK_HK.getCode().equals(order.getReportType()) || ReportTypeEnum.MAP_OWNERSHIP_HK.getCode().equals(order.getReportType())) {
                HkCorpBasicDetailTO corp = qccOvsBasicService.getHkCorpBasicDetailTO(order.getKeyNo());
                MsgExceptionUtils.checkIsNull(corp, "msg:hkCorpBasicDetailTO is null");
                // 非上市公司进行购买报告
                if (Boolean.TRUE.equals(corp.isListingFlag())) {
                    order.transitionStatus(OrderStatusEnum.SUCCESS);
                    logger.info("BuyGlobalReport hk corp is listed, keyNo:{}, orderId:{}", order.getKeyNo(), order.getId());
                } else {
                    String corpNameHk = StringUtils.getNotBlankStr(order.getCorpNumber(), order.getCorpName(), order.getCorpNameEn());
                    String apiOrderNo = GlobalCompanyDetailsInterface.createOrder4HKData(corpNameHk, hkSearcherInfo);
                    if (StringUtils.isBlank(apiOrderNo)) {
                        throw new MessageException("err.access");
                    }
                    order.setApiOrderNo(apiOrderNo);
                    logger.info("BuyGlobalReport create hk map, apiOrderNo:{}, orderId:{}", apiOrderNo, order.getId());
                }

            }
            else if (ReportTypeEnum.HK_BAS_AR.getCode().equals(order.getReportType())) { // added for v1.7.8 KNZT-3320
                String corpNameHk = StringUtils.getNotBlankStr(order.getCorpNumber(), order.getCorpName(), order.getCorpNameEn());
                String apiOrderNo = GlobalCompanyDetailsInterface.createOrderWithAr4HKData(corpNameHk, hkSearcherInfo);
                if (StringUtils.isBlank(apiOrderNo)) {
                    throw new MessageException("err.access");
                }
                order.setApiOrderNo(apiOrderNo);
                logger.info("BuyGlobalReport createOrderWithAr4HKData, apiOrderNo:{}, orderId:{}", apiOrderNo, order.getId());
            } else if (ReportTypeEnum.HK_DOCUMENT.getCode().equals(order.getReportType())) {
                String apiOrderNo = generateApiOrderNo();
                TblCompReportOrderHkDoc hkDoc = hkDocService.getByRelIdAndType(order.getId(), OrderHkDocRelTypeEnum.ORDER.getCode());
                MsgExceptionUtils.checkIsNull(hkDoc, "香港文档数据不存在");
                overseaKafkaService.sendMessage4BuyHkReport(apiOrderNo, order.getKeyNo(), hkDoc.getDocNumber());
                order.setApiOrderNo(apiOrderNo);
            } else if (ReportTypeEnum.hkIrdCorpList().contains(order.getReportType())) {
                String apiOrderNo = generateApiOrderNo();
                overseaKafkaService.sendMessage4BuyHkIrdReport(apiOrderNo, order.getKeyNo(), order.getReportType());
                order.setApiOrderNo(apiOrderNo);
            }
        } else if (ReportTypeEnum.SG_BAS.getCode().equals(order.getReportType())) {
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuySgReport(apiOrderNo, order.getKeyNo());
            order.setApiOrderNo(apiOrderNo);
            logger.info("BuyGlobalReport createSGOrder, apiOrderNo:{}, orderId:{}", apiOrderNo, order.getId());
        } else if (ReportTypeEnum.SG_FIN.getCode().equals(order.getReportType())
                || ReportTypeEnum.SG_FIN_YEARS.getCode().equals(order.getReportType())) {
            String finYear = commTblCompReportOrderExtService.getExtValue(order.getId(), OrderExtConstants.SG_FIN_YEAR);
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuySgFinReport(apiOrderNo, order.getKeyNo(), order.getReportType(), finYear);
            order.setApiOrderNo(apiOrderNo);
        } else if (ReportTypeEnum.MY_BASIC.getCode().equals(order.getReportType())) {
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuyMyReport(apiOrderNo, order.getKeyNo(), order.getCorpNumber(), order.getExtraInfo1());
            order.setApiOrderNo(apiOrderNo);
            logger.info("BuyGlobalReport sendMessage4BuyMyReport, apiOrderNo:{}, orderId:{}", order.getApiOrderNo(), order.getId());
        } else if (ReportTypeEnum.NZ_BASIC.getCode().equals(order.getReportType())) {
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuyNzReport(apiOrderNo, order.getKeyNo(), order.getCorpNumber());
            order.setApiOrderNo(apiOrderNo);
            logger.info("BuyGlobalReport sendMessage4BuyNzReport, apiOrderNo:{}, orderId:{}", order.getApiOrderNo(), order.getId());
        } else if (ReportTypeEnum.TW_BASIC.getCode().equals(order.getReportType())) {
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuyTwReport(apiOrderNo, order.getKeyNo());
            order.setApiOrderNo(apiOrderNo);
            logger.info("BuyGlobalReport sendMessage4BuyTwReport, apiOrderNo:{}, orderId:{}", order.getApiOrderNo(), order.getId());
        } else if (ReportTypeEnum.AU_BASIC.getCode().equals(order.getReportType())) {
            String apiOrderNo = generateApiOrderNo();
            overseaKafkaService.sendMessage4BuyAuReport(apiOrderNo, order.getKeyNo(), order.getCorpNumber());
            order.setApiOrderNo(apiOrderNo);
            logger.info("BuyGlobalReport sendMessage4BuyAuReport, apiOrderNo:{}, orderId:{}", order.getApiOrderNo(), order.getId());
        }

        save(order);
    }

    /**
     * 批量插入订单，并且增加创建时间，避免重复插入造成排序错乱
     * added for lvcy v1.9.9 KNZT-4980
     *
     * @param entityList
     * @return
     */
    public void batchPartialInsertWithIncreaseCreateDate(List<TblCompReportOrder> entityList) {
        Calendar calendar = Calendar.getInstance();
        if(entityList != null && entityList.size() > 0) {
            List<List<TblCompReportOrder>> partList = Lists.partition(entityList, 10000);
            for(List<TblCompReportOrder> list : partList) {
                for(TblCompReportOrder entity : list) {
                    entity.preInsert();
                    calendar.add(Calendar.MILLISECOND, 1);
                    entity.setCreateDate(new Date(calendar.getTimeInMillis()));
                    entity.setUpdateDate(new Date(calendar.getTimeInMillis()));
                    entity.setIsNewRecord(false);
                }
                dao.batchInsert(list);
            }
        }
    }

    /**
     * 报告生成回调
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param orderId
     * @param success
     * @param remark
     * @return
     */
    public void reportGeneratedCallback(String orderId, String success, String remark) throws MessageException {
        TblCompReportOrder order = this.get(orderId);
        MsgExceptionUtils.checkIsNull(order);
        if (Constants.YES.equals(success)) {
//            touchSonOrderReport(order);
            if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType())) {
                sendFinTaxSuccessEmail(order);
            }

            if (ReportTypeEnum.getVerifyList().contains(order.getReportType())) {
                redisService.addDelayedCommTask(RedisService.generateCommTaskId(CommDelayedTaskTypeEnum.VERIFY_ORDER_CHECK.getCode(), order.getId()), 60 * 60);
            }
        } else {
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(remark, AlarmTypeEnum.CORP_REPORT_FAIL, order.getOrderNo(), order.getReportType());
        }
    }

/*    private void touchSonOrderReport(TblCompReportOrder order) throws MessageException {
        // 报告处理成功以后，触发子订单生成报告 added for lvcy v2.0.6 KNZT-5664
        List<String> sonOrderIdList = commTblCompReportOrderRelService.getRelOrderIdList(order.getId(), OrderRelTypeEnum.PARENT_SON, order.getCompanyId());
        List<TblCompReportOrder> sonOrderList = this.batchGet(sonOrderIdList);
        for (TblCompReportOrder sonOrder : sonOrderList) {
            sonOrder.setRptStatus(OrderStatusEnum.PENDING.getCode());
            sonOrder.setBeginDate(DateUtils.getCurrentDate());
            this.save(sonOrder);
            this.createReport(sonOrder);
        }
    }*/

    /**
     * 发送财税报告提交邮件
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param order
     * @return
     */
    private void sendFinTaxSuccessEmail(TblCompReportOrder order) {
        try {
            SysTemplate template = commSysTemplateService.getByTemplateName("u_email_fin_tax_ready_downloaded");
            MsgExceptionUtils.checkIsNull(template, "u_email_fin_tax_ready_downloaded's template is not exist");
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("orderNo", order.getOrderNo());
            String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
            qccMailSenderService.send(order.getLoginName(), template.getTemplateSubject(), emailContent);
        } catch (Exception e) {
            logger.error("u_email_fin_tax_ready_downloaded failed", e);
        }
    }

    /**
     * 发送360延迟报告成功邮件
     *
     * @param order
     * @return
     */
    private void sendCorp360DelaySuccessEmail(TblCompReportOrder order) {
        try {
            order = getOrderWithEnNameProcessed(order);
            SysTemplate template = commSysTemplateService.getByTemplateName("u_email_360_delay_ready_downloaded");
            MsgExceptionUtils.checkIsNull(template, "u_email_360_delay_ready_downloaded's template is not exist");
            Map<String, Object> paramMap = Maps.newHashMap();
            String corpName = StringUtils.isNotBlank(order.getCorpName()) ? order.getCorpName():"";
            if (StringUtils.isNotBlank(order.getCorpNameEn())) {
                corpName = corpName + "(" + order.getCorpNameEn() + ")";
            }
            paramMap.put("corpName", corpName);
            paramMap.put("orderNo", order.getOrderNo());
            String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
            qccMailSenderService.send(order.getLoginName(), template.getTemplateSubject(), emailContent);
        } catch (Exception e) {
            logger.error("u_email_360_delay_ready_downloaded failed", e);
        }
    }

    public TblCompReportOrder getOrderWithEnNameProcessed(TblCompReportOrder order) {
        TblCompReportOrder orderCopied = new TblCompReportOrder();
        BeanUtil.copyProperties(orderCopied, order);
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.CN.getNameCode());
        transWrapper.addEntryOf(orderCopied,
                    TblCompReportOrder::setCorpName, TblCompReportOrder::setCorpNameEn,
                    TblCompReportOrder::getCorpName, TblCompReportOrder::getCorpNameEn)
                .withEntity(TransWrapper.ENTRY_TYPE_CORP, TblCompReportOrder::getCorpKeyNo);
        commonTransService.enPostProcessor(transWrapper);
        return orderCopied;
    }


    /**
     * 发送退款邮件
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param order
     * @return
     */
    private void sendRefundEmail(TblCompReportOrder order) {
        try {
            order = getOrderWithEnNameProcessed(order);
            SysTemplate template = commSysTemplateService.getByTemplateName("u_email_refund");
            MsgExceptionUtils.checkIsNull(template, "u_email_refund's template is not exist");
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("orderNo", order.getOrderNo());
            paramMap.put("reportName", ReportTypeEnum.getDesc(order.getReportType()));
            //added for v2.2.0 KNZT-7142 财税报告通知邮件中新增授权企业中英文名
            paramMap.put("corpName", StringUtils.isNotBlank(order.getCorpName()) ? order.getCorpName():"");
            paramMap.put("corpNameEn", StringUtils.isNotBlank(order.getCorpNameEn()) ? order.getCorpNameEn():"");
            paramMap.put("amountCreditTitle", PayTypeEnum.ONLINE.getCode().equals(order.getPayType()) ? "Amounts" : "Credits");
            paramMap.put("amountCreditValue", order.getTotalUnit());
            String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
            qccMailSenderService.send(order.getLoginName(), template.getTemplateSubject(), emailContent);
        } catch (Exception e) {
            logger.error("u_email_refund failed", e);
        }
    }

    public String getPdfSecret(String orderId) throws MessageException {
        TblCompReportOrder order = getByIdAndCompany(orderId, UserUtils.getUserCompanyId());
        MsgExceptionUtils.checkIsNull(order);
        MsgExceptionUtils.failBuild(!ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType()));
        MsgExceptionUtils.failBuild(!OrderStatusEnum.SUCCESS.getCode().equals(order.getRptStatus()));
        TblWebConverter webConverter = commTblWebConverterService.getByOrderId(orderId);
        return Optional.ofNullable(webConverter).map(TblWebConverter::getDocPassword).orElse(null);
    }

    /**
     * added for v2.1.1 fengsw KNZT-6127【新增】补充国标-联合国，国标-新加坡，国标-北美，国标-欧盟，国标-新加坡行业映射数据(2025)
     * 根据企业行业找出映射关系；然后根据映射关系找出各维度行业信息，每个行业层级对比，得到最终结果
     */
    public List<TblGlobalIndustryBaseTO> getIndustryMappingInfo(String keyNo, boolean flag) {
        if (StringUtils.isBlank(keyNo)) return null;
        try {
            CorpBasicDetail4UnitTO basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4Unit(keyNo, null, flag);
            if (Objects.nonNull(basicDetailTO) && Objects.nonNull(basicDetailTO.getIndustryInfo())) {
                String industryCode = StringUtils.getNotBlankStr(basicDetailTO.getIndustryInfo().getSmallIndustryCode(), basicDetailTO.getIndustryInfo().getMiddleIndustryCode(), basicDetailTO.getIndustryInfo().getSubIndustryCode(), basicDetailTO.getIndustryInfo().getIndustryCode());
                if (StringUtils.isBlank(industryCode)) {
                    return null;
                }
                List<TblGlobalIndustryQueryResultTO> globalIndustryList = commTblGlobalIndustryService.listMappingIndustryInfoByCnIndCodeWithTypeList(industryCode, IndustryCountryTypeEnum.merchantCountryCodeList());
//                List<TblGlobalIndustryQueryResultTO> targetList = getMappingIndustryList(globalIndustryList);
                return TblGlobalIndustryBaseTO.build(globalIndustryList);
            }
        } catch (Exception e) {
            logger.error("getIndustryMappingInfo failed, keyNo:{}", keyNo, e);
        }
        return null;
    }

    /**
     * updated for v2.1.3 fengsw KNZT-6226
     * 通过countryType分组所有匹配到的映射行业，找出一一对应的映射结果
     *
     * @param globalIndustryList
     * @return
     */
    /**private List<TblGlobalIndustryQueryResultTO> getMappingIndustryList(List<TblGlobalIndustryQueryResultTO> globalIndustryList) {
        List<TblGlobalIndustryQueryResultTO> targetList = new ArrayList<>();
        Map<String, List<TblGlobalIndustryQueryResultTO>> listMap = globalIndustryList.stream().collect(Collectors.groupingBy(TblGlobalIndustryQueryResultTO::getCountryType));
        for (Map.Entry<String, List<TblGlobalIndustryQueryResultTO>> entry : listMap.entrySet()) {
            List<TblGlobalIndustryQueryResultTO> queryResultList = entry.getValue();
            if (CollectionUtils.isNotEmpty(queryResultList)) {
                TblGlobalIndustryQueryResultTO tblGlobalIndustryQueryResultTO = queryResultList.get(0);
                String mpIndCode = tblGlobalIndustryQueryResultTO.getMpIndCode();
                TblGlobalIndustryQueryResultTO tblGlobalIndustryQueryResultTO1 = queryResultList.stream().filter(k -> mpIndCode.equals(k.getInd5Code())).findFirst().orElse(null);
                if (Objects.isNull(tblGlobalIndustryQueryResultTO1)) {
                    tblGlobalIndustryQueryResultTO1 = queryResultList.stream().filter(k -> mpIndCode.equals(k.getInd4Code()) && StringUtils.andBlank(k.getInd5Code())).findFirst().orElse(null);
                }
                if (Objects.isNull(tblGlobalIndustryQueryResultTO1)) {
                    tblGlobalIndustryQueryResultTO1 = queryResultList.stream().filter(k -> mpIndCode.equals(k.getInd3Code()) && StringUtils.andBlank(k.getInd4Code(), k.getInd5Code())).findFirst().orElse(null);
                }
                if (Objects.isNull(tblGlobalIndustryQueryResultTO1)) {
                    tblGlobalIndustryQueryResultTO1 = queryResultList.stream().filter(k -> mpIndCode.equals(k.getInd2Code()) && StringUtils.andBlank(k.getInd3Code(), k.getInd4Code(), k.getInd5Code())).findFirst().orElse(null);
                }
                if (Objects.isNull(tblGlobalIndustryQueryResultTO1)) {
                    tblGlobalIndustryQueryResultTO1 = queryResultList.stream().filter(k -> mpIndCode.equals(k.getInd1Code()) && StringUtils.andBlank(k.getInd2Code(), k.getInd3Code(), k.getInd4Code(), k.getInd5Code())).findFirst().orElse(null);
                }
                if (Objects.nonNull(tblGlobalIndustryQueryResultTO1)) {
                    targetList.add(tblGlobalIndustryQueryResultTO1);
                }
            }
        }
        return targetList;
    }/

    /**
     * added for v2.1.3 fengsw KNZT-6263
     * 获取时间范围内的公司账号额度消耗情况（含报告和API）
     *
     * @param companyIdList
     * @param startDate
     * @param endDate
     * @return
     */
    public List<CompanyConsumedInfoTO> listCompanyConsumedInfoWithDateRange(List<String> companyIdList, Date startDate, Date endDate) {
        return dao.listCompanyConsumedInfoWithDateRange(companyIdList, startDate, endDate);
    }

    /**
     * added for lvcy v2.1.4 KNZT-6254
     * 根据订单id列表和payRelId更新订单数据
     *
     * @param orderIdList
     * @param payRelId
     */
    public void updatePayRelId(List<String> orderIdList, String payRelId) {
        dao.updatePayRelId(orderIdList, payRelId);
    }

    /**
     * added for lvcy v2.1.5 KNZT-6384
     * 根据订单编号列表查询订单数据
     *
     * @param orderNoList
     * @return
     */
    public List<TblCompReportOrder> getByOrderNoList(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return new ArrayList<>();
        }
        return dao.getByOrderNoList(orderNoList);
    }

    /**
     * added for v2.1.6 fengsw KNZT-6513
     * 订单编号生成规则
     * 订单类型：R(报告)；M（图谱）；S（扫描）；A（API订单）
     * 生成规则如下：
     * 订单类型1位+ 年月日时分秒14位（yyyyMMddHHmmss）+订单数3位（001）+ 检验位
     * reportType
     */
    public String generateOrderNo(String reportType) {
        String orderType = KycOrderTypeEnum.getOrderTypeByReportType(reportType);
        return redisServUtils.generateNumber(orderType, "kyc_order");
    }


    /**
     * 生成api订单编号
     * 生成规则：KYC + 年月日时分秒14位（yyyyMMddHHmmss）+ 订单数3位（001）+ 检验位
     */
    public String generateApiOrderNo() {
        return redisServUtils.generateNumber("KYC", "api_order");
    }

    /**
     * added for v2.1.6 fengsw KNZT-6513
     * 将订单（只有状态为退款-RE、已取消-F）置为不可见（用户端逻辑删除）
     *
     * @param userId
     * @param orderNoList
     */
    public void updateOrderInvisible4User(String userId, List<String> orderNoList) {
        dao.updateOrderInvisible4User(userId, orderNoList);
    }

    /**
     * 按创建时间分批查询订单数据
     *
     * @param size 每批次大小
     * @param lastMinDate 上一批次最后一条记录的创建时间
     * @return List<TblCompReportOrder>
     */
    public List<TblCompReportOrder> loopByCreateDate(int size, Date lastMinDate, TblCompReportOrderCondition condition) {
        return dao.loopByCreateDate(size, lastMinDate, condition);
    }

    /**
     * 检查订单是否重复
     *
     * @param companyId
     * @param keyNoList
     * @param minOrderCreatedDate
     * @param maxOrderCreatedDate
     * @return
     */
    public List<OrderDistributionResultTO> listDuplicateOrder(String companyId, List<String> keyNoList, List<String> userIdList, Date minOrderCreatedDate, Date maxOrderCreatedDate) {
        return dao.listDuplicateOrderSummaryInfo(companyId, keyNoList, userIdList, minOrderCreatedDate, maxOrderCreatedDate);
    }

    /**
     * updated for v2.2.2 KNZT-7314 查询重复订单 排除指定类型 当前只排除图谱的，后续可能会有别的
     *
     * @param companyId
     * @param form
     * @param excludeReportTypeList
     * @return
     */
    public List<TblCompReportOrder> listDuplicateOrderWithDateRangeV2(String companyId, TblCompReportOrderDuplicateForm form, List<String> excludeReportTypeList) {
        return dao.listDuplicateOrderWithDateRangeV2(companyId, form, excludeReportTypeList);
    }


    /**
     * 核验
     * @param order
     * @return
     * @throws Exception
     */
    private void verify(TblCompReportOrder order) throws MessageException {
        try {
            EncryptedForm form = redisService.getObjectById(Constants.RedisKey.VERIFY_REQ, order.getOrderNo(), EncryptedForm.class);
            MsgExceptionUtils.checkIsNull(form, "verify request is not exist, orderId:{}", order.getOrderNo());
            VerifyResp verifyResp = VerifyInterface.verify(order.getOrderNo(), form);
            redisService.saveObject(Constants.RedisKey.VERIFY_RESP, order.getOrderNo(), verifyResp, 30 * 24 * 60 * 60);
        } catch (Exception e) {
            logger.error("verify error, orderNo:{}", order.getOrderNo(), e);
            throw new MessageException("err.access");
        }
    }

    /**
     * 海外报告回调
     *
     * @param apiOrderNo
     * @param success
     * @throws MessageException
     */
    public void overseaReportCallback(String apiOrderNo, String success) throws MessageException {
        TblCompReportOrder order = dao.getByApiOrderNo(apiOrderNo);
        if (Objects.isNull(order)) {
            logger.info("overseaReportCallback order is not exist, apiOrderNo:{}", apiOrderNo);
            return;
        }
        if (Constants.NO.equals(success)) {
            refundOrder(order);
            sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.SPIDER_ORDER_DATA_FAILED,
                    order.getOrderNo(), order.getApiOrderNo(), ReportTypeEnum.getDesc(order.getReportType()));
            logger.info("overseaReportCallback refund success, orderId:{}, apiOrderNo:{}", order.getId(), apiOrderNo);
        } else {
            // 如果是basic+ar，需要二次确认是否成功（basic+ar 会有两次回调）
            if (ReportTypeEnum.HK_BAS_AR.getCode().equals(order.getReportType())) {
                User user = userService.get(order.getUserId());
                MsgExceptionUtils.checkIsNull(user);
                WebContextHolder.setJobCompanyId(user.getCompany().getId());
                WebContextHolder.setCurrentUser(user);
                boolean dataSuccess = reportDataContextService.getService(order.getReportType(), order.getDataVersion())
                        .isDataSuccess(ReportDataGetResultForm.build(order.getId()));
                if (!dataSuccess) {
                    logger.info("overseaReportCallback dataSuccess is false, orderId:{}", order.getId());
                    return;
                }
            }
            if (ReportTypeEnum.apiOrderTypeList().contains(order.getReportType())) {
                doAfterApiOrderDataSuccess(order);
            }
        }
    }

/*    private void handleHkDocCallback(TblCompReportOrder order) throws MessageException {
        TblCompReportOrderHkDoc hkDoc = hkDocService.getByRelIdAndType(order.getId(), OrderHkDocRelTypeEnum.ORDER.getCode());
        if (Objects.isNull(hkDoc)) {
            logger.error("handleHkDocCallback overseaReportCallback hkDoc is not exist, orderId:{}", order.getId());
            return;
        }
        HongKongAnnouncementsTO hkAnnouncements = overseaHongKongAnnouncementsService.getByKeyNoAndDocNumber(hkDoc.getKeyNo(), hkDoc.getDocNumber());
        if (Objects.isNull(hkAnnouncements)) {
            logger.error("handleHkDocCallback overseaReportCallback hkAnnouncements is not exist, keyNo:{}", hkDoc.getKeyNo());
            return;
        }
        String documentFile = hkAnnouncements.getDocumentFile();
        if (StringUtils.isBlank(documentFile)) {
            logger.error("handleHkDocCallback overseaReportCallback hkAnnouncements documentFile is blank, keyNo:{}", hkDoc.getKeyNo());
            return;
        }

        order.setUrl(documentFile);
        order.transitionStatus(OrderStatusEnum.SUCCESS);
        this.save(order);

        hkDoc.setDocUrl(documentFile);
        hkDocService.save(hkDoc);
        logger.info("overseaReportCallback success, orderId:{}", order.getId());

    }*/

    public List<TblCompReportOrder> listNoHkDoc() {
        return dao.listNoHkDoc();
    }

    public List<TblCompReportOrder> listReportByCompanyIdAndKeyNo(String companyId, String keyNo) {
        return dao.listReportByCompanyIdAndKeyNo(companyId, keyNo);
    }

    /**
     * 批量处理高管关联的企业信息
     * 1、当前只处理企业所属行业信息
     *
     * @param objectPageDataBO
     */
    public void handleSeniorPersonInfo(PageDataBO<?> objectPageDataBO) {
        if (objectPageDataBO != null && CollectionUtils.isNotEmpty(objectPageDataBO.getList())) {
            List<?> list = objectPageDataBO.getList();
            Set<String> industryCodes = new HashSet<>();
            for (Object infoTO1 : list) {
                JSONObject infoTO = (JSONObject) infoTO1;
                JSONObject industry = infoTO.getJSONObject("industry");
                if (industry != null) {
                    String industryCode = StringUtils.getNotBlankStr(industry.getString("smallCategoryCode"), industry.getString("middleCategoryCode"),
                            industry.getString("subIndustryCode"), industry.getString("industryCode"));
                    if (StringUtils.isNotBlank(industryCode)) {
                        industryCodes.add(industryCode);
                    }
                }
            }

            Map<String, TblGlobalIndustry> code2Industry4CnMapping = new HashMap<>();
            if (CollectionUtils.isNotEmpty(industryCodes)) {
                code2Industry4CnMapping.putAll(commTblGlobalIndustryService.getCode2Industry4CnMapping(industryCodes));
            }
            for (Object infoTO1 : list) {
                JSONObject infoTO = (JSONObject) infoTO1;
                JSONObject industry = infoTO.getJSONObject("industry");
                if (industry != null) {
                    String industryCode = StringUtils.getNotBlankStr(industry.getString("smallCategoryCode"), industry.getString("middleCategoryCode"),
                            industry.getString("subIndustryCode"), industry.getString("industryCode"));
                    TblGlobalIndustry tblGlobalIndustry = code2Industry4CnMapping.get(industryCode);
                    if (tblGlobalIndustry != null) {
                        ExternalApiIndustryInfoTO industryInfoTO = new ExternalApiIndustryInfoTO(StringUtils.equals(tblGlobalIndustry.getInd1Code(), industryCode) ? industryCode : tblGlobalIndustry.getInd1Code() + industryCode,
                                StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4Name(), tblGlobalIndustry.getInd3Name(), tblGlobalIndustry.getInd2Name(), tblGlobalIndustry.getInd1Name()),
                                StringUtils.getNotBlankStr(tblGlobalIndustry.getInd4NameEn(), tblGlobalIndustry.getInd3NameEn(), tblGlobalIndustry.getInd2NameEn(), tblGlobalIndustry.getInd1NameEn()));
                        JSONArray jsonArray = new JSONArray();
                        jsonArray.add(JSONObject.toJSON(industryInfoTO));
                        infoTO.put("industryList", jsonArray);
                    }
                }
            }
        }
    }
}
