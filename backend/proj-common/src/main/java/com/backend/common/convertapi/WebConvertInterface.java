package com.backend.common.convertapi;

import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.OrderExtConstants;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * added for v1.7.9 KNZT-3396
 * web转换api
 * <AUTHOR>
 * @datetime 2024/5/15 14:54
 */
public class WebConvertInterface {
    protected static Logger logger = LoggerFactory.getLogger(WebConvertInterface.class);



    /**
     * updated for v2.0.2 chenbl KNZT-5292 增加是否需要加密支持
     * updated for v2.0.5 chenbl KNZT-5562
     * @param order
     * @param orderExtMap
     * @param picId
     * @throws MessageException
     */
    public static void convertCorpReport(TblCompReportOrder order, Map<String, String> orderExtMap, String picId) throws MessageException {
        String reportType = order.getReportType();
        String nameEn = "";
        if (Constants.Report.KEY_NO_TYPE_PERS.equals(order.getKeyNoType())) {
            nameEn = StringUtils.isBlank(order.getPersNameEn()) ? order.getPersName() : order.getPersNameEn();
        } else {
            nameEn = StringUtils.isBlank(order.getCorpNameEn()) ? order.getCorpName() : order.getCorpNameEn();
        }

        String fileName;
        String urlKeyword = null;
        String dateStr = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS);
        // added for v2.0.5 fensw KNZT-5597 KYC Lite 报告生成
        if (ReportTypeEnum.LITE.getCode().equals(reportType)) {
            fileName = nameEn + " - KYC Lite Report (China mainland) - " + dateStr;
        } else if (ReportTypeEnum.BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Basic Report (China mainland) - " + dateStr;
        } else if (ReportTypeEnum.UBO.getCode().equals(reportType)) {
            fileName = nameEn + " - UBO Report (China mainland) - " + dateStr;
        } else if (ReportTypeEnum.ADVANCED.getCode().equals(reportType)) {
            fileName = nameEn + " - Advanced Report (China mainland) - " + dateStr;
        } else if (ReportTypeEnum.PERS_BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Executive Snapshot (China mainland Individual) - " + dateStr;
            urlKeyword = "person";
        } else if (ReportTypeEnum.FIN_TAX.getCode().equals(reportType)) {
            fileName = nameEn + " - Financial & Tax Report (China mainland) - " + dateStr;
            urlKeyword = "finance";
        } else if (ReportTypeEnum.SG_BAS.getCode().equals(reportType)) { // added for v2.0.6 chenbl KNZT-5506
            fileName = nameEn + " - Basic Report (Singapore) - " + dateStr;
            urlKeyword = StringUtils.equals(order.getDataVersion(), "3") ? "sg-new" : "sg";
        } else if (ReportTypeEnum.SG_FIN.getCode().equals(reportType)) {
            String year = orderExtMap.getOrDefault(OrderExtConstants.SG_FIN_YEAR, "");
            fileName = nameEn + " - Financial Profile Report (" + year + ") (Singapore) - " + dateStr;
            urlKeyword = "sg-finance";
        } else if (ReportTypeEnum.SG_FIN_YEARS.getCode().equals(reportType)) {
            fileName = nameEn + " - Financial Profile Report (Latest 5 Available Years) (Singapore) - " + dateStr;
            urlKeyword = "sg-finance";
        } else if (ReportTypeEnum.SCAN.getCode().equals(reportType)) { // added for v2.0.9 chenbl KNZT-5362
            fileName = nameEn + " - Sanctions & Watchlist Scan Report - " + dateStr;
            urlKeyword = "sanction";
        } else if (ReportTypeEnum.HK_BAS.getCode().equals(reportType)) { // added for v2.0.9 fengsw KNZT-5608
            fileName = nameEn + " - Basic Report (Hong Kong) - " + dateStr;
            urlKeyword = "hk";
        } else if (ReportTypeEnum.HK_BAS_AR.getCode().contains(reportType)) { // added for v2.0.9 fengsw KNZT-5608
            fileName = nameEn + " - Basic Report + AR (Hong Kong) - " + dateStr;
            urlKeyword = "hk";
        } else if (ReportTypeEnum.HK_BR_EXTRACT.getCode().equals(reportType)) {
            fileName = nameEn + " - Business Register Extract (Hong Kong) - " + dateStr;
            urlKeyword = "hk-ird";
        } else if (ReportTypeEnum.HK_DUP_CERT.getCode().equals(reportType)) {
            fileName = nameEn + " - Duplicate of Registration Certificate (Hong Kong) - " + dateStr;
            urlKeyword = "hk-ird";
        } else if (ReportTypeEnum.MERCHANT.getCode().equals(reportType)) { // added for v2.1.1 lvcy KNZT-5973
            fileName = nameEn + " - Merchant Onboarding (China mainland) - " + dateStr;
            urlKeyword = "merchant";
        } else if (ReportTypeEnum.getVerifyList().contains(reportType)) {
            fileName = nameEn + " - " + ReportTypeEnum.getDesc(reportType)+ " - " + dateStr;
            urlKeyword = "verify";
        } else if (ReportTypeEnum.MY_BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Basic Report (Malaysia) - " + dateStr;
            urlKeyword = "my";
        } else if (ReportTypeEnum.NZ_BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Basic Report (New Zealand) - " + dateStr;
            urlKeyword = "nz";
        } else if (ReportTypeEnum.TW_BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Basic Report (Taiwan) - " + dateStr;
            urlKeyword = "tw";
        } else if (ReportTypeEnum.AU_BASIC.getCode().equals(reportType)) {
            fileName = nameEn + " - Basic Report (Australia) - " + dateStr;
            urlKeyword = "au";
        }
        else {
            logger.error("WebConvertInterface convertCorpReport not support reportType:{}", reportType);
            return;
        }

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("orderId", order.getId());
        paramMap.put("orderNo", order.getOrderNo());
        paramMap.put("keyNo", order.getKeyNo());
        paramMap.put("reportType", reportType);
        paramMap.put("fileName", fileName);
        paramMap.put("picId", picId);
        paramMap.put("needEncrypt", ReportTypeEnum.needEncryptReportDocList().contains(reportType) ? "1" : null);
        if (StringUtils.isNotBlank(urlKeyword)) {
            paramMap.put("urlKeyword", urlKeyword);
        }
        paramMap.put("loginName", order.getLoginName());
        String url = Global.getConfig("global.web.convert.interface.domain") + "/converter/corpReport";
        logger.info("WebConvertInterface convertCorpReport req:{}", JSONObject.toJSONString(paramMap));
        String resp = HttpUtils.post(url, paramMap);
        logger.info("WebConvertInterface convertCorpReport resp:{}", resp);

        if (StringUtils.isBlank(resp)) {
            throw new MessageException("convertCorpReport resp null");
        }
        JSONObject jsonObj = JsonUtils.parseObject(resp);
        if (Objects.isNull(jsonObj) || !Constants.Result.SUCCESS_STR.equals(jsonObj.getString("status"))) {
            throw new MessageException("convertCorpReport api error");
        }
    }

    public static String healthCheck() {
        try {
            String url = Global.getConfig("global.web.convert.interface.domain") + "/k8s/check/health";
            String resp = HttpUtils.get(url, new HashMap<>());
            logger.info("WebConvertInterface healthCheck resp:{}", resp);
            return resp;
        } catch (Exception e) {
            logger.error("WebConvertInterface healthCheck error,", e);
            return e.getMessage();
        }
    }
}
