package com.backend.common.stripeapi;

import com.alibaba.fastjson.JSON;
import com.backend.common.modules.common.model.ClientInfoTO;
import com.backend.common.modules.common.model.PaymentIntentRedisTO;
import com.backend.common.modules.common.service.CommTblCompOpIpLogService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.service.CommCompUserService;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.DeliveryContracProdAccTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCancelParams;
import com.stripe.param.PaymentIntentCreateParams;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * stripe 支付interface
 * added for v1.9.3 KNZT-4193
 * <AUTHOR>
 * @datetime 2024/7/26 10:47
 */
public class StripePaymentInterface {
    protected static Logger logger = LoggerFactory.getLogger(StripePaymentInterface.class);

    private static RedisService redisService = SpringContextHolder.getBean(RedisService.class);
    private static CommCompUserService compUserService = SpringContextHolder.getBean(CommCompUserService.class);

    static {
        Stripe.apiKey = Global.getConfigDefault("stripe.api.key", "***********************************************************************************************************");
    }

    public static PaymentIntent createPaymentIntent4Order(TblCompReportOrder order, BigDecimal amount, String topUpNo) throws MessageException {
        String description = String.format("KYC report (trans No. %s)", order.getOrderNo());
        Map<String, String> param = Maps.newHashMap();
        param.put("orderId", order.getId());
        param.put("orderNo", order.getOrderNo());
        param.put("reportType", order.getReportType());
        param.put("reportName", order.getReportName());
        param.put("loginName", order.getLoginName());
        param.put("userId", order.getUserId());
        param.put("companyName", StringUtils.getNotBlankStr(order.getCorpNameEn(), order.getCorpName()));
        String companyId = order.getCompanyId();
        param.put(Constants.StripeParam.TOP_UP_NO, topUpNo);
        param.put(Constants.StripeParam.PROD_ACC_TYPE, DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode());
        param.put(Constants.StripeParam.CREDIT_COUNT, order.getTotalUnit().toString());
        return createPaymentIntent(description, amount, param, companyId, order.getUserId());
    }


    public static PaymentIntent createPaymentIntent4CartOrder(List<TblCompReportOrder> orderList, BigDecimal amount, String topUpNo) throws MessageException {
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        }

        String description;
        if (orderList.size() > 1) {
            description = String.format("KYC report (%s items)", orderList.size());
        } else {
            description = String.format("KYC report (trans No. %s)", orderList.get(0).getOrderNo());
        }

        StringBuilder orderIdSB = new StringBuilder();
        StringBuilder orderNoSB = new StringBuilder();
        StringBuilder reportTypeSB = new StringBuilder();
        StringBuilder reportNameSB = new StringBuilder();
        StringBuilder companyNameSB = new StringBuilder();
        BigDecimal creditCount = BigDecimal.ZERO;
        for (int i = 0; i < orderList.size(); i++) {
            TblCompReportOrder order = orderList.get(i);
            orderIdSB.append(order.getId());
            orderNoSB.append(order.getOrderNo());
            reportTypeSB.append(order.getReportType());
            reportNameSB.append(order.getReportName());
            companyNameSB.append(StringUtils.getNotBlankStr(order.getCorpNameEn(), order.getCorpName()));
            if ((i + 1) < orderList.size()) {
                orderIdSB.append(",");
                orderNoSB.append(",");
                reportTypeSB.append(",");
                reportNameSB.append(",");
                companyNameSB.append(",");
            }
            creditCount = creditCount.add(order.getTotalUnit());
        }

        Map<String, String> param = Maps.newHashMap();
        param.put("orderId", StringUtils.left(orderIdSB.toString(), 500));
        param.put("orderNo", StringUtils.left(orderNoSB.toString(), 500));
        param.put("reportType", StringUtils.left(reportTypeSB.toString(), 500));
        param.put("reportName", StringUtils.left(reportNameSB.toString(), 500));
        param.put("loginName", orderList.get(0).getLoginName());
        param.put("userId", orderList.get(0).getUserId());
        param.put("companyName", StringUtils.left(companyNameSB.toString(), 500));
        String companyId = orderList.get(0).getCompanyId();
        param.put(Constants.StripeParam.TOP_UP_NO, topUpNo);
        param.put(Constants.StripeParam.PROD_ACC_TYPE, DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode());
        param.put(Constants.StripeParam.CREDIT_COUNT, creditCount.toString());
        return createPaymentIntent(description, amount, param, companyId, orderList.get(0).getUserId());

    }

    public static PaymentIntent createPaymentIntent4TopUp(String topUpNo, String companyId, String userId, BigDecimal amount, BigDecimal creditCount, Integer year) throws MessageException {
        Map<String, String> param = Maps.newHashMap();
        param.put(Constants.StripeParam.PROD_ACC_TYPE, DeliveryContracProdAccTypeEnum.TOP_UP.getCode());
        param.put(Constants.StripeParam.CREDIT_COUNT, creditCount.toString());
        param.put(Constants.StripeParam.TOP_UP_NO, topUpNo);
        param.put(Constants.StripeParam.YEARS, year.toString());
        String description = String.format("Top up %s Credits (%s years) (top-up No. %s)", creditCount, year, topUpNo);
        return createPaymentIntent(description, amount, param, companyId, userId);
    }



    private static PaymentIntent createPaymentIntent(String description, BigDecimal amount, Map<String, String> paramMap,
                                                     String companyId, String userId) throws MessageException {
        try {
            if (Objects.isNull(amount) || BigDecimal.ZERO.compareTo(amount) >= 0) {
                throw new MessageException("msg:createPaymentIntent error, amount miss");
            }
            // 通用参数赋值
            BigDecimal taxRate = compUserService.getTaxRate(companyId);
            MsgExceptionUtils.checkIsNull(taxRate, "msg:Please check address。");
            paramMap.put(Constants.StripeParam.TAX_RATE, taxRate.toString()); // 税率
            paramMap.put(Constants.StripeParam.AMOUNT_WITHOUT_TAX, amount.toString()); // 税前
            paramMap.put(Constants.StripeParam.COMPANY_ID, companyId); // companyId
            // 计算含税金额给stripe
            BigDecimal taxAmount = amount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            amount = amount.add(taxAmount);

            // 构建metadata
            Map<String, String> metadata = Maps.newHashMap();
            if (MapUtils.isNotEmpty(paramMap)) {
                int count = 0;
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    if (count++ >= 50) {
                        continue;
                    }
                    metadata.put(entry.getKey(), StringUtils.left(entry.getValue(), 500));
                }
            }
            PaymentIntentCreateParams params =
                    PaymentIntentCreateParams.builder()
                            .setAmount(amount.multiply(BigDecimal.valueOf(100)).longValue())
                            .setCurrency("usd")
                            .setAutomaticPaymentMethods(
                                    PaymentIntentCreateParams.AutomaticPaymentMethods
                                            .builder()
                                            .setEnabled(true)
                                            .build()
                            )
                            .setDescription(StringUtils.left(description, 500))
                            .putAllMetadata(metadata)
                            .build();
            PaymentIntent paymentIntent = PaymentIntent.create(params);
            cachePaymentIntent(paymentIntent, companyId, userId); // added for v2.1.8 chenbl KNZT-6936
            return paymentIntent;
        } catch (StripeException e) {
            logger.error("StripeException e:", e);
            throw new MessageException(e.getMessage());
        }
    }

    // added for v2.1.8 chenbl KNZT-6936
    private static void cachePaymentIntent(PaymentIntent paymentIntent, String companyId, String userId) {
        try {
            String paymentIntentId = paymentIntent.getId();
            PaymentIntentRedisTO redisTO = new PaymentIntentRedisTO();
            ClientInfoTO clientInfo = CommTblCompOpIpLogService.getClientInfo();
            redisTO.setCompanyId(companyId);
            redisTO.setUserId(userId);
            redisTO.setClientInfo(clientInfo);
            int timeout = Constants.DelayedTask.PAYMENT_INTENT_CANCEL_DELAYED_SEC + 30 * 60; // 缓存有效时间，在支付超时的基础上加上30分钟
            redisService.saveString(Constants.RedisKey.PAYMENT_INTENT_INFO, paymentIntentId, JSON.toJSONString(redisTO), timeout);
        } catch (Exception e) {
            logger.error("缓存paymentIntent失败", e);
        }
    }

    public static PaymentIntent retrievePaymentIntent(String paymentIntentId) throws MessageException {
        try {
            if (StringUtils.isEmpty(paymentIntentId)) {
                return null;
            }
            return PaymentIntent.retrieve(paymentIntentId);
        } catch (StripeException e) {
            logger.error("StripeException e:", e);
            throw new MessageException(e.getMessage());
        }
    }

    public static PaymentIntent cancelPaymentIntent(PaymentIntent paymentIntent) throws MessageException {
        try {
            if (Objects.isNull(paymentIntent)) {
                return null;
            }
            PaymentIntentCancelParams params = PaymentIntentCancelParams.builder().build();
            return paymentIntent.cancel(params);
        } catch (StripeException e) {
            logger.error("StripeException e:", e);
            throw new MessageException(e.getMessage());
        }
    }

    public static void cancelPaymentIntent(String paymentIntentId) throws MessageException {
        try {
            PaymentIntent resource = PaymentIntent.retrieve(paymentIntentId);
            PaymentIntentCancelParams params = PaymentIntentCancelParams.builder().build();
            resource.cancel(params);
        } catch (StripeException e) {
            logger.error("StripeException e:", e);
            throw new MessageException(e.getMessage());
        }
    }


    public static BigDecimal getPaymentAmountWithoutTax(PaymentIntent paymentIntent) {
        Map<String, String> metadata = paymentIntent.getMetadata();
        BigDecimal paymentAmount = getPaymentAmountWitTax(paymentIntent);
        String amountWithoutTaxStr = metadata.getOrDefault(Constants.StripeParam.AMOUNT_WITHOUT_TAX, paymentAmount.toString());
        return new BigDecimal(amountWithoutTaxStr);
    }

    public static BigDecimal getPaymentAmountWitTax(PaymentIntent paymentIntent) {
        return BigDecimal.valueOf(paymentIntent.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }
}
