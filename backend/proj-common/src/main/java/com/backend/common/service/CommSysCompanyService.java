package com.backend.common.service;

import com.backend.common.entity.SysCompRole;
import com.backend.common.entity.SysTemplate;
import com.backend.common.entity.mapping.CompanyConsumedInfoTO;
import com.backend.common.entity.mapping.CompanyListInfo;
import com.backend.common.entity.mapping.CompanyPatchInfoListTO;
import com.backend.common.entity.mapping.CompanyProKeyInfoPO;
import com.backend.common.entity.mapping.CompanyShortInfoTO;
import com.backend.common.entity.mapping.PlatformCompanyDetailTO;
import com.backend.common.mapper.SysCompanyDao;
import com.backend.common.model.CompCountTO;
import com.backend.common.modules.common.model.CompanyServiceUsageTO;
import com.backend.common.modules.delivery.model.InvoiceBankTO;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryTransactionService;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.entity.mapping.ReportOrderUnitInfo;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.CompanyListCondition;
import com.backend.common.modules.setting.condition.SysCompanyCondition;
import com.backend.common.modules.setting.model.CompanyPreferenceInfo;
import com.backend.common.modules.setting.form.HkSearcherInfoSaveForm;
import com.backend.common.modules.setting.form.ServiceDetailForm;
import com.backend.common.modules.setting.model.HkSearcherInfo;
import com.backend.common.modules.setting.model.HkSearcherInfo4Api;
import com.backend.common.modules.setting.model.ServiceDetailTO;
import com.backend.common.modules.setting.service.PlatformMgntService;
import com.backend.common.openapi.OpenApiUserInfoInterface;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompCanUpgradeEnum;
import com.qcc.frame.commons.ienum.CompDataSaveEnum;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.CompShowCreditEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.HkSearcherInfoAcceptEnum;
import com.qcc.frame.commons.ienum.HkSearcherInfoDocTypeEnum;
import com.qcc.frame.commons.ienum.PwdSecurityPolicyEnum;
import com.qcc.frame.commons.ienum.UserStatusEnum;
import com.qcc.frame.commons.ienum.company.CompCategoryEnum;
import com.qcc.frame.commons.util.EncryptUtil;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.FreemarkerUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.Dict;
import com.qcc.frame.jee.modules.sys.entity.Role;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoColExt;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompanyMainInfoTO;
import com.qcc.frame.jee.modules.sys.entity.mapping.CompanyNameTO;
import com.qcc.frame.jee.modules.sys.model.CompanyAddressTO;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.SaasLoginService;
import com.qcc.frame.jee.modules.sys.service.SysCompInfoColExtService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.DictUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.qcc.pa.utils.AesUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_NAME_UI_DEFAULT_VERSION;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_NAME_UI_VERSION;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_VALUE_UI_VERSION_V1;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.COL_VALUE_UI_VERSION_V2;
import static com.qcc.frame.commons.Constants.CompInfoColExt.CommExt.EXT_TYPE;

@Service
public class CommSysCompanyService extends CrudService<SysCompanyDao, Company> {

	private static final String HK_SEARCHER_INFO_AES_KEY = Global.getConfig("saas.hk.searcher.info.aes.key");
	private static final String HK_SEARCHER_INFO_AES_IV = Global.getConfig("saas.hk.searcher.info.aes.iv");

	@Autowired
	private UserService userService;
	@Autowired
	private CommTblCompCorporatesService tblCompCorporatesService;
	@Autowired
	private CommTblCompCorporatesRadarService tblCompCorporatesRadarService;
	@Autowired
	private CommSysCompPluginAccessLogService sysCompPluginAccessLogService;
	@Autowired
	private CommTblCompReportOrderService commTblCompReportOrderService;
	@Autowired
	private CommTblReportChargeUnitService commTblReportChargeUnitService;
	@Autowired
	private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
	@Autowired
	private CommSysTemplateService commSysTemplateService;
	@Autowired
	private QccMailSenderService qccMailSenderService;
	@Autowired
	private DictService dictService;
	@Autowired
	private SysCompInfoColExtService sysCompInfoColExtService;
	@Autowired
	private CommSysTemplateService templateService;
	@Autowired
	private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;
	@Autowired
	private CommSysUserCompGroupService commSysUserCompGroupService;
	@Autowired
	private SysCompRoleService sysCompRoleService;
	@Autowired
	private SysUserCompRoleService sysUserCompRoleService;
	@Autowired
	private CommCompUserService commCompUserService;
	@Autowired
	private SaasLoginService saasLoginService;
	@Autowired
	private CommSysCompInfoExtService compInfoExtService;

	public Company getByName(String name) {
		return dao.getByName(name);
	}

    public String getUserLoginName(String userId) {
        User user = userService.get(userId);
        if(user != null) {
            return user.getLoginName();
        }
        return "";
    }

	public List<Company> getCompsForJobRun() {
		return dao.getCompsForJobRun();
	}

	public boolean isCompExist(String id) {
		Company c = new Company();
		c.setId(id);
		Company cc = this.get(c);
		if(cc != null && DateUtils.isInValidityPeriod(cc.getBeginDate(), cc.getEndDate())) {
			return true;
		} else {
			return false;
		}
	}

	public Company getCompForKey(String id) {
		Company c = new Company();
		c.setId(id);
		Company cc = this.get(c);
		if(cc != null && DateUtils.isInValidityPeriod(cc.getBeginDate(), cc.getEndDate())) {
			return  cc;
		} else {
			return null;
		}
	}

	public boolean isCompExist(Company company) {
		if(company != null && DateUtils.isInValidityPeriod(company.getBeginDate(), company.getEndDate())) {
			return true;
		}
		return false;
	}

	public boolean isCompContainsRole(String companyId, String role) {
		User user = UserUtils.getUser();
		if(user != null && user.getCompany() != null && StringUtils.equals(user.getCompany().getId(), companyId)) {
			return UserUtils.isAssignedRole(role);
		}

		int count = dao.countCompMainUserRole(companyId, role);
		if(count > 0) {
			return true;
		}
		return false;
	}


	public Company getCompany(String id) {
		Company result = this.get(id);
		if(result != null && StringUtils.isNotBlank(result.getMainUserId())) {
			result.setMainUser(userService.get(result.getMainUserId()));
		}
		return result;
	}

	public Company getByCustomerId(Integer customerId){
		return dao.getByCustomerId(customerId);
	}

	public Map<String, String> getMd5ValidationHeaderMap(String key, String timestamp, String secretKey) {
		if(StringUtils.isBlank(secretKey)) {
			Company cc = this.get(key);
			if(cc != null) {
				secretKey = cc.getSecretKey();
			}
		}
		if(StringUtils.isBlank(timestamp)) {
			timestamp = (System.currentTimeMillis()/1000) +"";
		}

		Map<String, String> resultMap = new HashMap<String, String>();
		resultMap.put("Timespan", timestamp);
		resultMap.put("Token", EncryptUtil.encodeMd5(key + timestamp + secretKey));

		return resultMap;
	}


	/**
	 * 根据条件获取激活的公司id
	 * updated for v1.9.5 KNZT-4117
	 *
	 * @param condition
	 * @return List<String>
	 */
	public List<String> getActiveCompanyIdByCondition(SysCompanyCondition condition) {
		return dao.getActiveCompanyIdByCondition(condition);
	}

	/**
	 * 子账号数量
	 * @param companyId
	 * @return
	 */
	public CompCountTO getCompUserCount(String companyId) {
		CompCountTO resultTO = new CompCountTO();
		Company company = this.get(companyId);

		if(company != null && company.getPurchaseAccountNum() != null) {
			resultTO.setPurchaseAccountNum(company.getPurchaseAccountNum());
			int userCount = userService.findUserCountByCompanyId(companyId);
			int remain = company.getPurchaseAccountNum().intValue() - userCount;
			resultTO.setRemainPurchaseAccountNum(remain < 0 ? 0 : (remain > company.getPurchaseAccountNum().intValue() ? company.getPurchaseAccountNum().intValue() : remain));
			resultTO.setConsumedUserCount(userCount);
		}
		return resultTO;
	}

	public boolean isMainUser(String companyId, String userId) {
		Company company = this.get(companyId);
		if(company == null) {
			return false;
		}
		return StringUtils.equals(company.getMainUserId(), userId);
	}

	public boolean isMainUser() {
		Company company = this.get(UserUtils.getUserCompanyId());
		if(company == null) {
			return false;
		}
		return StringUtils.equals(company.getMainUserId(), UserUtils.getUserId());
	}

	public boolean isMainUser(String userId) {
		Company company = this.get(UserUtils.getUserCompanyId());
		if(company == null) {
			return false;
		}
		return StringUtils.equals(company.getMainUserId(), userId);
	}


	/**
	 * 返回公司详情， 包含子账号剩余数
	 * @param id
	 * @return
	 */
	public Company getCompanyDetail(String id) {
		Company result = getCompany(id);
//		int userCount = systemService.findUserCountByCompanyId(id);
		if(result.getPurchaseAccountNum() != null) {
			CompCountTO count = this.getCompUserCount(id);
			int remain = count.getRemainPurchaseAccountNum();
			result.setRemainPurchaseAccountNum(remain < 0 ? 0 : (remain > result.getPurchaseAccountNum().intValue() ? result.getPurchaseAccountNum().intValue() : remain));
			result.setConsumedPurchaseAccountNum(count.getConsumedUserCount());
		}
//		int corpCount = tblCompCorporatesService.countCompCorporatesByCompanyIdWithoutModule(TaskUtils.pendingTodo());
//		if(result.getMonitorEnterpriseNum() != null) {
//			int remain = result.getMonitorEnterpriseNum().intValue() - corpCount;
//			result.setRemainMonitorEnterpriseNum(remain < 0 ? 0 : remain);
//		}
		return result;
	}

	public Company getChannelCustomer(String companyId){
		return dao.getChannelCustomer(companyId);
	}

	public List<String> listCompanyId4Job() {
		return dao.listCompanyId4Job();
	}

	public List<CompanyNameTO> listCompanyNameByIdList(List<String> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return Lists.newArrayList();
		}
		return dao.listCompanyNameByIdList(idList);
	}

	public CompanyNameTO getCompanyByMainLoginName(String loginName) {
		return dao.getByMainLoginName(loginName);
	}


	public Page<CompanyListInfo> pageListCompanyInfo(CompanyListCondition condition) {
		List<CompanyListInfo> list = dao.pageListCompanyInfo(condition);
		// added for v2.0.4 chenbl KNZT-5371
		List<String> companyIds = list.stream().map(CompanyListInfo::getId).collect(Collectors.toList());
		Map<String, Boolean> companyId2EnableMfaMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(companyIds)) {
			companyId2EnableMfaMap.putAll(sysCompInfoColExtService.listByCompanyIds(companyIds, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA)
					.stream()
					.filter(sysCompInfoColExt -> StringUtils.isNotBlank(sysCompInfoColExt.getCompanyId()) && StringUtils.isNotBlank(sysCompInfoColExt.getExtValue()))
					.collect(Collectors.toMap(SysCompInfoColExt::getCompanyId, val -> Constants.YES.equals(val.getExtValue()), (k1, k2) -> k1)));
		}

		// added for v2.1.3 fengsw KNZT-6263 查询当前月份账号的额度消耗数据 根据试用和非试用的账号 来进行分类查询统计额度消耗
		Map<String, CompanyConsumedInfoTO> trialCompanyConsumedInfoMap = new HashMap<>();
		Map<String, CompanyConsumedInfoTO> normaCompanyConsumedInfoMap = new HashMap<>();
		Map<Boolean, List<String>> compTypeMap = list.stream().collect(Collectors.groupingBy(k -> CompTypeEnum.TRIAL.getCode().equals(k.getType()),
				Collectors.mapping(CompanyListInfo::getId, Collectors.toList())));
		List<String> trialCompIdList = compTypeMap.get(Boolean.TRUE);
		List<String> normalCompIdList = compTypeMap.get(Boolean.FALSE);
		LocalDate localDate = YearMonth.now().atDay(1);
		if (CollectionUtils.isNotEmpty(trialCompIdList)) {
			trialCompanyConsumedInfoMap = Optional.ofNullable(commTblCompReportOrderService.listCompanyConsumedInfoWithDateRange(trialCompIdList, DateUtils.toDate(localDate), DateUtils.getCurrentDate()))
					.map(item -> item.stream().collect(Collectors.toMap(CompanyConsumedInfoTO::getCompanyId, Function.identity(), (k1, k2) -> k1))).orElse(new HashMap<>());
		}
		if (CollectionUtils.isNotEmpty(normalCompIdList)) {
			normaCompanyConsumedInfoMap = Optional.ofNullable(contractDeliveryTransactionService.listCompanyConsumedInfoWithDateRange(normalCompIdList, DateUtils.toDate(localDate), DateUtils.getCurrentDate()))
					.map(item -> item.stream().collect(Collectors.toMap(CompanyConsumedInfoTO::getCompanyId, Function.identity(), (k1, k2) -> k1))).orElse(new HashMap<>());
		}
		for (CompanyListInfo companyListInfo : list) {
			if (companyListInfo.getCreditReportTotalCount() == null) {
				companyListInfo.setCreditReportTotalCount(BigDecimal.ZERO);
			}
			if (companyListInfo.getCreditReportConsumedCount() == null) {
				companyListInfo.setCreditReportConsumedCount(BigDecimal.ZERO);
			}
			// removed for v1.8.2 KNZT-3722
			companyListInfo.setEnableMfa(companyId2EnableMfaMap.getOrDefault(companyListInfo.getId(), false)); // added for v2.0.4 chenbl KNZT-5371
			// added for v2.0.5 KNZT-5573 fengsw 禁用账号提示
			if (CompTypeEnum.isSelfPay(companyListInfo.getType())) {
				if (companyListInfo.getEndDate().before(new Date())) {
					companyListInfo.setDisableAccount(Constants.YES);
				} else {
					companyListInfo.setDisableAccount(Constants.NO);
				}
			}
			//added for v2.1.3 fengsw KNZT-6263 赋值当前月份消耗额度数据
			CompanyConsumedInfoTO companyConsumedInfoTO;
			if (CompTypeEnum.TRIAL.getCode().equals(companyListInfo.getType())) {
				companyConsumedInfoTO = trialCompanyConsumedInfoMap.get(companyListInfo.getId());
			} else {
				companyConsumedInfoTO = normaCompanyConsumedInfoMap.get(companyListInfo.getId());
			}
			companyListInfo.setCreditReportCurMonthConsumedCount(Objects.nonNull(companyConsumedInfoTO) ? companyConsumedInfoTO.getCreditReportConsumedCount() : BigDecimal.ZERO);
			companyListInfo.setPayTypeDesc(CompPayTypeEnum.getDescCnByCode(companyListInfo.getPayType()));
			companyListInfo.setCategoryDesc(CompCategoryEnum.getDescByCode(companyListInfo.getCategory()));
		}
		condition.getPage().setList(list);
		return condition.getPage();
	}

	public PlatformCompanyDetailTO getCompanyDetailInfo(String companyId) {
		PlatformCompanyDetailTO detailTO = dao.getCompanyDetailInfo(companyId);

		// 设置默认值
		if (detailTO.getCreditReportTotalCount() == null) {
			detailTO.setCreditReportTotalCount(BigDecimal.ZERO);
		}
		if (detailTO.getCreditReportConsumedCount() == null) {
			detailTO.setCreditReportConsumedCount(BigDecimal.ZERO);
		}

		// 查询SysCompInfoColExt相关数据，获取MFA、安全策略、UI版本等扩展配置
		List<SysCompInfoColExt> commExtList = sysCompInfoColExtService.listColExtByType(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE);
		Map<String, String> extConfigMap = commExtList.stream().collect(Collectors.toMap(SysCompInfoColExt::getExtKey, SysCompInfoColExt::getExtValue, (v1, v2) -> v1));
		
		// MFA配置
		String enableMfaValue = extConfigMap.get(Constants.CompInfoColExt.CommExt.COL_NAME_ENABLE_MFA);
		detailTO.setEnableMfa(Constants.YES.equals(enableMfaValue));

		// 安全策略配置
		String securityPolicyValue = extConfigMap.get(Constants.CompInfoColExt.CommExt.COL_NAME_SECURITY_POLICY);
		detailTO.setSecurityPolicy(StringUtils.isNotBlank(securityPolicyValue) ? securityPolicyValue : PwdSecurityPolicyEnum.BAS.getCode());

		// 重复订单提醒
		String duplicateOrderRemindValue = extConfigMap.get(Constants.CompInfoColExt.CommExt.COL_DUPLICATE_ORDER_REMIND);
		detailTO.setDuplicateOrderRemind(StringUtils.isNotBlank(duplicateOrderRemindValue) ? duplicateOrderRemindValue : "N");

		// tax 相关
		String taxIdType = extConfigMap.get(Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_TYPE);
		String taxIdNumber = extConfigMap.get(Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_NUMBER);
		detailTO.setTaxIdType(taxIdType);
		detailTO.setTaxIdNumber(taxIdNumber);

		// invoice remark
		String invoiceRemark = compInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.INVOICE_REMARK);
		detailTO.setInvoiceRemark(invoiceRemark);

		// 实现字段描述转换
		detailTO.setPayTypeDesc(CompPayTypeEnum.getDescCnByCode(detailTO.getPayType()));
		detailTO.setCategoryDesc(CompCategoryEnum.getDescByCode(detailTO.getCategory()));

		// 地址
		CompanyAddressTO companyAddress = commCompUserService.getCompanyAddress(companyId);
		detailTO.setCompanyAddress(companyAddress);

		// 测试账号标识
		detailTO.setInnerAccount(commCompUserService.isTestCompany(companyId));

		return detailTO;
	}

	/**
	 * added for v1.6.0 KNZT-2730
	 * 获取service 详细信息
	 *
	 * @param form
	 * @return ServiceDetailTO
	 */
	public ServiceDetailTO getServiceDetail(ServiceDetailForm form) {
		boolean isMainUser = this.isMainUser();
		boolean isDateNull = Objects.isNull(form.getMinOrderCreatedDate()) && Objects.isNull(form.getMaxOrderCreatedDate());
		ServiceDetailTO detailTO = new ServiceDetailTO();
		String companyId = UserUtils.getUserCompanyId();

		// added for v1.6.0 KNZT-2730 增加service消耗明细
		List<CompanyServiceUsageTO> serviceUsageList = Lists.newArrayListWithCapacity(5);
		// updated for v1.9.3 KNZT-4350
//		String loginNameFilter = isMainUser ? null : UserUtils.getUserLoginName();
		String loginNameFilter = isMainUser && (form.getPersonal() == null || !form.getPersonal()) ? null : UserUtils.getUserLoginName();
		// updated for v1.8.0 KNZT-3524
		List<ReportOrderUnitInfo> orderUnitList = commTblCompReportOrderService.getReportOrderUnitInfo(companyId, loginNameFilter,
				form.getMinOrderCreatedDate(), form.getMaxOrderCreatedDate(), true);
		// updated for v1.8.0 KNZT-3524
		Map<String, ReportOrderUnitInfo> reportType2UnitInfoMap = orderUnitList.stream()
				.filter(reportOrderUnitInfo -> StringUtils.isNotBlank(reportOrderUnitInfo.getReportType()))
				.collect(Collectors.toMap(ReportOrderUnitInfo::getReportType, Function.identity(), (k1, k2) -> k1));
		List<TblReportChargeUnit> chargeUnits = commTblReportChargeUnitService.listChargeUnitByCompany(UserUtils.getUserCompanyId());
		for (TblReportChargeUnit chargeUnit : chargeUnits) {
			/*Predicate<ReportOrderUnitInfo> reportGroupTypePredicate = k -> StringUtils.equals(k.getReportType(), chargeUnit.getReportType());
			long count = orderUnitList.stream().filter(reportGroupTypePredicate).count();
			BigDecimal subConsumedCredits = orderUnitList.stream()
					.filter(reportGroupTypePredicate)
					.map(ReportOrderUnitInfo::getTotalUnit)
					.reduce(BigDecimal.ZERO, BigDecimal::add);*/
			ReportOrderUnitInfo reportOrderUnitInfo = reportType2UnitInfoMap.get(chargeUnit.getReportType());
			long count = 0L;
			BigDecimal subConsumedCredits = BigDecimal.ZERO;
			if (reportOrderUnitInfo != null) {
				count = reportOrderUnitInfo.getCount();
				subConsumedCredits = reportOrderUnitInfo.getTotalUnit();
			}

			// updated for v1.8.2 KNZT-3722
			if (count != 0 || StringUtils.equals(Constants.YES, chargeUnit.getEnabled())) {
				CompanyServiceUsageTO companyServiceUsageTO = CompanyServiceUsageTO.build(chargeUnit, count, subConsumedCredits);
				serviceUsageList.add(companyServiceUsageTO);
			}
		}
		detailTO.setServiceUsageList(serviceUsageList);

		BigDecimal cnSubConsumedCredits = serviceUsageList.stream().map(CompanyServiceUsageTO::getSubConsumedCredits).reduce(BigDecimal.ZERO, BigDecimal::add);
		detailTO.setConsumedCreditsFromUsageSum(cnSubConsumedCredits);
		// added for v1.9.8 KNZT-4538
		SysCompInfoFuncCount compInfoFuncCount = commSysCompInfoFuncCountService.readOnlyGetCompFuncCount(companyId);
		if (compInfoFuncCount != null) {
			detailTO.setTotalUnit(compInfoFuncCount.getTotalCount());
			detailTO.setConsumedUnit(compInfoFuncCount.getConsumedCount());
			detailTO.setRemainingUnit(detailTO.getTotalUnit().subtract(detailTO.getConsumedUnit()));
		}

// removed for v1.9.8 KNZT-4538
//		// 大陆额度，消耗额度统一从外层订单金额汇总数据
//		SysCompInfoFuncCount funcCountWithConsume = commSysCompInfoFuncCountService.getCompInfoFuncCountByFuncTableId(companyId, Constants.FunctionTable.ID_REPORT_ID);
//		if(funcCountWithConsume != null) {
//			BigDecimal cnTotalConsumedCredits = cnServiceUsageList.stream().map(CompanyServiceUsageTO::getSubConsumedCredits).reduce(BigDecimal.ZERO, BigDecimal::add);
//			detailTO.setConsumedUnit(cnTotalConsumedCredits);
//			if (isMainUser && isDateNull) {
//				detailTO.setTotalUnit(funcCountWithConsume.getTotalCount());
//				detailTO.setRemainingUnit(funcCountWithConsume.getTotalCount().subtract(detailTO.getConsumedUnit()));
//			}
//		}
//		// 境外额度，主账号使用开放平台的消耗额度数据，子账号使用自己的订单金额汇总数据
//		// 境外额度统一从国际版数据库中读取
//		funcCountWithConsume = commSysCompInfoFuncCountService.getCompInfoFuncCountByFuncTableId(companyId, Constants.FunctionTable.ID_HK_SERVICE_ID);
//		if(funcCountWithConsume != null) {
//			/*if (isMainUser && isDateNull) {
//				Map<String, Object> queryMap = new HashMap<>();
//				List<String> companyIdList = Lists.newArrayList(companyId);
//				queryMap.put("companyIds", companyIdList);
//				QuotaConsumptionResult quotaConsumptionResult = OpenApiUserInfoInterface.getQuotaConsumptionInfo(queryMap);
//				if (quotaConsumptionResult != null && CollectionUtils.isNotEmpty(quotaConsumptionResult.getResult())) {
//					QuotaConsumptionTO quotaConsumptionTO = quotaConsumptionResult.getResult().get(0);
//					detailTO.setTotalUnit4Hk(quotaConsumptionTO.getAccountRechargeTotal());
//					detailTO.setConsumedUnit4Hk(quotaConsumptionTO.getAccountRechargeTotal().subtract(quotaConsumptionTO.getAccountBalance()));
//					detailTO.setRemainingUnit4Hk(quotaConsumptionTO.getAccountBalance());
//				}
//			} else {*/
//				BigDecimal globalTotalConsumedCredits = globalServiceUsageList.stream().map(CompanyServiceUsageTO::getSubConsumedCredits).reduce(BigDecimal.ZERO, BigDecimal::add);
//				detailTO.setConsumedUnit4Hk(globalTotalConsumedCredits);
//				detailTO.setTotalUnit4Hk(funcCountWithConsume.getTotalCount());
//				detailTO.setRemainingUnit4Hk(funcCountWithConsume.getTotalCount().subtract(detailTO.getConsumedUnit4Hk()));
//			//}
//		}

		return detailTO;
	}


	public CompanyMainInfoTO getCompanyByProMainLoginName(String proMainLoginName) {
		return dao.getByProMainLoginName(proMainLoginName);
	}

	// added for V2.0.7 ZS-ZS-477 【框架】调用专业版接口时（如添加监控），参数传值调整 新增根据登录名获取用户id和所属公司id
	public CompanyMainInfoTO getCompanyByLoginName(String loginName){
		return dao.getCompanyByLoginName(loginName);
	}

	public CompanyMainInfoTO getCompByIcWithDelFlag(String companyId){
		return dao.getCompByIdWithDelFlag(companyId);
	}

	public List<CompanyProKeyInfoPO> listCompanyProKeyInfo() {
		return dao.listCompanyProKeyInfo();
	}

	/**
	 * 根据companyId|主账号loginName获取公司账号信息
	 * 
	 * @param searchKey
	 * @return
	 */
	public List<CompanyShortInfoTO> listCompanyShortInfoBySearchKey(String searchKey) {
		if(StringUtils.isBlank(searchKey)){
			return null;
		}
		return dao.listCompanyShortInfoBySearchKey(searchKey);
	}

	/**
	 * 根据companyId获取公司账号信息
	 *
	 * @param id
	 * @return
	 */
	public CompanyShortInfoTO getCompanyShortInfoById(String id) {
		if(StringUtils.isBlank(id)){
			return null;
		}
		return dao.getCompanyShortInfoById(id);
	}
	/**
	 * 更新open_user_id
	 * 
	 * @param id
	 * @param openApiUserId
	 */
	public void updateOpenApiUserId(String id, String openApiUserId) {
		dao.updateOpenApiUserId(id, openApiUserId);
	}


	/**
	 * 获取uopen_api_user_id不存在的用户
	 *
	 * @return
	 */
	public List<CompanyPatchInfoListTO> listCompanyToOpenApiForPatch(List<String> idList) {
		return dao.listCompanyToOpenApiForPatch(idList);
	}

	/**
	 * 获取open_api_user_id存在的账号详情信息，包含开始结束时间以及香港额度消耗数据
	 *
	 * @return
	 */
	public List<CompanyPatchInfoListTO> listCompanyAccountInfoForOpenApiToPatchWithExistsOpenUserId(List<String> idList) {
		return dao.listCompanyAccountInfoForOpenApiToPatchWithExistsOpenUserId(idList);
	}

	/**
	 * added for v1.8.8 KNZT-4013
	 * 更新公司类型
	 *
	 * @param companyId
	 * @param type
	 */
	public void updateCompanyType(String companyId, Integer type) {
		Company company = new Company();
		company.setId(companyId);
		company.setType(type);
		company.preUpdate();
		dao.updateCompanyType(company);
	}

	/**
	 * added for KNZT-1481 【后台管理】账号开通，自动发邮件，通知账号、密码和链接
	 *
	 */
	/* removed for v2.0.4 chenbl KNZT-5442
	public void sendNotification(Company company, String pwd) throws MessageException {
		// updated for v1.6.7 KNZT-2955
		SysTemplate template = commSysTemplateService.getByTemplateName("email_company_main_save");
		if(template == null) {
			logger.error("email_company_main_save template does not existed");
			throw new MessageException("err.access");
		}
		Map<String, Object> paramMap = new HashMap<>();
		String userName = this.getUserNameFromEmail(company.getEmail());
		paramMap.put("userName", userName);
		paramMap.put("email", company.getEmail());
		paramMap.put("password", pwd);
		// added for v1.6.7 KNZT-2955
		paramMap.put("beginDate", DateUtils.formatDateForSg(company.getBeginDate(), "dd-MMM-yyyy", Locale.ENGLISH));
		paramMap.put("endDate", DateUtils.formatDateForSg(company.getEndDate(), "dd-MMM-yyyy", Locale.ENGLISH));

		String sendTo = company.getEmail();
		String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
		String emailSubject = template.getTemplateSubject();
		List<String> toList = StringUtils.split2List(sendTo, ",");
		List<String> ccList = null;
		if(toList.size() > 1) {
			ccList = toList.subList(1, toList.size());
		}
		ArrayList<EmailAttachmentFile> attachFileList = new ArrayList<>(0);
		List<String> bccList = Lists.newArrayList();
		// updated for v1.5.0 KNZT-2253
		bccList.add(Constants.NOTIFICATION_EMAIL);
		qccMailSenderService.send(toList.get(0), ccList, bccList, emailSubject, emailContent, false, attachFileList);
	}*/

	private String getUserNameFromEmail(String email){
		return StringUtils.substringBefore(email, "@");
	}


	public List<String> getCompanyIdByCondition(SysCompanyCondition condition) {
		if (Objects.isNull(condition)) {
			condition = new SysCompanyCondition();
		}
		condition.removePage();
		return dao.getCompanyIdByCondition(condition);
	}

	// added for v1.9.9 chenbl KNZT-4974
	public boolean existUnactivatedCompanyByDateRange(String beginTimeStr, String endTimeStr) {
		return dao.existUnactivatedCompanyByDateRange(beginTimeStr, endTimeStr) > 0;
	}

	// 校验香港查册人信息，true合法 added for v2.0.0 chenbl KNZT-5094
	public boolean validateHkSearcherInfo(String companyId) throws MessageException {
		HkSearcherInfo hkSearcherInfo = getHkSearcherInfo(companyId);
		return hkSearcherInfo != null;
	}

    /**
     * 获取香港查册人信息 added for v2.0.0 chenbl KNZT-5094
     *
     * @param companyId
     * @return 以下场景
     * 1: return=null 查册人缺失
     * 2: return!=null
     * 2.1 ignore=true 忽略查册人信息
     * 2.2 ignore=false 查册人信息完整
     * @throws MessageException
     */
	public HkSearcherInfo getHkSearcherInfo(String companyId) throws MessageException {
		MsgExceptionUtils.checkIsNull(companyId);
		List<SysCompInfoColExt> sysCompInfoColExts = sysCompInfoColExtService.listColExtByType(companyId, Constants.CompInfoColExt.HkSearcherInfo.EXT_TYPE);
		if (CollectionUtils.isEmpty(sysCompInfoColExts)) {
			return null;
		}
		HkSearcherInfo hkSearcherInfo = new HkSearcherInfo();
		boolean ignore = sysCompInfoColExts.stream().anyMatch(t -> Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_IGNORE.equals(t.getExtKey()));
		if (ignore) {
			hkSearcherInfo.setIgnore(true);
			return hkSearcherInfo;
		}
		hkSearcherInfo.setEnglishSurname(getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ENGLISH_SURNAME));
		hkSearcherInfo.setEnglishOtherNames(getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ENGLISH_OTHER_NAMES));
		hkSearcherInfo.setDocType(getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_DOC_TYPE));
		hkSearcherInfo.setIssuingPlace(getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ISSUING_PLACE));
		hkSearcherInfo.setIssuingAuthority(getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ISSUING_AUTHORITY));
		String idDocNumEncrypted = getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ID_DOC_NUM);
		if (StringUtils.isNotBlank(idDocNumEncrypted)) {
			try {
				String idDocNumDecrypted = AesUtil.decrypt(idDocNumEncrypted, HK_SEARCHER_INFO_AES_KEY, HK_SEARCHER_INFO_AES_IV);
				hkSearcherInfo.setIdDocNum(idDocNumDecrypted);
			} catch (Exception e) {
				logger.error("解密失败", e);
				throw new MessageException("err.access");
			}
		}
		String colValue = getColValue(sysCompInfoColExts, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ACCEPT_LIST);
		if (StringUtils.isNotBlank(colValue)) {
			try {
				List<Integer> acceptList = StringUtils.split2List(colValue, ",").stream().map(Integer::parseInt).collect(Collectors.toList());
				hkSearcherInfo.setAcceptList(acceptList);
			} catch (Exception e) {
				logger.error("acceptList获取失败", e);
			}
		}
		return hkSearcherInfo;
	}

	public HkSearcherInfo4Api getHkSearchInfo4Api(String companyId) throws MessageException {
		HkSearcherInfo hkSearcherInfo = getHkSearcherInfo(companyId);
		return buildHkSearchInfo4Api(hkSearcherInfo);
	}

	public static HkSearcherInfo4Api buildHkSearchInfo4Api(HkSearcherInfo hkSearcherInfo) {
		HkSearcherInfo4Api hkSearcherInfo4Api = new HkSearcherInfo4Api();
		if (hkSearcherInfo == null || hkSearcherInfo.getIgnore()) {
			return hkSearcherInfo4Api;
		}
		if (CollectionUtils.isNotEmpty(hkSearcherInfo.getAcceptList())) {
			hkSearcherInfo4Api.setLoginOption(hkSearcherInfo.getAcceptList());
		}
		String[] loginInputFullArr = new String[8];
		loginInputFullArr[1] = hkSearcherInfo.getEnglishSurname();
		loginInputFullArr[2] = hkSearcherInfo.getEnglishOtherNames();
		if (HkSearcherInfoDocTypeEnum.HKID.getKey().equals(hkSearcherInfo.getDocType())) {
			loginInputFullArr[3] = hkSearcherInfo.getIdDocNum();
		} else if (HkSearcherInfoDocTypeEnum.PASSPORT.getKey().equals(hkSearcherInfo.getDocType())) {
			loginInputFullArr[4] = "护照";
			loginInputFullArr[5] = hkSearcherInfo.getIssuingPlace();
			loginInputFullArr[6] = hkSearcherInfo.getIssuingAuthority();
			loginInputFullArr[7] = hkSearcherInfo.getIdDocNum();
		} else if (HkSearcherInfoDocTypeEnum.OTHERID.getKey().equals(hkSearcherInfo.getDocType())) {
			loginInputFullArr[4] = "香港以外的政府机构签发的身分证";
			loginInputFullArr[5] = hkSearcherInfo.getIssuingPlace();
			loginInputFullArr[6] = hkSearcherInfo.getIssuingAuthority();
			loginInputFullArr[7] = hkSearcherInfo.getIdDocNum();
		}
		List<String> loginInputFullList = Stream.of(loginInputFullArr).collect(Collectors.toList());
		for (int i = 0; i < loginInputFullList.size(); i++) {
			if (loginInputFullList.get(i) == null) {
				loginInputFullList.set(i, "");
			}
		}
		hkSearcherInfo4Api.setLoginInputFull(loginInputFullList);
		return hkSearcherInfo4Api;
	}

	// added for v2.0.0 chenbl KNZT-5094
	private static String getColValue(List<SysCompInfoColExt> sysCompInfoColExts, String colKey) {
		if (CollectionUtils.isEmpty(sysCompInfoColExts) || StringUtils.isBlank(colKey)) {
			return null;
		}
		return sysCompInfoColExts.stream().filter(t -> colKey.equals(t.getExtKey())).map(SysCompInfoColExt::getExtValue).findFirst().orElse(null);
	}

	// added for v2.0.0 chenbl KNZT-5094
	public void saveHkSearcherInfo(HkSearcherInfoSaveForm form) throws MessageException {
		validate(form);
		// 先删除ignore 字段
		sysCompInfoColExtService.deleteColExtValue(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.EXT_TYPE, Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_IGNORE);
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ENGLISH_SURNAME, form.getEnglishSurname());
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ENGLISH_OTHER_NAMES, form.getEnglishOtherNames());
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_DOC_TYPE, form.getDocType());
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ISSUING_PLACE, form.getIssuingPlace());
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ISSUING_AUTHORITY, form.getIssuingAuthority());
		// idDocNum加密存储
		if (StringUtils.isNotBlank(form.getIdDocNum())) {
			try {
				String idDocNumEncrypted = AesUtil.encrypt(form.getIdDocNum(), HK_SEARCHER_INFO_AES_KEY, HK_SEARCHER_INFO_AES_IV);
				saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ID_DOC_NUM, idDocNumEncrypted);
			} catch (Exception e) {
				logger.error("加密失败", e);
				throw new MessageException("err.access");
			}
		}
		saveIntoCompColExt4HkSearcherInfo(form.getCompanyId(), Constants.CompInfoColExt.HkSearcherInfo.COL_NAME_ACCEPT_LIST, StringUtils.join(form.getAcceptList(), StringUtils.COMMA));
		// added for v2.0.9 fengsw KNZT-5944 开放平台接口绑定的用户 添加或修改查册人信息
		Company company = get(form.getCompanyId());
		if (Objects.nonNull(company) && StringUtils.isNotBlank(company.getOpenApiUserId())) {
			saveHkSearchInfo2OpenApi(form.getCompanyId());
		}
	}

	// added for v2.0.0 chenbl KNZT-5094
	private void saveIntoCompColExt4HkSearcherInfo(String companyId, String colName, String value) {
		sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.HkSearcherInfo.EXT_TYPE, colName, value);
	}

	// added for v2.0.0 chenbl KNZT-5094
	private void validate(HkSearcherInfoSaveForm form) throws MessageException {
		MsgExceptionUtils.checkIsNull(form.getCompanyId(), "msg:companyId不能为空");
		Company company = get(form.getCompanyId());
		MsgExceptionUtils.checkIsNull(company, "msg:公司不存在");
		MsgExceptionUtils.failBuild(StringUtils.isBlank(form.getEnglishSurname()) && StringUtils.isBlank(form.getEnglishOtherNames()), "msg:englishSurname和englishOtherNames不能都为空");
		MsgExceptionUtils.failBuild(StringUtils.length(form.getEnglishSurname()) > 50, "msg:englishSurname长度不能超过50");
		MsgExceptionUtils.failBuild(StringUtils.length(form.getEnglishOtherNames()) > 110, "msg:englishOtherNames长度不能超过110");
		HkSearcherInfoDocTypeEnum docTypeEnum = HkSearcherInfoDocTypeEnum.getByKey(form.getDocType());
		MsgExceptionUtils.failBuild(Objects.isNull(docTypeEnum), "msg:docType非法");
		if (HkSearcherInfoDocTypeEnum.HKID == docTypeEnum) {
			MsgExceptionUtils.checkIsNull(form.getIdDocNum(), "msg:idDocNum不能为空");
			// TODO HKID规则校验
		} else if (HkSearcherInfoDocTypeEnum.PASSPORT == docTypeEnum) {
			MsgExceptionUtils.checkIsNull(form.getIssuingPlace(), "msg:issuingPlace不能为空");
			MsgExceptionUtils.failBuild(!getIssuingPlaceCodes().contains(form.getIssuingPlace()), "msg:issuingPlace非法");
			MsgExceptionUtils.checkIsNull(form.getIdDocNum(), "msg:idDocNum不能为空");
			MsgExceptionUtils.failBuild(StringUtils.length(form.getIdDocNum()) > 25, "msg:idDocNum长度不能超过25");
		} else if (HkSearcherInfoDocTypeEnum.OTHERID == docTypeEnum) {
			MsgExceptionUtils.checkIsNull(form.getIssuingPlace(), "msg:issuingPlace不能为空");
			MsgExceptionUtils.failBuild(!getIssuingPlaceCodes().contains(form.getIssuingPlace()), "msg:issuingPlace非法");
			MsgExceptionUtils.checkIsNull(form.getIssuingAuthority(), "msg:issuingAuthority不能为空");
			MsgExceptionUtils.failBuild(StringUtils.length(form.getIssuingAuthority()) > 95, "msg:issuingAuthority长度不能超过95");
			MsgExceptionUtils.checkIsNull(form.getIdDocNum(), "msg:idDocNum不能为空");
			MsgExceptionUtils.failBuild(StringUtils.length(form.getIdDocNum()) > 25, "msg:idDocNum长度不能超过25");
		} else {
			throw new MessageException("msg:docType非法");
		}
		MsgExceptionUtils.failBuild(CollectionUtils.isEmpty(form.getAcceptList()), "msg:acceptList不能为空");
		for (Integer accept : form.getAcceptList()) {
			HkSearcherInfoAcceptEnum acceptEnum = HkSearcherInfoAcceptEnum.getByKey(accept);
			MsgExceptionUtils.failBuild(Objects.isNull(acceptEnum), "msg:acceptList非法");
		}
	}

	// added for v2.0.0 chenbl KNZT-5094
	private List<String> getIssuingPlaceCodes() {
		List<Dict> dictList = dictService.getDictList(Constants.DictType.HK_SEARCHER_INFO_ISSUING_PLACE);
		return dictList.stream().map(Dict::getValue).collect(Collectors.toList());
	}

	public void sendActivateUserEmail4MainUser(Company company) throws MessageException {
		this.sendActivateUserEmail4MainUser(company, false);
	}

	/**
	 * added for v2.0.9 fengsw KNZT-5944
	 * 存在开放平台账号绑定的接口用户，添加查册人信息
	 * 
	 * @param companyId
	 * @throws MessageException
	 */
	private void saveHkSearchInfo2OpenApi(String companyId) throws MessageException {
		if (StringUtils.isBlank(companyId)) return;
		HkSearcherInfo hkSearcherInfo = getHkSearcherInfo(companyId);
		if (Objects.isNull(hkSearcherInfo)) return;
		try {
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("companyId", companyId);
			paramMap.put("englishSurname", hkSearcherInfo.getEnglishSurname());
			paramMap.put("englishOtherName", hkSearcherInfo.getEnglishOtherNames());
			paramMap.put("cardType", HkSearcherInfoDocTypeEnum.getTypeByKey(hkSearcherInfo.getDocType()));
			paramMap.put("cardNum", hkSearcherInfo.getIdDocNum());
			paramMap.put("country", hkSearcherInfo.getIssuingPlace());
			paramMap.put("issuingAuthority", hkSearcherInfo.getIssuingAuthority());
			if (CollectionUtils.isNotEmpty(hkSearcherInfo.getAcceptList())) {
				paramMap.put("LoginOption", hkSearcherInfo.getAcceptList().stream().map(String::valueOf).collect(Collectors.toList()));
			}
			OpenApiUserInfoInterface.saveHKDataSearcherInfo(paramMap);
		} catch (Exception e) {
			logger.error("saveHkSearchInfo2OpenApi error", e);
			throw new MessageException("err.access");
		}
	}

	/**
	 * updated for v2.0.5 fengsw KNZT-5602
	 * 试用、正式账号激活邮件需要增加密送人
	 *
	 * @param company
	 * @throws MessageException
	 */
	public void sendActivateUserEmail4MainUser(Company company, boolean bccFlag) throws MessageException {
		User user = userService.get(company.getMainUserId());
		MsgExceptionUtils.checkIsNull(user);
		sendActivateUserEmail(company, user, bccFlag);
	}


	// updated for v2.0.5 fengsw KNZT-5602
	public void sendActivateUserEmail(Company company, User user) throws MessageException {
		this.sendActivateUserEmail(company, user, false);
	}

	// updated for v2.0.5 fengsw KNZT-5602 KA账号激活邮件 增加密送人
	// updated for v2.0.4 chenbl KNZT-5442
	public void sendActivateUserEmail(Company company, User user, boolean bccFlag) throws MessageException {
		SysTemplate template = getSysTemplate4EmailActivateUser();
		if (Objects.isNull(template)) {
			logger.error("email_activate_user template not exist");
			return;
		}
		List<String> bccList = null;
		if (bccFlag) {
			bccList = getBccConfigList();
		}
		this.sendActivateUserEmail(template, company, user, bccList);
	}

	public SysTemplate getSysTemplate4EmailActivateUser() {
        return templateService.getByTemplateName("email_activate_user");
	}

	public List<String> getBccConfigList() {
		String sendTo = DictUtils.getDictValue("ka_account_active_email_bcc_list", "system_config", "<EMAIL>,<EMAIL>,<EMAIL>");
		return StringUtils.split2List(sendTo, ",");
	}

	public void sendActivateUserEmail(SysTemplate template, Company company, User user, List<String> bccList) throws MessageException {
		Map<String, Object> emailParamMap = new HashMap<>();
		emailParamMap.put("userId", UserService.generateUserIdToken(user.getId()));
		emailParamMap.put("isKa", CompTypeEnum.isKa(company.getType()));// updated for v2.3.6 fengsw KNZT-8575 激活模版恢复之前版本，该字段回归
		emailParamMap.put("isAdvSecurity", PwdSecurityPolicyEnum.ADV == userService.getCompPwdSecurityPolicy(company.getId()));
		emailParamMap.put("isAdmin", UserUtils.isCompanyAdmin(user.getDataScope())); // added for v2.0.7 chenbl KNZT-6595
		String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), emailParamMap);
		String emailSubject = template.getTemplateSubject();
		if (CollectionUtils.isEmpty(bccList)) {
			qccMailSenderService.send(user.getLoginName(), emailSubject, emailContent);
		} else {
			qccMailSenderService.send(user.getLoginName(), null, bccList, emailSubject, emailContent);
		}
	}


	// added for v2.1.7 chenbl KNZT-6477
	// 平台管理员修改默认UI版本
	public void configUiDefaultVersion4platformMgnt(String userCompanyId, String version) {
		if (StringUtils.isBlank(userCompanyId) || StringUtils.isBlank(version)) {
			return;
		}
		sysCompInfoColExtService.saveColExtValue(userCompanyId, EXT_TYPE, COL_NAME_UI_DEFAULT_VERSION,
				StringUtils.equals(version, COL_VALUE_UI_VERSION_V1) ? COL_VALUE_UI_VERSION_V1 : COL_VALUE_UI_VERSION_V2);
		// 同步修改当前版本
		doConfigUiVersion(userCompanyId, version);
	}

	// 获取默认UI版本
	public String getConfigUiDefaultVersion(String userCompanyId) {
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(userCompanyId, EXT_TYPE, COL_NAME_UI_DEFAULT_VERSION);
		return StringUtils.equals(value, COL_VALUE_UI_VERSION_V1) ? COL_VALUE_UI_VERSION_V1 : COL_VALUE_UI_VERSION_V2;
	}

	// 获取默认UI版本
	public Map<String, String> getCompanyId2UiDefaultVersionMap(List<String> companyIds) {
		Map<String, String> companyId2UiDefaultVersionMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(companyIds)) {
			companyId2UiDefaultVersionMap.putAll(sysCompInfoColExtService.listByCompanyIds(companyIds, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_UI_DEFAULT_VERSION)
					.stream()
					.filter(sysCompInfoColExt -> StringUtils.isNotBlank(sysCompInfoColExt.getCompanyId()) && StringUtils.isNotBlank(sysCompInfoColExt.getExtValue()))
					.collect(Collectors.toMap(SysCompInfoColExt::getCompanyId, val -> StringUtils.equals(val.getExtValue(), COL_VALUE_UI_VERSION_V1) ? COL_VALUE_UI_VERSION_V1 : COL_VALUE_UI_VERSION_V2, (k1, k2) -> k1)));
		}
		return companyId2UiDefaultVersionMap;
	}

	// 用户管理员配置当前UI版本
	public void configUiVersion4CompanyAdmin(String version) throws MessageException {
		String userCompanyId = UserUtils.getUserCompanyId();
		MsgExceptionUtils.failBuild(!canSwitchUi(UserUtils.getUserId()));
		doConfigUiVersion(userCompanyId, version);
	}

	// 是否能切换UI版本
	public boolean canSwitchUi(String userId) {
		User user = userService.get(userId);
		if(user == null){
			return false;
		}
		if (!UserUtils.isCompanyAdmin(user.getDataScope())) {
			return false;
		}
		String configUiDefaultVersion = getConfigUiDefaultVersion(user.getCompanyId());
		// 默认版本为新版本时不支持用户切换版本
		return !StringUtils.equals(configUiDefaultVersion, COL_VALUE_UI_VERSION_V2);
	}

	private void doConfigUiVersion(String userCompanyId, String version) {
		if (StringUtils.isBlank(version)) {
			return;
		}
		sysCompInfoColExtService.saveColExtValue(userCompanyId, EXT_TYPE, COL_NAME_UI_VERSION,
				StringUtils.equals(version, COL_VALUE_UI_VERSION_V1) ? COL_VALUE_UI_VERSION_V1 : COL_VALUE_UI_VERSION_V2);
	}

	// 获取当前UI版本
	public String getConfigUiVersion(String userCompanyId) {
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(userCompanyId, EXT_TYPE, COL_NAME_UI_VERSION);
		return StringUtils.equals(value, COL_VALUE_UI_VERSION_V1) ? COL_VALUE_UI_VERSION_V1 : COL_VALUE_UI_VERSION_V2;
	}

	public boolean existsExtColByType(String extType) {
		String userCompanyId = UserUtils.getUserCompanyId();
		List<SysCompInfoColExt> sysCompInfoColExts = sysCompInfoColExtService.listColExtByType(userCompanyId, extType);
		return CollectionUtils.isNotEmpty(sysCompInfoColExts);
	}

	/**
	 * 获取是否开启重复订单项
	 * 
	 * @return
	 */
	public boolean getConfigDuplicateOrderRemind(String companyId) {
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_DUPLICATE_ORDER_REMIND);
		return StringUtils.equals(value, Constants.YES);
	}

	// added for v2.1.8 chenbl KNZT-6899
	public void updateCompanyMainUser(String companyId, String mainUserId, String email) {
		dao.updateCompanyMainUser(companyId, mainUserId, email, UserUtils.getUserId());
	}

	// added for v2.1.8 chenbl KNZT-6899
	public void toggleMainUser(String companyId, String userId) throws MessageException {
		MsgExceptionUtils.checkIsNull(companyId);
		MsgExceptionUtils.checkIsNull(userId);
		Company company = this.get(companyId);
		MsgExceptionUtils.checkIsNull(company);
		MsgExceptionUtils.failBuild(StringUtils.equals(company.getMainUserId(), userId), "msg:不能切换给自身");
		User mainUser = userService.get(company.getMainUserId());
		MsgExceptionUtils.checkIsNull(mainUser);
		User user = userService.get(userId);
		MsgExceptionUtils.checkIsNull(user);
		MsgExceptionUtils.failBuild(!UserStatusEnum.ACTIVE.getKey().equals(user.getStatus()), "msg:子账号未激活"); // 用户状态校验
		// part1	Role交换
		List<Role> userRoles = userService.findUserRole(userId, null);
		Set<String> roleIds = userRoles.stream().map(Role::getId).collect(Collectors.toSet());
		List<Role> rolesOfMainUser = userService.findUserRole(mainUser.getId(), null);
		Set<String> roleIdsOfMainUser = rolesOfMainUser.stream().map(Role::getId).collect(Collectors.toSet());
		userService.updateUserRole(userId, roleIdsOfMainUser);
		userService.updateUserRole(mainUser.getId(), roleIds);
		// part2	CompGroup交换
		String compGroupIdByUserId = userService.getCompGroupIdByUserId(userId);
		commSysUserCompGroupService.doSaveUserCompGroup(company.getId(), userId, null);
		commSysUserCompGroupService.doSaveUserCompGroup(company.getId(), mainUser.getId(), compGroupIdByUserId == null ? null : Sets.newHashSet(compGroupIdByUserId));
		// part3	CompRole交换
		List<SysCompRole> compRoles = sysCompRoleService.listCompRolesByUserId(userId);
		Set<String> compRoleIds = compRoles.stream().map(SysCompRole::getId).collect(Collectors.toSet());
		List<SysCompRole> compRolesOfMainUser = sysCompRoleService.listCompRolesByUserId(mainUser.getId());
		Set<String> compRoleIdsOfMainUser = compRolesOfMainUser.stream().map(SysCompRole::getId).collect(Collectors.toSet());
		sysUserCompRoleService.saveUserCompRole(userId, compRoleIdsOfMainUser);
		sysUserCompRoleService.saveUserCompRole(mainUser.getId(), compRoleIds);
		// part4	更新dataScope
		commCompUserService.updateUserDataScope(userId, compRolesOfMainUser);
		commCompUserService.updateUserDataScope(mainUser.getId(), compRoles);
		// part5	系统角色、公司角色、分组检查
		commSysUserCompGroupService.doRoleCheckAfterEditGroupOrRole(company.getId(), userId);
		commSysUserCompGroupService.doRoleCheckAfterEditGroupOrRole(company.getId(), mainUser.getId());
		// part6	公司主账号信息更新
		this.updateCompanyMainUser(company.getId(), userId, user.getLoginName());
		// part7	更新开放平台关联的用户邮箱
		if (Boolean.TRUE.equals(company.getEnableOpenApi())) {
			PlatformMgntService.updateOpenApiUser(company.getId(),
					company.getOpenApiUserId(),
					company.getName(),
					user.getLoginName(),
					DateUtils.formatDate(company.getBeginDate(), DateUtils.DATE_FORMAT),
					DateUtils.formatDate(company.getEndDate(), DateUtils.DATE_FORMAT),
					null);
		}
		// part8	踢掉这两个用户
		try {
			saasLoginService.logoutUser(user.getLoginName());
			saasLoginService.logoutUser(mainUser.getLoginName());
		} catch (Exception e) {
			logger.error("用户踢出失败, " + StringUtils.join(user.getLoginName(), mainUser.getLoginName(), ","), e);
		}
	}
	
	/**
	 * added for v2.2.8 fengsw KNZT-7656
	 * 获取APImock访问的key配置
	 * 
	 * @param extKey-对应
	 * @return
	 */
	public SysCompInfoColExt getApiMockKeyConfig(String extKey) {
		return sysCompInfoColExtService.getByTypeAndKey(Constants.CompInfoColExt.API_MOCK_CONFIG, extKey);
	}

	/**
	 * added for v2.2.8 fengsw KNZT-7656 
	 * 生成api_mock key、secretKey
	 *
	 * @param companyId
	 */
	public void generateApiMockKeyAndSecretKey(String companyId) {
		SysCompInfoColExt sysCompInfoColExts = getMockConfigByCompanyId(companyId);
		if (sysCompInfoColExts == null) {
			String mockKey = IdGenUtil.uuid();
			String mockSecretKey = IdGenUtil.generateSecretKey();
			sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.API_MOCK_CONFIG, mockKey, mockSecretKey);
		}
	}
	
	public SysCompInfoColExt getMockConfigByCompanyId(String companyId) {
		List<SysCompInfoColExt> sysCompInfoColExts = sysCompInfoColExtService.listColExtByType(companyId, Constants.CompInfoColExt.API_MOCK_CONFIG);
		if (CollectionUtils.isNotEmpty(sysCompInfoColExts)){
			return sysCompInfoColExts.get(0);
		}
		return null;
	}

	/**
	 * 获取当前公司的偏好信息
	 * added for v2.3.2 fengsw KNZT-7930
	 * 
	 * @return
	 */
	public List<CompanyPreferenceInfo> listPreferences() {
		String userCompanyId = UserUtils.getUserCompanyId();
		List<CompanyPreferenceInfo> preferences = new ArrayList<>();
		String anonymousOrderConfig = sysCompInfoColExtService.getColExtValueByTypeAndKey(userCompanyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ANONYMOUS_ORDER);
		Boolean anonymousOrder = StringUtils.isBlank(anonymousOrderConfig) || StringUtils.equals(anonymousOrderConfig, Constants.YES);
		preferences.add(new CompanyPreferenceInfo(anonymousOrder, Constants.CompInfoColExt.CommExt.COL_NAME_ANONYMOUS_ORDER));
		String value1 = sysCompInfoColExtService.getColExtValueByTypeAndKey(userCompanyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_NOT_ACTIVATE_USER_NOTIFY);
		preferences.add(new CompanyPreferenceInfo(StringUtils.equals(value1, Constants.YES), Constants.CompInfoColExt.CommExt.COL_NAME_NOT_ACTIVATE_USER_NOTIFY));
		return preferences;
	}

	/**
	 * added for v2.3.2 fengsw KNZT-7930
	 * 配置偏好信息
	 * 
	 * @param preferenceInfoList
	 * @throws MessageException
	 */
	public void configPreferences(List<CompanyPreferenceInfo> preferenceInfoList) throws MessageException {
		if (CollectionUtils.isEmpty(preferenceInfoList)) {
			return;
		}
		MsgExceptionUtils.failBuild(!UserUtils.isCompanyAdmin());
		String userCompanyId = UserUtils.getUserCompanyId();
		for (CompanyPreferenceInfo companyPreferenceInfo : preferenceInfoList) {
			if (companyPreferenceInfo == null || companyPreferenceInfo.getOpen() == null || StringUtils.isBlank(companyPreferenceInfo.getType())) {
				continue;
			}
			switch (companyPreferenceInfo.getType()) {
				case Constants.CompInfoColExt.CommExt.COL_NAME_ANONYMOUS_ORDER:
					sysCompInfoColExtService.saveColExtValue(userCompanyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ANONYMOUS_ORDER, companyPreferenceInfo.getOpen() ? Constants.YES : Constants.NO);
					break;
				case Constants.CompInfoColExt.CommExt.COL_NAME_NOT_ACTIVATE_USER_NOTIFY:
					sysCompInfoColExtService.saveColExtValue(userCompanyId, Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_NOT_ACTIVATE_USER_NOTIFY, companyPreferenceInfo.getOpen() ? Constants.YES : Constants.NO);
					break;
				default:
					logger.warn("configPreferences warn, preference:{}, doesn't exists, value:{}", companyPreferenceInfo.getType(), companyPreferenceInfo.getOpen());
					break;
			}
		}
	}

    public List<Company> listByIdList(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return dao.listByIdList(idList);
    }

	public boolean getConfigAnonymousOrder() {
		String userCompanyId = UserUtils.getUserCompanyId();
		String value = sysCompInfoColExtService.getColExtValueByTypeAndKey(userCompanyId,
				Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_ANONYMOUS_ORDER);
		return StringUtils.equals(value, Constants.YES);
	}

	/**
	 * 保存开票银行信息
	 *
	 * @param companyId
	 * @param bankTO
	 */
	public void saveBankInfo(String companyId, InvoiceBankTO bankTO) {
		if (bankTO == null) {
			return;
		}
		sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.BANK, Constants.CompInfoColExt.Bank.ACCOUNT_NAME, bankTO.getAccountName());
		sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.BANK, Constants.CompInfoColExt.Bank.DEPOSIT_BANK, bankTO.getDepositBank());
		sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.BANK, Constants.CompInfoColExt.Bank.ACCOUNT_NUMBER, bankTO.getAccountNumber());
		sysCompInfoColExtService.saveColExtValue(companyId, Constants.CompInfoColExt.BANK, Constants.CompInfoColExt.Bank.SWIFT, bankTO.getSwift());
	}

	/**
	 * 获取开票银行信息
	 *
	 * @param companyId
	 * @return
	 */
	public InvoiceBankTO getBankInfo(String companyId) {
		Map<String, String> bankInfoMap = sysCompInfoColExtService.getColMapByType(companyId, Constants.CompInfoColExt.BANK);
		if (bankInfoMap.isEmpty()) {
			return null;
		}
		InvoiceBankTO bankTO = new InvoiceBankTO();
		bankTO.setAccountName(bankInfoMap.get(Constants.CompInfoColExt.Bank.ACCOUNT_NAME));
		bankTO.setDepositBank(bankInfoMap.get(Constants.CompInfoColExt.Bank.DEPOSIT_BANK));
		bankTO.setAccountNumber(bankInfoMap.get(Constants.CompInfoColExt.Bank.ACCOUNT_NUMBER));
		bankTO.setSwift(bankInfoMap.get(Constants.CompInfoColExt.Bank.SWIFT));
		return bankTO;
	}

	/**
	 * 统计公司数量
	 * @param condition
	 * @return
	 */
	public Integer countCompany(SysCompanyCondition condition) {
		return dao.countCompany(condition);
	}
}
