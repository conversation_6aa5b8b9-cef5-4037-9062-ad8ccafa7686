package com.backend.common.modules.report.model;

import com.backend.common.modules.person_vrfy.form.EncryptedForm;
import com.backend.common.modules.report.entity.TblCompReportCart;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.sg.SgBasicTO;
import com.backend.common.overseamongo.entity.QccOvsBasicInfo;
import com.backend.common.yunjuapi.model.ApiGlobalCorpDetailTO;
import com.backend.common.yunjuapi.model.HkCorpBasicDetailTO;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.HkStatusEnum;
import com.qcc.frame.commons.ienum.RegistrationStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class OrderSubmitTO {
    // region 新版提交时需要提交的参数
    private String corpKeyNo;
    private String corpName;
    private String corpNameEn;
    private String persKeyNo;
    private String persName;
    private String persNameEn;
    private String reportType;
    private BigDecimal unit;
    private String mapNo; // 图谱编号，仅图谱订单需要
    private String mapNodeNo; // 图谱节点编号，仅图谱订单需要
    private EncryptedForm encryptedVerifyForm; // 核验加密参数，仅核验订单需要
    private String hkDocNumber; // 香港文档编号，仅香港订单需要
    private String cartId; // 购物车来源id，如果是购物车来源，则有该id
    private String sgFinYear; // reportType = SG_FIN 的时候，需要传递
    // endregion


    /**
     * 内部注入参数,需要在逻辑开始前调用 {@link OrderSubmitTO#clearInnerParam()}，避免从入参中赋值
     */
    // region 内部注入参数
    private Boolean useBenefit;
    private String corpNumber;
    private String regNo; // 当前仅马来西亚会落存
    private String corpStatusEn;
    private String corpShortStatusCn;
    private String reportGroup;
    private String corpJurisdiction; // added for lvcy v1.9.9 KNZT-4980
    private String corpDistrict; // added for lvcy v1.9.9 KNZT-4980
    private String corpDateOfReg; // added for lvcy v1.9.9 KNZT-4980
    // endregion



    public static OrderSubmitTO build(TblCompReportCart cart) {
        OrderSubmitTO orderSubmitTO = new OrderSubmitTO();
        orderSubmitTO.setCorpKeyNo(cart.getCorpKeyNo());
        orderSubmitTO.setCorpName(cart.getCorpName());
        orderSubmitTO.setCorpNameEn(cart.getCorpNameEn());
        orderSubmitTO.setCorpNumber(cart.getCorpNumber());
        orderSubmitTO.setCorpStatusEn(cart.getCorpStatus());
        orderSubmitTO.setCorpShortStatusCn(cart.getCorpStatusCheck());
        if (Constants.Report.KEY_NO_TYPE_PERS.equals(cart.getKeyNoType())) {
            orderSubmitTO.setPersKeyNo(cart.getKeyNo());
        }
        orderSubmitTO.setPersName(StringUtils.getNotBlankStr(cart.getPersName(), cart.getPersNameEn()));
        orderSubmitTO.setReportGroup(cart.getReportGroup());
        orderSubmitTO.setReportType(cart.getReportType());
        orderSubmitTO.setCorpJurisdiction(cart.getCorpJurisdiction());
        orderSubmitTO.setCorpDistrict(cart.getCorpDistrict());
        orderSubmitTO.setCorpDateOfReg(cart.getCorpDateOfReg());
        orderSubmitTO.setCartId(cart.getId());
        if (ReportTypeEnum.hkCorpList().contains(cart.getReportType())) {
            orderSubmitTO.setHkDocNumber(cart.getExtraInfo1());
        } else if (ReportTypeEnum.SG_FIN.getCode().equals(cart.getReportType())) {
            orderSubmitTO.setSgFinYear(cart.getExtraInfo1());
        }

        return orderSubmitTO;
    }

    public static OrderSubmitTO build(CartInfoSubmitTO cartInfoSubmitTO) {
        OrderSubmitTO orderSubmitTO = new OrderSubmitTO();
        orderSubmitTO.setCorpKeyNo(cartInfoSubmitTO.getCorpKeyNo());
        orderSubmitTO.setCorpName(cartInfoSubmitTO.getCorpName());
        orderSubmitTO.setCorpNameEn(cartInfoSubmitTO.getCorpNameEn());
        orderSubmitTO.setCorpNumber(cartInfoSubmitTO.getCorpNumber());
        orderSubmitTO.setReportGroup(cartInfoSubmitTO.getReportGroup());
        orderSubmitTO.setReportType(cartInfoSubmitTO.getReportType());
        orderSubmitTO.setPersKeyNo(cartInfoSubmitTO.getPersKeyNo());
        orderSubmitTO.setPersName(cartInfoSubmitTO.getPersName());
        return orderSubmitTO;
    }

    public static List<OrderSubmitTO> build(List<TblCompReportCart> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Lists.newArrayList();
        }
        return cartList.stream().map(OrderSubmitTO::build).collect(Collectors.toList());
    }

    public static List<OrderSubmitTO> build4CartSubmitTO(List<CartInfoSubmitTO> cartInfoSubmitTOList) {
        if (CollectionUtils.isEmpty(cartInfoSubmitTOList)) {
            return Lists.newArrayList();
        }
        return cartInfoSubmitTOList.stream().map(OrderSubmitTO::build).collect(Collectors.toList());
    }

    public OrderSubmitTO correct(ApiGlobalCorpDetailTO source) {
        if (Objects.isNull(source)) {
            return this;    
        }
        this.setCorpNumber(source.getCreditCode());
        this.setCorpShortStatusCn(source.getShortStatus());
        this.setCorpStatusEn(RegistrationStatusEnum.getShortStatusEnByShortStatusCn(source.getShortStatus()));
        this.setCorpDistrict(source.getAddress());
        this.setCorpDateOfReg(source.getStartDate());
        this.setCorpJurisdiction(Constants.JURISDICTION.CHINA_MAINLAND); // updated for v6.7.2 KNZT-84
        this.setCorpName(source.getCorpName());
        return this;
    }

    public OrderSubmitTO correct(HkCorpBasicDetailTO hkDetail) {
        if (Objects.isNull(hkDetail)) {
            return this;

        }
        this.setCorpNumber(hkDetail.getCompanyNumber());
        HkStatusEnum statusEnumByStatus = HkStatusEnum.getStatusEnumByStatus(hkDetail.getStatus());
        if (statusEnumByStatus != null) {
            this.setCorpShortStatusCn(statusEnumByStatus.getStatusCn());
            this.setCorpStatusEn(statusEnumByStatus.getStatusEn());
        } else {
            this.setCorpShortStatusCn(hkDetail.getStatus());
        }
        this.setCorpDistrict(hkDetail.getContactAddress());
        this.setCorpDateOfReg(hkDetail.getRegistrationDate());
        this.setCorpJurisdiction(Constants.JURISDICTION.HK);
        return this;
    }

    public void correct4HkIrd(QccOvsBasicInfoCommTO qccOvsBasicInfo) {
        if (Objects.isNull(qccOvsBasicInfo)) {
            return;
        }
        this.setCorpNumber(StringUtils.joinIgnoreNull(Lists.newArrayList(qccOvsBasicInfo.getCompNo(), qccOvsBasicInfo.getBranchNo()), "-"));
        this.setCorpJurisdiction(Constants.JURISDICTION.HK);
    }

    public OrderSubmitTO correct(SgBasicTO sgBasicTO) {
        if (Objects.isNull(sgBasicTO)) {
            return this;
        }
        this.setCorpNumber(sgBasicTO.getCompNo());
        this.setCorpStatusEn(sgBasicTO.getCompStatusLabel());
        this.setCorpDistrict(sgBasicTO.getContactAddress());
        this.setCorpDateOfReg(sgBasicTO.getCompStartDate());
        this.setCorpJurisdiction(Constants.JURISDICTION.SG); // updated for v6.7.2 KNZT-84
        return this;
    }

    public void correct4My(QccOvsBasicInfo myBasic) {
        if (Objects.isNull(myBasic)) {
            return;
        }

        this.setCorpNumber(myBasic.getCompNo());
        this.setRegNo(myBasic.getRegNo());
        this.setCorpJurisdiction(Constants.JURISDICTION.MY);
    }

    public void correct4Nz(QccOvsBasicInfo myBasic) {
        if (Objects.isNull(myBasic)) {
            return;
        }

        this.setCorpNumber(myBasic.getCompNo());
        this.setRegNo(myBasic.getRegNo());
        this.setCorpJurisdiction(Constants.JURISDICTION.NZ);
    }

    public void correct4Tw(QccOvsBasicInfo twBasic) {
        if (Objects.isNull(twBasic)) {
            return;
        }

        this.setCorpNumber(twBasic.getCompNo());
        this.setRegNo(twBasic.getRegNo());
        this.setCorpJurisdiction(Constants.JURISDICTION.TW);
    }

    public void clearInnerParam() {
        this.setUseBenefit(null);
        this.setCorpNumber(null);
        this.setRegNo(null);
        this.setCorpStatusEn(null);
        this.setCorpShortStatusCn(null);
        this.setCorpJurisdiction(null);
        this.setCorpDistrict(null);
        this.setCorpDateOfReg(null);
    }

    public String getKeyNo() {
        return StringUtils.isNotBlank(persKeyNo) ? persKeyNo : corpKeyNo;
    }
}
