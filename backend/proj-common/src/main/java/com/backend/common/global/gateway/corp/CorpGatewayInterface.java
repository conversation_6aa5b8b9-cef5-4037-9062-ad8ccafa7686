package com.backend.common.global.gateway.corp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.corp.form.ListCorpBasicInfoForm;
import com.backend.common.global.gateway.corp.model.CompAllEnNameTO;
import com.backend.common.global.gateway.corp.model.CorpBasicInfoTO;
import com.backend.common.modules.trans.base.form.TransWrapper;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.model.CourtCaseTO;
import com.backend.common.yunjuapi.model.EquityShareChangesByDateResultTO;
import com.backend.common.yunjuapi.model.merchant.CertificationListTO;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CorpGatewayInterface {
    private static final Logger logger = LoggerFactory.getLogger(CorpGatewayInterface.class);
    private static CommonTransService commonTransService = SpringContextHolder.getBean(CommonTransService.class);

    public static List<CorpBasicInfoTO> listBasicInfo(ListCorpBasicInfoForm form) {
        String respStr = GatewayInvoker.postJson("/api/global/corp/listBasicInfo", JsonUtils.parseObject(JSON.toJSONString(form)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CorpBasicInfoTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpBasicInfoTO>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    public static List<String> listSupportedKycKeyNos(Collection<String> keyNos) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNos", keyNos);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listSupportedKycKeyNos", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<String> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<String>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    // 查询大陆企业所有英文名
    public static List<CompAllEnNameTO> listAllEnName4Local(Collection<String> keyNos) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNos", keyNos);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listAllEnName4Local", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CompAllEnNameTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CompAllEnNameTO>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    public static EquityShareChangesByDateResultTO getEquityShareChangesByDate(String keyNo) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getEquityShareChangesByDate", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResult<EquityShareChangesByDateResultTO> jsonResult = jsonObject.toJavaObject(new TypeReference<JsonResult<EquityShareChangesByDateResultTO>>() {
            });
            if (jsonResult != null && jsonResult.getResult() != null) {
                return jsonResult.getResult();
            }
        }
        return null;
    }

    public static JsonResultList<CertificationListTO> getCertificationList(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getCertificationList", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(new TypeReference<JsonResultList<CertificationListTO>>() {
            });
        }
        return null;
    }

    public static JsonResultList<CourtCaseTO> getCourtCaseList(CourtCaseForm form) {
        String respStr = GatewayInvoker.postJson("/api/global/corp/getCourtCaseList", JsonUtils.parseObject(JSON.toJSONString(form)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(new TypeReference<JsonResultList<CourtCaseTO>>() {
            });
        }
        return null;
    }

    public static Object getFinancialCashFlowInfo(String keyNo) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getFinancialCashFlowInfo", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResult<?> jsonResult = jsonObject.toJavaObject(new TypeReference<JsonResult<?>>() {
            });
            if (jsonResult != null) {
                return jsonResult.getResult();
            }
        }
        return null;
    }
}
