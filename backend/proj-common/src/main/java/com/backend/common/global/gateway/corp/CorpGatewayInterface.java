package com.backend.common.global.gateway.corp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.corp.form.ListCorpBasicInfoForm;
import com.backend.common.global.gateway.corp.model.CompAllEnNameTO;
import com.backend.common.global.gateway.corp.model.CorpBasicInfoTO;
import com.backend.common.global.gateway.corp.model.EquityFreezeListTO;
import com.backend.common.global.gateway.corp.model.PledgeOfEquityListTO;
import com.backend.common.global.gateway.corp.model.UboListTO;
import com.backend.common.modules.report.model.HiddenConnectionSearchTO;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.model.CorpPledgeV2TO;
import com.backend.common.yunjuapi.model.CourtCaseTO;
import com.backend.common.yunjuapi.model.CoutShiXin;
import com.backend.common.yunjuapi.model.CoutZhiXing;
import com.backend.common.yunjuapi.model.EquityShareChangesByDateResultTO;
import com.backend.common.yunjuapi.model.LimitExit;
import com.backend.common.yunjuapi.model.PersonSumptuary;
import com.backend.common.yunjuapi.model.merchant.CertificationListTO;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CorpGatewayInterface {
    private static final Logger logger = LoggerFactory.getLogger(CorpGatewayInterface.class);
    private static CommonTransService commonTransService = SpringContextHolder.getBean(CommonTransService.class);

    public static List<CorpBasicInfoTO> listBasicInfo(ListCorpBasicInfoForm form) {
        String respStr = GatewayInvoker.postJson("/api/global/corp/listBasicInfo", JsonUtils.parseObject(JSON.toJSONString(form)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CorpBasicInfoTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpBasicInfoTO>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    public static List<String> listSupportedKycKeyNos(Collection<String> keyNos) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNos", keyNos);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listSupportedKycKeyNos", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<String> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<String>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    // 查询大陆企业所有英文名
    public static List<CompAllEnNameTO> listAllEnName4Local(Collection<String> keyNos) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNos", keyNos);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listAllEnName4Local", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CompAllEnNameTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CompAllEnNameTO>>() {
            });
            if (jsonResultList != null && jsonResultList.getResultList() != null) {
                return jsonResultList.getResultList();
            }
        }
        return new ArrayList<>();
    }

    public static EquityShareChangesByDateResultTO getEquityShareChangesByDate(String keyNo) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getEquityShareChangesByDate", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResult<EquityShareChangesByDateResultTO> jsonResult = jsonObject.toJavaObject(new TypeReference<JsonResult<EquityShareChangesByDateResultTO>>() {
            });
            if (jsonResult != null && jsonResult.getResult() != null) {
                return jsonResult.getResult();
            }
        }
        return null;
    }

    public static JsonResultList<CertificationListTO> getCertificationList(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getCertificationList", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(new TypeReference<JsonResultList<CertificationListTO>>() {
            });
        }
        return null;
    }

    public static JsonResultList<CourtCaseTO> getCourtCaseList(CourtCaseForm form) {
        String respStr = GatewayInvoker.postJson("/api/global/corp/getCourtCaseList", JsonUtils.parseObject(JSON.toJSONString(form)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(new TypeReference<JsonResultList<CourtCaseTO>>() {
            });
        }
        return null;
    }

    public static Object getFinancialCashFlowInfo(String keyNo) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        String respStr = GatewayInvoker.postJson("/api/global/corp/getFinancialCashFlowInfo", JsonUtils.parseObject(JSON.toJSONString(bodyMap)), null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResult<?> jsonResult = jsonObject.toJavaObject(new TypeReference<JsonResult<?>>() {
            });
            if (jsonResult != null) {
                return jsonResult.getResult();
            }
        }
        return null;
    }

    public static JsonResultList<HiddenConnectionSearchTO> searchHiddenConnection(String searchKey, String searchIndex, Integer pageIndex, Integer pageSize) throws MessageException {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("searchKey", searchKey);
        bodyMap.put("searchIndex", searchIndex);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/searchHiddenConnection", bodyMap, null);
        MsgExceptionUtils.checkIsNull(respStr);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        MsgExceptionUtils.checkIsNull(jsonObject);
        return jsonObject.toJavaObject(new TypeReference<JsonResultList<HiddenConnectionSearchTO>>() {
        });
    }

    public static PageDataBO<CoutZhiXing> listZhixing(String keyNo, String isValid, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("isValid", isValid);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listZhixing", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CoutZhiXing> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CoutZhiXing>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<CoutShiXin> listShixin(String keyNo, String isValid, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("isValid", isValid);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listShixin", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CoutShiXin> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CoutShiXin>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<PersonSumptuary> listSumptuary(String keyNo, String isValid, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("isValid", isValid);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listSumptuary", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<PersonSumptuary> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<PersonSumptuary>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<LimitExit> listLimitExit(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listLimitExit", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<LimitExit> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<LimitExit>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<UboListTO> listUbo(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listUbo", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<UboListTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<UboListTO>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<EquityFreezeListTO> listEquityFreeze(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listEquityFreeze", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<EquityFreezeListTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<EquityFreezeListTO>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<CorpPledgeV2TO> listEquityPledge(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listEquityPledge", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<CorpPledgeV2TO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<CorpPledgeV2TO>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }

    public static PageDataBO<PledgeOfEquityListTO> listPledgeOfEquity(String keyNo, String pageIndex, String pageSize) {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("keyNo", keyNo);
        bodyMap.put("pageIndex", pageIndex);
        bodyMap.put("pageSize", pageSize);
        String respStr = GatewayInvoker.postJson("/api/global/corp/listPledgeOfEquity", bodyMap, null);
        JSONObject jsonObject = JsonUtils.parseObject(respStr);
        if (jsonObject != null) {
            JsonResultList<PledgeOfEquityListTO> jsonResultList = jsonObject.toJavaObject(new TypeReference<JsonResultList<PledgeOfEquityListTO>>() {
            });
            if (jsonResultList != null) {
                return PageDataBO.build(jsonResultList);
            }
        }
        return new PageDataBO<>();
    }
}
