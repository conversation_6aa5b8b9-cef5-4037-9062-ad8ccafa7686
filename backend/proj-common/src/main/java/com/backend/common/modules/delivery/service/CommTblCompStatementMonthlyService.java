package com.backend.common.modules.delivery.service;

import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.model.StatementMonthlyListTO;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.service.DictService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.backend.common.modules.delivery.condition.TblCompStatementMonthlyCondition;
import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.backend.common.modules.delivery.mapper.TblCompStatementMonthlyDao;
import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO;

import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;

import java.net.URLDecoder;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class CommTblCompStatementMonthlyService extends CrudService<TblCompStatementMonthlyDao, TblCompStatementMonthly> {
    @Autowired
    private CommTblContractDeliveryService deliveryService;
    @Autowired
    private DictService dictService;
    private static final Date SELF_PAY_CUTOFF_DATE = DateUtils.parseDate("2025-09-15 00:00:00", DateUtils.DATETIME_FORMAT);// GST 正式生效时间，此时间之前都是历史数据
    /**
     * 生成唯一的发票号
     *
     * @param companyId      公司id
     * @param contractNo
     * @param yearMonth      年月
     * @return 发票号
     * @throws MessageException 异常
     */
    public String generateInvoiceNo(String companyId, String contractNo, YearMonth yearMonth) throws MessageException {
        String baseInvoiceNo = generateBaseInvoiceNo(companyId, contractNo, yearMonth, 0);
        // 检查是否已存在，最多尝试26次（A-Z）
        for (int i = 0; i < 26; i++) {
            TblCompStatementMonthly exist = dao.getByInvoiceNo(baseInvoiceNo);
            if (exist == null) {
                return baseInvoiceNo;
            }
            if (StringUtils.equals(companyId, exist.getCompanyId()) && StringUtils.equals(contractNo, exist.getMainContractNo())) {
                return baseInvoiceNo;
            }
            // 已存在且属于其他公司，生成新的号码
            baseInvoiceNo = generateBaseInvoiceNo(companyId, contractNo, yearMonth, i + 1);
        }
        throw new MessageException("无法生成唯一的发票号");
    }

    /**
     * 生成基础发票号
     *
     * @param companyId  公司id
     * @param contractNo
     * @param yearMonth  年月
     * @param extraSeed  额外种子
     * @return 基础发票号
     */
    private static String generateBaseInvoiceNo(String companyId, String contractNo, YearMonth yearMonth, int extraSeed) {
        // 使用companyId和年月的组合计算hash值
        int hash = (companyId + contractNo + yearMonth.toString() + extraSeed).hashCode();
        StringBuilder randomStr = new StringBuilder();
        // 确保为正数
        hash = Math.abs(hash);
        // 从hash值中提取3个0-25的数字，转换为A-Z
        for (int i = 0; i < 3; i++) {
            randomStr.append((char) ('A' + (hash % 26)));
            hash = hash / 26;
        }
        return "INV" + companyId.substring(0, 6).toUpperCase() + randomStr + DateUtils.formatYearMonthInt(yearMonth);
    }

    /**
     * 更新保存月度报表
     * added for v1.9.5 KNZT-4117
     *
     * @param statement
     * @return
     */
    public void saveOrUpdateStatementByContractMonth(TblCompStatementMonthly statement) {
        TblCompStatementMonthly existStatement = getByCompanyAndContractAndYearMonth(
                statement.getCompanyId(), statement.getMainContractNo(), statement.getStatementMonth());
        saveOrUpdate(statement, existStatement);
    }


    public void saveOrUpdateStatementByInvoiceNo(TblCompStatementMonthly statement) {
        TblCompStatementMonthly existStatement = getByInvoiceNo(statement.getInvoiceNo());
        saveOrUpdate(statement, existStatement);
    }

    private void saveOrUpdate(TblCompStatementMonthly statement, TblCompStatementMonthly existStatement) {
        if (Objects.isNull(existStatement)) {
            save(statement);
        } else {
            statement.setId(existStatement.getId());
            save(statement);
            // 删除不可用的obs文件
            if (StringUtils.isNotEmpty(existStatement.getUrl())
                    && !StringUtils.equals(existStatement.getUrl(), statement.getUrl())) {
                deleteDiscardObsFile(existStatement.getUrl());
            }
        }
    }


    private void deleteDiscardObsFile(String url) {
        try {
            String decodedUrl = URLDecoder.decode(url, "UTF-8");
            int startIndex = decodedUrl.indexOf("statement");
            String obsObjectName = decodedUrl.substring(startIndex);
            HuaweiObsServUtils.getInstance().deleteObject(obsObjectName);
            logger.info("delete obs file success, url:{}", url);
        } catch (Exception e) {
            logger.error("delete obs file error", e);
        }
    }


    /**
     * 根据公司id查指定月度报告
     * added for v1.9.5 KNZT-4117
     *
     * @param companyId
     * @param mainContractNo
     * @param statementMonth
     * @return TblCompStatementMonthly
     */
    public TblCompStatementMonthly getByCompanyAndContractAndYearMonth(String companyId, String mainContractNo, Integer statementMonth) {
        return dao.getByCompanyAndYearMonth(companyId, mainContractNo, statementMonth);
    }

    /**
     * 分页查询
     * added for v1.9.5 KNZT-4117
     *
     * @param condition
     * @return Page<StatementMonthlyListTO>
     */
    public Page<StatementMonthlyListTO> listByCondition(TblCompStatementMonthlyCondition condition) {
        condition.setCompanyId(UserUtils.getUserCompanyId());
        condition.getPage().setOrderBy("a.statement_month desc, a.create_date desc");
        if (CompTypeEnum.isSelfPay(UserUtils.getUserCommInfo().getType())) {// 自助账号需要单独处理时间查询范围
            handleQueryConditionForSelfPay(condition);
        }
        Page<TblCompStatementMonthly> page = this.findListByCondtion(condition);
        String amountStd = null;
        List<TblContractDelivery> deliveryList = deliveryService.getByCompanyId(UserUtils.getUserCompanyId());
        deliveryList.sort(Comparator.comparing(TblContractDelivery::getCreateDate).reversed());
        if (CollectionUtils.isNotEmpty(deliveryList)) {
            amountStd = deliveryList.get(deliveryList.size() - 1).getAmountStd();
        } else {
            amountStd = "USD";
        }

        Page<StatementMonthlyListTO> statementMonthlyListTOPage = new Page<>();
        statementMonthlyListTOPage.setList(StatementMonthlyListTO.build(page.getList(), amountStd));
        statementMonthlyListTOPage.setCount(page.getCount());
        statementMonthlyListTOPage.setPageNo(page.getPageNo());
        statementMonthlyListTOPage.setPageSize(page.getPageSize());


        return statementMonthlyListTOPage;
    }

    /**
     * 根据用户类型和查询条件设置自付费用户的时间范围限制
     *
     * @param condition 查询条件对象，包含创建时间的起止范围
     * 该方法主要处理自付费用户的数据查询时间范围限制逻辑：
     *  1.对于历史数据查询，将结束时间限制在SELF_PAY_CUTOFF_DATE之前
     *  2.对于当前数据查询，确保查询时间范围不早于SELF_PAY_CUTOFF_DATE
     *  3.处理开始时间和结束时间的各种组合情况，确保查询逻辑正确
     */
    private void handleQueryConditionForSelfPay(TblCompStatementMonthlyCondition condition) {
        String dateStr = dictService.getDictValueByTypeLabel("gst_comm", "self_pay_invoice_cutoff_date", "2025-09-11 18:00:00");
        Date SELF_PAY_CUTOFF_DATE = DateUtils.parseDate(dateStr, DateUtils.DATETIME_FORMAT);
        if (Constants.YES.equals(condition.getLegacy())) {
            condition.setCreateDateEnd(SELF_PAY_CUTOFF_DATE);
        } else {
            // 查询当前数据，时间范围不能早于 SELF_PAY_CUTOFF_DATE
            Date begin = condition.getCreateDateBegin();
            Date end = condition.getCreateDateEnd();
            // 如果开始时间早于截止时间，则设置为截止时间
            if (begin != null && begin.before(SELF_PAY_CUTOFF_DATE)) {
                condition.setCreateDateBegin(SELF_PAY_CUTOFF_DATE);
            }
            // 如果结束时间早于截止时间，则设置为截止时间
            if (end != null && end.before(SELF_PAY_CUTOFF_DATE)) {
                condition.setCreateDateEnd(SELF_PAY_CUTOFF_DATE);
            }
            // 如果只设置了结束时间而没有开始时间，则设置开始时间为截止时间
            if (end != null && condition.getCreateDateBegin() == null) {
                condition.setCreateDateBegin(SELF_PAY_CUTOFF_DATE);
            }
            // 如果开始和结束时间都为空，则设置开始时间为截止时间
            if (condition.getCreateDateBegin() == null && condition.getCreateDateEnd() == null) {
                condition.setCreateDateBegin(SELF_PAY_CUTOFF_DATE);
            }
        }
    }

    /**
     * 月度账单管理分页查询
     * @param condition 查询条件
     * @return 分页结果
     */
    public List<StatementMonthly4ManagementTO> page4Management(StatementMonthly4ManagementCondition condition) {
        return dao.page4Management(condition);
    }


    /**
     * 根据发票号查询
     * @param invoiceNo 发票号
     * @return 月度报表
     */
    public TblCompStatementMonthly getByInvoiceNo(String invoiceNo) {
        return dao.getByInvoiceNo(invoiceNo);
    }
    public List<TblCompStatementMonthly> listByIds(List<String> statementMonthlyIdList) {
        return dao.listByIds(statementMonthlyIdList);
    }

    /**
     * 查询未回款的账单据
     * 
     * @return
     */
    public List<TblCompStatementMonthly> getUnfinishedPayments() {
        return dao.getUnfinishedPayments();
    }
}
