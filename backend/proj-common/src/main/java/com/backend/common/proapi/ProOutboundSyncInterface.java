package com.backend.common.proapi;

import com.alibaba.fastjson.JSONObject;
import com.backend.common.entity.TblGlobalOutboundCorpPersSync;
import com.backend.common.proapi.model.BatchSaveOutboundCorpPersTO;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.config.Global;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.HttpUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProOutboundSyncInterface {
    protected static Logger logger = LoggerFactory.getLogger(ProOutboundSyncInterface.class);

    public static String saveOutBoundInfo(String keyNoType, String keyNo, String name, String nameEn) throws MessageException {
        String url = Global.getConfig("qcc.pro.interface.domain") + "/global/outbound/saveOutBoundInfo";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("keyNoType",keyNoType);
        paramMap.put("keyNo",keyNo);
        paramMap.put("name",name);
        paramMap.put("nameEn",nameEn);
        String resp = null;
        try {
            resp = HttpUtils.getProApi(url, paramMap, HttpUtils.HTTP_CLIENT_TIMEOUT_20000);
            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj != null) {
                if (!Constants.Result.SUCCESS_STR.equals(jsonObj.getString("status"))) {
                    throw new MessageException("err.access");
                } else {
                    logger.info("saveOutBoundInfo to qcc req:" + paramMap.toString() + " resp:" + resp);
                    return resp;
                }
            } else {
                throw new MessageException("err.access");
            }
        } catch (MessageException e) {
            logger.error("saveOutBoundInfo to qcc req:" + paramMap.toString() + " resp:" + resp);
            throw e;
        } catch (Exception e) {
            logger.error("saveOutBoundInfo to qcc req:" + paramMap.toString() + " resp:" + resp);
            throw new MessageException("err.access");
        }
    }

    /**
     * added for v1.6.5 KNZT-2804 保存企业关联人员的额度数据
     * updated for v1.6.5 KNZT-2810 方法重载，入参加入needCompanyKey 为false则代表使用默认key, true代表使用主账号绑定的key
     *
     * @param keyNo
     * @return
     */
    public static void saveOutBoundCorpPersInfo(String keyNo, String keyNoType, List<TblGlobalOutboundCorpPersSync> corpPersSyncList) throws MessageException {
        saveOutBoundCorpPersInfo(keyNo, keyNoType, corpPersSyncList, true);
    }

    /**
     * added for v1.6.5 KNZT-2804 保存企业关联人员的额度数据
     *
     * @param keyNo
     * @return
     */
    public static void saveOutBoundCorpPersInfo(String keyNo, String keyNoType, List<TblGlobalOutboundCorpPersSync> corpPersSyncList, boolean needCompanyKey) throws MessageException {
        if (CollectionUtils.isEmpty(corpPersSyncList)) {
            return;
        }
        String url = Global.getConfig("qcc.pro.interface.domain") + "/global/outbound/saveOutBoundCorpPersInfo";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("keyNo", keyNo);
        paramMap.put("keyNoType", keyNoType);
        paramMap.put("corpPersList", BatchSaveOutboundCorpPersTO.build(corpPersSyncList));

        String resp = null;
        try {
            if(needCompanyKey) {
                resp = HttpUtils.postJsonProApi(url, paramMap);
            }else {
                resp = HttpUtils.postJsonProApiNoUser(url, paramMap);
            }
            logger.info("saveOutBoundCorpPersInfo to qcc req:" + paramMap.toString() + " resp:" + resp);

            JSONObject jsonObj = JsonUtils.parseObject(resp);
            if (jsonObj == null || !Constants.Result.SUCCESS_STR.equals(jsonObj.getString("status"))) {
                throw new MessageException("err.access");
            }

        } catch (MessageException e) {
            logger.error("saveOutBoundCorpPersInfo to qcc req:" + paramMap.toString() + " resp:" + resp, e);
            throw e;
        } catch (Exception e) {
            logger.error("saveOutBoundCorpPersInfo to qcc req:" + paramMap.toString() + " resp:" + resp, e);
            throw new MessageException("err.access");
        }

    }

}
