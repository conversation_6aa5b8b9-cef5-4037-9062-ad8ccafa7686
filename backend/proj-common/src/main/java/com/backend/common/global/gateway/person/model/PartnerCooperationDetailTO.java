package com.backend.common.global.gateway.person.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PartnerCooperationDetailTO {
    private String Name;
    private String NameEn;
    private String CompanykeyNo;
    private String RegNo;

    private RoleJson RoleJson;
    private RoleJson TargetRoleJson;

    @Getter
    @Setter
    public static final class RoleJson {
        private String KeyNo;
        private String Name;
        private String NameEn;
        private List<Role> Role;
    }

    @Getter
    @Setter
    public static class Role {
        private String Type;
        private String TypeDesc;
        private String Value;
        private String ValueEn;
    }
}