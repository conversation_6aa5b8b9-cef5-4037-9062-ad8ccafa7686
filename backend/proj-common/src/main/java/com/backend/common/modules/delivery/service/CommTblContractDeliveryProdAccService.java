package com.backend.common.modules.delivery.service;

import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.modules.delivery.condition.TblContractDeliveryProdAccCondition;
import com.backend.common.modules.delivery.form.CompChargeRecordForm;
import com.backend.common.modules.delivery.form.CompCreditChargeHistoryForm;
import com.backend.common.modules.delivery.form.CompCreditSummaryForm;
import com.backend.common.modules.delivery.model.CreditsSummaryTO;
import com.backend.common.modules.delivery.model.SortedFiled;
import com.backend.common.modules.delivery.model.TopUpHistoryTO;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.utils.BeanUtil;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.mapper.TblContractDeliveryProdAccDao;
import com.backend.common.modules.delivery.model.CompChargeRecordTO;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.utils.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * added for v1.8.8 KNZT-3324
 * <AUTHOR>
 * @datetime 2024/6/26 10:18
 */
@Service
@Slf4j
public class CommTblContractDeliveryProdAccService extends CrudService<TblContractDeliveryProdAccDao, TblContractDeliveryProdAcc> {


    /**
     * 锁定companyId、functionTableId的有效产品账户
     * 1.当前时间在合同交付有效期内
     * 2.产品对应
     *
     * @param companyId
     * @return List<TblContractDeliveryProdAcc>
     */
    public List<TblContractDeliveryProdAcc> lockValidAccByCompanyId(String companyId) {
        Date curDate = DateUtils.getCurDate();
        logger.info("lockValidAccByCompanyId, companyId:{}", companyId);
        return dao.lockByCompanyId(companyId, curDate);
    }


    /**
     * 查询companyId、functionTableId的有效产品账户
     * 1.当前时间在合同交付有效期内
     * 2.产品对应
     *
     * @param companyId
     * @return List<TblContractDeliveryProdAcc>
     */
    public List<TblContractDeliveryProdAcc> getValidAccByCompanyId(String companyId) {
        LocalDate now = LocalDate.now();
        List<TblContractDeliveryProdAcc> accList = dao.getByCompanyId(companyId);
        return accList.stream().filter(acc -> acc.ifInEffectRange(now)).collect(Collectors.toList());
    }

    public List<TblContractDeliveryProdAcc> getValidAccByCompanyIdAndDate(String companyId, LocalDate date) {
        List<TblContractDeliveryProdAcc> accList = dao.getByCompanyId(companyId);
        return accList.stream().filter(acc -> acc.ifInEffectRange(date)).collect(Collectors.toList());
    }


    /**
     * 锁定id对应的产品账户
     *
     * @param id
     * @return TblContractDeliveryProdAcc
     */
    public TblContractDeliveryProdAcc lockById(String id) {
        return dao.lockById(id);
    }


    public int updateConsumedUnit(String accId, BigDecimal afterConsumedUnit, BigDecimal beforeConsumedUnit) {
        return dao.updateConsumedUnit(accId, afterConsumedUnit, beforeConsumedUnit);
    }

    public List<TblContractDeliveryProdAcc> getByContractDeliveryId(String companyId, String contractDeliveryId) {
        return dao.getByContractDeliveryId(companyId, contractDeliveryId);
    }

    public List<TblContractDeliveryProdAcc> getByCompanyId(String companyId) {
        return dao.getByCompanyId(companyId);
    }

    public List<TblContractDeliveryProdAcc> pageByCreateDate(int size, Date lastMinDate) {
        return dao.pageByCreateDate(size, lastMinDate);
    }

    /**
     * 计算签约用户当前有效的额度信息
     * added for v1.9.8 KNZT-4538
     *
     * @param companyId
     * @return SysCompInfoFuncCount
     */
    public FunctionCountInfoTO calcCompFuncCount(String companyId) {
        List<TblContractDeliveryProdAcc> prodAccList = this.getValidAccByCompanyId(companyId);
        BigDecimal totalUnit = prodAccList.stream()
                .map(TblContractDeliveryProdAcc::getTotalUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal consumedUnit = prodAccList.stream()
                .map(TblContractDeliveryProdAcc::getTotalConsumedUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return FunctionCountInfoTO.build(totalUnit, consumedUnit, totalUnit.subtract(consumedUnit));
    }

    // added for v1.9.8 KNZT-4538
    public Page<CompChargeRecordTO> getChargeRecords(CompChargeRecordForm form) {
        Page<CompChargeRecordTO> rtnPage = new Page<>();
        TblContractDeliveryProdAccCondition condition = new TblContractDeliveryProdAccCondition();
        BeanUtil.copyPropertiesSpring(condition, form);
        condition.setCompanyId(UserUtils.getUserCompanyId());
        List<TblContractDeliveryProdAcc> prodAccs = dao.pageByCondition(condition);
        BeanUtil.copyPropertiesSpring(rtnPage, form.getPage());
        if (CollectionUtils.isNotEmpty(prodAccs)) {
            rtnPage.setList(prodAccs.stream().map(CompChargeRecordTO::buildFromProdAcc).collect(Collectors.toList()));
        }
        return rtnPage;
    }

    /**
     * 是否存在在指定日期有变化的账户
     * added for v1.9.8 KNZT-4538
     *
     * @param companyId
     * @param date
     * @return boolean
     */
    public boolean existChangeProdAccInDateNoContract(String companyId, LocalDate date) {
        LocalDate yesterday = date.minusDays(1);
        Integer res = dao.existChangeProdAccInDateNoContract(companyId, DateUtils.toDate(date), DateUtils.toDate(yesterday));
        return Objects.nonNull(res);
    }

    /**
     * 查询系统账号的余额详情
     * 
     * @param form
     * @return
     */
    public Page<CreditsSummaryTO> pageCreditsBreakdownCondition(CompCreditSummaryForm form) {
        form.getCondition().setCompanyId(UserUtils.getUserCompanyId());
        if (CollectionUtils.isEmpty(form.getCondition().getSortedFiledList())) {
            form.getCondition().setSortedFiledList(Lists.newArrayList(new SortedFiled("expiryDate", "asc")));
        }
        List<CreditsSummaryTO> creditsSummaryTOS = dao.pageCreditsBreakdownCondition(form.getCondition());
        if (CollectionUtils.isNotEmpty(creditsSummaryTOS)) {
            creditsSummaryTOS.forEach(k -> k.setStatus(k.getDaysUntilExpiry() > 30 ? "Available" : "Expiring soon"));
        }
        form.getCondition().getPage().setList(creditsSummaryTOS);
        return form.getCondition().getPage();
    }

    /**
     * 查询companyId下的充值记录数据
     * 
     * @return List<TopUpHistoryTO>
     */
    public Page<TopUpHistoryTO> pageChargeRecords(CompCreditChargeHistoryForm form) {
        form.getCondition().setCompanyId(UserUtils.getUserCompanyId());
        if (CollectionUtils.isEmpty(form.getCondition().getSortedFiledList())) {
            form.getCondition().setSortedFiledList(Lists.newArrayList(new SortedFiled("chargeDate", "desc")));
        }
        List<TopUpHistoryTO> creditsSummaryTOS = dao.pageChargeRecords(form.getCondition());
        if (CollectionUtils.isNotEmpty(creditsSummaryTOS)) {
            creditsSummaryTOS.forEach(k -> {
                if (TransactionTypeEnum.PREPAID.getCode().equals(k.getPaymentMethod())) {
                    k.setPaymentMethod("Contract"); //预付费充值特定改成 Contract
                } else {
                    k.setPaymentMethod(TransactionTypeEnum.getDescEn(k.getPaymentMethod()));
                }
            });
        }
        form.getCondition().getPage().setList(creditsSummaryTOS);
        return form.getCondition().getPage();
    }

    /**
     * 30天内即将过企的账户额度
     * 
     * @param userCompanyId
     * @return
     */
    public BigDecimal countExpireSoonAccountCredits(String userCompanyId) {
        return dao.countExpireSoonAccountCredits(userCompanyId, 30);
    }


    /**
     * 查询指定日期过期的未消耗完的额度账户ID列表
     * 
     * @param expiredDate 过期日期
     * @return 过期但未消耗完的额度账户ID列表
     */
    public List<String> findUnconsumedExpiredAccountIds(LocalDate expiredDate) {
        Date targetDate = DateUtils.toDate(expiredDate);
        return dao.findUnconsumedExpiredAccountIds(targetDate);
    }
    
    /**
     * 批量获取账户详情
     * 
     * @param accountIds 账户ID列表
     * @return 账户详情列表
     */
    public List<TblContractDeliveryProdAcc> batchGetByIds(List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }
        
        List<TblContractDeliveryProdAcc> resultList = new ArrayList<>();
        for (String id : accountIds) {
            TblContractDeliveryProdAcc account = this.get(id);
            if (account != null) {
                resultList.add(account);
            }
        }
        return resultList;
    }
}
