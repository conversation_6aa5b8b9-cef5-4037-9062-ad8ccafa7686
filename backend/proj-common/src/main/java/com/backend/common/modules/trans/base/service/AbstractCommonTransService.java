package com.backend.common.modules.trans.base.service;

import com.backend.common.modules.trans.base.form.BatchTranslateForm;
import com.backend.common.modules.trans.base.form.CompAllEnNameDTO;
import com.backend.common.modules.trans.base.form.TransWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public abstract class AbstractCommonTransService {

    protected static final Pattern PATTERN_ENGLISH = Pattern.compile("[A-Za-z]+");
    protected static final Pattern PATTERN_CN = Pattern.compile("\\p{IsHan}");
    protected static final Pattern PATTERN_PUNCTUATION = Pattern.compile(".*[\\p{Punct}\\p{IsPunctuation}].*"); // 中英文符号

    protected static final String TAIWAN_CORPORATE_PREFIX = "t";
    protected static final String HONGKONG_CORPORATE_PREFIX = "h";
    protected static final String FOREIGN_CORPORATE_PREFIX = "z";
    protected static final String US_STOCKS = "l";
    protected static final String PERSON_PREFIX = "p";

    /**
     * 英文后置处理
     * 1、非实体, 直接机翻
     * 2、实体, 人员类型, 允许被拼音翻译的情况，走拼音翻译
     * 3、实体, (企业类型 or 空实体类型) and (keyNo为空 or keyNo为大陆企业), 机翻
     * 4、其他后置处理
     */
    public void enPostProcessor(TransWrapper transWrapper) {
        if (transWrapper == null || transWrapper.isEmpty()) {
            return;
        }

        // 初始化机器翻译标记
        transWrapper.entryList().forEach(entry -> entry.setMtFlag(false));

        // 一些特殊场景直接填充英文
        fillEnTextByDirect(transWrapper);
        // 从数据库查询填充英文(实体, 且不为人员, 且keyNo不为空)
        fillEnTextByDb(transWrapper);
        // 用拼音/机翻填充英文
        fillEnTextByMachine(transWrapper);

        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            String localText = entry.getLocalText();
            String enText = entry.getEnText();
            // 本地语言文本包含英文文本
            boolean ifLocalTextContainsEnText = ifLocalTextContainsEnText(localText, enText);
            if (ifLocalTextContainsEnText) {
                entry.setMtFlag(false); // 本地语言和英语相同, 机翻标记设为false
                if (entry.getIfMerge()) {
                    entry.setEnText(null);
                }
            }

            // 企业实体 英文改为纯大写
            if (entry.ifCorp()) {
                entry.setEnText(upperCaseExceptEmTags(entry.getEnText()));
                if (ifLocalTextContainsEnText) {
                    entry.setLocalText(upperCaseExceptEmTags(entry.getLocalText()));
                }
            }
        }

        // 将结果写回对象
        transWrapper.entryList().forEach(TransWrapper.Entry::flush);
    }

    // 一些特殊场景直接填充英文
    private void fillEnTextByDirect(TransWrapper transWrapper) {
        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            String sourceArea = entry.getSourceArea();
            String localText = entry.getLocalText();
            if (StringUtils.isAnyBlank(sourceArea, localText)) {
                continue;
            }
            switch (sourceArea) {
                case "SG":
                case "NZ":
                case "MY":
                    entry.setEnText(entry.getLocalText());
                    break;
                case "CN":
                case "TW":
                case "HK":
                    // 本地语言文本不包含中文, 则认为是英文
                    if (!isContainChinese(localText)) {
                        entry.setEnText(entry.getLocalText());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    // 从底层数据库查询填充英文
    private void fillEnTextByDb(TransWrapper transWrapper) {
        List<TransWrapper.Entry<?>> entriesNeedFillFromDb = new ArrayList<>();
        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            String enText = entry.getEnText();
            if (StringUtils.isNotBlank(enText)) {
                continue; // 英文名不为空, 跳过
            }
            String keyNo = entry.getKeyNo();
            // 实体, 且不为人员, 且keyNo不为空
            if (entry.getIfEntity() && !entry.ifPerson() && StringUtils.isNotBlank(keyNo)) {
                entriesNeedFillFromDb.add(entry);
            }
        }
        String overseaKeyNoRegex = "^(" + FOREIGN_CORPORATE_PREFIX + "|" + HONGKONG_CORPORATE_PREFIX + "|" + TAIWAN_CORPORATE_PREFIX + ").*";
        String usStocksKeyNoRegex = "^" + US_STOCKS + ".*";
        List<TransWrapper.Entry<?>> overseaEntries = entriesNeedFillFromDb.stream()
                .filter(entry -> entry.getKeyNo().matches(overseaKeyNoRegex)).collect(Collectors.toList());
        List<TransWrapper.Entry<?>> mainlandEntries = entriesNeedFillFromDb.stream()
                .filter(entry -> !entry.getKeyNo().matches(overseaKeyNoRegex) && !entry.getKeyNo().matches(usStocksKeyNoRegex))
                .collect(Collectors.toList());

        // 大陆企业英文名处理(已支持被屏蔽的英文名显示、历史中文名称对应历史英文名称)
        if (CollectionUtils.isNotEmpty(mainlandEntries)) {
            Set<String> keyNos = mainlandEntries.stream().map(TransWrapper.Entry::getKeyNo).filter(Objects::nonNull).collect(Collectors.toSet());
            List<CompAllEnNameDTO> compAllEnNameDTOS = listAllEnName4Local(keyNos);
            Map<String, List<CompAllEnNameDTO.Detail>> keyNo2NameDetailsMap = compAllEnNameDTOS.stream().filter(item -> Objects.nonNull(item.getKeyNo()) && Objects.nonNull(item.getDetails()))
                    .collect(Collectors.toMap(CompAllEnNameDTO::getKeyNo, CompAllEnNameDTO::getDetails, (k1, k2) -> k1));
            for (TransWrapper.Entry<?> entry : entriesNeedFillFromDb) {
                List<CompAllEnNameDTO.Detail> details = keyNo2NameDetailsMap.get(entry.getKeyNo());
                if (CollectionUtils.isNotEmpty(details)) {
                    entry.setEnText(details.stream()
                            .filter(detail -> StringUtils.equals(removeEmTags(entry.getLocalText()), detail.getName())) // 兼容搜索时本地语言包含<em>
                            .findFirst().map(CompAllEnNameDTO.Detail::getEnName).orElse(null));
                }
            }
        }

        // 海外企业英文名处理
        if (CollectionUtils.isNotEmpty(overseaEntries)) {
            Set<String> keyNos = overseaEntries.stream().map(TransWrapper.Entry::getKeyNo).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(keyNos)) {
                Map<String, String> keyNo2EnNameMap = getKeyNo2EnNameMap4Oversea(keyNos);
                for (TransWrapper.Entry<?> entry : entriesNeedFillFromDb) {
                    entry.setEnText(keyNo2EnNameMap.get(entry.getKeyNo()));
                }
            }
        }
    }

    /**
     * 获取大陆企业所有英文名
     *
     * @param keyNos
     * @return
     */
    protected abstract List<CompAllEnNameDTO> listAllEnName4Local(Collection<String> keyNos);

    /**
     * 获取海外企业英文名
     *
     * @param keyNos
     * @return
     */
    protected abstract Map<String, String> getKeyNo2EnNameMap4Oversea(Collection<String> keyNos);

    // 处理需要机翻的项
    private void fillEnTextByMachine(TransWrapper transWrapper) {
        List<TransWrapper.Entry<?>> entriesNeedPinyin = new ArrayList<>();
        List<TransWrapper.Entry<?>> entriesNeedMachineTranslate = new ArrayList<>();
        for (TransWrapper.Entry<?> entry : transWrapper.entryList()) {
            detectTransType(transWrapper, entry, entriesNeedPinyin, entriesNeedMachineTranslate);
        }

        // 处理需要拼音翻译的项 begin
        for (TransWrapper.Entry<?> entry : entriesNeedPinyin) {
            String localText = entry.getLocalText();
            if (StringUtils.isNotBlank(localText)) {
                String enText = Arrays.stream(StringUtils.split(removeEmTags(localText), ",")) // 兼容搜索时本地语言包含<em>
                        .filter(StringUtils::isNotBlank)
                        .map(this::chineseNameToPinyin)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(", "));
                entry.setEnText(enText);
                entry.setMtFlag(StringUtils.isNotBlank(entry.getEnText()));
            }
        }
        // 处理需要拼音翻译的项 end

        // 处理需要机器翻译的项 begin
        // 按照机翻源语言分组, 每组源语言单独翻译
        BatchTranslateForm batchTranslateForm = new BatchTranslateForm();
        entriesNeedMachineTranslate.forEach(entry -> batchTranslateForm.addItem(removeEmTags(entry.getLocalText()), entry.getSourceArea())); // 兼容搜索时本地语言包含<em>
        Map<String, String> text2EnTextMap = batchTranslateByMachine(batchTranslateForm);
        if (!text2EnTextMap.isEmpty()) {
            for (TransWrapper.Entry<?> entry : entriesNeedMachineTranslate) {
                String localText = entry.getLocalText();
                if (StringUtils.isNotBlank(localText)) {
                    entry.setEnText(text2EnTextMap.get(removeEmTags(entry.getLocalText())));
                    entry.setMtFlag(StringUtils.isNotBlank(entry.getEnText()));
                }
            }
        }
    }

    private static void detectTransType(TransWrapper transWrapper,
                                        TransWrapper.Entry<?> entry,
                                        List<TransWrapper.Entry<?>> entriesNeedPinyin,
                                        List<TransWrapper.Entry<?>> entriesNeedMachineTranslate) {
        String localText = entry.getLocalText();
        String enText = entry.getEnText();
        if (StringUtils.isNotBlank(enText) || StringUtils.isBlank(localText)) {
            return; // 英文名不为空 or 本地语言为空, 跳过
        }
        String sourceArea = entry.getSourceArea();
        if (sourceArea == null) {
            return;
        }
        // 本地语言即英文
        switch (sourceArea) {
            case "HK":
                if (!entry.ifPerson()) {
                    entriesNeedMachineTranslate.add(entry);
                }
                break;

            case "CN":
            case "TW":
                // 非实体 or 企业实体
                if (!entry.getIfEntity() || entry.ifCorp()) {
                    entriesNeedMachineTranslate.add(entry);
                    break;
                }
                // 人员实体 or 未知类型的实体
                if (isContainPunctuation(localText)
                        || localText.length() >= 5) {
                    // 包含符号 or 长度大于等于5
                    entriesNeedMachineTranslate.add(entry);
                } else {
                    entriesNeedPinyin.add(entry);
                }
                break;
            default:
                entriesNeedMachineTranslate.add(entry);
                break;
        }
    }

    /**
     * 本地语言是英文
     *
     * @param localText
     * @param enText
     * @return
     */
    private static boolean ifLocalTextContainsEnText(String localText, String enText) {
        if (StringUtils.isNotBlank(localText) && StringUtils.isNotBlank(enText)) {
            localText = removeEmTags(localText);
            enText = removeEmTags(enText);
            return StringUtils.containsIgnoreCase(localText, enText);
        } else {
            return false;
        }
    }

    public static String upperCaseExceptEmTags(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        return input.toUpperCase().replaceAll("(?i)<em>", "<em>").replaceAll("(?i)</em>", "</em>");
    }

    public static String removeEmTags(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        return input.replaceAll("(?i)</?em>", "");
    }

    public static boolean isContainChinese(String str) {
        return StringUtils.isNotBlank(str)
                && PATTERN_CN.matcher(str).find();
    }

    public static boolean isContainEnglish(String str) {
        return StringUtils.isNotBlank(str)
                && PATTERN_ENGLISH.matcher(str).find();
    }

    public static boolean isContainPunctuation(String str) {
        return StringUtils.isNotBlank(str)
                && PATTERN_PUNCTUATION.matcher(str).find();
    }

    /**
     * 拼音翻译
     *
     * @param chinese
     * @return
     */
    protected abstract String chineseNameToPinyin(String chinese);

    /**
     * 批量机翻
     *
     * @param form
     * @return
     */
    protected abstract Map<String, String> batchTranslateByMachine(BatchTranslateForm form);
}
