package com.backend.common.modules.benefit.service;

import com.backend.common.entity.SysTemplate;
import com.backend.common.form.ding.msg.DingMsgSendDTO;
import com.backend.common.modules.benefit.entity.TblBenefitDelivery;
import com.backend.common.modules.benefit.entity.TblBenefitPool;
import com.backend.common.modules.benefit.form.BenefitDeliveryForm;
import com.backend.common.modules.benefit.model.ContractBenefitWorkFlowDetailTO;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.service.CommCrmCompTrackingRecordService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.delivery.service.TransactionBusinessService;
import com.backend.common.modules.monitor.service.CommTblMonitorEntityCompanyService;
import com.backend.common.modules.monitor.service.CommTblMonitorEntityCompanyUserHisService;
import com.backend.common.modules.monitor.service.CommTblMonitorEntityCompanyUserService;
import com.backend.common.modules.monitor.service.CommTblMonitorUserPortfolioNotifySettingService;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.model.CompChargeUnitTO;
import com.backend.common.openapi.KzzApiInterface;
import com.backend.common.openapi.model.KzzBenefitWorkFlowDetailTO;
import com.backend.common.service.CommSysTemplateService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.BenefitGroupEnum;
import com.qcc.frame.commons.ienum.BenefitPoolStatusEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.CompTrackingRecordSourceEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryBenefitStatusEnum;
import com.qcc.frame.commons.ienum.DeliveryContracStatusEnum;
import com.qcc.frame.commons.ienum.MapTrialOperateTypeEnum;
import com.qcc.frame.commons.ienum.ReportGroupMappingEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.commons.ienum.ding.DingMsgLevelEnum;
import com.qcc.frame.commons.ienum.exception.MessageExceptionEnum;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权益综合处理 serivce
 *
 * <AUTHOR>
 * @datetime 8/5/2025 4:29 下午
 */
@Service
public class BenefitBusinessService {
    private static final Logger log = LoggerFactory.getLogger(BenefitBusinessService.class);

    @Autowired
    private CommTblBenefitDeliveryService benefitDeliveryService;
    @Autowired
    private CommTblBenefitPoolService benefitPoolService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblContractDeliveryProdAccService prodAccService;
    @Autowired
    private CommSysCompInfoFuncCountService funcCountService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private CommTblMonitorEntityCompanyUserService monitorEntityCompanyUserService;
    @Autowired
    private CommTblMonitorEntityCompanyUserHisService commTblMonitorEntityCompanyUserHisService;
    @Autowired
    private CommTblMonitorEntityCompanyService monitorEntityCompanyService;
    @Autowired
    private CommSysCompanyService companyService;
    @Autowired
    private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
    @Autowired
    private CommTblMonitorUserPortfolioNotifySettingService monitorNotifySettingService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;
    @Autowired
    private CommSysTemplateService commSysTemplateService;
    @Autowired
    private QccMailSenderService qccMailSenderService;
    @Autowired
    private UserService userService;

    /**
     * 获取合同额度工单详情
     *
     * @param companyId    公司ID
     * @param workFlowCode 工单编号
     * @return 合同额度工单详情
     * @throws MessageException 消息异常
     */
    public ContractBenefitWorkFlowDetailTO getContractBenefitWorkFlowDetailTO(String companyId, String workFlowCode) throws MessageException {
        // 获取工单信息
        KzzBenefitWorkFlowDetailTO workFlowDetail = KzzApiInterface.getBenefitWorkFlowDetail(workFlowCode);
        MsgExceptionUtils.checkIsNull(workFlowDetail, "msg:未找到对应的合同，请核实后重试");

        // 模板基本信息校验
        MsgExceptionUtils.checkIsNull(workFlowDetail.getContractNo(), "msg:模板信息错误，未找到对应的合同，请核实后重试");
        MsgExceptionUtils.checkIsNull(workFlowDetail.getProductCode(), "msg:模板信息错误，未找到对应的产品编号，请核实后重试");
        MsgExceptionUtils.checkIsNull(workFlowDetail.getDeliveryQuantity(), "msg:模板信息错误，未找到对应的交付数量，请核实后重试");
        MsgExceptionUtils.checkIsNull(workFlowDetail.getConsumedCredits(), "msg:模板信息错误，未找到对应的额度数量，请核实后重试");
        MsgExceptionUtils.checkIsNull(workFlowDetail.getBeginDate(), "msg:模板信息错误，未找到对应的权益开始时间，请核实后重试");

        // 校验工单是否已审批通过
        MsgExceptionUtils.failBuild(!workFlowDetail.isPass(), "msg:未找到对应的合同，请核实后重试");

        String contractNo = workFlowDetail.getContractNo();
        String productCode = workFlowDetail.getProductCode();
        Integer deliveryQuantity = workFlowDetail.getDeliveryQuantity();
        Integer consumedCredits = workFlowDetail.getConsumedCredits();

        // 检查产品编号是否有效
        BenefitTypeEnum benefitTypeEnum = BenefitTypeEnum.getByKzzProductCode(productCode);
        MsgExceptionUtils.checkIsNull(benefitTypeEnum, "msg:找不到对应的产品编号，请核对后重试");

        // 获取合同信息
        List<TblContractDelivery> contractDeliveries = contractDeliveryService.getByContractNo(contractNo);
        MsgExceptionUtils.checkIsNull(contractDeliveries, "msg:未找到对应的合同，请核实后重试");

        // 获取第一个合同，通常只有一个
        TblContractDelivery contract = contractDeliveries.get(0);
        MsgExceptionUtils.failBuild(DeliveryContracStatusEnum.OVERDUE.getCode().equals(contract.getStatus()), "msg:该合同已过期");


        // 获取合同的额度信息
        List<TblContractDeliveryProdAcc> prodAccs = prodAccService.getByContractDeliveryId(companyId, contract.getId());
        MsgExceptionUtils.checkIsNull(prodAccs, "msg:未找到对应的合同额度信息，请核实后重试");
        TblContractDeliveryProdAcc prodAcc = prodAccs.get(0);
        MsgExceptionUtils.failBuild(!prodAcc.ifInEffectRange(), "msg:合同额度不在有效期，请核实后重试");
        BigDecimal totalCredits = prodAcc.getTotalUnit();
        BigDecimal remainingCredits = prodAcc.calTotalRemainUnit();

        // 创建返回对象
        ContractBenefitWorkFlowDetailTO result = new ContractBenefitWorkFlowDetailTO();
        result.setContractNo(contractNo);
        result.setWorkOrderNo(workFlowCode);
        result.setBeginDate(contract.getBeginDate());
        result.setEndDate(contract.getEndDate());
        result.setTotalCredits(totalCredits);
        result.setRemainingCredits(remainingCredits);
        result.setProdAccId(prodAcc.getId());

        // 创建服务明细
        result.setServiceName(benefitTypeEnum.getName());
        result.setBenefitType(benefitTypeEnum.getCode());
        result.setKzzProductCode(productCode);
        result.setDeliveryQuantity(deliveryQuantity);
        // 计算单价，避免除以零，
        if (deliveryQuantity > 0) {
            BigDecimal unitCredits = BigDecimal.valueOf(consumedCredits).divide(BigDecimal.valueOf(deliveryQuantity), 2, RoundingMode.HALF_UP);
            result.setUnitCredits(unitCredits);
        }
        result.setConsumedCredits(consumedCredits);
        result.setBenefitBeginDate(workFlowDetail.getBeginDate());
        result.setBenefitEndDate(result.getEndDate()); // 权益到期日与合同到期日相同

        // 设置统计信息
        result.setCreditsRemainAfterConsume(remainingCredits.subtract(new BigDecimal(consumedCredits)));

        return result;
    }

    /**
     * 权益交付
     *
     * @param form 参数
     * @throws MessageException 消息异常
     */
    public void delivery(BenefitDeliveryForm form) throws MessageException {
        String workFlowNo = form.getWorkFlowNo();
        String companyId = form.getCompanyId();
        Date curBenefitBeginDate = form.getBeginDate();
        funcCountService.lockByCompanyId(companyId);
        ContractBenefitWorkFlowDetailTO workFlow = this.getContractBenefitWorkFlowDetailTO(companyId, workFlowNo);
        // 校验额度是否足够
        MsgExceptionUtils.failBuild(workFlow.getCreditsRemainAfterConsume().compareTo(BigDecimal.ZERO) < 0, "msg:该合同额度不足");

        LocalDate contractBeginLocalDate = DateUtils.toLocalDate(workFlow.getBeginDate());
        LocalDate contractEndLocalDate = DateUtils.toLocalDate(workFlow.getEndDate());
        LocalDate currentLocalDate = LocalDate.now();
        LocalDate curBenefitBeginLocalDate = DateUtils.toLocalDate(curBenefitBeginDate);

        // 权益开始日期不能晚于合同到期日
        MsgExceptionUtils.failBuild(curBenefitBeginLocalDate.isAfter(contractEndLocalDate), "msg:权益开始时间不能晚于到期时间，请核对后重试");
        // 权益开始时间不能早于合同开始时间
        MsgExceptionUtils.failBuild(curBenefitBeginLocalDate.isBefore(contractBeginLocalDate), "msg:权益开始时间不能早于合同开始时间，请核对后重试");
        // 权益开始时间不能早于当前时间
        MsgExceptionUtils.failBuild(curBenefitBeginLocalDate.isBefore(currentLocalDate), "msg:权益开始时间不能早于当前时间，请核对后重试");

        List<TblBenefitDelivery> companyBenefitDeliveryList = benefitDeliveryService.getByCompanyId(companyId);
        // 不能存在不同合同都生效的权益交付记录
        boolean existDiffContractBenefitDelivery = companyBenefitDeliveryList.stream()
                .anyMatch(k -> DeliveryBenefitStatusEnum.EFFECT.getCode().equals(k.getStatus())
                        && !StringUtils.equals(k.getContractNo(), workFlow.getContractNo()));
        MsgExceptionUtils.failBuild(existDiffContractBenefitDelivery, "msg:配置的权益暂不符合交付要求, 不能存在不同合同的权益");

        List<String> monitorBenefitTypeList = BenefitTypeEnum.getMonitorBenefitTypeList();
        if (monitorBenefitTypeList.contains(workFlow.getBenefitType())) {
            List<TblBenefitDelivery> monitorBenefitDeliveryList = companyBenefitDeliveryList.stream()
                    .filter(k -> monitorBenefitTypeList.contains(k.getBenefitType()))
                    .collect(Collectors.toList());
            // 不能存在其他生效的监控权益
            boolean existDiffBenefitTypeBenefitDelivery = monitorBenefitDeliveryList.stream()
                    .anyMatch(k -> DeliveryBenefitStatusEnum.EFFECT.getCode().equals(k.getStatus())
                            && !StringUtils.equals(k.getBenefitType(), workFlow.getBenefitType()));
            MsgExceptionUtils.failBuild(existDiffBenefitTypeBenefitDelivery, "msg:配置的权益暂不符合交付要求, 不能存在不同类型的监控权益");

            // 开始时间要晚于其他监控权益的开始时间  目的：每一个company在一天内，只能允许有一种套餐类型
            boolean existDiffBeginDateBenefitDelivery = monitorBenefitDeliveryList.stream()
                    .anyMatch(k -> !curBenefitBeginLocalDate.isAfter(DateUtils.toLocalDate(k.getBeginDate())));
            MsgExceptionUtils.failBuild(existDiffBeginDateBenefitDelivery, "msg:配置的权益暂不符合交付要求, 开始时间要晚于其他权益交付的开始时间");
        }


        // 保存数据
        TblBenefitDelivery benefitDelivery = new TblBenefitDelivery();
        benefitDelivery.setCompanyId(companyId);
        benefitDelivery.setContractNo(workFlow.getContractNo());
        benefitDelivery.setWorkOrderNo(workFlow.getWorkOrderNo());
        benefitDelivery.setBeginDate(curBenefitBeginDate);
        benefitDelivery.setEndDate(workFlow.getEndDate());
        benefitDelivery.setStatus(DeliveryBenefitStatusEnum.PENDING.getCode());
        benefitDelivery.setConsumedCredits(workFlow.getConsumedCredits());
        if (BenefitTypeEnum.getInfiniteList().contains(workFlow.getBenefitType())) {
            benefitDelivery.setBenefitGroup(BenefitGroupEnum.INF.getCode());
            benefitDelivery.setPoolCount(0);
        } else {
            benefitDelivery.setBenefitGroup(BenefitGroupEnum.SEAT.getCode());
            benefitDelivery.setPoolCount(workFlow.getDeliveryQuantity());
        }
        benefitDelivery.setBenefitType(workFlow.getBenefitType());
        benefitDeliveryService.save(benefitDelivery);

        // 扣费
        transactionBusinessService.consumeTransaction(companyId, benefitDelivery.getId(), null,
                BigDecimal.valueOf(workFlow.getConsumedCredits()), new Date(), TransactionTypeEnum.BENEFIT.getCode(),
                workFlow.getProdAccId());

        // 如果已经在生效时间，则直接生效
        if (curBenefitBeginLocalDate.isEqual(currentLocalDate)) {
            this.effectBenefit(benefitDelivery);
        }

        // 保存跟踪记录
        commCrmCompTrackingRecordService.save(companyId, form.getTrackingContent(), CompTrackingRecordSourceEnum.SIGN_DELIVERY.getCode());
    }

    public void deliveryMonitorForTrial(BenefitDeliveryForm form) throws MessageException {
        String companyId = form.getCompanyId();
        Company company = companyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:未找到对应的company，请核实后重试");
        // 只有试用客户才能交付
        MsgExceptionUtils.failBuild(!CompTypeEnum.TRIAL.getCode().equals(company.getType()), "msg:只有试用客户才能交付");
        SysCompInfoFuncCount funcCount = funcCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.failBuild(Objects.isNull(funcCount) || funcCount.getTotalCount().compareTo(BigDecimal.ZERO) == 0, "msg:需要充值以后再进行权益交付");

        // 校验开始时间在结束时间之前
        LocalDate beginDate = DateUtils.toLocalDate(form.getBeginDate());
        LocalDate endDate = DateUtils.toLocalDate(form.getEndDate());
        MsgExceptionUtils.failBuild(beginDate.isAfter(endDate), "msg:开始时间不能晚于结束时间");

        // 校验开始时间必须为当天
        LocalDate currentLocalDate = LocalDate.now();
        MsgExceptionUtils.failBuild(!beginDate.isEqual(currentLocalDate), "msg:权益有效期到日期不超过账号到期日，请核对后重试");

        // 权益有效期到日期不超过账号到期日
        LocalDate accountEndDate = DateUtils.toLocalDate(company.getEndDate());
        MsgExceptionUtils.failBuild(endDate.isAfter(accountEndDate), "msg:权益有效期到日期不超过账号到期日，请核对后重试");

        // 开始时间必须在其他权益交付开始时间之后，请核对后重试
        List<String> monitorBenefitTypeList = BenefitTypeEnum.getMonitorBenefitTypeList();
        if (monitorBenefitTypeList.contains(form.getBenefitType())) {
            boolean isExist = benefitDeliveryService.getByCompanyId(companyId).stream()
                    .anyMatch(k -> DeliveryBenefitStatusEnum.EFFECT.getCode().equals(k.getStatus())
                            && monitorBenefitTypeList.contains(k.getBenefitType())
                            && !beginDate.isAfter(DateUtils.toLocalDate(k.getBeginDate())));
            MsgExceptionUtils.failBuild(isExist, "msg:开始时间必须在其他权益交付开始时间之后，请核对后重试");

        }

        // 清空旧数据
        invalidMonitorBenefitByCompanyId(companyId);

        // 保存数据
        TblBenefitDelivery benefitDelivery = new TblBenefitDelivery();
        benefitDelivery.setCompanyId(companyId);
        benefitDelivery.setBeginDate(form.getBeginDate());
        benefitDelivery.setEndDate(form.getEndDate());
        benefitDelivery.setStatus(DeliveryBenefitStatusEnum.PENDING.getCode());
        if (BenefitTypeEnum.getInfiniteList().contains(form.getBenefitType())) {
            benefitDelivery.setBenefitGroup(BenefitGroupEnum.INF.getCode());
            benefitDelivery.setPoolCount(0);
        } else {
            benefitDelivery.setBenefitGroup(BenefitGroupEnum.SEAT.getCode());
            benefitDelivery.setPoolCount(form.getDeliveryQuantity());
        }
        benefitDelivery.setBenefitType(form.getBenefitType());
        benefitDeliveryService.save(benefitDelivery);

        this.effectBenefit(benefitDelivery);

        // 保存跟踪记录
        commCrmCompTrackingRecordService.save(companyId, form.getTrackingContent(), CompTrackingRecordSourceEnum.SIGN_DELIVERY.getCode());
    }


    /**
     * 权益生效
     *
     * @param benefitDelivery 权益交付
     * @throws MessageException 消息异常
     */
    public void effectBenefit(TblBenefitDelivery benefitDelivery) throws MessageException {
        log.info("effectBenefit 权益生效，更新产品，benefitDeliveryId:{}", benefitDelivery.getId());
        // 校验是否已经生效
        MsgExceptionUtils.failBuild(!benefitDelivery.getStatus().equals(DeliveryBenefitStatusEnum.PENDING.getCode()), "msg:该权益交付状态不允许生效");

        // 更新状态
        benefitDelivery.setStatus(DeliveryBenefitStatusEnum.EFFECT.getCode());
        benefitDelivery.setEffectTime(new Date());
        benefitDeliveryService.save(benefitDelivery);

        String companyId = benefitDelivery.getCompanyId();
        if (BenefitGroupEnum.SEAT.getCode().equals(benefitDelivery.getBenefitGroup()) || BenefitGroupEnum.PER.getCode().equals(benefitDelivery.getBenefitGroup())) {
            TblBenefitPool benefitPool = benefitPoolService.lockEffect(companyId, benefitDelivery.getContractNo(), benefitDelivery.getBenefitType());
            if (Objects.isNull(benefitPool)) {
                benefitPool = new TblBenefitPool();
                benefitPool.setCompanyId(companyId);
                benefitPool.setContractNo(benefitDelivery.getContractNo());
                benefitPool.setBenefitType(benefitDelivery.getBenefitType());
                benefitPool.setTotalCount(benefitDelivery.getPoolCount());
                benefitPool.setBenefitGroup(benefitDelivery.getBenefitGroup());
                benefitPool.setConsumedCount(0);
                // 设置权益池状态为生效
                benefitPool.setStatus(BenefitPoolStatusEnum.EFFECT.getCode());
                log.info("effectBenefit 权益生效，创建产品，benefitPoolId:{}, totalCount:{}", benefitPool.getId(), benefitPool.getTotalCount());
            } else {
                int oriTotalCount = benefitPool.getTotalCount();
                benefitPool.setTotalCount(oriTotalCount + benefitDelivery.getPoolCount());
                log.info("effectBenefit 权益生效，更新产品，benefitPoolId:{}，oriTotalCount:{}，newTotalCount:{}", benefitPool.getId(), oriTotalCount, benefitPool.getTotalCount());
            }
            benefitPoolService.save(benefitPool);
            // 试用图谱权益生效对应的产品
            if (BenefitTypeEnum.getTrialMapBenefitTypeList().contains(benefitPool.getBenefitType())) {
                List<String> reportTypes = BenefitTypeEnum.getReportTypesByBenefit(benefitPool.getBenefitType());
                for (String reportType : reportTypes) {
                    TblReportChargeUnit exist = chargeUnitService.getChargeUnitByUniqueKey(companyId, ReportGroupMappingEnum.MAP_G.getRptGroup(), reportType);

                    if (Objects.isNull(exist)) {
                        TblReportChargeUnit commChargeUnit = chargeUnitService.getChargeUnitByUniqueKey(
                                Constants.DEFAULT_COMPANY_ID, ReportGroupMappingEnum.MAP_G.getRptGroup(), reportType);
                        TblReportChargeUnit saveChargeUnit = TblReportChargeUnit.createCompanyChargeUnitByCommon(commChargeUnit, companyId);
                        chargeUnitService.save(saveChargeUnit);
                    } else {
                        exist.setEnabled(Constants.YES);
                        chargeUnitService.save(exist);
                    }
                    log.info("effectBenefit 生效图谱产品，companyId:{}, reportType:{}", companyId, reportType);
                }
            }

            if (BenefitTypeEnum.getMonitorBenefitTypeList().contains(benefitPool.getBenefitType())) {
                // 权益交付生效时，初始化监控推送的配置项
                monitorNotifySettingService.initMonitorSetting(benefitPool.getCompanyId(), benefitPool.getBenefitType());
            }
        }



    }


    /**
     * 权益过期
     * 当有多个权益交付记录对应一个权益池时，第二次进入过期权益逻辑查不到对应的数据，不会重复执行
     *
     * @param benefitDelivery 权益交付
     */
    public void expireBenefit(TblBenefitDelivery benefitDelivery) {
        log.info("expireBenefit 权益过期，更新产品，benefitDeliveryId:{}", benefitDelivery.getId());
        // 校验是否已经过期
        if (!benefitDelivery.getStatus().equals(DeliveryBenefitStatusEnum.EFFECT.getCode())) {
            log.info("expireBenefit 不在生效中 不能进行过期，benefitDeliveryId:{}", benefitDelivery.getId());
            return;
        }

        // 更新状态
        benefitDelivery.setStatus(DeliveryBenefitStatusEnum.INVALID.getCode());
        benefitDelivery.setOverTime(new Date());
        benefitDeliveryService.save(benefitDelivery);

        String companyId = benefitDelivery.getCompanyId();
        String contractNo = benefitDelivery.getContractNo();
        // 过期权益
        List<TblBenefitPool> benefitPools = benefitPoolService.getEffectByCompanyId(companyId)
                .stream()
                // 合同号为空或者合同号与当前合同号相同(试用时合同号为空)
                .filter(k -> (StringUtils.isBlank(k.getContractNo()) && StringUtils.isBlank(contractNo))
                        || StringUtils.equals(k.getContractNo(), contractNo))
                // 同一个套餐
                .filter(k -> StringUtils.equals(k.getBenefitType(), benefitDelivery.getBenefitType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(benefitPools)) {
            log.info("expireBenefit 未找到对应的权益池 或 已经被处理，companyId:{}, contractNo:{}", companyId, contractNo);
            return;
        }
        for (TblBenefitPool benefitPool : benefitPools) {
            // 更新权益池状态为失效
            benefitPool.setStatus(BenefitPoolStatusEnum.INVALID.getCode());
            benefitPoolService.save(benefitPool);
            log.info("expireBenefit 权益池状态更新为失效，companyId:{}, contractNo:{}，benefitType:{}", companyId, contractNo, benefitPool.getBenefitType());
            // 处理监控企业信息
            if (BenefitTypeEnum.getMonitorBenefitTypeList().contains(benefitPool.getBenefitType())) {
                // 处理企业关注信息，取消关注
                monitorEntityCompanyUserService.deleteByCompanyId(companyId, SysConstants.SYSTEM_USER_ID);
                commTblMonitorEntityCompanyUserHisService.updateEndDateByCompanyId(companyId, new Date(), SysConstants.SYSTEM_USER_ID);
                monitorEntityCompanyService.deleteByCompanyId(companyId, SysConstants.SYSTEM_USER_ID);
            }

            if (BenefitTypeEnum.getTrialMapBenefitTypeList().contains(benefitPool.getBenefitType())) {
                List<String> reportTypes = BenefitTypeEnum.getReportTypesByBenefit(benefitPool.getBenefitType());
                for (String reportType : reportTypes) {
                    TblReportChargeUnit chargeUnit = chargeUnitService.getChargeUnitByUniqueKey(companyId, ReportGroupMappingEnum.MAP_G.getRptGroup(), reportType);
                    if (Objects.nonNull(chargeUnit)) {
                        chargeUnit.setEnabled(Constants.NO);
                        chargeUnitService.save(chargeUnit);
                        log.info("expireBenefit 取消图谱权益，companyId:{}, reportType:{}", companyId, reportType);
                    }
                }
            }
        }
    }


    /**
     * 清空权益
     *
     * @param companyId
     */
    public void invalidMonitorBenefitByCompanyId(String companyId) {
        benefitDeliveryService.getByCompanyId(companyId).stream()
                .filter(k -> DeliveryBenefitStatusEnum.EFFECT.getCode().equals(k.getStatus()))
                .filter(k -> BenefitTypeEnum.getMonitorBenefitTypeList().contains(k.getBenefitType()))
                .forEach(k -> {
                    // 设置成T-1
                    k.setEndDate(DateUtils.addDays(new Date(), -1));
                    benefitDeliveryService.save(k);
                    expireBenefit(k);
                });
    }

    public void reconciliation(TblBenefitDelivery benefitDelivery) {
        try {
            if (BenefitTypeEnum.getMonitorBenefitTypeList().contains(benefitDelivery.getBenefitType())) {
                String companyId = benefitDelivery.getCompanyId();
                TblBenefitPool benefitPool = benefitPoolService.getEffectByContractNoAndBenefitType(companyId, benefitDelivery.getContractNo(), benefitDelivery.getBenefitType());
                Assert.notNull(benefitPool, "权益池不存在");
                Integer totalCount = benefitPool.getTotalCount();
                Integer consumedCount = benefitPool.getConsumedCount();
                Assert.isTrue(consumedCount <= totalCount, "消耗额度 consumedCount 大于总权益数量 totalCount");
                Set<String> keyNoSetFromMonitorUser = monitorEntityCompanyUserService.listMonitoredKeyNos(companyId);
                Set<String> keyNoSetFromMonitorCompany = monitorEntityCompanyService.listMonitoredKeyNos(companyId);
                Set<String> keyNoSetFromMonitorUserHis = commTblMonitorEntityCompanyUserHisService.listMonitoredKeyNos(companyId);
                Assert.isTrue(keyNoSetFromMonitorUser.equals(keyNoSetFromMonitorCompany),
                        "当前正在监控的企业不一致, user: " + StringUtils.join(keyNoSetFromMonitorUser, ",")
                                + ", company: " + StringUtils.join(keyNoSetFromMonitorCompany, ","));
                Assert.isTrue(keyNoSetFromMonitorUser.equals(keyNoSetFromMonitorUserHis),
                        "当前正在监控的企业不一致, user: " + StringUtils.join(keyNoSetFromMonitorUser, ",")
                                + ", his: " + StringUtils.join(keyNoSetFromMonitorUserHis, ","));
                Assert.isTrue(keyNoSetFromMonitorUser.size() == consumedCount, "监控企业数量与消耗额度不一致");
            }
        } catch (Exception e) {
            log.error("权益对账失败", e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.BENEFIT_RECONCILIATION_ERROR, e, benefitDelivery.getId());
        }
    }

    /**
     * 图谱试用申请
     *
     * @throws MessageException 消息异常
     */
    public void applyDataMapTrial() throws MessageException {
        String companyId = UserUtils.getUserCompanyId();
        Company company = companyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company);

        // 操作权限校验
        MsgExceptionUtils.failBuild(!UserUtils.isCompanyAdmin(), MessageExceptionEnum.MAP_TRIAL_BENEFIT_AUTH_ERROR);

        // 校验是否曾经有过图谱的权益，有过的不能交付
        List<TblBenefitDelivery> existingMapBenefits = benefitDeliveryService.getByCompanyId(companyId)
                .stream()
                .filter(k -> BenefitTypeEnum.getMapBenefitTypeList().contains(k.getBenefitType()))
                .collect(Collectors.toList());
        MsgExceptionUtils.failBuild(CollectionUtils.isNotEmpty(existingMapBenefits), "msg:You have already applied for the map trial.");
        List<CompChargeUnitTO> compChargeUnits = chargeUnitService.getCompChargeUnits(companyId, ReportGroupMappingEnum.MAP_G.getRptGroup());
        MsgExceptionUtils.failBuild(CollectionUtils.isNotEmpty(compChargeUnits), "msg:You have already applied for the map trial.");

        Date currentDate = new Date();
        Date endDate = DateUtils.addDays(currentDate, 14); // 当天+14天

        // 生成3个不同权益类型的交付记录
        String[] benefitTypes = {
                BenefitTypeEnum.MAP_CN_COMM.getCode(),
                BenefitTypeEnum.MAP_CN_REL_3ENTITY.getCode()
        };
        Integer[] poolCounts = {10, 5};

        for (int i = 0; i < benefitTypes.length; i++) {
            TblBenefitDelivery benefitDelivery = new TblBenefitDelivery();
            benefitDelivery.setCompanyId(companyId);

            if (CompTypeEnum.isSelfPay(company.getType())) {
                benefitDelivery.setStatus(DeliveryBenefitStatusEnum.APPLYING.getCode());
            } else {
                benefitDelivery.setStatus(DeliveryBenefitStatusEnum.PENDING.getCode());
                benefitDelivery.setBeginDate(currentDate);
                benefitDelivery.setEndDate(endDate);
            }
            benefitDelivery.setPoolCount(poolCounts[i]);
            benefitDelivery.setBenefitGroup(BenefitGroupEnum.PER.getCode()); // 按次类
            benefitDelivery.setBenefitType(benefitTypes[i]);
            benefitDeliveryService.save(benefitDelivery);

            // 立即生效权益
            if (DeliveryBenefitStatusEnum.PENDING.getCode().equals(benefitDelivery.getStatus())) {
                this.effectBenefit(benefitDelivery);
            }

            log.info("图谱试用权益处理完成，companyId:{}, benefitType:{}, poolCount:{}",
                    companyId, benefitTypes[i], poolCounts[i]);
        }

        log.info("图谱试用申请完成，companyId:{}", companyId);
    }

    /**
     * 批量操作图谱试用申请（同意或拒绝）
     *
     * @param deliveryIds 权益交付ID列表
     * @param operateType 操作类型：APPROVE-同意，REJECT-拒绝
     * @throws MessageException 消息异常
     */
    public void batchOperateMapTrialBenefit(List<String> deliveryIds, String operateType) throws MessageException {
        MsgExceptionUtils.checkIsNull(deliveryIds, "msg:权益交付ID列表不能为空");
        
        // 限制最大处理数量
        MsgExceptionUtils.failBuild(deliveryIds.size() > 50, "msg:批量操作最多支持50条记录");
        
        MapTrialOperateTypeEnum operateTypeEnum = MapTrialOperateTypeEnum.getByCode(operateType);
        MsgExceptionUtils.checkIsNull(operateTypeEnum, "msg:操作类型无效");


        // 根据deliveryIds查询对应的delivery数据
        List<TblBenefitDelivery> deliveryList = deliveryIds.stream()
                .map(benefitDeliveryService::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(deliveryList)) {
            throw new MessageException("msg:未找到对应的权益交付记录");
        }

        // 获取所有涉及的公司ID
        List<String> companyIdSet = deliveryList.stream()
                .map(TblBenefitDelivery::getCompanyId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 批量查询所有相关公司的图谱试用delivery数据
        List<TblBenefitDelivery> allCompanyTrialDeliveries = benefitDeliveryService.getByCompanyIdList(
            companyIdSet, BenefitTypeEnum.getTrialMapBenefitTypeList());
        
        // 过滤试用数据（contractNo为空）
        List<TblBenefitDelivery> filteredTrialDeliveries = allCompanyTrialDeliveries.stream()
                .filter(delivery -> StringUtils.isBlank(delivery.getContractNo()))
                .collect(Collectors.toList());

        log.info("批量查询到图谱试用delivery数据数量：{}", filteredTrialDeliveries.size());

        // 对所有图谱试用delivery进行操作
        Set<String> notifiedCompanyIds = new HashSet<>();
        Map<String, String> companyId2LoginNameMap = userService.listMainUserByCompanyId(companyIdSet).stream()
            .collect(Collectors.toMap(User::getCompanyId, User::getLoginName, (a, b) -> a));
        for (TblBenefitDelivery delivery : filteredTrialDeliveries) {
            boolean operated = operateMapTrialDelivery(delivery, operateTypeEnum);
            if (operated) {
                // 同一公司只发送一次通知
                if (notifiedCompanyIds.add(delivery.getCompanyId())) {
                    sendMapTrialOperateEmail(companyId2LoginNameMap.get(delivery.getCompanyId()), operateTypeEnum);
                }
            }
        }

        log.info("批量{}图谱试用申请完成，处理的公司数量：{}", 
                operateTypeEnum.getDesc(), companyIdSet.size());
    }

    /**
     * 操作单个图谱试用申请（同意或拒绝）
     *
     * @param delivery 权益交付记录
     * @param operateType 操作类型
     * @throws MessageException 消息异常
     */
    private boolean operateMapTrialDelivery(TblBenefitDelivery delivery, MapTrialOperateTypeEnum operateType) throws MessageException {
        String status = delivery.getStatus();
        
        // 校验操作权限
        if (!DeliveryBenefitStatusEnum.APPLYING.getCode().equals(status)) {
            log.error("权益交付状态不允许{}操作，deliveryId:{}, status:{}", operateType.getDesc(), delivery.getId(), status);
            return false;
        }

        // 根据操作类型更新状态
        if (MapTrialOperateTypeEnum.APPROVE.equals(operateType)) {
            // 同意：更新状态为生效，并调用生效方法
            Date currentDate = new Date();
            Date endDate = DateUtils.addDays(currentDate, 14); // 当天+14天
            delivery.setBeginDate(currentDate);
            delivery.setEndDate(endDate);
            delivery.setStatus(DeliveryBenefitStatusEnum.PENDING.getCode());
            benefitDeliveryService.save(delivery);
            this.effectBenefit(delivery);
            log.info("{}图谱试用申请 生效，deliveryId:{}, companyId:{}, benefitType:{}", 
                    operateType.getDesc(), delivery.getId(), delivery.getCompanyId(), delivery.getBenefitType());
            return true;
        } else if (MapTrialOperateTypeEnum.REJECT.equals(operateType)) {
            // 拒绝：更新状态为已拒绝
            delivery.setStatus(DeliveryBenefitStatusEnum.REJECTED.getCode());
            benefitDeliveryService.save(delivery);
            log.info("{}图谱试用申请 拒绝，deliveryId:{}, companyId:{}, benefitType:{}", 
                    operateType.getDesc(), delivery.getId(), delivery.getCompanyId(), delivery.getBenefitType());
            return true;
        }
        return false;
    }

    /**
     * 图谱试用审批结果邮件通知
     */
    private void sendMapTrialOperateEmail(String loginName, MapTrialOperateTypeEnum operateTypeEnum) {
        try {
            if (StringUtils.isBlank(loginName)) {
                log.warn("图谱试用审批结果邮件通知，loginName为空，跳过发送，loginName:{}", loginName);
                return;
            }

            String templateName = MapTrialOperateTypeEnum.APPROVE.equals(operateTypeEnum)
                    ? "u_email_map_trial_pass"
                    : "u_email_map_trial_reject";

            SysTemplate template = commSysTemplateService.getByTemplateName(templateName);
            if (template == null) {
                log.error("邮件模板不存在，templateName:{}", templateName);
                return;
            }
            // 模板不需要参数，直接发送
            qccMailSenderService.send(loginName, template.getTemplateSubject(), template.getTemplateContent());
            log.info("发送图谱试用审批结果通知成功，loginName:{}, template:{}", loginName, templateName);
        } catch (Exception e) {
            log.error("发送图谱试用审批结果通知失败, loginName:{}", loginName, e);
        }
    }

    /**
     * 7天后到期的权益通知
     * @param delivery
     */
    public void notifyBenefitExpireAfterSevenDays(TblBenefitDelivery delivery) {
        if (Objects.isNull(delivery) || StringUtils.isAnyBlank(delivery.getCompanyId(), delivery.getBenefitType())) {
            return;
        }
        try {
            // 试用账号 7天后要过期的权益信息
            String companyId = delivery.getCompanyId();
            TblBenefitPool benefitPool = benefitPoolService.getEffectByContractNoAndBenefitType(companyId, delivery.getContractNo(), delivery.getBenefitType());
            Integer consumedCount = benefitPool.getConsumedCount();
            Integer totalCount = benefitPool.getTotalCount();
            Company company = companyService.get(companyId);
            if (company != null) {
                String companyName = company.getName();
                String mdContent = String.format("**说明:** %s  \n" +
                                "**用户信息:** %s",
                        String.format("%s 权益7天后到期；已用额度/全部额度：%s/%s", BenefitTypeEnum.getNameByCode(benefitPool.getBenefitType()),
                                consumedCount == null ? 0 : consumedCount, totalCount == null ? 0 : totalCount), companyName);
                DingMsgSendDTO sendDTO = DingMsgSendDTO.build(DingMsgLevelEnum.INFO, "KYC业务提醒", mdContent);
                sysDingMsgNewTranService.newTranSave(sendDTO);
            }
        } catch (Exception e) {
            log.error("检查试用账号到期权益任务失败, companyId:{}, benefitType:{}", delivery.getCompanyId(), delivery.getBenefitType(), e);
        }
    }
}
