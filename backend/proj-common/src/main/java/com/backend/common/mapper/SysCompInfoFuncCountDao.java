package com.backend.common.mapper;

import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.entity.mapping.FunctionTableInfoTO;
import com.backend.common.modules.setting.model.FunctionCountTO;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface SysCompInfoFuncCountDao extends CrudDao<SysCompInfoFuncCount> {

    SysCompInfoFuncCount lockByFuncTableId(@Param("companyId")String companyId);

    SysCompInfoFuncCount getCompInfoFuncCountByFuncTableId(@Param("companyId")String companyId, @Param("functionTableId") String functionTableId);
    SysCompInfoFuncCount getCompInfoFuncCountByFuncTableId4PageConsumedCount(@Param("companyId")String companyId, @Param("functionTableId") String functionTableId);
    List<FunctionCountTO> listFunctionCount(String companyId);
    // updated for v2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口 根据角色获取服务列表信息
    List<FunctionTableInfoTO> getFunctionTableInfoByRoleIds(@Param("list") List<String> roleIds);
    // updated for v2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口 根据角色以及companyId获取服务列表信息
    List<FunctionTableInfoTO> getFunctionTableInfoByRoleIdsAndCompanyId(@Param("companyId") String companyId, @Param("list") List<String> roleIds);
    // added for v2.0.6 for 2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口
    // 本地读取所有账号配置的服务列表的总额度，管理员除外
    FunctionCountInfoTO getFunctionCountInfoByFunctionTableId(@Param("functionTableId")String functionTableId);
    List<SysCompInfoFuncCount> getSysCompFuncCountDetailByCompanyIdIncludeDel(@Param("companyId")String companyId);
    // added for v1.8.8 KNZT-4033
    void deleteByCompanyId(SysCompInfoFuncCount entity);
    // added for v1.8.8 KNZT-4033
    List<SysCompInfoFuncCount> getSysCompFuncCountDetailByCompanyId(@Param("companyId")String companyId);

}
