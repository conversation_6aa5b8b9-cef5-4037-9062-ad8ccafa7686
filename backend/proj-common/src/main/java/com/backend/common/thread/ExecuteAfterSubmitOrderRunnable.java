package com.backend.common.thread;

import com.backend.common.entity.SysTemplate;
import com.backend.common.global.gateway.verify.VerifyInterface;
import com.backend.common.global.gateway.verify.model.VerifyResp;
import com.backend.common.modules.common.service.CommTblUserQuotaLimitService;
import com.backend.common.modules.person_vrfy.form.EncryptedForm;
import com.backend.common.modules.report.condition.TblCompReportOrderCondition;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.MerchantShopBusinessService;
import com.backend.common.modules.report.service.ScanBusinessService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysTemplateService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.PayStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.thread.BaseRunnable;
import com.qcc.frame.jee.commons.utils.FreemarkerUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.SpringContextHolder;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.ApiUserLoginService;
import com.qcc.frame.jee.modules.sys.service.QccMailSenderService;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.DictUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ExecuteAfterSubmitOrderRunnable extends BaseRunnable {

    CommTblCompReportOrderService commTblCompReportOrderService = SpringContextHolder.getBean(CommTblCompReportOrderService.class);
    SysDingMsgNewTranService sysDingMsgNewTranService = SpringContextHolder.getBean(SysDingMsgNewTranService.class);
    QccMailSenderService qccMailSenderService = SpringContextHolder.getBean(QccMailSenderService.class);
    CommSysCompInfoFuncCountService funcCountService = SpringContextHolder.getBean(CommSysCompInfoFuncCountService.class);
    ApiUserLoginService apiUserLoginService = SpringContextHolder.getBean(ApiUserLoginService.class);
    CommSysTemplateService commSysTemplateService = SpringContextHolder.getBean(CommSysTemplateService.class);
    ScanBusinessService scanBusinessService = SpringContextHolder.getBean(ScanBusinessService.class);
    MerchantShopBusinessService merchantShopBusinessService = SpringContextHolder.getBean(MerchantShopBusinessService.class);
    CommTblUserQuotaLimitService commTblUserQuotaLimitService = SpringContextHolder.getBean(CommTblUserQuotaLimitService.class);


    private final String orderId;

    public ExecuteAfterSubmitOrderRunnable(String orderId) {
        super();
        this.orderId = orderId;
    }



    @Override
    public void process() {
        logger.info("ExecuteAfterSubmitOrderRunnable begin, orderId:{}", orderId);
        if (StringUtils.isEmpty(orderId)) {
            logger.error("ExecuteAfterSubmitOrderRunnable failed, orderId is null");
            return;
        }
        TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
        if (Objects.isNull(order)) {
            logger.error("ExecuteAfterSubmitOrderRunnable failed, order is null, orderId:{}", orderId);
            return;
        }

        try {
            apiUserLoginService.loginUser(order.getUserId(), () -> {
                User user = UserUtils.getUser();
                MsgExceptionUtils.checkIsNull(user);
                Company company = user.getCompany();
                MsgExceptionUtils.checkIsNull(company);

                // 预处理数据
                if (ReportTypeEnum.apiOrderTypeList().contains(order.getReportType())) {
                    commTblCompReportOrderService.buyGlobalReport(order);
                } else if (ReportTypeEnum.needRunDataInSubmitRunnable().contains(order.getReportType())) {
                    if (ReportTypeEnum.MERCHANT.getCode().equals(order.getReportType())) {
                        merchantShopBusinessService.saveMerchantShop(orderId, StringUtils.getNotBlankStr(order.getCorpNumber(), order.getCorpName()));
                    } else if (ReportTypeEnum.SCAN.getCode().equals(order.getReportType())) { // added for v2.0.9 chenbl KNZT-5362
                        scanBusinessService.match4Order(orderId);
                    }
                }
                // 对已经处理好数据的报告，进行生成
                if (ReportTypeEnum.immediateReportList().contains(order.getReportType()) || ReportTypeEnum.needRunDataInSubmitRunnable().contains(order.getReportType())) {
                    // 流转状态到数据处理中
                    order.transitionStatus(OrderStatusEnum.PENDING);
                    commTblCompReportOrderService.save(order);
                    commTblCompReportOrderService.createReport(order);
                }

                // C端客户支付成功订单超过100，发送邮件给po、商务跟进
                if (CompTypeEnum.CLIENT.getCode().equals(company.getType())) {
                    TblCompReportOrderCondition condition = new TblCompReportOrderCondition();
                    condition.setPayStatusList(Lists.newArrayList(PayStatusEnum.PAID.getCode()));
                    int count = commTblCompReportOrderService.readOnlyCountByCompanyId(order.getCompanyId(), condition);
                    String configValue = ConfigUtils.getConfigValueByTypeAndKey("self_account", "reminder_order_count", "100");
                    int limitCount = Integer.parseInt(configValue);
                    if (limitCount == count) {
                        sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.ORDER_OVER_100, company.getName());
                    }
                } else {
                    // added for v1.9.0 KNZT-4110
                    if (!ReportTypeEnum.subOrderReportTypeList().contains(order.getReportType())) {
                        SysCompInfoFuncCount funcCount = funcCountService.readOnlyGetCompFuncCount(order.getCompanyId());
                        BigDecimal remainFuncCount = Objects.nonNull(funcCount) ? funcCount.calRemainCount() : BigDecimal.ZERO;
                        BigDecimal beforeRemainFuncCount = remainFuncCount.add(order.getTotalUnit());
                        BigDecimal threshold = BigDecimal.valueOf(200);
                        if (remainFuncCount.compareTo(threshold) < 0 && beforeRemainFuncCount.compareTo(threshold) >= 0 ) {
                            sendLimitUnitAlarmEmail();
                        }
                    }
                }
                // added for lvcy v2.0.2 KNZT-5131 财税报告邮件
                if (ReportTypeEnum.FIN_TAX.getCode().equals(order.getReportType())) {
                    sendFinTaxSubmitEmail(order);
                }
                // 用户额度超出限额通知邮件
                commTblUserQuotaLimitService.sendOverQuotaLimitNotification(order.getCompanyId(), order.getUserId(), UserUtils.getUser().getName(), order);
                return null;
            });
            logger.info("ExecuteAfterSubmitOrderRunnable end, orderId:{}", orderId);
        } catch (Exception e) {
            logger.error("ExecuteAfterSubmitOrderRunnable failed, orderId:{}, error:", orderId, e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(e.getMessage(), AlarmTypeEnum.EXECUTE_AFTER_SUBMIT_ORDER_FAIL, orderId, ReportTypeEnum.getDesc(order.getReportType()));
        }
    }

    private void sendLimitUnitAlarmEmail() {
        String sendToStr = DictUtils.getDictValue("email_business", "system_config", "<EMAIL>,<EMAIL>");
        String ccToStr = DictUtils.getDictValue("email_po", "system_config", "<EMAIL>@qcc.com");

        List<String> toList = StringUtils.isBlank(sendToStr) ? Lists.newArrayList()
                : StringUtils.split2List(sendToStr, ",");
        List<String> ccList = StringUtils.isBlank(ccToStr) ? Lists.newArrayList()
                : StringUtils.split2List(ccToStr, ",");

        User user = UserUtils.getUser();
        Company company = user.getCompany();
        String content = String.format("%s - %s 的额度已经不足200。请及时联系客户。",
                company.getName(), user.getLoginName());
        qccMailSenderService.send2InnerStaff(toList, ccList, "【提醒】国际版用户额度不足200", content, null);
    }


    /**
     * 发送财税报告提交邮件
     * added for lvcy v2.0.2 KNZT-5131
     *
     * @param order
     * @return
     */
    private void sendFinTaxSubmitEmail(TblCompReportOrder order) {
        try {
            order = commTblCompReportOrderService.getOrderWithEnNameProcessed(order);
            SysTemplate template = commSysTemplateService.getByTemplateName("u_email_fin_tax_submit");
            MsgExceptionUtils.checkIsNull(template, "u_email_fin_tax_submit's template is not exist");
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("orderNo", order.getOrderNo());
            //added for v2.2.0 KNZT-7142 财税报告通知邮件中新增授权企业中英文名
            paramMap.put("corpName", StringUtils.isNotBlank(order.getCorpName()) ? order.getCorpName() : "");
            paramMap.put("corpNameEn", StringUtils.isNotBlank(order.getCorpNameEn()) ? order.getCorpNameEn() : "");
            String emailContent = FreemarkerUtils.parseTemplate(template.getId(), template.getTemplateContent(), paramMap);
            qccMailSenderService.send(order.getLoginName(), template.getTemplateSubject(), emailContent);
        } catch (Exception e) {
            logger.error("u_email_fin_tax_submit failed, orderId:{}, error:", order.getId(), e);
        }
    }

}
