package com.backend.common.modules.delivery.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.entity.SysTemplate;
import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProd;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.model.InvoiceBankTO;
import com.backend.common.modules.delivery.model.StatementMonthly4ManagementTO;
import com.backend.common.modules.delivery.model.StatementPushDetailTO;
import com.backend.common.modules.delivery.model.TransactionOrderUserConsumeTO;
import com.backend.common.openapi.KzzApiInterface;
import com.backend.common.openapi.model.KzzContractCreateRequestTO;
import com.backend.common.openapi.model.KzzContractDetailTO;
import com.backend.common.openapi.model.KzzContractPaymentSheet;
import com.backend.common.openapi.model.KzzInvoiceDetailTO;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompInfoExtService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.CommSysTemplateService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.backend.common.stripeapi.StripePaymentInterface;
import com.backend.common.thread.GenerateInvoiceRunnable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.AmtStdEnum;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryContracProdAccTypeEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReturnStatusEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.thirdparty_service.HuaweiObsServUtils;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.FileUtils;
import com.qcc.frame.jee.commons.utils.InvoiceUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.PdfUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.model.CompanyAddressTO;
import com.qcc.frame.jee.modules.sys.service.SysCompInfoColExtService;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.stripe.model.PaymentIntent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * invoice 
 * <AUTHOR>
 * @datetime 1/8/2025 5:28 下午
 */
@Service
@Slf4j
public class InvoiceBusinessService {
    @Autowired
    private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblCompStatementMonthlyService commTblCompStatementMonthlyService;
    @Autowired
    private CommSysTemplateService commSysTemplateService;
    @Autowired
    private SysConfigCacheService sysConfigCacheService;
    @Autowired
    private CommSysCompInfoExtService commSysCompInfoExtService;
    @Autowired
    private CommCompUserService commCompUserService;
    @Autowired
    private ThreadPoolService threadPoolService;
    @Autowired
    private TransactionBusinessService transactionBusinessService;
    @Autowired
    private SysCompInfoColExtService sysCompInfoColExtService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;


    public TblCompStatementMonthly saveOrPreviewInvoice4After(TblContractDelivery contractDelivery, Company company, YearMonth targetYearMonth,
                                                              BigDecimal taxRate, BigDecimal exchangeRate, boolean isPreview) throws MessageException {
        MsgExceptionUtils.checkIsNull(taxRate, "taxRate is null");
        MsgExceptionUtils.checkIsNull(exchangeRate, "exchangeRate is null");
        String companyId = company.getId();
        LocalDate now = LocalDate.now();
        String contractDeliveryId = contractDelivery.getId();
        LocalDate firstDayOfMonth = targetYearMonth.atDay(1);
        LocalDate endDayOfMonth = targetYearMonth.atEndOfMonth();
        int beginDateInt = DateUtils.getDateInt(firstDayOfMonth);
        int endDateInt = DateUtils.getDateInt(endDayOfMonth);

        Map<String, Object> paramMap = Maps.newHashMap();
        // 通用参数
        paramMap.put("compType", company.getType());
        paramMap.put("taxRate", taxRate.multiply(BigDecimal.valueOf(100)).stripTrailingZeros() + "%");
        paramMap.put("amtStd", contractDelivery.getAmountStd());
        // 抬头信息
        String toParam = company.getShortName() + " (Account ID: "+ maskCompanyId(companyId) + ")";
        paramMap.put("to", toParam);
        CompanyAddressTO companyAddress = commCompUserService.getCompanyAddress(companyId);
        MsgExceptionUtils.failBuild(!companyAddress.isValid(), "companyAddress is invalid");
        String addressParam = InvoiceUtils.generateBillAddressParam(companyAddress);
        paramMap.put("address", addressParam);
        // 税号
        String taxIdType = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE,
                Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_TYPE);
        String taxIdNumber = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE,
                Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_NUMBER);
        if (StringUtils.isNotBlank(taxIdType) && StringUtils.isNotBlank(taxIdNumber)) {
            paramMap.put("taxIdType", taxIdType);
            paramMap.put("taxIdNumber", taxIdNumber);
        }

        String issueDateParam = DateUtils.formatDateEn(now, DateUtils.DATE_FORMAT_DDMMMYYYY);
        paramMap.put("issueDate", issueDateParam);
        String invoiceNoParam = commTblCompStatementMonthlyService.generateInvoiceNo(companyId, contractDelivery.getContractNo(), targetYearMonth);
        paramMap.put("invoiceNo", invoiceNoParam);
        String invoiceRemark = commSysCompInfoExtService.retrieveFirstInfoByCompanyIdAndType(companyId, Constants.CompInfoColExt.INVOICE_REMARK);
        paramMap.put("remark", invoiceRemark);

        // 消费明细
        paramMap.put("creditsType", "Credits Used");
        BigDecimal usedCredit = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(
                contractDeliveryId, beginDateInt, endDateInt);
        // 本月消耗额度小于0的时候不生产invoice
        if (BigDecimal.ZERO.compareTo(usedCredit) > 0) {
            log.info("saveOrPreviewInvoice4After companyId:{}, usedCredit is less than 0, not generate invoice", companyId);
            return null;
        }
        // usedCredit 千分位，3000 展示3,000; 2.4 展示2.4； 2.45 展示 2.45
        String usedCreditStr = NumberUtils.formatWithThousandSplitAndAutoDec(usedCredit);
        paramMap.put("qty", usedCreditStr);
        paramMap.put("unitPrice", NumberUtils.format2ScaleWithThousandSplit(contractDelivery.getActualDiscountAmount()));

        // 生成后付费的creditDesc: During the month of {年月}
        String targetYearMonthEn = DateUtils.formatYearMonthEn(targetYearMonth, DateUtils.DATE_FORMATMMMYYYY);
        String creditDesc = String.format("During the month of %s", targetYearMonthEn);
        paramMap.put("creditDesc", creditDesc);

        // 总金额(本币) = 总额度 * 单价
        BigDecimal totalUsedAmount = usedCredit.multiply(contractDelivery.getActualDiscountAmount());
        paramMap.put("totalUsedAmount", NumberUtils.format2ScaleWithThousandSplit(totalUsedAmount));
        // 税额(本币) = 总金额 * 税率
        BigDecimal taxAmount = totalUsedAmount.multiply(taxRate);
        paramMap.put("taxAmount", NumberUtils.format2ScaleWithThousandSplit(taxAmount));
        // 税后金额(本币) = 总金额 + 税额
        BigDecimal afterTaxAmount = totalUsedAmount.add(taxAmount);
        paramMap.put("afterTaxAmount", NumberUtils.format2ScaleWithThousandSplit(afterTaxAmount));

        String isSGD = AmtStdEnum.SGD.getCode().equals(contractDelivery.getAmountStd()) ? Constants.YES : Constants.NO;
        paramMap.put("isSGD", isSGD);
        BigDecimal totalUsedAmountInSGD = null;
        BigDecimal afterTaxAmountInSGD = null;
        if (Constants.NO.equals(isSGD)) {
            paramMap.put("exchangeRate", NumberUtils.formatPercent4Dig(exchangeRate));
            // 总金额(SGD) = 总金额 * 汇率
            totalUsedAmountInSGD = totalUsedAmount.multiply(exchangeRate);
            paramMap.put("totalUsedAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(totalUsedAmountInSGD));
            // 税额(SGD) = 税额(本币) * 汇率
            BigDecimal taxAmountInSGD = taxAmount.multiply(exchangeRate);
            paramMap.put("taxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(taxAmountInSGD));
            // 税后金额(SGD) = 总金额(SGD) + 税额(SGD)
            afterTaxAmountInSGD = totalUsedAmountInSGD.add(taxAmountInSGD);
            paramMap.put("afterTaxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(afterTaxAmountInSGD));
        }
        // Amount Due (本币) = 税后金额(本币)
        // Amount Due (SGD) = 税后金额(SGD)

        // 用户订单使用情况
        List<TransactionOrderUserConsumeTO> userConsumeList = contractDeliveryTransactionService.getChangedSumOrderUnitGroupByUser(
                contractDeliveryId, beginDateInt, endDateInt, TransactionTypeEnum.getOrderTypeWithoutApiList());
        // api使用额度
        BigDecimal apiUsedCredits = contractDeliveryTransactionService.sumChangedUnitByContractDeliveryWithType(
                contractDeliveryId, beginDateInt, endDateInt, TransactionTypeEnum.getOrderTypeApiList());
        if (BigDecimal.ZERO.compareTo(apiUsedCredits) != 0) {
            userConsumeList.add(new TransactionOrderUserConsumeTO("API System Users", apiUsedCredits));
        }
        for (TransactionOrderUserConsumeTO userConsumeTO : userConsumeList) {
            userConsumeTO.setCreditsStr(NumberUtils.formatWithThousandSplitAndAutoDec(userConsumeTO.getCredits()));
        }

        paramMap.put("userConsumeList", userConsumeList);

        // 银行信息
        InvoiceBankTO bankInfo = commSysCompanyService.getBankInfo(companyId);
        MsgExceptionUtils.checkIsNull(bankInfo, "bankInfo is null");
        paramMap.put("accountName", bankInfo.getAccountName());
        paramMap.put("depositBank", bankInfo.getDepositBank());
        paramMap.put("accountNumber", bankInfo.getAccountNumber());
        paramMap.put("swiftCode", bankInfo.getSwift());

        // 存储 上传 invoice
        TblCompStatementMonthly statementMonthly = new TblCompStatementMonthly();
        statementMonthly.setInvoiceNo(invoiceNoParam);
        statementMonthly.setCompanyId(companyId);
        statementMonthly.setStatementMonth(DateUtils.formatYearMonthInt(targetYearMonth));
        statementMonthly.setBillingAddress(addressParam);
        statementMonthly.setInvoiceRemark(invoiceRemark);
        statementMonthly.setIssueDate(DateUtils.toDate(now));
        statementMonthly.setCreditsUsed(usedCredit);
        statementMonthly.setAmountUsed(totalUsedAmount);
        statementMonthly.setAmountUsedInSGD(totalUsedAmountInSGD);
        statementMonthly.setTotalCreditDue(usedCredit);
        statementMonthly.setTotalAmountDue(afterTaxAmount);
        statementMonthly.setTotalAmountDueInSGD(afterTaxAmountInSGD);
        statementMonthly.setReturnStatus(BigDecimal.ZERO.compareTo(afterTaxAmount) == 0 ?
                ReturnStatusEnum.RETURNED.getCode() : ReturnStatusEnum.PENDING_CONFIRM.getCode());
        statementMonthly.setAmountStd(contractDelivery.getAmountStd());
        statementMonthly.setExchangeRate(exchangeRate);
        statementMonthly.setTaxRate(taxRate);
        statementMonthly.setMainContractNo(contractDelivery.getContractNo());
        String url = generateStatementPdf(statementMonthly.getInvoiceNo(), paramMap);
        statementMonthly.setUrl(url);
        if (!isPreview) {
            commTblCompStatementMonthlyService.saveOrUpdateStatementByContractMonth(statementMonthly);
            log.info("calStatementMonthly companyId:{}, save to db and delete obs file", company.getId());
            return statementMonthly;
        }

        return statementMonthly;
    }

    public TblCompStatementMonthly saveInvoice4Payment(String contractNo, PaymentIntent paymentIntent,
                                                       TblContractDeliveryProdAcc prodAcc, Company company) {
        String companyId = company.getId();
        try {
            BigDecimal exchangeRate = KzzApiInterface.getExchangeRate4TargetCurrencyToSGD(AmtStdEnum.USD.getCode());
            MsgExceptionUtils.checkIsNull(exchangeRate, "exchangeRate is null");

            Map<String, String> metadata = paymentIntent.getMetadata();
            BigDecimal amountWithoutTax = StripePaymentInterface.getPaymentAmountWithoutTax(paymentIntent);
            BigDecimal amount = StripePaymentInterface.getPaymentAmountWitTax(paymentIntent);
            String taxRateStr = metadata.getOrDefault(Constants.StripeParam.TAX_RATE, "0");
            BigDecimal taxRate = new BigDecimal(taxRateStr);
            BigDecimal offsetCredits = null;
            // 额度抵扣，仅当组合支付场景的时候才会不为0
            if (StringUtils.equals(prodAcc.getType(), DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode())) {
                String creditCountStr = metadata.getOrDefault(Constants.StripeParam.CREDIT_COUNT, amountWithoutTax.toString());  // 如果订单没有credits，优先采用税前支付金额
                BigDecimal totalCredits = new BigDecimal(creditCountStr);
                offsetCredits = totalCredits.subtract(amountWithoutTax);
            }

            String invoiceNo = contractNo.replace("KYCTOP", "INV");
            Map<String, Object> paramMap = Maps.newHashMap();
            // 通用参数
            paramMap.put("compType", company.getType());
            String exchangeRateParam = NumberUtils.formatPercent4Dig(exchangeRate);
            paramMap.put("exchangeRate", exchangeRateParam);
            String taxRateParam = taxRate.multiply(BigDecimal.valueOf(100)) + "%";
            paramMap.put("taxRate", taxRateParam);
            paramMap.put("amtStd", AmtStdEnum.USD.getCode());

            // 抬头信息
            String toParam = company.getShortName() + " (Account ID: " + maskCompanyId(companyId) + ")";
            paramMap.put("to", toParam);
            CompanyAddressTO companyAddress = commCompUserService.getCompanyAddress(companyId);
            MsgExceptionUtils.failBuild(!companyAddress.isValid(), "companyAddress is invalid");
            String addressParam = InvoiceUtils.generateBillAddressParam(companyAddress);
            paramMap.put("address", addressParam);
            // 税号
            String taxIdType = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE,
                    Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_TYPE);
            String taxIdNumber = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId, Constants.CompInfoColExt.CommExt.EXT_TYPE,
                    Constants.CompInfoColExt.CommExt.COL_NAME_TAX_ID_NUMBER);
            if (StringUtils.isNotBlank(taxIdType) && StringUtils.isNotBlank(taxIdNumber)) {
                paramMap.put("taxIdType", taxIdType);
                paramMap.put("taxIdNumber", taxIdNumber);
            }

            String issueDateParam = DateUtils.formatDateEn(prodAcc.getBeginDate(), DateUtils.DATE_FORMAT_DDMMMYYYY);
            paramMap.put("issueDate", issueDateParam);
            paramMap.put("invoiceNo", invoiceNo);

            // 消费明细
            if (DeliveryContracProdAccTypeEnum.TOP_UP_CONSUMED.getCode().equals(prodAcc.getType())) {
                paramMap.put("creditsType", "Credits Used");
                paramMap.put("qty", NumberUtils.formatWithThousandSplitAndAutoDec(prodAcc.getTotalUnit()));
                paramMap.put("unitPrice", "1.00");
                if (Objects.nonNull(offsetCredits) && BigDecimal.ZERO.compareTo(offsetCredits) != 0) {
                    paramMap.put("offsetCreditsQty", NumberUtils.formatWithThousandSplitAndAutoDec(offsetCredits));
                }
            } else if (DeliveryContracProdAccTypeEnum.TOP_UP.getCode().equals(prodAcc.getType())) {
                String totalUnitParam = NumberUtils.formatWithThousandSplitAndAutoDec(prodAcc.getTotalUnit());
                String creditsTypeParam = "Credit Packs (" + totalUnitParam + " Credits)";
                paramMap.put("creditsType", creditsTypeParam);
                paramMap.put("qty", "1");
                paramMap.put("unitPrice", NumberUtils.format2ScaleWithThousandSplit(amountWithoutTax));
                // 生成creditDesc: Credits valid from {开始日期} to {结束日期}
                String creditDesc = String.format("Credits valid from %s to %s",
                        DateUtils.formatDateForSg(prodAcc.getBeginDate(), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH),
                        DateUtils.formatDateForSg(prodAcc.getEndDate(), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH));
                paramMap.put("creditDesc", creditDesc);
            } else {
                log.error("wrong prodAcc type, prodAccId:{}, type:{}", prodAcc.getId(), prodAcc.getType());
                throw new MessageException("wrong prodAcc type");
            }
            paramMap.put("totalUsedAmount", NumberUtils.format2ScaleWithThousandSplit(amountWithoutTax));
            BigDecimal taxAmount = amount.subtract(amountWithoutTax);
            paramMap.put("taxAmount", NumberUtils.format2ScaleWithThousandSplit(taxAmount));
            paramMap.put("afterTaxAmount", NumberUtils.format2ScaleWithThousandSplit(amount));
            paramMap.put("isSGD", Constants.NO);

            // 总金额(SGD) = 总金额 * 汇率
            BigDecimal totalUsedAmountInSGD = amountWithoutTax.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
            paramMap.put("totalUsedAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(totalUsedAmountInSGD));

            // 税额(SGD) = 税额(本币) * 汇率
            BigDecimal taxAmountInSGD = taxAmount.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
            paramMap.put("taxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(taxAmountInSGD));
            // 税后金额(SGD) = 总金额(SGD) + 税额(SGD)
            BigDecimal afterTaxAmountInSGD = totalUsedAmountInSGD.add(taxAmountInSGD);
            paramMap.put("afterTaxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(afterTaxAmountInSGD));

            // 银行信息
            paramMap.put("accountName", "QCC TECH PTE. LTD.");
            paramMap.put("depositBank", "Oversea-Chinese Banking Corporation Limited");
            paramMap.put("accountNumber", "************");
            paramMap.put("swiftCode", "OCBCSGSGXXX");

            // 存储 上传 invoice
            TblCompStatementMonthly statementMonthly = new TblCompStatementMonthly();
            statementMonthly.setInvoiceNo(invoiceNo);
            statementMonthly.setCompanyId(companyId);
            LocalDate beginLocalDate = DateUtils.toLocalDate(prodAcc.getBeginDate());
            YearMonth ym = YearMonth.from(beginLocalDate);
            statementMonthly.setStatementMonth(DateUtils.formatYearMonthInt(ym));
            statementMonthly.setIssueDate(prodAcc.getBeginDate());
            statementMonthly.setBillingAddress(addressParam);
            statementMonthly.setCreditsUsed(prodAcc.getTotalUnit());
            statementMonthly.setTotalCreditDue(prodAcc.getTotalUnit());
            statementMonthly.setAmountUsed(amountWithoutTax);
            statementMonthly.setAmountUsedInSGD(totalUsedAmountInSGD);
            statementMonthly.setTotalAmountDueInSGD(afterTaxAmountInSGD);
            statementMonthly.setTotalAmountDue(amount);
            statementMonthly.setAmountStd(AmtStdEnum.USD.getCode());
            statementMonthly.setExchangeRate(exchangeRate);
            statementMonthly.setTaxRate(taxRate);
            statementMonthly.setMainContractNo(contractNo);
            String url = generateStatementPdf(statementMonthly.getInvoiceNo(), paramMap);
            statementMonthly.setUrl(url);
            commTblCompStatementMonthlyService.saveOrUpdateStatementByInvoiceNo(statementMonthly);
            return statementMonthly;
        } catch (Exception e) {
            log.error("createTopUpContract 发送invoice失败, companyId:{}, payRelId:{}", companyId, paymentIntent.getId(), e);
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.STRIPE_INVOICE_SEND_ERROR, companyId, contractNo);
            return null;
        }
    }


    // 预付费 生成发票
    public void saveInvoice4PrePaid(String kzzInvoiceId) throws MessageException {
        // 调用客找找发票接口，获取发票数据
        KzzInvoiceDetailTO invoiceDetailTO = KzzApiInterface.getInvoiceDetail(kzzInvoiceId);
        checkInvoiceDetail4PrePaid(invoiceDetailTO);

        // 读取合同信息
        KzzContractDetailTO contractDetail = KzzApiInterface.getContractDetail(invoiceDetailTO.getContractNumber());
        checkKzzContractInfoValid4PrePaid(contractDetail);

        // 解析合同产品，为了获取合同充值credits，以及合同产品的信息
        List<TblContractDeliveryProd> product = DeliveryBusinessService.transferContractProductFromKzz(contractDetail.getContractProducts());
        MsgExceptionUtils.checkIsNull(product, "msg:合同产品信息未找到", kzzInvoiceId);

        BigDecimal qyt = product.get(0).getTotalUnit();
        BigDecimal discountAmount = product.get(0).getDiscountAmount();
        String amountStd = contractDetail.getAbbreviation();// 合同币种
        String isSGD = AmtStdEnum.SGD.getCode().equals(amountStd) ? Constants.YES : Constants.NO;
        Date beginDate = new Date();// 当前时间

        // 本币币种、汇率、税率
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getCountry(), "msg:国家信息未找到", kzzInvoiceId);
        GlobalAreaEnum areaEnum = GlobalAreaEnum.getEnumByName(invoiceDetailTO.getCountry());
        MsgExceptionUtils.checkIsNull(areaEnum, "msg:开票地址国家信息错误,无法匹配到对应的二字国家码");
        BigDecimal taxRate = new BigDecimal(ConfigUtils.getConfigValueByTypeAndKey(Constants.SysConfigType.TAX_RATE, areaEnum.getNameCode(), "0"));

        // 汇率获取 接口获取
        BigDecimal exchangeRate = KzzApiInterface.getExchangeRate4TargetCurrencyToSGD(amountStd);// 货币是SGD时，汇率为1；否则从接口获取汇率计算
        MsgExceptionUtils.checkIsNull(exchangeRate, "msg:汇率信息未找到", kzzInvoiceId);

        Map<String, Object> paramMap = Maps.newHashMap();
        // 通用参数
        paramMap.put("compType", 1);// 账号类型 试用、正式
        paramMap.put("exchangeRate", NumberUtils.formatPercent4Dig(exchangeRate));// 汇率
        paramMap.put("taxRate", taxRate.multiply(BigDecimal.valueOf(100)) + "%");// 税率 固定 9%
        paramMap.put("amtStd", amountStd);// 币种单位-code

        // 抬头信息
        String toParam = invoiceDetailTO.getCustomerName();
        paramMap.put("to", toParam);// 取客户名称
        // 地址信息维度
        CompanyAddressTO companyAddress = new CompanyAddressTO(); // 国家、省、市、地址1、地址2、邮编 从开票维度中获取
        companyAddress.setAddressLine1(invoiceDetailTO.getAddressLine1());
        companyAddress.setAddressLine2(invoiceDetailTO.getAddressLine2());
        companyAddress.setCountry(areaEnum.getNameCode());
        companyAddress.setCity(invoiceDetailTO.getCity());
        companyAddress.setProvince(invoiceDetailTO.getProvince());
        companyAddress.setZipCode(invoiceDetailTO.getPostCode());
        String addressParam = InvoiceUtils.generateBillAddressParam(companyAddress);
        paramMap.put("address", addressParam);

        // 开票日期
        paramMap.put("issueDate",  DateUtils.formatDateForSg(beginDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH));// 开票日期取当前时间
        String fakeCompanyId = invoiceDetailTO.getCode();
        String invoiceNoParam = commTblCompStatementMonthlyService.generateInvoiceNo(fakeCompanyId, invoiceDetailTO.getContractNumber(), YearMonth.now());
        paramMap.put("invoiceNo", invoiceNoParam);
        paramMap.put("remark", StringUtils.trim(invoiceDetailTO.getNote()));// 预付费获取开票备注字段；后付费-取账号后台，开票备注字段文本 代码判空

        // 充值明细
        paramMap.put("creditsType", "Credits Top-up");
        paramMap.put("qty", NumberUtils.formatWithThousandSplitAndAutoDec(qyt)); //购买额度 取合同主产品的额度数量
        paramMap.put("unitPrice", NumberUtils.format2ScaleWithThousandSplit(discountAmount));// 单价 取合同主产品的单价

        // 总金额(本币) = 总额度 * 单价
        BigDecimal totalUsedAmount = invoiceDetailTO.getAmount(); // 取开票金额
        paramMap.put("totalUsedAmount", NumberUtils.format2ScaleWithThousandSplit(totalUsedAmount));
        // 税额(本币) = 总金额 * 税率
        BigDecimal taxAmount = totalUsedAmount.multiply(taxRate);// 取开票金额 * 税率
        paramMap.put("taxAmount", NumberUtils.format2ScaleWithThousandSplit(taxAmount));
        // 税后金额(本币) = 总金额 + 税额
        BigDecimal afterTaxAmount = totalUsedAmount.add(taxAmount);
        paramMap.put("afterTaxAmount", NumberUtils.format2ScaleWithThousandSplit(afterTaxAmount));

        paramMap.put("isSGD", isSGD); // 1、新加坡计税 + 9%；2、非新加坡 直接忽略
        BigDecimal totalUsedAmountInSGD = null;
        BigDecimal afterTaxAmountInSGD = null;
        if (!Constants.YES.equals(isSGD)) {// 非新加坡币种 需要转换汇率
            // 总金额(SGD) = 总金额 * 汇率
            totalUsedAmountInSGD = totalUsedAmount.multiply(exchangeRate);
            paramMap.put("totalUsedAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(totalUsedAmountInSGD));
            // 税额(SGD) = 税额(本币) * 汇率
            BigDecimal taxAmountInSGD = taxAmount.multiply(exchangeRate);
            paramMap.put("taxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(taxAmountInSGD));
            // 税后金额(SGD) = 总金额(SGD) + 税额(SGD)
            afterTaxAmountInSGD = totalUsedAmountInSGD.add(taxAmountInSGD);
            paramMap.put("afterTaxAmountInSGD", NumberUtils.format2ScaleWithThousandSplit(afterTaxAmountInSGD));
        }

        // 开票银行信息 取合同中的数据
        paramMap.put("accountName", contractDetail.getAccountName());
        paramMap.put("depositBank", contractDetail.getDepositBank());
        paramMap.put("accountNumber", contractDetail.getAccountNumber());
        paramMap.put("swiftCode", contractDetail.getSwift());

        // 存储 上传 invoice
        TblCompStatementMonthly statementMonthly = new TblCompStatementMonthly();
        statementMonthly.setInvoiceNo(invoiceNoParam);
        statementMonthly.setCompanyId(fakeCompanyId);
        statementMonthly.setStatementMonth(DateUtils.formatYearMonthInt(YearMonth.now()));
        statementMonthly.setBillingAddress(addressParam);
        statementMonthly.setInvoiceRemark(StringUtils.trim(invoiceDetailTO.getNote()));
        statementMonthly.setIssueDate(beginDate);// 开票时间取当前时间
        statementMonthly.setCreditsUsed(qyt);
        statementMonthly.setAmountUsed(totalUsedAmount);
        statementMonthly.setAmountUsedInSGD(totalUsedAmountInSGD);
        statementMonthly.setTotalCreditDue(qyt);
        statementMonthly.setTotalAmountDue(afterTaxAmount);
        statementMonthly.setTotalAmountDueInSGD(afterTaxAmountInSGD);
        statementMonthly.setAmountStd(amountStd);
        statementMonthly.setExchangeRate(exchangeRate);
        statementMonthly.setTaxRate(taxRate);
        statementMonthly.setMainContractNo(contractDetail.getCode());
        String url = generateStatementPdf(statementMonthly.getInvoiceNo(), paramMap);
        statementMonthly.setUrl(url);


        commTblCompStatementMonthlyService.saveOrUpdateStatementByInvoiceNo(statementMonthly);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("invoiceUrl", url);
        jsonObject.put("invoiceDate", beginDate);// 开票时间取当前时间
        KzzApiInterface.postJsonRequest("/openapi/kzz/invoice/editUrl/"+kzzInvoiceId, jsonObject.toString());
    }


    /**
     * 异步生成发票
     *
     * @param kzzInvoiceId 客找找发票ID
     */
    public void asyncGenerateInvoice(String kzzInvoiceId) {
        // 新开一个线程处理
        threadPoolService.execute("pool.invoice.generate", 1, new GenerateInvoiceRunnable(kzzInvoiceId));
    }


    /**
     * 月度账单管理分页查询
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    public Page<StatementMonthly4ManagementTO> pageStatement4Management(StatementMonthly4ManagementCondition condition) {
        // 查询数据
        List<StatementMonthly4ManagementTO> list = commTblCompStatementMonthlyService.page4Management(condition);

        List<String> companyIds = list.stream().map(StatementMonthly4ManagementTO::getCompanyId).distinct().collect(Collectors.toList());
        Map<String, String> companyAmountStdMap = contractDeliveryService.getCompanyAmountStdMap(companyIds);

        // 处理数据，设置描述信息
        for (StatementMonthly4ManagementTO to : list) {
            // 设置账号类型描述
            to.setCompanyTypeDesc(CompTypeEnum.getDesc(to.getCompanyType()));
            // 设置付费类型描述
            to.setPayTypeDesc(CompPayTypeEnum.getDescCnByCode(to.getPayType()));
            // 设置是否可以下载
            to.setCanDownload(StringUtils.isNotBlank(to.getUrl()));
            // 设置货币单位
            to.setAmountStd(companyAmountStdMap.getOrDefault(to.getCompanyId(), "USD"));

            if (to.getStatementMonth() != null) {
                // 设置账单月份描述
                YearMonth yearMonth = YearMonth.parse(to.getStatementMonth().toString(), DateTimeFormatter.ofPattern("yyyyMM"));
                to.setStatementMonthDesc(DateUtils.formatYearMonth(yearMonth));
            }

            if (to.getReturnStatus() != null) {
                // 设置回款状态描述
                to.setReturnStatusDesc(ReturnStatusEnum.getDescCnByCode(to.getReturnStatus()));
                if (CompPayTypeEnum.AFTER.getCode().equals(to.getPayType())) {
                    // 设置是否可以确认 - 待确认状态可以确认
                    to.setCanConfirm(ReturnStatusEnum.PENDING_CONFIRM.getCode().equals(to.getReturnStatus()));
                    // 设置是否可以重新生成 - 待回款状态可以重新生成
                    to.setCanReGenerate(ReturnStatusEnum.PENDING_RETURN.getCode().equals(to.getReturnStatus()));
                }
            }
        }

        Page<StatementMonthly4ManagementTO> page = new Page<>();
        // 复制分页信息
        if (condition.getPage() != null) {
            page.setPageNo(condition.getPage().getPageNo());
            page.setPageSize(condition.getPage().getPageSize());
            page.setOrderBy(condition.getPage().getOrderBy());
            page.setCount(condition.getPage().getCount());
        }
        page.setList(list);
        return page;
    }



    /**
     * 预览invoice
     *
     * @param statementId 发票号
     * @return 预览结果
     * @throws MessageException 异常
     * @throws IOException      异常
     */
    public String previewStatementMonthly(String statementId) throws MessageException, IOException {
        TblCompStatementMonthly statement = commTblCompStatementMonthlyService.get(statementId);
        MsgExceptionUtils.checkIsNull(statement, "msg:invoice不存在");
        YearMonth ym = YearMonth.parse(statement.getStatementMonth().toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        TblContractDelivery contractDelivery = contractDeliveryService.getByCompanyId(statement.getCompanyId()).stream()
                .filter(c -> StringUtils.isBlank(statement.getMainContractNo()) || StringUtils.equals(statement.getMainContractNo(), c.getContractNo()))
                .findAny().orElse(null);
        MsgExceptionUtils.checkIsNull(contractDelivery, "msg:合同不存在");

        Company company = commSysCompanyService.get(statement.getCompanyId());
        MsgExceptionUtils.checkIsNull(company);
        TblCompStatementMonthly statementMonthly = this.saveOrPreviewInvoice4After(contractDelivery, company, ym,
                statement.getTaxRate(), statement.getExchangeRate(), true);
        return Objects.isNull(statementMonthly) ? null : statementMonthly.getUrl();
    }



    /**
     * 重新生成月度报表 并推送客找找
     *
     * @param statementId
     * @throws MessageException
     * @throws IOException
     */
    public void reGenerateStatementMonthly(String statementId) throws MessageException, IOException {
        TblCompStatementMonthly statement = commTblCompStatementMonthlyService.get(statementId);
        MsgExceptionUtils.checkIsNull(statement, "msg:invoice不存在");
        MsgExceptionUtils.failBuild(ReturnStatusEnum.RETURNED.getCode().equals(statement.getReturnStatus()), "msg:已回款，不允许重新生成");
        YearMonth ym = YearMonth.parse(statement.getStatementMonth().toString(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        TblContractDelivery contractDelivery = contractDeliveryService.getByCompanyId(statement.getCompanyId()).stream()
                .filter(c -> StringUtils.isBlank(statement.getMainContractNo()) || StringUtils.equals(statement.getMainContractNo(), c.getContractNo()))
                .findAny().orElse(null);
        MsgExceptionUtils.checkIsNull(contractDelivery, "msg:合同不存在");

        Company company = commSysCompanyService.get(statement.getCompanyId());
        MsgExceptionUtils.checkIsNull(company);
        TblCompStatementMonthly statementMonthly = this.saveOrPreviewInvoice4After(contractDelivery, company, ym,
                statement.getTaxRate(), statement.getExchangeRate(), false);

        // 触发推送账单到客找找，出错则钉钉邮件提醒
        pushAfterConfirmContract2Kzz(statementMonthly);
    }



    /**
     * 推送合同信息
     *
     * @param tblCompStatementMonthly
     * @return
     */
    public void pushAfterConfirmContract2Kzz(TblCompStatementMonthly tblCompStatementMonthly) throws MessageException {
        try {
            //金额为0直接 设置为已回款 且不推送到客找找 updated for v2.2.5 fengsw KNZT-7570【优化】【后付费合同】金额为0的Invoice不推送收入数据
            if (BigDecimal.ZERO.compareTo(tblCompStatementMonthly.getTotalAmountDue()) == 0) {
                tblCompStatementMonthly.setReturnStatus(ReturnStatusEnum.RETURNED.getCode());
            } else {
                String contractNo = tblCompStatementMonthly.getMainContractNo();//直接取主合同编号
                MsgExceptionUtils.checkIsNull(contractNo, "msg:未找到对应的交付合同编号信息", tblCompStatementMonthly.getCompanyId());
                log.info("push afterConfirmContract2Kzz, contractNo:{}, invoiceNo:{}, dateInt:{} start", contractNo, tblCompStatementMonthly.getInvoiceNo(), tblCompStatementMonthly.getStatementMonth());
                KzzContractDetailTO kzzContractDetail = KzzApiInterface.getContractDetail(contractNo);
                MsgExceptionUtils.checkIsNull(kzzContractDetail, "msg:未找到对应的交付合同信息", contractNo);
                StatementPushDetailTO statementPushDetailTO = StatementPushDetailTO.build(contractNo, tblCompStatementMonthly);
                KzzContractCreateRequestTO createRequestTO = buildAfterAccountContractInfo(statementPushDetailTO, kzzContractDetail);
                String requestJSONStr = JSON.toJSONString(createRequestTO);
                String responseStr = KzzApiInterface.postJsonRequest("/openapi/kzz/contract/v2/add", requestJSONStr);
                JSONObject jsonObj = JsonUtils.parseObject(responseStr);
                MsgExceptionUtils.checkIsNull(jsonObj, "接口响应内容为空");
                MsgExceptionUtils.failBuild(!"OK".equals(jsonObj.getString("status")), jsonObj.getString("error"));
                tblCompStatementMonthly.setKzzContractNo(createRequestTO.getCode());
                tblCompStatementMonthly.setReturnStatus(ReturnStatusEnum.PENDING_RETURN.getCode());
            }
            commTblCompStatementMonthlyService.saveOrUpdateStatementByContractMonth(tblCompStatementMonthly);
        } catch (MessageException e) {
            log.error("push contract error{}, invoiceNo:{}, dateInt:{}", e, tblCompStatementMonthly.getInvoiceNo(), tblCompStatementMonthly.getStatementMonth());
            throw new MessageException(e.getMessage());
        }
    }


    /**
     * 根据invoice构建创建合同请求
     *
     * @return
     */
    private KzzContractCreateRequestTO buildAfterAccountContractInfo(StatementPushDetailTO statementPushDetailTO, KzzContractDetailTO kzzContractDetail) throws MessageException {
        KzzContractCreateRequestTO createRequestTO = new KzzContractCreateRequestTO();
        YearMonth yearMonth = YearMonth.parse(statementPushDetailTO.getStatementMonth() + "", DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        checkKzzContractInfoValid(kzzContractDetail);
        // updated for v2.2.7 fengsw KNZT-7684
        String contractNoKzz = statementPushDetailTO.getContractNo() + "-" + "H" + statementPushDetailTO.getInvoiceNo().replace("INV", "");
        buildPushContractBaseInfo(contractNoKzz, statementPushDetailTO.getAmountUsed(), yearMonth, kzzContractDetail, createRequestTO);
        // 构建推送产品信息
        buildPushContractProductInfo(createRequestTO, kzzContractDetail);
        // 构建回款信息
        buildPushContractPaymentInfo(createRequestTO, kzzContractDetail, contractNoKzz, statementPushDetailTO.getAmountUsed(), statementPushDetailTO.getInvoiceNo());
        // 构建自定义信息
        buildPushContractCustomerInfo(createRequestTO, kzzContractDetail);
        return createRequestTO;
    }

    private void buildPushContractCustomerInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetail) throws MessageException {
        JSONObject customObjectValues = new JSONObject();// 合同自定义字段
        //开票⽅式-先票后款
        String billType = kzzContractDetail.getInvoiceMethod();
        if (StringUtils.isNotBlank(billType)) {
            customObjectValues.put("CFLD2023033100006", Lists.newArrayList(billType));
        }
        //回款⽅式-后付费
        String paymentType = kzzContractDetail.getPayType();
        if (StringUtils.isNotBlank(paymentType)) {
            customObjectValues.put("CFLD2023033100004", Lists.newArrayList(paymentType));
        }
        //结算周期-月结
        String statementTerm = sysConfigCacheService.getCreateContractConfigValueByKey("afterPaymentType");
        MsgExceptionUtils.checkIsNull(statementTerm, "结算周期缺失，请检查");
        customObjectValues.put("CFLD2023033100002", Lists.newArrayList(statementTerm));

        // 收款⽅式-⽹银转账
        String paymentMethod = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300081");
        MsgExceptionUtils.checkIsNull(paymentMethod, "收款⽅式缺失，请检查");
        customObjectValues.put("CFLD202208300081", Lists.newArrayList(paymentMethod));
        //客户需要合同-不需要合同
        String customerNeedContract = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300054");
        MsgExceptionUtils.checkIsNull(customerNeedContract, "客户需要合同配置项缺失，请检查");
        customObjectValues.put("CFLD202208300054", Lists.newArrayList(customerNeedContract));
        //客户是否先⽤印-否
        String needSign = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032900009");
        MsgExceptionUtils.checkIsNull(needSign, "客户是否先⽤印配置项缺失，请检查");
        customObjectValues.put("CFLD2023032900009", Lists.newArrayList(needSign));
        //是否标准模板-否
        String standardTemplate = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300057");
        MsgExceptionUtils.checkIsNull(standardTemplate, "是否标准模板配置项缺失，请检查");
        customObjectValues.put("CFLD202208300057", Lists.newArrayList(standardTemplate));
        //我司签约主体
        String ourSigningParty = kzzContractDetail.getSigningParty();
        if (StringUtils.isNotBlank(ourSigningParty)) {
            customObjectValues.put("CFLD202208300069", Lists.newArrayList(ourSigningParty));
        }
        // 客户开票名称
        String customerInvoicingName = kzzContractDetail.getInvoiceName();
        if (StringUtils.isNotBlank(customerInvoicingName)) {
            customObjectValues.put("CFLD202208300090", customerInvoicingName);
        }
        // 收件⼈姓名
        String recipientName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300098");
        MsgExceptionUtils.checkIsNull(recipientName, "收件⼈姓名配置项缺失，请检查");
        customObjectValues.put("CFLD202208300098", recipientName);
        // 收件人电话
        String recipientPhone = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300101");
        MsgExceptionUtils.checkIsNull(recipientPhone, "收件人电话配置项缺失，请检查");
        customObjectValues.put("CFLD202208300101", recipientPhone);
        // 收件人地址
        String recipientAddress = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300100");
        MsgExceptionUtils.checkIsNull(recipientAddress, "收件人地址配置项缺失，请检查");
        customObjectValues.put("CFLD202208300100", recipientAddress);
        // 合同来源-线下合同
        String contractSource = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023040400002");
        MsgExceptionUtils.checkIsNull(contractSource, "合同来源配置项缺失，请检查");
        customObjectValues.put("CFLD2023040400002", Lists.newArrayList(contractSource));
        //主产品-KYC国际业务
        customObjectValues.put("CFLD202208300061", Lists.newArrayList("KYC国际业务"));
        customObjectValues.put("CFLD2023042500004", kzzContractDetail.getServiceYear());//服务年限
        createRequestTO.setCustomObjectValues(customObjectValues);
    }



    // 校验开票信息
    private void checkInvoiceDetail4PrePaid(KzzInvoiceDetailTO invoiceDetailTO) throws MessageException {
        MsgExceptionUtils.checkIsNull(invoiceDetailTO, "msg:发票信息未找到");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getCode(), "msg:发票编号缺失");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getCustomerName(), "msg:开票客户缺失");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getContractNumber(), "msg:开票关联合同编号缺失");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getAddressLine1(), "msg:开票地址缺失");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getCountry(), "msg:开票国家缺失");
        MsgExceptionUtils.checkIsNull(invoiceDetailTO.getAmount(), "msg:开票金额缺失");
    }

    // 校验合同信息
    private void checkKzzContractInfoValid4PrePaid(KzzContractDetailTO contractDetail) throws MessageException {
        MsgExceptionUtils.checkIsNull(contractDetail, "msg:合同信息未找到");
        MsgExceptionUtils.checkIsNull(contractDetail.getCode(), "msg:合同编号缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getAccountName(), "msg:合同开票银行账户名称缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getDepositBank(), "msg:合同开票银行名称缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getAccountNumber(), "msg:合同开票银行账号缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getSwift(), "msg:合同开票银行SWIFT码缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getAbbreviation(), "msg:合同币种缺失");
        MsgExceptionUtils.checkIsNull(contractDetail.getCountry(), "msg:合同关联客户所在国家缺失");
        MsgExceptionUtils.failBuild(contractDetail.getServiceYear() <= 0, "msg:合同年限缺失");
    }


    private void buildPushContractBaseInfo(String contractNoKzz, BigDecimal amount, YearMonth yearMonth, KzzContractDetailTO kzzContractDetail, KzzContractCreateRequestTO createRequestTO) throws MessageException {
        createRequestTO.setCode(contractNoKzz);// 合同编号
        createRequestTO.setCustomerId(kzzContractDetail.getCustomerId());// 客户id
        createRequestTO.setContactId(kzzContractDetail.getContactId());// 联系人
        createRequestTO.setRenewType(1);// 续约类型 一次性合同
        createRequestTO.setContractType(3);//类型-框架子合同
        createRequestTO.setSkipApproval(1);// 默认跳过审批，否则客找找无法保存指定的开始日期和到期日期
        createRequestTO.setAmount(amount);//合同金额
        createRequestTO.setTotal(amount);//合同原金额
        createRequestTO.setOrderDiscountRate(new BigDecimal("100"));// 整单折扣率
        createRequestTO.setComDiscountRate(new BigDecimal("100"));// 综合折扣率
        //开始时间：推送周期当月1⽇；结束日期：推送周期当月最后⼀天
        LocalDate beginDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        createRequestTO.setBeginDate(DateUtils.formatDate(beginDate));
        createRequestTO.setExpiredDate(DateUtils.formatDate(endDate));
        //合同名称 【2024/1/1-2024/1/31】 【客户名称】消耗账单
        String contractName = DateUtils.formatDate(beginDate, "yyyy/MM/dd") + "-" + DateUtils.formatDate(endDate, "yyyy/MM/dd") + kzzContractDetail.getCustomerName() + "消耗账单";
        createRequestTO.setName(contractName);
        createRequestTO.setParentContractId(kzzContractDetail.getContractId());// 关联主合同
        createRequestTO.setCurrencyId(kzzContractDetail.getCurrencyId());
        createRequestTO.setSignerDate(DateUtils.getDate(DateUtils.DATETIME_FORMAT));// 签订时间-当前时间,精确到时分秒
        createRequestTO.setCreateDate(createRequestTO.getSignerDate());// 创建时间-当前时间
        //合同状态-签约
        String contractStatus = sysConfigCacheService.getCreateContractConfigValueByKey("contractStatus");
        MsgExceptionUtils.checkIsNull(contractStatus, "合同状态配置项缺失，请检查");
        createRequestTO.setStatus(Integer.parseInt(contractStatus));
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        // updated for v2.2.7 fengsw KNZT-7687 跟进人 取主合同的客户负责人id
        createRequestTO.setSignerId(Objects.nonNull(kzzContractDetail.getCustomerUserId()) ? kzzContractDetail.getCustomerUserId() : singerId);
        createRequestTO.setCreateBy(singerId);// 创建人 取默认内置用户
    }

    /**
     * 检查同基本信息是否存在缺失
     *
     * @param kzzContractDetail
     * @throws MessageException
     */
    private void checkKzzContractInfoValid(KzzContractDetailTO kzzContractDetail) throws MessageException {
        MsgExceptionUtils.failBuild(kzzContractDetail.getSignerId() == 0L, "签订人信息缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getCurrencyId() == 0L, "合同货币信息缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getContractId() == 0L, "关联主合同缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getContactId() == 0L, "联系人缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getCustomerId() == 0L, "客户id缺失，请检查");
        MsgExceptionUtils.failBuild(kzzContractDetail.getServiceYear() <= 0, "服务年限缺失，请检查");
    }

    /**
     * 回款计划填充
     *
     * @param createRequestTO
     * @param kzzContractDetailTO
     * @param contractNoKzz
     * @param amount
     * @param note
     * @throws MessageException
     */
    private void buildPushContractPaymentInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetailTO, String contractNoKzz, BigDecimal amount, String note) throws MessageException {
        KzzContractCreateRequestTO.ContractPayment contractPayment = new KzzContractCreateRequestTO.ContractPayment();
        contractPayment.setCode(contractNoKzz + ".1");
        contractPayment.setAmount(amount);
        contractPayment.setType(2);// 回款类型，常规
        contractPayment.setNote(note);// 备注
        YearMonth now = YearMonth.now();
        LocalDate localDate = now.atEndOfMonth();
        contractPayment.setPaymentDate(DateUtils.toDate(localDate));// 预计回款时间为推送当前时间的当月30天
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        // updated for v2.2.7 fengsw KNZT-7687 跟进人 取主合同的客户负责人id
        contractPayment.setLeaderId(Objects.nonNull(kzzContractDetailTO.getCustomerUserId()) ? kzzContractDetailTO.getCustomerUserId() : singerId);
        contractPayment.setCreateBy(singerId);// 创建人 取默认内置用户
        JSONObject obj = new JSONObject();// 自定义字段
        // 回款条件-开票之日起
        String paymentConditions = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032100002");
        MsgExceptionUtils.checkIsNull(paymentConditions, "回款条件缺失，请检查");
        obj.put("CFLD2023032100002", Lists.newArrayList(paymentConditions));
        //账龄天数-30
        String agingDays = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202111050008");
        MsgExceptionUtils.checkIsNull(agingDays, "账龄天数缺失，请检查");
        obj.put("CFLD202111050008", Integer.valueOf(agingDays));
        contractPayment.setCustomObjectValues(obj);
        createRequestTO.setPayments(Lists.newArrayList(contractPayment));
    }


    /**
     * added for v2.1.9 fengsw KNZT-7061
     * 构建产品信息时，需要给主产品 设置数量、总金额 通过消耗的总金额，除以产品单价计算得出
     *
     * @param createRequestTO
     * @param kzzContractDetail
     */
    private void buildPushContractProductInfo(KzzContractCreateRequestTO createRequestTO, KzzContractDetailTO kzzContractDetail) {
        if (CollectionUtils.isNotEmpty(kzzContractDetail.getContractProducts())) {
            String afterMainProductCodeHK = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCodeHK");
            String afterMainProductCodeCN = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCodeCN");
            String afterMainProductCode = sysConfigCacheService.getCreateContractConfigValueByKey("afterMainProductCode");
            Integer mainProductCNIndex = null;
            Integer mainProductHKIndex = null;
            Integer mainProductIndex = null;
            List<KzzContractCreateRequestTO.ContractProduct> contractProductList = new ArrayList<>();
            for (int i = 0; i < kzzContractDetail.getContractProducts().size(); i++) {
                KzzContractDetailTO.ContractProduct kzzContractProduct = kzzContractDetail.getContractProducts().get(i);
                KzzContractCreateRequestTO.ContractProduct contractProduct = new KzzContractCreateRequestTO.ContractProduct();
                contractProduct.setProductId(kzzContractProduct.getProductId());
                contractProduct.setPeriod(kzzContractProduct.getPeriod());
                contractProduct.setTimeLimits(kzzContractProduct.getTimeLimits());
                contractProduct.setPrice(kzzContractProduct.getPrice());
                contractProduct.setDiscountPrice(kzzContractProduct.getDiscountPrice());
                contractProduct.setDiscountRate(kzzContractProduct.getDiscountRate());
                contractProduct.setQuantity(kzzContractProduct.getQuantity());
                contractProduct.setTotalPrice(kzzContractProduct.getTotalPrice());
                String productCode = kzzContractProduct.getProductCode();
                if (afterMainProductCode.equals(productCode)) {
                    mainProductIndex = i;
                } else if (afterMainProductCodeCN.equals(productCode)) {
                    mainProductCNIndex = i;
                } else if (afterMainProductCodeHK.equals(productCode)) {
                    mainProductHKIndex = i;
                }
                contractProductList.add(contractProduct);
            }
            // 确定目标主产品索引
            Integer targetIndex = null;
            if (mainProductIndex != null) {
                targetIndex = mainProductIndex;
            } else if (mainProductCNIndex != null) {
                targetIndex = mainProductCNIndex;
            } else if (mainProductHKIndex != null) {
                targetIndex = mainProductHKIndex;
            }

            if (targetIndex != null) {
                KzzContractCreateRequestTO.ContractProduct mainProduct = contractProductList.get(targetIndex);
                if (BigDecimal.ZERO.compareTo(mainProduct.getDiscountPrice()) != 0) {
                    // 计算数量和总价
                    BigDecimal divide = createRequestTO.getAmount().divide(mainProduct.getDiscountPrice(), 2, RoundingMode.HALF_UP);
                    mainProduct.setQuantity(divide.intValue());
                    mainProduct.setTotalPrice(createRequestTO.getAmount());
                } else {
                    log.error("main product price is zero, contractNo:{}", createRequestTO.getCode());
                }
            }
            createRequestTO.setProducts(contractProductList);
        }
    }

    /**
     * 检查invoice 是否已经回款
     * 回款逻辑判断，根据回款类型=已核销的回款单的总金额计总 比较账号金额是否相等，如果相等则代表已经回款，更新invoice的回款状态，并给用户增加额度
     */
    public void checkInvoicePaymentStatus() {
        List<TblCompStatementMonthly> statementMonthlyList = commTblCompStatementMonthlyService.getUnfinishedPayments();
        for (TblCompStatementMonthly statementMonthly : statementMonthlyList) {
            try {
                KzzContractDetailTO contractDetail = KzzApiInterface.getContractDetail(statementMonthly.getKzzContractNo());
                MsgExceptionUtils.checkIsNull(contractDetail, "msg:合同信息未找到", statementMonthly.getKzzContractNo());
                if (CollectionUtils.isNotEmpty(contractDetail.getPaymentSheets())) {
                    for (KzzContractDetailTO.PaymentSheet kzzPaymentSheet : contractDetail.getPaymentSheets()) {
                        KzzContractPaymentSheet paymentSheet = KzzApiInterface.getPaymentSheet(kzzPaymentSheet.getCode());
                        if (Objects.nonNull(paymentSheet) && Objects.equals(paymentSheet.getType(), 1) && paymentSheet.getAmount().compareTo(statementMonthly.getAmountUsed()) == 0) {
                            statementMonthly.setReturnStatus(ReturnStatusEnum.RETURNED.getCode());
                            commTblCompStatementMonthlyService.saveOrUpdateStatementByContractMonth(statementMonthly);
                            transactionBusinessService.createTransaction4Return(statementMonthly);
                        }
                    }
                }
            } catch (MessageException e) {
                log.error("check payment status failed, dateInt:{}, invoiceNo:{}, contractNo:{}, error:{}", statementMonthly.getStatementMonth(), statementMonthly.getInvoiceNo(), statementMonthly.getKzzContractNo(), e);
            }
        }
    }




    /**
     * 生成 invoice pdf
     * added for v1.9.5 KNZT-4117
     * updated for lvcy v2.0.6 KNZT-5499
     *
     * @param invoiceNo
     * @param paramMap
     * @return String
     */
    private String generateStatementPdf(String invoiceNo, Map<String, Object> paramMap) throws MessageException {
        String route = String.format("invoice/%s.pdf", invoiceNo);

        SysTemplate template = commSysTemplateService.getByTemplateName("pdf_statement_monthly");

        File pdf = null;
        try {
            pdf = PdfUtils.createPdf("pdf_statement_monthly", template.getTemplateContent(), paramMap);
            String url = HuaweiObsServUtils.getInstance().putObject(route, pdf);
            String decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.name());
            log.info("generateStatementPdfTest uploadPdf end, url:{}", decodedUrl);
            return decodedUrl;
        } catch (Exception e) {
            log.error("", e);
            throw new MessageException("", e);
        } finally {
            if (Objects.nonNull(pdf)) {
                FileUtils.deleteFile(pdf.getPath());
            }
        }
    }

    /**
     * 生成 invoice pdf (测试版本 - 直接从本地文件读取模板)
     * 使用本地文件 invoice_20250707.ftl 作为模板
     *
     * @param invoiceNo 发票号
     * @param paramMap  模板参数
     * @return String PDF文件的URL
     * @throws MessageException 异常
     */
    public String generateStatementPdfTest(String invoiceNo, Map<String, Object> paramMap) throws MessageException {
        String route = String.format("invoice/%s.pdf", invoiceNo);
        String templateId = "invoice_20250707";

        File pdf = null;
        try {
            // 从本地文件系统读取模板内容
            String templateContent = readTemplateFromLocalFile("invoice_20250707.ftl");

            // 使用读取到的模板内容生成PDF
            pdf = PdfUtils.createPdf(templateId, templateContent, paramMap);
            String url = HuaweiObsServUtils.getInstance().putObject(route, pdf);
            String decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.name());
            log.info("generateStatementPdfTest uploadPdf end, templateId:{}, url:{}", templateId, decodedUrl);
            return decodedUrl;
        } catch (Exception e) {
            log.error("generateStatementPdfTest error, invoiceNo:{}", invoiceNo, e);
            throw new MessageException("生成PDF失败", e);
        } finally {
            if (Objects.nonNull(pdf)) {
                FileUtils.deleteFile(pdf.getPath());
            }
        }
    }


    /**
     * 从本地文件系统读取模板文件内容
     *
     * @param templateFileName 模板文件名
     * @return String 模板内容
     * @throws IOException 文件读取异常
     */
    private String readTemplateFromLocalFile(String templateFileName) throws IOException {
        // 构建模板文件路径 - 相对于项目根目录
        Path templatePath = Paths.get("D:/workspace/global_backend", "backend/webjob/src/main/resources/template", templateFileName);
        if (Files.exists(templatePath)) {
            log.info("找到模板文件: {}", templatePath.toAbsolutePath());
        } else {
            templatePath = null;
        }

        log.info("读取本地模板文件: {}", templatePath.toAbsolutePath());

        // 检查文件是否存在
        if (!Files.exists(templatePath)) {
            throw new IOException("模板文件不存在: " + templatePath.toAbsolutePath());
        }

        // 读取文件内容
        byte[] bytes = Files.readAllBytes(templatePath);
        String content = new String(bytes, StandardCharsets.UTF_8);

        log.info("成功读取模板文件，内容长度: {} 字符", content.length());
        return content;
    }


    /**
     * 掩码公司ID,保留前6位和后4位
     * added for v1.9.5 KNZT-4117
     *
     * @param companyId
     * @return String
     */
    private static String maskCompanyId(String companyId) {
        // 检查公司ID长度是否符合要求
        if (companyId == null || companyId.length() < 10) {
            return companyId;
        }
        // 保留前面的6位
        String prefix = companyId.substring(0, 6);
        // 保留后面的4位
        String suffix = companyId.substring(companyId.length() - 4);
        // 中间用4个星号替换
        return prefix + "****" + suffix;
    }

}
