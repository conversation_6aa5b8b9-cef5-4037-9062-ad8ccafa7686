package com.backend.common.service;

import com.backend.common.entity.mapping.FunctionCountInfoTO;
import com.backend.common.entity.mapping.FunctionTableInfoTO;
import com.backend.common.mapper.SysCompInfoFuncCountDao;
import com.backend.common.modules.api.service.CommTblCompApiOrderService;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryProdAccService;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.setting.model.FunctionCountTO;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.jee.commons.service.CrudService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.BeanUtil;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.IdGenUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCountHis;
import com.qcc.frame.jee.modules.sys.entity.SysFunctionTableCountConfig;
import com.qcc.frame.jee.modules.sys.entity.mapping.SysFunctionTableCountConfigPO;
import com.qcc.frame.jee.modules.sys.service.SysConfigService;
import com.qcc.frame.jee.modules.sys.service.SysFunctionTableCountConfigService;
import com.qcc.frame.jee.modules.sys.service.SysFunctionTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class CommSysCompInfoFuncCountService extends CrudService<SysCompInfoFuncCountDao, SysCompInfoFuncCount> {
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private CommTblCompCorporatesService tblCompCorporatesService;
    @Autowired
    private CommTblCompCorporatesRadarService tblCompCorporatesRadarService;
    @Autowired
    private CommSysCompPluginAccessLogService sysCompPluginAccessLogService;
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private SysFunctionTableCountConfigService sysFunctionTableCountConfigService;
    @Autowired
    private CommSysCompInfoFuncCountHisService commSysCompInfoFuncCountHisService;
    @Autowired
    private SysFunctionTableService sysFunctionTableService;
    @Autowired
    private CommTblCompApiOrderService apiOrderService;
    @Autowired
    private CommTblContractDeliveryProdAccService commTblContractDeliveryProdAccService;

    //added for v2.0.3 ZS-417 服务列表取数从调用专业版接口，修改为本地获取
    public List<FunctionCountTO> listFunctionCount(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            return null;
        }
        return dao.listFunctionCount(companyId);
    }

    /**
     * added for v2.0.4 ZS-416 服务列表详情查询
     * @param companyId
     * @return List<SysCompInfoFuncCount>
     */
    public List<SysCompInfoFuncCount> getSysCompFuncCountDetailByCompanyIdIncludeDel(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            return null;
        } else {
            return dao.getSysCompFuncCountDetailByCompanyIdIncludeDel(companyId);
        }
    }

    /**
     * added for v1.8.8 KNZT-4033
     * 根据companyId查
     *
     * @param companyId
     * @return List<SysCompInfoFuncCount>
     */
    public List<SysCompInfoFuncCount> getSysCompFuncCountDetailByCompanyId(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            return null;
        } else {
            return dao.getSysCompFuncCountDetailByCompanyId(companyId);
        }
    }

    /**
     * 获取company下唯一的额度
     * added for v1.9.8 KNZT-4538
     *
     * @param companyId
     * @return SysCompInfoFuncCount
     */
    public SysCompInfoFuncCount readOnlyGetCompFuncCount(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            return null;
        } else {
            return dao.getSysCompFuncCountDetailByCompanyId(companyId).get(0);
        }
    }

    /**
     * added for v2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口
     *
     * @param roleIds
     * @return List<SysCompInfoFuncCount>
     */
    public List<FunctionTableInfoTO> getFunctionTableInfoByRoleIds(List<String> roleIds) {
        if (roleIds == null || roleIds.size() <= 0) {
            return new ArrayList<>();
        } else {
            return dao.getFunctionTableInfoByRoleIds(roleIds);
        }
    }

    /**
     * added for v2.0.5 ZS-416 根据权限id查询服务列表
     * updated for v2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口
     *
     * @param companyId
     * @param roleIds
     * @return List<SysCompInfoFuncCount>
     */
    public List<FunctionTableInfoTO> getFunctionTableInfoByRoleIdsAndCompanyId(String companyId, List<String> roleIds) {
        if (StringUtils.isBlank(companyId) || roleIds == null || roleIds.size() <= 0) {
            return new ArrayList<>();
        } else {
            return dao.getFunctionTableInfoByRoleIdsAndCompanyId(companyId, roleIds);
        }
    }

    /**
     * added for v2.0.6 for 2.0.6 ZS-455 【角色管理】提供根据角色id查询服务列表接口
     * 获取本地已分配总额度（汇总）
     *
     *
     * @return List<FunctionCountInfoTO>
     */
    public FunctionCountInfoTO getFunctionCountInfoByFunctionTableId(String functionTableId){
        return dao.getFunctionCountInfoByFunctionTableId(functionTableId);
    }

    public List<FunctionCountTO> getFunctionCountListBySysConfigForAdmin() {
        List<FunctionCountTO> functionCountInfoList = new ArrayList<>();
        List<SysFunctionTableCountConfigPO> configList = sysFunctionTableCountConfigService.listFunctionTableCountConfigInfo();
        if(CollectionUtils.isEmpty(configList)) {
            for (SysFunctionTableCountConfigPO tableCountConfig : configList) {
                FunctionCountTO functionCountTO = new FunctionCountTO();
                // 处理已分配额度
                FunctionCountInfoTO countInfoTO = this.getFunctionCountInfoByFunctionTableId(tableCountConfig.getFunctionTableId());
                functionCountTO.setAllocatedAmount(countInfoTO.getTotalCount());
                functionCountTO.setServiceContent(tableCountConfig.getFunctionName());
                functionCountTO.setFunctionTableId(countInfoTO.getFunctionTableId());
                functionCountInfoList.add(functionCountTO);
            }
        }
        return functionCountInfoList;
    }

    public SysCompInfoFuncCount lockByCompanyId(String companyId) {
        return dao.lockByFuncTableId(companyId);
    }

    /**
     * 获取服务信息无消耗计数
     * @param companyId
     * @param functionTableId
     * @return
     */
    public SysCompInfoFuncCount getCompInfoFuncCountByFuncTableId(String companyId, String functionTableId) {
        SysCompInfoFuncCount result = dao.getCompInfoFuncCountByFuncTableId(companyId, functionTableId);
        return result;
    }
    /**
     * 获取服务信息含消耗计数
     * @param companyId
     * @param functionTableId
     * @return
     */
    @Deprecated
    public SysCompInfoFuncCount getCompInfoFuncCountByFuncTableIdWithConsumed(String companyId, String functionTableId) {
       /* SysCompInfoFuncCount result = dao.getCompInfoFuncCountByFuncTableId4PageConsumedCount(companyId, functionTableId);
        if(result != null) {
            if(Constants.FunctionTable.ID_REPORT_ID.equals(result.getFunctionTableId())) {
                BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, Collections.singletonList(UnitGroup2FunctionTableMapEnum.getUnitGroupByFunctionTableId(functionTableId)));
                BigDecimal apiConsumedCount = apiOrderService.sumOrderTotalUnitByCompanyId(companyId);
                consumedCount = consumedCount.add(apiConsumedCount);
                result.setConsumedCount(consumedCount);
            }
            if(Constants.FunctionTable.ID_HK_SERVICE_ID.equals(result.getFunctionTableId())) {
                BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, UnitGroupEnum.globalFunctionTableUnitGroup());
                result.setConsumedCount(consumedCount);
            }
        }
        return result;*/
        return null;
    }

    @Deprecated
    public List<SysCompInfoFuncCount> getCompInfoFuncCountByFuncTableIdWithConsumed(String companyId) {
        /*List<SysCompInfoFuncCount> funcCountList = dao.getSysCompFuncCountDetailByCompanyId(companyId);
        for (SysCompInfoFuncCount funcCount : funcCountList) {
            if(Constants.FunctionTable.ID_REPORT_ID.equals(funcCount.getFunctionTableId())) {
                BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, Collections.singletonList(UnitGroup2FunctionTableMapEnum.getUnitGroupByFunctionTableId(Constants.FunctionTable.ID_REPORT_ID)));
                BigDecimal apiConsumedCount = apiOrderService.sumOrderTotalUnitByCompanyId(companyId);
                consumedCount = consumedCount.add(apiConsumedCount);
                funcCount.setConsumedCount(consumedCount);
            }
            if(Constants.FunctionTable.ID_HK_SERVICE_ID.equals(funcCount.getFunctionTableId())) {
                BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, UnitGroupEnum.globalFunctionTableUnitGroup());
                funcCount.setConsumedCount(consumedCount);
            }
        }
        return funcCountList;*/
        return null;
    }

// removed for v1.9.8 KNZT-4538
//    //added for v2.0.3 ZS-417 服务列表取数从调用专业版接口，修改为本地获取
//    public List<FunctionCountTO> listFunctionCount(){
//        String companyId = UserUtils.getUserCompanyId();
//        List<FunctionCountTO> result = new ArrayList<>();
//        List<FunctionCountTO> functionCountTOList = listFunctionCount(companyId);
//        for (FunctionCountTO functionCountTO : functionCountTOList) {
//            if(Constants.FunctionTable.ID_REPORT_ID.equals(functionCountTO.getFunctionTableId()) || Constants.FunctionTable.ID_HK_SERVICE_ID.equals(functionCountTO.getFunctionTableId())) {
//                List<String> unitGroupList;
//                if (Constants.FunctionTable.ID_HK_SERVICE_ID.equals(functionCountTO.getFunctionTableId())) {
//                    unitGroupList = UnitGroupEnum.globalFunctionTableUnitGroup();
//                } else {
//                    unitGroupList = Collections.singletonList(UnitGroup2FunctionTableMapEnum.getUnitGroupByFunctionTableId(functionCountTO.getFunctionTableId()));
//                }
//                BigDecimal consumedCount = commTblCompReportOrderService.sumOrderTotalUnitByCompanyId(companyId, unitGroupList);
//                functionCountTO.setConsumedCount(consumedCount);
//                functionCountTO.setConsumedUnit(consumedCount);
//                functionCountTO.setRemainingCount(functionCountTO.getTotalCount().subtract(consumedCount));
//                result.add(functionCountTO);
//            }
//        }
//        return result;
//    }

    /**
     * added for v2.0.5 ZS-416 服务列表的额度产生变化时复制服务列表数据到历史表，并更新服务列表的额度
     * updated for v2.0.6 ZS-452 【角色管理】角色信息编辑与保存 涉及服务列表的额度编辑与保存
     *
     * @param companyId
     * @param functionTable
     * @return void
     */
    @Deprecated
    public void copyCompInfoFuncCountToHisAndUpdateCompInfoFuncCount(String companyId, List<FunctionCountInfoTO> functionTable) throws MessageException {
        /*
        // 校验参数是否有误
        if (checkFunctionIsCorrect(functionTable)) {
            List<SysCompInfoFuncCount> funcCountList = this.getSysCompFuncCountDetailByCompanyIdIncludeDel(companyId);
            Map<String, List<SysCompInfoFuncCount>> listMap = funcCountList.stream().collect(Collectors.groupingBy(SysCompInfoFuncCount::getFunctionTableId));
            for (FunctionCountInfoTO functionCountInfoTO : functionTable) {
                if (StringUtils.isBlank(functionCountInfoTO.getFunctionTableId()) || functionCountInfoTO.getTotalCount() == null || BigDecimal.ZERO.compareTo(functionCountInfoTO.getTotalCount()) > 0) {
                    throw new MessageException("msg:服务列表" + functionCountInfoTO.getFunctionTableId() + "信息缺失");
                }
                // 列表未给到的数据，则做删除
                if(funcCountList.size() > 0 && listMap.containsKey(functionCountInfoTO.getFunctionTableId())){
                    List<SysCompInfoFuncCount> infoFuncCounts = listMap.get(functionCountInfoTO.getFunctionTableId());
                    SysCompInfoFuncCount funcCount = infoFuncCounts.get(0);
                    // added for v2.0.6 ZS-452 【角色管理】角色信息编辑与保存 服务列表额度校验 必须大于等于已消耗额度
                    // added for KNZT-1420 香港服务额度交由开放平台管理，所以配置香港服务列表时，额度不校验
                    if (funcCount.getTotalCount().intValue() != functionCountInfoTO.getTotalCount().intValue()) {
                        funcCount.setTotalCount(functionCountInfoTO.getTotalCount());
                        funcCount.setDelFlag("0");
                        this.save(funcCount);
                    }else {
                        if(StringUtils.equals(funcCount.getDelFlag(),"1")){
                            funcCount.setDelFlag("0");
                            this.save(funcCount);
                        }
                    }
                    listMap.remove(functionCountInfoTO.getFunctionTableId());
                }else {
                    this.insertNewSysCompInfoFuncCountRecord(companyId, functionCountInfoTO.getFunctionTableId(),"0",functionCountInfoTO.getTotalCount());
                }
            }
            if(listMap.size() > 0){
                for (Map.Entry<String, List<SysCompInfoFuncCount>> entry : listMap.entrySet()) {
                    SysCompInfoFuncCount funcCount = entry.getValue().get(0);
                    if(StringUtils.equals(funcCount.getDelFlag(), "0")) {
                        funcCount.setDelFlag("1");
                        this.save(funcCount);
                    }
                }
            }

            // 都保留上个版本的额度，每次变更都有记录
            for (SysCompInfoFuncCount funcCount : funcCountList) {
                SysCompInfoFuncCountHis sysCompInfoFuncCountHis = new SysCompInfoFuncCountHis();
                BeanUtil.copyProperties(sysCompInfoFuncCountHis, funcCount);
                String uuid = IdGenUtil.uuid();
                sysCompInfoFuncCountHis.setId(uuid);
                sysCompInfoFuncCountHis.setIsNewRecord(true);
                commSysCompInfoFuncCountHisService.save(sysCompInfoFuncCountHis);
            }
        }
        */
    }
    
    /**
     * 更新总额度
     * added for v1.9.8 KNZT-4538
     *
     * @param cur 当前额度情况
     * @param afterTotalCount  更新后的总额度
     * @return 
     */
    public SysCompInfoFuncCount updateFuncTotalCount(SysCompInfoFuncCount cur, BigDecimal afterTotalCount) {
        return this.updateFuncCount(cur, afterTotalCount, null);
    }

    /**
     * 更新额度信息
     * added for v1.9.8 KNZT-4538
     *
     * @param beforeFuncCount
     * @param afterTotalCount
     * @param afterConsumedCount
     * @return
     */
    public SysCompInfoFuncCount updateFuncCount(SysCompInfoFuncCount beforeFuncCount, BigDecimal afterTotalCount, BigDecimal afterConsumedCount) {
        if (Objects.nonNull(afterTotalCount)) {
            beforeFuncCount.setTotalCount(afterTotalCount);
        }
        if (Objects.nonNull(afterConsumedCount)) {
            beforeFuncCount.setConsumedCount(afterConsumedCount);
        }
        this.save(beforeFuncCount);

        logger.info("updateFuncCount updateFuncCount, companyId:{}, totalCount:{} -> {}, consumedCount:{} -> {}",
                beforeFuncCount.getCompanyId(), beforeFuncCount.getTotalCount(), afterTotalCount, beforeFuncCount.getConsumedCount(), afterConsumedCount);

        SysCompInfoFuncCountHis sysCompInfoFuncCountHis = new SysCompInfoFuncCountHis();
        BeanUtil.copyProperties(sysCompInfoFuncCountHis, beforeFuncCount);
        String uuid = IdGenUtil.uuid();
        sysCompInfoFuncCountHis.setId(uuid);
        sysCompInfoFuncCountHis.setIsNewRecord(true);
        commSysCompInfoFuncCountHisService.save(sysCompInfoFuncCountHis);
        return beforeFuncCount;
    }

    /**
     * 根据prod acc 更新 func count
     * added for v1.9.8 KNZT-4538
     *
     * @param companyId
     * @return
     */
    public SysCompInfoFuncCount updateFuncCountByValidProdAcc(String companyId) throws MessageException {
        SysCompInfoFuncCount curFuncCount = this.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(curFuncCount, "缺少账户额度记录");
        FunctionCountInfoTO funcCountInfo = commTblContractDeliveryProdAccService.calcCompFuncCount(companyId);
        BigDecimal afterTotalCount = funcCountInfo.getTotalCount();
        BigDecimal afterConsumedCount = funcCountInfo.getConsumedCount();
        logger.info("updateFuncCountByValidProdAcc companyId:{}, totalCount:{}->{}, consumedCount:{} -> {}",
                companyId, curFuncCount.getTotalCount(), afterTotalCount, curFuncCount.getConsumedCount(), afterConsumedCount);
        return this.updateFuncCount(curFuncCount, afterTotalCount, afterConsumedCount);
    }


    /**
     * 更新已消耗额度
     * added for v1.9.8 KNZT-4538
     *
     * @param cur 当前额度情况
     * @param consumedCount  本次消耗额度
     * @return
     */
    public void consumeFuncCount(SysCompInfoFuncCount cur, BigDecimal consumedCount) {
        BigDecimal afterConsumedCount = cur.getConsumedCount().add(consumedCount);
        cur.setConsumedCount(afterConsumedCount);
        this.save(cur);
    }
    

    /**
     * added for v2.0.5 ZS-416 根据companyId查询服务列表数据
     * updated for v1.9.8 KNZT-4538
     * @param companyId
     * @param totalCount
     */
    public void insertNewSysCompInfoFuncCountRecord(String companyId, BigDecimal totalCount) {
        SysCompInfoFuncCount sysCompInfo = new SysCompInfoFuncCount();
        String id = IdGenUtil.uuid();
        sysCompInfo.setId(id);
        sysCompInfo.setIsNewRecord(true);
        sysCompInfo.setCompanyId(companyId);
        sysCompInfo.setFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
        //added v6.7.1 KNZT-38 修复保存到sys_comp_info_func_count表计费方式错误的问题
        SysFunctionTableCountConfig config = sysFunctionTableCountConfigService.getFunctionTableCountConfigInfoByFunctionTableId(Constants.FunctionTable.ID_REPORT_ID);
        sysCompInfo.setCountStd(config != null ? config.getSupportCountStd() : SysConstants.UserAction.COUNT_STD_Q);
        sysCompInfo.setMainFuncFlag((short) 0);
        sysCompInfo.setConsumedCount(BigDecimal.ZERO);
        sysCompInfo.setTotalCount(totalCount);
        sysCompInfo.setDelFlag(SysCompInfoFuncCount.DEL_FLAG_NORMAL);
        this.save(sysCompInfo);
    }



    public void deleteByCompanyId(String companyId) {
        SysCompInfoFuncCount entity = new SysCompInfoFuncCount();
        entity.setCompanyId(companyId);
        entity.preUpdate();
        dao.deleteByCompanyId(entity);
        commSysCompInfoFuncCountHisService.deleteByCompanyId(companyId);
    }

    /**
     * 清空funcCount数据
     * added for lvcy v1.9.9 KNZT-5091
     *
     * @param companyId
     * @return
     */
    public void clearFuncCount(String companyId) {
        List<SysCompInfoFuncCount> funcCountList = this.getSysCompFuncCountDetailByCompanyId(companyId);
        for (SysCompInfoFuncCount funcCount : funcCountList) {
            funcCount.setTotalCount(BigDecimal.ZERO);
            funcCount.setConsumedCount(BigDecimal.ZERO);
            this.save(funcCount);
        }
    }
}
