package com.backend.common.modules.report.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.global.gateway.map.DataMapInterface;
import com.backend.common.modules.benefit.condition.MapTrialListCondition;
import com.backend.common.modules.benefit.entity.TblBenefitDelivery;
import com.backend.common.modules.benefit.entity.TblBenefitPool;
import com.backend.common.modules.report.condition.MapTrialStatisticsForm;
import com.backend.common.modules.report.model.PlatformMapTrialStatisticsTO;
import com.backend.common.modules.report.model.PlatformMapTrialListTO;
import com.backend.common.modules.benefit.model.PaymentStrategyTO;
import com.backend.common.modules.benefit.service.CommTblBenefitDeliveryService;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblCompReportOrderMap;
import com.backend.common.modules.report.entity.TblCompReportOrderHkDoc;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.mapper.TblCompReportOrderMapDao;
import com.backend.common.modules.report.model.DataMapUnitResult;
import com.backend.common.modules.report.model.DataMapOriginalDocTO;
import com.backend.common.modules.report.model.MapBenefitInfo;
import com.backend.common.modules.report.model.UserBenefitInfoResult;
import com.backend.common.modules.setting.condition.SysCompanyCondition;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.BenefitPoolStatusEnum;
import com.qcc.frame.commons.ienum.CompPayTypeEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.DeliveryBenefitStatusEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.ReportGroupMappingEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.OrderHkDocRelTypeEnum;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.SysUserExt;
import com.qcc.frame.jee.modules.sys.service.SysUserExtService;
import com.qcc.frame.jee.modules.sys.utils.ConfigUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图谱综合服务类
 * added for lvcy v2.1.5 KNZT-6384
 *
 * <AUTHOR>
 * @datetime 17/2/2025 2:10 下午
 */
@Service
public class DataMapBusinessService {
    private static final Logger logger = LoggerFactory.getLogger(DataMapBusinessService.class);

    @Autowired
    private CommTblCompReportOrderService orderService;
    @Autowired
    private SysUserExtService sysUserExtService;
    @Autowired
    private CommSysCompInfoFuncCountService commSysCompInfoFuncCountService;
    @Autowired
    private TblCompReportOrderMapDao tblCompReportOrderMapDao;
    @Autowired
    private CommTblCompReportOrderHkDocService hkDocService;
    @Autowired
    private CommTblCompReportOrderMapService tblCompReportOrderMapService;
    @Autowired
    private CommTblBenefitPoolService benefitPoolService;
    @Autowired
    private CommTblBenefitDeliveryService commTblBenefitDeliveryService;
    @Autowired
    private CommTblReportChargeUnitService chargeUnitService;
    @Autowired
    private CommSysCompanyService sysCompanyService;


    public String proxyApi(String url, Map<String, Object> bodyJsonMap) throws MessageException {
        List<String> allowUrlList = ConfigUtils.listValueByTypeAndKey("data_map", "proxy_url");
        boolean allow = allowUrlList.stream().anyMatch(url::contains);
        MsgExceptionUtils.failBuild(!allow, "msg:Forbidden");
        return DataMapInterface.proxy(url, bodyJsonMap, false);
    }

    public String proxyApiByOrder(String orderId, Map<String, Object> bodyJsonMap, String url) throws MessageException {
        TblCompReportOrder order = orderService.getByIdAndCompany(orderId, UserUtils.getUserCompanyId());
        MsgExceptionUtils.failBuild(Objects.isNull(order) || !OrderStatusEnum.SUCCESS.getCode().equals(order.getRptStatus()));
        String reportType = order.getReportType();

        if (Objects.isNull(bodyJsonMap)) {
            bodyJsonMap = Maps.newHashMap();
        }
        if (ReportTypeEnum.MAP_OWNERSHIP.getCode().equals(reportType) || ReportTypeEnum.MAP_OWNERSHIP_HK.getCode().equals(reportType)) {
            bodyJsonMap.put("keyNo", order.getKeyNo());
            url = "/graph/ownership-structure/get-graph";
        } else if (ReportTypeEnum.MAP_NETWORK.getCode().equals(reportType) || ReportTypeEnum.MAP_NETWORK_HK.getCode().equals(reportType)) {
            bodyJsonMap.put("keyNo", order.getKeyNo());
            url = "/graph/network-map/get-graph";
        } else if (ReportTypeEnum.MAP_RELATION.getCode().equals(reportType) || ReportTypeEnum.MAP_RELATION_3.getCode().equals(reportType)) {
            Object queryNodesObj = bodyJsonMap.get("queryNodes");
            MsgExceptionUtils.checkIsNull(queryNodesObj, "err.param.invalid", "queryNodes is null");
            JSONArray queryNodesJsonArray = JSONObject.parseArray(JSONObject.toJSONString(queryNodesObj));
            // 提取queryNodes中的所有keyNo
            List<String> keyNoList = new ArrayList<>();
            for (int i = 0; i < queryNodesJsonArray.size(); i++) {
                JSONArray nodeArray = queryNodesJsonArray.getJSONArray(i);
                if (nodeArray != null && !nodeArray.isEmpty()) {
                    String keyNo = nodeArray.getString(0); // 第一个元素是keyNo
                    if (StringUtils.isNotBlank(keyNo)) {
                        keyNoList.add(keyNo);
                    }
                }
            }
            MsgExceptionUtils.failBuild(CollectionUtils.isEmpty(keyNoList), "err.param.invalid", "queryNodes is empty");
            // 校验所有keyNo都在查询范围内
            boolean contain = tblCompReportOrderMapService.isKeyNoListContain(order.getCompanyId(), keyNoList, order.getOrderNo());
            MsgExceptionUtils.failBuild(!contain, "err.param.invalid", "keyNo cannot be proxy by others");
            
            // 如果不是这两个url，则不继续请求
            boolean allow = StringUtils.containsAnyFromArr(url, "/relationship/find-relationship-by-dim", "/relationship/find-relationship-by-chn");
            MsgExceptionUtils.failBuild(!allow, "msg:Forbidden");
        }
        if (StringUtils.isNotBlank(url)) {
            return DataMapInterface.proxy(url, bodyJsonMap, false);
        } else {
            return "";
        }
    }

    public DataMapUnitResult getCreditsInfo(String mapNo, String rootKeyNo, String reportType) {
        DataMapUnitResult result = new DataMapUnitResult();
        String companyId = UserUtils.getUserCompanyId();
        String userId = UserUtils.getUserId();
        
        // 获取剩余额度
        SysCompInfoFuncCount compFuncCount = commSysCompInfoFuncCountService.readOnlyGetCompFuncCount(companyId);
        if (Objects.nonNull(compFuncCount)) {
            result.setRemainCredits(compFuncCount.calRemainCount());
        }

        // 获取图谱关联订单额度
        if (StringUtils.isNotBlank(mapNo)) {
            List<TblCompReportOrderMap> mapList = tblCompReportOrderMapDao.getMapListByMapNodeNo(UserUtils.getUserId(), mapNo, null);
            List<String> orderNoList = mapList.stream().map(TblCompReportOrderMap::getReportOrderNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderNoList)) {
                List<TblCompReportOrder> orderList = orderService.getByOrderNoList(orderNoList);
                BigDecimal mapOrderQuota = orderList.stream().map(TblCompReportOrder::getTotalUnit).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setMapConsumedCredits(mapOrderQuota);
            } else {
                result.setMapConsumedCredits(BigDecimal.ZERO);
            }
            List<String> mapNodeNoList = mapList.stream().map(TblCompReportOrderMap::getMapNodeNo).collect(Collectors.toList());
            result.setMapNodeNoList(mapNodeNoList);
        }
        
        // 处理权益信息和canUseBenefit逻辑
        if (StringUtils.isNotBlank(reportType)) {
            PaymentStrategyTO strategy = benefitPoolService.determinePaymentStrategyByKeyNo(reportType, rootKeyNo);
            result.setCanUseBenefit(strategy.isUseBenefit());

            // 如果在试用期间，不能使用credits支付
            TblReportChargeUnit chargeUnit = chargeUnitService.getChargeUnitByUniqueKey(companyId, ReportGroupMappingEnum.MAP_G.getRptGroup(), reportType);
            if (Objects.nonNull(chargeUnit) && Constants.YES.equals(chargeUnit.getEnabled())) {
                TblBenefitPool pool = strategy.getPool();
                if (Objects.nonNull(pool) && BenefitTypeEnum.getTrialMapBenefitTypeList().contains(pool.getBenefitType())) {
                    result.setCanUseCredit(false);
                } else {
                    result.setCanUseCredit(true);
                }
            } else {
                result.setCanUseCredit(false);
            }
        }

        // 判断是否完成模糊搜索指引
        SysUserExt fuzzySearchGuide = sysUserExtService.getByUserIdAndType(userId, Constants.UserExtType.DATA_MAP_FUZZY_SEARCH_GUIDE);
        result.setFuzzySearchGuide(Objects.nonNull(fuzzySearchGuide) && Constants.YES.equals(fuzzySearchGuide.getValue()));
        
        // 判断是否完成OwnershipStructure指引
        SysUserExt ownershipGuide = sysUserExtService.getByUserIdAndType(userId, Constants.UserExtType.DATA_MAP_OWNERSHIP_GUIDE);
        result.setOwnershipGuide(Objects.nonNull(ownershipGuide) && Constants.YES.equals(ownershipGuide.getValue()));
        
        // 判断是否完成NetWork指引
        SysUserExt networkGuide = sysUserExtService.getByUserIdAndType(userId, Constants.UserExtType.DATA_MAP_NETWORK_GUIDE);
        result.setNetworkGuide(Objects.nonNull(networkGuide) && Constants.YES.equals(networkGuide.getValue()));
        
        // 静默通知
        SysUserExt silentNotify = sysUserExtService.getByUserIdAndType(userId, Constants.UserExtType.SILENT_NOTIFY);
        result.setSilentNotify(Objects.nonNull(silentNotify) && Constants.YES.equals(silentNotify.getValue()));
        
        return result;
    }
    
    /**
     * 获取当前用户的套餐情况
     * @return 用户套餐信息
     */
    public UserBenefitInfoResult getUserBenefitInfo() {
        UserBenefitInfoResult result = new UserBenefitInfoResult();
        String companyId = UserUtils.getUserCompanyId();
        
        // 获取公司生效状态的权益池
        List<TblBenefitPool> allPoolList = benefitPoolService.getByCompanyIdInBenefitTypeList(companyId, BenefitTypeEnum.getMapBenefitTypeList());

        List<MapBenefitInfo> benefitInfoList = Lists.newArrayList();
        for (String benefitType : BenefitTypeEnum.getMapBenefitTypeList()) {
            TblBenefitPool effectPool = allPoolList.stream()
                    .filter(k -> benefitType.equals(k.getBenefitType())
                            && BenefitPoolStatusEnum.EFFECT.getCode().equals(k.getStatus()))
                    .findAny()
                    .orElse(null);
            MapBenefitInfo mapBenefitInfo;
            if (Objects.nonNull(effectPool)) {
                mapBenefitInfo = MapBenefitInfo.buildFromPool(effectPool);
                boolean usedUp = effectPool.getTotalCount().compareTo(effectPool.getConsumedCount()) == 0;
                MapBenefitInfo.Status status = usedUp ? MapBenefitInfo.Status.USED_UP : MapBenefitInfo.Status.AVAILABLE;
                mapBenefitInfo.setStatus(status.getCode());
                mapBenefitInfo.setStatusDesc(status.getDesc());
            } else {
                TblBenefitPool invalidTrialPool = allPoolList.stream()
                    .filter(k -> benefitType.equals(k.getBenefitType())
                            && BenefitPoolStatusEnum.INVALID.getCode().equals(k.getStatus())
                            && StringUtils.isBlank(k.getContractNo()))
                    .findAny()
                    .orElse(null);
                boolean hasNoContractPool = allPoolList.stream()
                    .noneMatch(k -> benefitType.equals(k.getBenefitType())
                        && BenefitPoolStatusEnum.INVALID.getCode().equals(k.getStatus())
                        && StringUtils.isNotBlank(k.getContractNo()));
                // 判断是否仅有试用的过期,没有其他的pool
                if (Objects.nonNull(invalidTrialPool) && hasNoContractPool) {
                    mapBenefitInfo = MapBenefitInfo.buildFromPool(invalidTrialPool);
                    mapBenefitInfo.setStatus(MapBenefitInfo.Status.EXPIRED.getCode());
                    mapBenefitInfo.setStatusDesc(MapBenefitInfo.Status.EXPIRED.getDesc());
                } else {
                    mapBenefitInfo = MapBenefitInfo.buildPayPerUse(benefitType);
                }
            }
            // 注入过期时间
            List<TblBenefitDelivery> deliveryList = commTblBenefitDeliveryService.getByCompanyIdAndContractNo(companyId, mapBenefitInfo.getContractNo());
            if (CollectionUtils.isNotEmpty(deliveryList)) {
                mapBenefitInfo.setExpiryDate(deliveryList.get(0).getEndDate());
            }
            // 注入单位
            if (StringUtils.equals(BenefitTypeEnum.MAP_CN_REL_3ENTITY.getCode(), mapBenefitInfo.getBenefitType())) {
                mapBenefitInfo.setUnit("quires");
            } else {
                mapBenefitInfo.setUnit("entities");
            }
            benefitInfoList.add(mapBenefitInfo);
        }
        benefitInfoList.sort(Comparator.comparing(MapBenefitInfo::getSort));
        result.setBenefitInfoList(benefitInfoList);

        return result;
    }


    public void silent() {
        SysUserExt userExt = sysUserExtService.getByUserIdAndType(UserUtils.getUserId(), Constants.UserExtType.SILENT_NOTIFY);
        if (Objects.isNull(userExt)) {
            userExt = new SysUserExt();
            userExt.setUserId(UserUtils.getUserId());
            userExt.setType(Constants.UserExtType.SILENT_NOTIFY);
            userExt.setValue(Constants.YES);
            sysUserExtService.save(userExt);
        } else {
            userExt.setValue(Constants.YES);
            sysUserExtService.save(userExt);
        }
    }




    public void updateUserExtInfo4Map(String pageMenu) {
        if (StringUtils.isBlank(pageMenu)) return;
        switch (pageMenu) {
            case "FuzzySearch":
                updateUserExtInfo(Constants.UserExtType.DATA_MAP_FUZZY_SEARCH_GUIDE);
                break;
            case "Ownership":    
                updateUserExtInfo(Constants.UserExtType.DATA_MAP_OWNERSHIP_GUIDE);
                break;
            case "Network":
                updateUserExtInfo(Constants.UserExtType.DATA_MAP_NETWORK_GUIDE);
                break;
        }
    }

    public void updateUserExtInfo(String userExtType) {
        String userId = UserUtils.getUserId();
        SysUserExt userExt = sysUserExtService.getByUserIdAndType(userId, userExtType);
        if (Objects.isNull(userExt)) {
            userExt = new SysUserExt();
            userExt.setUserId(userId);
            userExt.setType(userExtType);
            userExt.setValue(Constants.YES);
            sysUserExtService.save(userExt);
        } else {
            userExt.setValue(Constants.YES);
            sysUserExtService.save(userExt);
        }
    }

    /**
     * 根据mapNo查询原始文档信息
     * @param mapNo 图谱编号
     * @return List<DataMapOriginalDocTO>
     */
    public List<DataMapOriginalDocTO> getOriginalDocsByMapNo(String mapNo) {
        List<DataMapOriginalDocTO> resultList = new java.util.ArrayList<>();
        if (StringUtils.isBlank(mapNo)) {
            return resultList;
        }
        // 1. 获取图谱关联订单号
        List<TblCompReportOrderMap> mapList = tblCompReportOrderMapDao.getMapListByMapNodeNo(UserUtils.getUserId(), mapNo, null);
        List<String> orderNoList = mapList.stream().map(TblCompReportOrderMap::getReportOrderNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNoList)) {
            return resultList;
        }
        // 2. 获取订单信息
        List<TblCompReportOrder> orderList = orderService.getByOrderNoList(orderNoList);
        // 3. 获取原始文档
        List<String> orderIdList = orderList.stream().map(TblCompReportOrder::getId).collect(Collectors.toList());
        Map<String, TblCompReportOrderHkDoc> hkDocMap = hkDocService.batchGetHkDocMap(orderIdList, OrderHkDocRelTypeEnum.ORDER.getCode());
        for (TblCompReportOrder order : orderList) {
            TblCompReportOrderHkDoc hkDoc = hkDocMap.get(order.getId());
            if (hkDoc == null) continue;
            
            DataMapOriginalDocTO docTO = new DataMapOriginalDocTO();
            docTO.setCorpName(order.getCorpName());
            docTO.setCorpNameEn(order.getCorpNameEn());
            docTO.setDocTitleEn(hkDoc.getDocTitleEn());
            docTO.setDocUrl(hkDoc.getDocUrl());

            Long docDateLong = hkDoc.getDocDate();
            if (docDateLong != null && docDateLong > 0) {
                String docDateStr = DateUtils.formatDateForSg(new Date(docDateLong), DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
                docTO.setDocDateStr(docDateStr);
            }

            resultList.add(docTO);
        }
        return resultList;
    }

    /**
     * 图谱试用列表分页查询
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    public Page<PlatformMapTrialListTO> pageMapTrialList(MapTrialListCondition condition) {
        Page<PlatformMapTrialListTO> page = condition.getPage();
            // 查询列表数据
        List<PlatformMapTrialListTO> list = commTblBenefitDeliveryService.pageMapTrialList(condition);
            
        // 处理数据转换和状态映射
        if (CollectionUtils.isEmpty(list)) {
            return page;
        }

        // 获取所有公司ID用于批量查询权益池
        List<String> companyIdList = list.stream()
                .map(PlatformMapTrialListTO::getCompanyId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询所有公司的试用权益池
        Map<String, List<TblBenefitPool>> companyBenefitPoolMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(companyIdList)) {
            for (String companyId : companyIdList) {
                List<TblBenefitPool> pools = benefitPoolService.getByCompanyIdInBenefitTypeList(
                        companyId, BenefitTypeEnum.getTrialMapBenefitTypeList());
                // 过滤试用数据（contractNo为空）
                List<TblBenefitPool> trialPools = pools.stream()
                        .filter(pool -> StringUtils.isBlank(pool.getContractNo()))
                        .collect(Collectors.toList());
                companyBenefitPoolMap.put(companyId, trialPools);
            }
        }

        // 处理每条记录
        for (PlatformMapTrialListTO item : list) {
            // 账号类型描述
            item.setAccountTypeDesc(CompTypeEnum.getDesc(item.getCompType()));

            // 付费类型描述
            item.setPayTypeDesc(CompPayTypeEnum.getDescCnByCode(item.getPayType()));

            // 状态处理
            String status = item.getStatus();
            if (DeliveryBenefitStatusEnum.EFFECT.getCode().equals(status)) {
                item.setStatusDesc("试用中");
            } else {
                item.setStatusDesc(DeliveryBenefitStatusEnum.getDescByCode(status));
            }

            // 权益使用情况
            processBenefitUsage(item, companyBenefitPoolMap.get(item.getCompanyId()));
            
            // 操作按钮标识
            // 同意按钮：只有状态为申请中时可用（已拒绝的情况下不能有通过按钮）
            boolean canApprove = DeliveryBenefitStatusEnum.APPLYING.getCode().equals(status);
            item.setCanApprove(canApprove);

            // 拒绝按钮：状态为申请中时可用
            boolean canReject = DeliveryBenefitStatusEnum.APPLYING.getCode().equals(status);
            item.setCanReject(canReject);

        }
            
        page.setList(list);
        return page;
    }
    

    
    /**
     * 处理试用状态
     */
    private void processTrialStatus(PlatformMapTrialListTO item, List<TblBenefitPool> benefitPools) {
        String status = item.getStatus();

        item.setStatus(status);
        if (DeliveryBenefitStatusEnum.EFFECT.getCode().equals(status)) {
            item.setStatusDesc("试用中");
        } else {
            item.setStatusDesc(DeliveryBenefitStatusEnum.getDescByCode(status));
        }
    }
    
    /**
     * 处理权益使用情况
     */
    private void processBenefitUsage(PlatformMapTrialListTO item, List<TblBenefitPool> benefitPools) {
        List<PlatformMapTrialListTO.BenefitUsageInfo> usageInfoList = Lists.newArrayList();
        
        if (CollectionUtils.isNotEmpty(benefitPools)) {
            for (TblBenefitPool pool : benefitPools) {
                PlatformMapTrialListTO.BenefitUsageInfo usageInfo = new PlatformMapTrialListTO.BenefitUsageInfo();
                usageInfo.setBenefitType(pool.getBenefitType());
                usageInfo.setBenefitTypeDesc(BenefitTypeEnum.getNameByCode(pool.getBenefitType()));
                usageInfo.setTotalCount(pool.getTotalCount());
                usageInfo.setConsumedCount(pool.getConsumedCount());
                usageInfoList.add(usageInfo);
            }
        }
        
        // 按sort排序
        usageInfoList.sort(Comparator.comparing(usage -> 
            BenefitTypeEnum.getSortByCode(usage.getBenefitType())));
            
        item.setBenefitUsageList(usageInfoList);
    }

    /**
     * 图谱试用统计
     * @param from 查询条件
     * @return 统计结果
     */
    public PlatformMapTrialStatisticsTO getMapTrialStatistics(MapTrialStatisticsForm from) {
        PlatformMapTrialStatisticsTO result = new PlatformMapTrialStatisticsTO();
        
        // 统计试用中的账号数量
        Integer activeTrialCount = commTblBenefitDeliveryService.countMapEffectTrialCompany(from);
        result.setActiveTrialCompanyCount(activeTrialCount != null ? activeTrialCount : 0);
        
        // 统计累计试用账号数量
        Integer totalTrialCount = commTblBenefitDeliveryService.countMapTotalTrialCompany(from);
        result.setTotalTrialCompanyCount(totalTrialCount != null ? totalTrialCount : 0);
        
        // 统计总账号数量
        SysCompanyCondition companyCondition = new SysCompanyCondition();
        companyCondition.setPayTypeList(from.getPayTypeList());
        Integer totalCompanyCount = sysCompanyService.countCompany(companyCondition);
        result.setTotalCompanyCount(totalCompanyCount != null ? totalCompanyCount : 0);
        
        return result;
    }

}
