package com.backend.common.modules.api.service;

import com.alibaba.fastjson.JSONObject;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.global.gateway.person.PersonCommonForm;
import com.backend.common.modules.api.entity.TblCompApiOrderResp;
import com.backend.common.modules.api.model.ApiOrderRespTO;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.CorpInfoTO;
import com.backend.common.modules.report.model.CorpMerchantShopTO;
import com.backend.common.modules.report.model.FinancialData;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.MerchantShopBusinessService;
import com.backend.common.modules.search.PersSearchService;
import com.backend.common.modules.trans.base.form.TransWrapper;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.openapi.model.*;
import com.backend.common.overseamongo.entity.QccOvsBasic;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.ECILocalInterface;
import com.backend.common.yunjuapi.FuzzySearchInterface;
import com.backend.common.yunjuapi.QccEntDetailInfoTO;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.model.*;
import com.backend.common.yunjuapi.model.merchant.CertificationListTO;
import com.backend.common.yunjuapi.model.merchant.GeneralTaxPayerTO;
import com.backend.common.yunjuapi.model.merchant.GetCreditRateTO;
import com.backend.common.yunjuapi.model.merchant.GetCreditRateTrendTO;
import com.backend.common.yunjuapi.model.merchant.GetIllegalListTO;
import com.backend.common.yunjuapi.model.merchant.GetInvoiceDetailTO;
import com.backend.common.yunjuapi.model.merchant.GetListOfOweNoticeNewTO;
import com.backend.common.yunjuapi.model.merchant.GetTaxUnnormalsTO;
import com.backend.common.yunjuapi.model.merchant.HonorCertificationV2TO;
import com.backend.common.yunjuapi.model.merchant.IETO;
import com.backend.common.yunjuapi.model.merchant.PatentTO;
import com.backend.common.yunjuapi.model.merchant.TrademarkTO;
import com.backend.common.yunjuapi.model.search.PersonSearchApiTO;
import com.backend.common.yunjuapi.model.search.PersonSearchResultTO;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AliyunTranslateSourceTypeEnum;
import com.qcc.frame.commons.ienum.ApiModelEnum;
import com.qcc.frame.commons.ienum.ApiOrderStatusEnum;
import com.qcc.frame.commons.ienum.ApiStatusCodeEnum;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import com.qcc.frame.commons.ienum.ForeignCorpStatusEnum;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.QccEntCodeTypeEnum;
import com.qcc.frame.commons.ienum.UnitGroupEnum;
import com.qcc.frame.commons.ienum.corp.company.type.OverseaCompanyTypeEnum;
import com.qcc.frame.jee.commons.model.AmountUnit;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.ApiStatusException;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.ApiStatusExceptionUtils;
import com.qcc.frame.jee.commons.utils.BeanUtil;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.CurrencyUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.enums.TranslationMapEnum;
import com.qcc.frame.jee.modules.sys.service.SysCompInfoColExtService;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import com.qcc.frame.jee.modules.sys.translation.PositionTraslationUtils;
import com.qcc.frame.jee.modules.sys.utils.TranslationMapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * added for v2.3.4 fegnsw KNZT-8340【API】下单流程后置，提升接口响应速度
 * 外部接口数据服务，用于组装所有API订单的数据内容
 */
@Service
public class CommExternalApiDataService {
    private final static Logger logger = LoggerFactory.getLogger(CommExternalApiDataService.class);
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private CommTblCompApiOrderService apiOrderService;
    @Autowired
    private CommTblCompApiOrderRespService apiOrderRespService;
    @Autowired
    private CommTblCompApiOrderMockService apiOrderMockService;
    @Autowired
    private CommTblGlobalIndustryService commTblGlobalIndustryService;
    @Autowired
    private MerchantShopBusinessService merchantShopBusinessService;
    @Autowired
    private SysCompInfoColExtService sysCompInfoColExtService;
    @Autowired
    private CommExternalApiBusinessService apiBusinessService;
    @Resource
    private TranslaterService translaterService;
    @Resource
    private OvsQccOvsBasicService ovsQccOvsBasicService;
    @Resource
    private CommTblCompReportOrderService reportOrderService;
    @Autowired
    private CommonTransService commonTransService;
    //中国大陆企业查询时，需要包含的维度信息最新公示、工商登记的股东、主要人员信息
    private final static String includeExtraFields = "PartnerList,PubPartnerList,EmployeeList,PubEmployeeList";
    private static final Map<ApiTypeEnum, Class<? extends ExternalApiRespBaseTO>> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put(ApiTypeEnum.CN_BASIC, ExternalApiCorpBasicInfoTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_UBO, ExternalApiCorpUBOInfoTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_ADVANCED, ExternalApiCorpAdvancedInfoTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_MERCHANT, ExternalApiCorpMerchantInfoTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_FIN_TAX, ExternalApiFinancialTaxDataTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_PERS_BASIC, ExternalApiPersBasicInfoTO.class);
        TYPE_MAP.put(ApiTypeEnum.CN_LITIGATION, ExternalApiLitigationInfoTO.class);
    }

    /**
     * 组装企业基本信息 数据转换 将企业数据转化为对外输出字段格式
     *
     * @param keyNo
     * @return
     */
    public ExternalApiCorpBasicInfoTO getCorpBasicInfo4External(String keyNo) {
        // 获取企业基本信息
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        // 转换出参
        return buildCorpBasicInfo4External(corpInfoTO);
    }

    /**
     * 组装企业基本信息+UBO 数据转换 将企业数据转化为对外输出字段格式
     *
     * @param keyNo
     * @return
     */
    private ExternalApiCorpUBOInfoTO getCorpBasicAndUBOInfo4External(String keyNo) {
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        // 转换出参
        return buildCorpBasicAndUBOInfo4External(keyNo, corpInfoTO);
    }

    /**
     * Basic+UBO信息处理
     * 填充工商信息
     * 填充行业信息
     * 填充股东信息
     * 填充主要人员信息
     * 组装分支机构数据
     * UBO信息处理
     * 填充变更历史信息
     * 填充历史主要人员
     * 填充历史对外投资
     * 填充历史股东（历史股东镜像）
     * 填充诉讼案件
     * 司法、工商、股权相关信息
     */

    private ExternalApiCorpAdvancedInfoTO getCorpAdvancedInfo4External(String keyNo) {
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        ExternalApiCorpAdvancedInfoTO corpAdvancedInfoTO = new ExternalApiCorpAdvancedInfoTO();
        String dataReportTime = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS) + " " + "UTC+8";
        corpAdvancedInfoTO.setReportDateTime(dataReportTime);
        corpAdvancedInfoTO.setCompanyProfile(populateCompanyProfile(corpInfoTO));
        corpAdvancedInfoTO.setContactInfo(populateContactInfo(corpInfoTO));
        corpAdvancedInfoTO.setShareCapital(populateShareCapital(corpInfoTO));
        corpAdvancedInfoTO.setShareholders(fetchShareholders(corpInfoTO));
        corpAdvancedInfoTO.setOfficers(fetchOfficers(corpInfoTO));
        corpAdvancedInfoTO.setBranches(fetchBranches(keyNo));// 分支机构获取5000条
        corpAdvancedInfoTO.setSubsidiaries(fetchSubsidiaries(keyNo));
        corpAdvancedInfoTO.setAffiliates(fetchAffiliates(keyNo));
        corpAdvancedInfoTO.setUBOInfo(populateUboInfo(keyNo));
        corpAdvancedInfoTO.setChangeHistory(populateChangeHistory(keyNo, corpInfoTO.getBuyBasicDetailTO()));// 变更历史
        corpAdvancedInfoTO.setHistoryOfficers(fetchHistoryOfficers(keyNo));// 历史主要人员
        corpAdvancedInfoTO.setHistoryInvestment(fetchHistoryInvestment(keyNo));// 历史对外投资
        corpAdvancedInfoTO.setHistoryShareholders(populateHistoryShareholders(keyNo));// 历史股东(历史股东镜像)
        corpAdvancedInfoTO.setLitigations(fetchLitigations(keyNo));// 诉讼案件
        corpAdvancedInfoTO.setLawEnforce(fetchLawEnforce(keyNo, 1000, 500)); // 司法处罚信息
        corpAdvancedInfoTO.setRegEnforce(fetchRegEnforce(keyNo, 5000, 500));// 工商经营处罚信息
        corpAdvancedInfoTO.setEquityPledge(fetchCharges(keyNo, 5000, 500));// 股权相关信息
        corpAdvancedInfoTO.setAdditionalFields(populateAdditionalFields(corpInfoTO));
        return corpAdvancedInfoTO;
    }

    private List<ExternalApiBranchInfoTO> fetchBranches(String keyNo) {
        List<ExternalApiBranchInfoTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500,
                (pageIndex, size) -> getBranchList(keyNo, pageIndex, size));
        return finalResultList;
    }

    private List<ExternalApiShareHolderInfoTO> fetchShareholders(CorpInfoTO corpInfoTO) {
        List<ExternalApiShareHolderInfoTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500,
                (pageIndex, size) -> listShareholders(corpInfoTO.getCorpKeyNo(), pageIndex, size, true, corpInfoTO.getBuyBasicDetailTO()));
        return finalResultList;
    }

    private List<ExternalApiOfficerInfoTO> fetchOfficers(CorpInfoTO corpInfoTO) {
        List<ExternalApiOfficerInfoTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500,
                (pageIndex, size) -> listEmployees(corpInfoTO.getCorpKeyNo(), pageIndex, size, corpInfoTO.getBuyBasicDetailTO()));
        return finalResultList;
    }

    private List<ExternalApiSubsidiaryInfoTO> fetchSubsidiaries(String keyNo) {
        List<ExternalApiSubsidiaryInfoTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500,
                (pageIndex, size) -> listSubsidiaries(keyNo, pageIndex, size));
        return finalResultList;
    }

    private List<ExternalApiSubsidiaryInfoTO> fetchAffiliates(String keyNo) {
        List<ExternalApiSubsidiaryInfoTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500,
                (pageIndex, size) -> listAffiliates(keyNo, pageIndex, size));
        return finalResultList;
    }

    private List<ExternalApiHistEmployeeTO> fetchHistoryOfficers(String keyNo) {
        List<ExternalApiHistEmployeeTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500, (pageIndex, size) -> listHistoryEmployees(keyNo, pageIndex, size));
        return finalResultList;
    }

    private List<ExternalApiHistoryInvestmentCompTO> fetchHistoryInvestment(String keyNo) {
        List<ExternalApiHistoryInvestmentCompTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 5000, 500, (pageIndex, size) -> listHistoryInvestment(keyNo, pageIndex, size));
        return finalResultList;
    }

    private List<ExternalApiCourtCaseTO> fetchLitigations(String keyNo) {
        List<ExternalApiCourtCaseTO> finalResultList = new ArrayList<>();
        fetchDataWithPagination(finalResultList, 1000, 500, (pageIndex, size) -> getCourtCaseList(keyNo, pageIndex, size));
        return finalResultList;
    }

    /**
     * 支持指定每个维度查询的数据条数，并通过循环调用获取完整数据
     *
     * @param keyNo             企业唯一标识
     * @param maxRecordsPerType 每个维度最多获取的记录数
     * @param pageSize          单次请求的分页大小
     * @return 合并后的执法信息列表
     */
    public List<ExternalApiLawEnforceTO> fetchLawEnforce(String keyNo, int maxRecordsPerType, int pageSize) {
        List<ExternalApiLawEnforceTO> finalResultList = new ArrayList<>();

        // 查询被执行信息（当前有效）
        List<ExternalApiLawEnforceTO> judgmentDebtorCurrentList = new ArrayList<>();
        fetchDataWithPagination(judgmentDebtorCurrentList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listJudgmentDebtor(keyNo, "1", pageIndex, size));

        // 查询被执行信息（历史）
        List<ExternalApiLawEnforceTO> judgmentDebtorHistoryList = new ArrayList<>();
        fetchDataWithPagination(judgmentDebtorHistoryList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listJudgmentDebtor(keyNo, "0", pageIndex, size));

        // 查询失信被执行信息（当前有效）
        List<ExternalApiLawEnforceTO> dishonestJudgmentDebtorCurrentList = new ArrayList<>();
        fetchDataWithPagination(dishonestJudgmentDebtorCurrentList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listDishonestJudgmentDebtor(keyNo, "1", pageIndex, size));

        // 查询失信被执行信息（历史）
        List<ExternalApiLawEnforceTO> dishonestJudgmentDebtorHistoryList = new ArrayList<>();
        fetchDataWithPagination(dishonestJudgmentDebtorHistoryList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listDishonestJudgmentDebtor(keyNo, "0", pageIndex, size));

        // 查询限制高消费信息（当前有效）
        List<ExternalApiLawEnforceTO> highConsumptionCurrentList = new ArrayList<>();
        fetchDataWithPagination(highConsumptionCurrentList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listHighConsumption(keyNo, "1", pageIndex, size));

        // 查询限制高消费信息（历史）
        List<ExternalApiLawEnforceTO> highConsumptionHistoryList = new ArrayList<>();
        fetchDataWithPagination(highConsumptionHistoryList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> listHighConsumption(keyNo, "0", pageIndex, size));

        // 查询限制出境信息（不区分历史和当前）
        List<ExternalApiLawEnforceTO> limitExitList = new ArrayList<>();
        fetchDataWithPagination(limitExitList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> getLimitExitList(keyNo, pageIndex, size));

        // 合并所有维度的数据
        finalResultList.addAll(judgmentDebtorCurrentList);
        finalResultList.addAll(judgmentDebtorHistoryList);
        finalResultList.addAll(dishonestJudgmentDebtorCurrentList);
        finalResultList.addAll(dishonestJudgmentDebtorHistoryList);
        finalResultList.addAll(highConsumptionCurrentList);
        finalResultList.addAll(highConsumptionHistoryList);
        finalResultList.addAll(limitExitList);

        return finalResultList;
    }

    /**
     * 支持指定每个维度查询的数据条数，并通过循环调用获取完整数据
     *
     * @param keyNo             企业唯一标识
     * @param maxRecordsPerType 每个维度最多获取的记录数
     * @param pageSize          单次请求的分页大小
     * @return 合并后的工商执法信息列表
     */
    public List<ExternalApiRegEnforceTO> fetchRegEnforce(String keyNo, int maxRecordsPerType, int pageSize) {
        List<ExternalApiRegEnforceTO> finalResultList = new ArrayList<>();

        // 查询行政处罚信息（当前有效）
        List<ExternalApiRegEnforceTO> penaltyList = new ArrayList<>();
        fetchDataWithPagination(penaltyList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> getRiskPenaltySumList(keyNo, pageIndex, size));

        // 查询经营异常信息（当前有效）
        List<ExternalApiRegEnforceTO> exceptionList = new ArrayList<>();
        fetchDataWithPagination(exceptionList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> getExceptionList(keyNo, pageIndex, size));

        // 查询严重违法信息（当前有效）
        List<ExternalApiRegEnforceTO> seriousViolationList = new ArrayList<>();
        fetchDataWithPagination(seriousViolationList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> getSeriousViolationList(keyNo, pageIndex, size));

        // 合并所有维度的数据
        finalResultList.addAll(penaltyList);
        finalResultList.addAll(exceptionList);
        finalResultList.addAll(seriousViolationList);

        return finalResultList;
    }


    /**
     * 支持指定每个维度查询的数据条数，并通过循环调用获取完整数据
     *
     * @param keyNo             企业唯一标识
     * @param maxRecordsPerType 每个维度最多获取的记录数
     * @param pageSize          单次请求的分页大小
     * @return 合并后的股权信息列表
     */
    public List<ExternalApiChargeTO> fetchCharges(String keyNo, int maxRecordsPerType, int pageSize) {
        List<ExternalApiChargeTO> finalResultList = new ArrayList<>();
        // 根据案件类型获取数据
        fetchDataWithPagination(finalResultList, maxRecordsPerType, pageSize,
                (pageIndex, size) -> getPledgeList(keyNo, pageIndex, size));
        // 可以扩展其他类型，如 mortgage、charges 等
        return finalResultList;
    }

    /**
     * 获取企业基本信息
     *
     * @param keyNo
     * @return
     */
    private CorpInfoTO getCorpBasicInfo(String keyNo) {
        CorpBasicDetail4UnitTO basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4Unit(keyNo, includeExtraFields, false);
        // 企业基本详情为空 返回
        if (Objects.isNull(basicDetailTO)) {
            return null;
        }
        CorpInfoTO corpInfoTO = new CorpInfoTO();
        commTblCompReportOrderService.populateOrderByCorpDetail(corpInfoTO, basicDetailTO);
        commTblCompReportOrderService.populateCorpInfoTOBySearchKey(corpInfoTO, keyNo, basicDetailTO, false);
        corpInfoTO.setCorpKeyNo(keyNo);
        return corpInfoTO;
    }

    /**
     * 获取工商基本信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiCompanyProfileTO getCompanyProfile(String keyNo) {
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        return populateCompanyProfile(corpInfoTO);
    }

    /**
     * 获取股本信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiShareCapitalTO getShareCapital(String keyNo) {
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        return populateShareCapital(corpInfoTO);
    }

    /**
     * 转换企业基本信息
     * 填充工商信息
     * 填充行业信息
     * 填充股东信息
     * 填充主要人员信息
     * 填充额外字段
     *
     * @param corpInfoTO
     * @return
     */
    public ExternalApiCorpBasicInfoTO buildCorpBasicInfo4External(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO)) {
            return null;
        }
        ExternalApiCorpBasicInfoTO basicDetailInfo = new ExternalApiCorpBasicInfoTO();
        basicDetailInfo.setCompanyProfile(populateCompanyProfile(corpInfoTO));
        basicDetailInfo.setContactInfo(populateContactInfo(corpInfoTO));
        basicDetailInfo.setShareCapital(populateShareCapital(corpInfoTO));
        basicDetailInfo.setShareholders(populateShareholders(corpInfoTO));
        basicDetailInfo.setOfficers(populateOfficers(corpInfoTO));
        basicDetailInfo.setAdditionalFields(populateAdditionalFields(corpInfoTO));
        String dataReportTime = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS) + " " + "UTC+8";
        basicDetailInfo.setReportDateTime(dataReportTime);
        return basicDetailInfo;
    }

    /**
     * Basic+UBO信息处理
     * 填充工商信息
     * 填充联系信息
     * 填充行业信息
     * 填充股东信息
     * 填充主要人员信息
     * 组装分支机构数据
     * UBO信息处理
     *
     * @param keyNo
     * @return
     */
    private ExternalApiCorpUBOInfoTO buildCorpBasicAndUBOInfo4External(String keyNo, CorpInfoTO corpInfoTO) {
        ExternalApiCorpUBOInfoTO corpBasicAndUBOInfoTO = new ExternalApiCorpUBOInfoTO();
        ExternalApiCorpBasicInfoTO corpBasicInfoTO = buildCorpBasicInfo4External(corpInfoTO);
        if (Objects.isNull(corpBasicInfoTO)) {
            return null;
        }
        BeanUtil.copyProperties(corpBasicAndUBOInfoTO, corpBasicInfoTO);
        corpBasicAndUBOInfoTO.setBranches(populateBranches(keyNo));
        corpBasicAndUBOInfoTO.setSubsidiaries(populateSubsidiaries(keyNo));
        corpBasicAndUBOInfoTO.setAffiliates(populateAffiliates(keyNo));
        corpBasicAndUBOInfoTO.setUBOInfo(populateUboInfo(keyNo));
        return corpBasicAndUBOInfoTO;
    }

    /**
     * 保存api接口数据并持久化到数据库中
     *
     * @param apiOrderRespId
     */
    public void savaApiData2DB(String apiOrderRespId) {
        logger.info("basic info start, apiOrderRespId:{}", apiOrderRespId);
        TblCompApiOrderResp apiOrderResp = apiOrderRespService.get(apiOrderRespId);
        if (Objects.isNull(apiOrderResp)) {
            logger.error("api order resp not exists, apiOrderRespId:{}", apiOrderRespId);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_205);
        }
        ApiOrderRespTO apiOrderTO;
        if (ApiModelEnum.ORD.getCode().equals(apiOrderResp.getApiReqType())) {
            apiOrderTO = apiOrderRespService.getApiOrderRespInfo(apiOrderRespId);
        } else {
            apiOrderTO = apiOrderRespService.getApiOrderRespInfo4Mock(apiOrderRespId);
        }
        if (Objects.isNull(apiOrderTO)) {
            logger.error("api order resp not exists, apiOrderRespId:{}", apiOrderRespId);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_205);
        }
        // special process method 当前只处理电商报告数据
        specialProcessApiOrderData(apiOrderTO);
        // 根据订单类型来组装数据
        String jsonDataStr = getOrderJsonData(apiOrderTO);
        ApiStatusExceptionUtils.failBuild(StringUtils.isBlank(jsonDataStr), ApiStatusCodeEnum.STATUS_199);
        logger.info("customer:{}, orderNo:{}, search corp :{} basic info end", apiOrderTO.getCompanyId(), apiOrderTO.getOrderNo(), apiOrderTO.getKeyNo());

        if (StringUtils.isNotBlank(jsonDataStr)) {
            apiOrderRespService.updateApiResp(apiOrderTO.getApiRespId(), jsonDataStr, Constants.YES);
            if (ApiModelEnum.ORD.getCode().equals(apiOrderResp.getApiReqType())) {
                apiOrderService.updateApiOrderStatus(apiOrderTO.getOrderId(), ApiOrderStatusEnum.SUCCESS.getCode());
            } else {
                apiOrderMockService.updateApiOrderStatus(apiOrderTO.getOrderId(), ApiOrderStatusEnum.SUCCESS.getCode());
            }
            // 回调通知用户，数据已经处理完成，间隔3秒，重试3次，还是失败时，会钉钉告警处理
            if (ApiTypeEnum.withNeedCallbackTypeList().contains(apiOrderTO.getApiType())) {
                apiBusinessService.callbackNotifyCustomerWithStatus(apiOrderTO.getName(), apiOrderTO.getNameEn(), apiOrderTO.getCompanyId(), apiOrderTO.getQccCode(), apiOrderTO.getOrderNo(), apiOrderTO.getApiReqType(), "S");
            }
        } else {
            logger.error("api order data processing failed, companyId:{}, orderNo:{}", apiOrderTO.getCompanyId(), apiOrderTO.getOrderNo());
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        }
    }

    /**
     * 特殊处理一下api订单数据
     *
     * @param apiOrderTO
     */
    private void specialProcessApiOrderData(ApiOrderRespTO apiOrderTO) {
        if (ApiTypeEnum.CN_MERCHANT.getApiCode().equals(apiOrderTO.getApiCode())) {
            try {
                merchantShopBusinessService.saveMerchantShop(apiOrderTO.getOrderId(), StringUtils.getNotBlankStr(apiOrderTO.getExtraInfo1(), apiOrderTO.getKeyNo()));
            } catch (Exception e) {
                logger.error("api order data processing failed, companyId:{}, orderNo:{}", apiOrderTO.getCompanyId(), apiOrderTO.getOrderNo());
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
    }

    /**
     * 获取api订单的接口数据
     *
     * @param apiOrderTO
     * @return
     */
    private String getOrderJsonData(ApiOrderRespTO apiOrderTO) {
        ApiTypeEnum apiTypeEnum = ApiTypeEnum.getEnumByCode(apiOrderTO.getApiType());
        if (Objects.isNull(apiTypeEnum)) {
            logger.warn("api type not exists, apiType:{}", apiOrderTO.getApiType());
            throw new ApiStatusException(ApiStatusCodeEnum.NORMAL_STATUS_201);
        }
        switch (apiTypeEnum) {
            case CN_BASIC:
                ExternalApiCorpBasicInfoTO corpBasicInfoTO = getCorpBasicInfo4External(apiOrderTO.getKeyNo());
                return JSONObject.toJSONString(corpBasicInfoTO);
            case CN_UBO:
                ExternalApiCorpUBOInfoTO uboInfoTO = getCorpBasicAndUBOInfo4External(apiOrderTO.getKeyNo());
                return JSONObject.toJSONString(uboInfoTO);
            case CN_ADVANCED:
                ExternalApiCorpAdvancedInfoTO advancedInfoTO = getCorpAdvancedInfo4External(apiOrderTO.getKeyNo());
                return JSONObject.toJSONString(advancedInfoTO);
            case CN_MERCHANT:
                ExternalApiCorpMerchantInfoTO merchantInfoTO = getCorpMerchantInfo4External(apiOrderTO);
                return JSONObject.toJSONString(merchantInfoTO);
            case CN_FIN_TAX:
                ExternalApiFinancialTaxDataTO finTaxInfoTO; // mock订单 取默认数据
                if (ApiModelEnum.ORD.getCode().equals(apiOrderTO.getApiReqType())) {
                    finTaxInfoTO = getCorpFinTaxInfo4External(apiOrderTO.getKeyNo());
                } else {
                    finTaxInfoTO = getCorpFinTaxInfo4ExternalWithMock(apiOrderTO.getKeyNo());
                }
                return JSONObject.toJSONString(finTaxInfoTO);
            case CN_PERS_BASIC:
                ExternalApiPersBasicInfoTO persBasicInfoTO = getPersBasicInfo4External(apiOrderTO.getKeyNo());
                return JSONObject.toJSONString(persBasicInfoTO);
            case CN_LITIGATION:
                ExternalApiLitigationInfoTO litigationInfoTO = getLitigationInfo4External(apiOrderTO.getKeyNo());
                return JSONObject.toJSONString(litigationInfoTO);
        }
        return null;
    }

    private ExternalApiLitigationInfoTO getLitigationInfo4External(String keyNo) {
        // 获取企业基本信息
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        ExternalApiCompanyProfileTO companyProfile = populateCompanyProfile(corpInfoTO);
        ExternalApiJsonSimpleResultList<ExternalApiCourtCaseTO> caseList = getCourtCaseList(keyNo, "1", "200");
        return ExternalApiLitigationInfoTO.build(companyProfile, caseList);
    }

    private ExternalApiPersBasicInfoTO getPersBasicInfo4External(String keyNo) {
        PersonDetailInfoTO personDetail = ECILocalInterface.getPersonDetail(keyNo);
        ApiStatusExceptionUtils.failBuild(Objects.isNull(personDetail), ApiStatusCodeEnum.NORMAL_STATUS_201);
        ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonBaseInfoTO> legalRepresentative = listLegalRepresentative4Person(keyNo, "1", "200", "0", "1");
        ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonInfoTO> shareholders = listShareholders4Person(keyNo, "1", "200", "1", "1");
        ExternalApiJsonSimpleResultList<ExternalApiSeniorBaseInfoTO> executives = listExecutives4Person(keyNo, "1", "200", "2", "1");
        return ExternalApiPersBasicInfoTO.build(personDetail, legalRepresentative, shareholders, executives);
    }

    /**
     * 提供api接口数据 读取自数据库中json串
     *
     * @param apiOrder
     * @return
     */
    public ExternalApiRespBaseTO provideApiData4External(ExternalApiValidateResult apiOrder) {
        String respStr = apiOrderRespService.getApiRespByCompanyIdAndOrderNo(apiOrder.getCompanyId(), apiOrder.getOrderNo());
        if (StringUtils.isNotBlank(respStr)) {
            ApiTypeEnum apiType = ApiTypeEnum.getEnumByCode(apiOrder.getApiType());
            if (Objects.isNull(apiType)) {
                logger.warn("api type not exists, apiType:{}", apiOrder.getApiType());
                throw new ApiStatusException(ApiStatusCodeEnum.NORMAL_STATUS_201);
            }
            Class<? extends ExternalApiRespBaseTO> targetClass = TYPE_MAP.get(apiType);
            if (targetClass == null) {
                logger.warn("unsupported api type: {}", apiType);
                throw new ApiStatusException(ApiStatusCodeEnum.NORMAL_STATUS_201);
            }
            try {
                return JSONObject.parseObject(respStr, targetClass);
            } catch (Exception e) {
                logger.error("Failed to parse response string to target class: {}", targetClass.getSimpleName(), e);
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        }
        throw new ApiStatusException(ApiStatusCodeEnum.STATUS_204);
    }

    /**
     * 填充工商基本信息
     *
     * @param corpInfoTO
     * @return
     */
    public ExternalApiCompanyProfileTO populateCompanyProfile(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        ExternalApiCompanyProfileTO regInfo = new ExternalApiCompanyProfileTO();
        regInfo.setRegNo(StringUtils.getValue(buyBasicDetailTO.getCreditCode(), null));
        regInfo.setQccCode(StringUtils.getValue(buyBasicDetailTO.getQccCode(), null));
        regInfo.setStatus(ForeignCorpStatusEnum.getForeignStatus(buyBasicDetailTO.getRegistrationStatus(), UnitGroupEnum.CN_UNIT.getGroup()));
        regInfo.setCompanyScriptName(StringUtils.getValue(buyBasicDetailTO.getCompanyNameInLocalLanguage(), null));
        regInfo.setCompanyEnglishName(StringUtils.getValue(buyBasicDetailTO.getCompanyName(), null));
        // 法定代表人 结构修改，整合到企业负责人维度下
        regInfo.setPersonInCharge(handlePersonInChargeInfo(buyBasicDetailTO));
        regInfo.setLegalForm(buyBasicDetailTO.getCompanyTypeEn());
        regInfo.setDateOfIncorp(StringUtils.getValue(buyBasicDetailTO.getEstablishmentDate(), null));
        // 注册地址
        regInfo.setRegisteredAddress(StringUtils.getValue(buyBasicDetailTO.getRegisteredAddress(), null));
        regInfo.setRegisteredAddressEn(StringUtils.getValue(buyBasicDetailTO.getRegisteredAddressEn(), null));

        // 其他编号
        regInfo.setOtherBrn(populateOtherBrnInfo(corpInfoTO));
        // 企业名称生效日期
        List<ExternalApiFormerNameTO> pastNameList = ExternalApiFormerNameTO.build(buyBasicDetailTO);
        if (CollectionUtils.isNotEmpty(pastNameList)) {
            ExternalApiFormerNameTO apiFormerNameTO = pastNameList.stream().filter(item ->
                    StringUtils.equals(regInfo.getCompanyEnglishName(), item.getNameEn())).findFirst().orElse(null);
            regInfo.setCurrentNameEffective(Objects.nonNull(apiFormerNameTO) ? apiFormerNameTO.getStartDate() : null);
            ExternalApiFormerNameTO lastFormerNameTO = pastNameList.stream().filter(item ->
                    !StringUtils.equals(regInfo.getCompanyScriptName(), item.getName())).findFirst().orElse(null);
            if (Objects.nonNull(lastFormerNameTO)) {
                regInfo.setPastOriginalName(lastFormerNameTO.getName());
                regInfo.setPastEnglishName(lastFormerNameTO.getNameEn());
                regInfo.setPastNameEffective(lastFormerNameTO.getStartDate());
            }
        }
        // 行业信息
        regInfo.setIndustryInfo(populateSectorInfo(corpInfoTO));
        GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getEnumByName(corpInfoTO.getCorpJurisdiction());
        regInfo.setJurisdiction(Objects.nonNull(globalAreaEnum) ? globalAreaEnum.getNameCode() : corpInfoTO.getCorpJurisdiction());
        regInfo.setPostalCode(buyBasicDetailTO.getAddressPostalCode());
        return regInfo;
    }

    /**
     * 填充股本信息
     *
     * @param corpInfoTO
     * @return
     */
    public ExternalApiShareCapitalTO populateShareCapital(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        ExternalApiShareCapitalTO shareCapital = new ExternalApiShareCapitalTO();
        // 注册资本数据
        AmountUnit amountUnit = CurrencyUtils.transformAmount(buyBasicDetailTO.getRegisteredCapital());
        if (Objects.nonNull(amountUnit) && amountUnit.getAmount() != null) {
            shareCapital.setSubscribedCapital(amountUnit.getAmount().toPlainString());
            if (Objects.nonNull(amountUnit.getUnit())) {
                shareCapital.setCurrency(amountUnit.getUnit().getCurrencyUnitNameEn());
            }
        }
        // 实缴资本数据
        AmountUnit realAmountUnit = CurrencyUtils.transformAmount(buyBasicDetailTO.getRealCapital());
        if (Objects.nonNull(realAmountUnit) && realAmountUnit.getAmount() != null) {
            shareCapital.setPaidUpCapital(realAmountUnit.getAmount().toPlainString());
        }
        return shareCapital;
    }

    /**
     * 其他编号处理
     *
     * @param corpInfoTO
     * @return
     */
    private List<ExternalApiOtherBrnTO> populateOtherBrnInfo(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        List<ExternalApiOtherBrnTO> otherBrn = new ArrayList<>();
        if (StringUtils.isNotBlank(buyBasicDetailTO.getLeiCode())) {
            ExternalApiOtherBrnTO leiCode = new ExternalApiOtherBrnTO(buyBasicDetailTO.getLeiCode(), "LEI");
            otherBrn.add(leiCode);
        }
        if (StringUtils.isNotBlank(buyBasicDetailTO.getOrgNo())) {
            ExternalApiOtherBrnTO orgNo = new ExternalApiOtherBrnTO(buyBasicDetailTO.getOrgNo(), "Organization Code");
            otherBrn.add(orgNo);
        }
        if (StringUtils.isNotBlank(buyBasicDetailTO.getRegistrationNo())) {
            ExternalApiOtherBrnTO regNo = new ExternalApiOtherBrnTO(buyBasicDetailTO.getRegistrationNo(), "Registration number");
            otherBrn.add(regNo);
        }
        return otherBrn;
    }

    /**
     * @param corpInfoTO
     * @return
     */
    private ExternalApiShareHolderResultTO populateShareholders(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        ExternalApiJsonSimpleResultList<ExternalApiShareHolderInfoTO> resultList = listShareholders(corpInfoTO.getCorpKeyNo(), "1", "200",
                true, corpInfoTO.getBuyBasicDetailTO());
        if (resultList != null && CollectionUtils.isNotEmpty(resultList.getResultList())) {
            return new ExternalApiShareHolderResultTO(resultList.getResultList(), resultList.getTotalCount());
        }
        return null;
    }

    private ExternalApiOfficerResultTO populateOfficers(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        ExternalApiJsonSimpleResultList<ExternalApiOfficerInfoTO> resultList = listEmployees(corpInfoTO.getCorpKeyNo(), "1", "200",
                corpInfoTO.getBuyBasicDetailTO());
        if (resultList != null && CollectionUtils.isNotEmpty(resultList.getResultList())) {
            return new ExternalApiOfficerResultTO(resultList.getResultList(), resultList.getTotalCount());
        }
        return null;
    }

    private HistoryInfoTO getHistoryInfoTO(String keyNo) {
        return CompanyDetailsInterface.getCoyHistoryInfo(keyNo, false);
    }

    private List<ExternalApiPersonInChargeInfoTO> handlePersonInChargeInfo(CorpBasicDetail4UnitTO buyBasicDetailTO) {
        if (Objects.isNull(buyBasicDetailTO) || Objects.isNull(buyBasicDetailTO.getLegalRepresentativeInfo()) ||
                CollectionUtils.isEmpty(buyBasicDetailTO.getLegalRepresentativeInfo().getItems())) {
            return null;
        }
        return handleLegalRepresentative(StringUtils.getValue(buyBasicDetailTO.getOperTypeEn(), "Legal Representative"), buyBasicDetailTO.getLegalRepresentativeInfo());
    }

    private List<ExternalApiPersonInChargeInfoTO> handleLegalRepresentative(String type, LegalRepresentativeInfoTO legalRepresentativeInfoTO) {
        List<ExternalApiPersonInChargeInfoTO> chargeInfoTOList = new ArrayList<>();
        if (Objects.nonNull(legalRepresentativeInfoTO) && CollectionUtils.isNotEmpty(legalRepresentativeInfoTO.getItems())) {
            for (LegalRepresentativeInfoTO.Item item : legalRepresentativeInfoTO.getItems()) {
                if (CollectionUtils.isNotEmpty(item.getAssignorList())) {
                    item.getAssignorList().forEach(assignor -> {
                        ExternalApiPersonInChargeInfoTO personInChargeInfoTO = new ExternalApiPersonInChargeInfoTO();
                        personInChargeInfoTO.setType(type);
                        personInChargeInfoTO.setName(item.getName());
                        personInChargeInfoTO.setNameEn(item.getNameEn());
                        personInChargeInfoTO.setAdditionalPersonName(assignor.getName());
                        personInChargeInfoTO.setAdditionalPersonNameEn(assignor.getNameEn());
                        chargeInfoTOList.add(personInChargeInfoTO);
                    });
                } else {
                    ExternalApiPersonInChargeInfoTO personInChargeInfoTO = new ExternalApiPersonInChargeInfoTO();
                    personInChargeInfoTO.setType(type);
                    personInChargeInfoTO.setName(item.getName());
                    personInChargeInfoTO.setNameEn(item.getNameEn());
                    chargeInfoTOList.add(personInChargeInfoTO);
                }
            }
        }
        return chargeInfoTOList;
    }


    /**
     * 填充联系信息
     *
     * @param corpInfoTO
     * @return
     */
    private ExternalApiContactInfoTO populateContactInfo(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO()) ||
                Objects.isNull(corpInfoTO.getBuyBasicDetailTO().getContactInfo())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        ContactInfoTO contactInfoTO = buyBasicDetailTO.getContactInfo();
        return ExternalApiContactInfoTO.build(contactInfoTO, buyBasicDetailTO.getAddressPostalCode(),
                buyBasicDetailTO.getAnnualAddress(), buyBasicDetailTO.getAnnualAddressEn());
    }

    /**
     * 填充行业信息
     *
     * @param corpInfoTO
     * @return
     */
    private ExternalApiSectorInfoTO populateSectorInfo(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO()) ||
                Objects.isNull(corpInfoTO.getBuyBasicDetailTO().getIndustryInfo())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        IndustryInfoTO industryInfo = buyBasicDetailTO.getIndustryInfo();
        ExternalApiSectorInfoTO sectorInfo = new ExternalApiSectorInfoTO();
        sectorInfo.setSectionCode(StringUtils.getValue(industryInfo.getIndustryCode(), null));
        sectorInfo.setSectionName(StringUtils.getValue(industryInfo.getIndustry(), null));
        sectorInfo.setSectionNameEn(StringUtils.getValue(industryInfo.getIndustryEn(), null));

        sectorInfo.setDivisionCode(StringUtils.getValue(industryInfo.getSubIndustryCode(), null));
        sectorInfo.setDivisionName(StringUtils.getValue(industryInfo.getSubIndustry(), null));
        sectorInfo.setDivisionNameEn(StringUtils.getValue(industryInfo.getSubIndustryEn(), null));

        sectorInfo.setGroupCode(StringUtils.getValue(industryInfo.getMiddleIndustryCode(), null));
        sectorInfo.setGroupName(StringUtils.getValue(industryInfo.getMiddleIndustry(), null));
        sectorInfo.setGroupNameEn(StringUtils.getValue(industryInfo.getMiddleIndustryEn(), null));

        sectorInfo.setClassCode(StringUtils.getValue(industryInfo.getSmallIndustryCode(), null));
        sectorInfo.setClassName(StringUtils.getValue(industryInfo.getSmallIndustry(), null));
        sectorInfo.setClassNameEn(StringUtils.getValue(industryInfo.getSmallIndustryEn(), null));

        // 行业标准 当前固定编码
        sectorInfo.setOriginalStandard("GB/T 4754-2017");
        //构建映射行业信息
        sectorInfo.setMappedSectorInfo(handleMappedSectorInfo(industryInfo));

        sectorInfo.setBusinessScope(StringUtils.getValue(buyBasicDetailTO.getScope(), null));
        sectorInfo.setBusinessScopeEn(StringUtils.getValue(buyBasicDetailTO.getScopeEn(), null));
        return sectorInfo;
    }

    /**
     * updated for v1.9.9 KNZT-4961【新增】【国际版API】补充大陆公司国标映射到联合国行业的一级，二级，三级行业字段（含中文，英文，code）
     * 处理映射的行业信息，如果映射匹配第4级行业存在，则填充所有值，否则返空
     *
     * @param industryInfo
     * @return
     */
    private List<ExternalApiMappedSectorInfoTO> handleMappedSectorInfo(IndustryInfoTO industryInfo) {
        if (Objects.isNull(industryInfo) || StringUtils.isAnyBlank(industryInfo.getIntlSmallIndustryCode(),
                industryInfo.getIntlSmallIndustry(), industryInfo.getIntlSmallIndustryEn())) {
            return null;
        }
        ExternalApiMappedSectorInfoTO sectorInfoTO = new ExternalApiMappedSectorInfoTO();
        sectorInfoTO.setMappedSectionCode(StringUtils.getValue(industryInfo.getIntlIndustryCode(), null));
        sectorInfoTO.setMappedSectionName(StringUtils.getValue(industryInfo.getIntlIndustry(), null));
        sectorInfoTO.setMappedSectionNameEn(StringUtils.getValue(industryInfo.getIntlIndustryEn(), null));
        sectorInfoTO.setMappedDivisionCode(StringUtils.getValue(industryInfo.getIntlSubIndustryCode(), null));
        sectorInfoTO.setMappedDivisionName(StringUtils.getValue(industryInfo.getIntlSubIndustry(), null));
        sectorInfoTO.setMappedDivisionNameEn(StringUtils.getValue(industryInfo.getIntlSubIndustryEn(), null));
        sectorInfoTO.setMappedGroupCode(StringUtils.getValue(industryInfo.getIntlMiddleIndustryCode(), null));
        sectorInfoTO.setMappedGroupName(StringUtils.getValue(industryInfo.getIntlMiddleIndustry(), null));
        sectorInfoTO.setMappedGroupNameEn(StringUtils.getValue(industryInfo.getIntlMiddleIndustryEn(), null));
        sectorInfoTO.setMappedClassCode(StringUtils.getValue(industryInfo.getIntlSmallIndustryCode(), null));
        sectorInfoTO.setMappedClassName(StringUtils.getValue(industryInfo.getIntlSmallIndustry(), null));
        sectorInfoTO.setMappedClassNameEn(StringUtils.getValue(industryInfo.getIntlSmallIndustryEn(), null));
        // 行业标准 当前固定编码
        sectorInfoTO.setMappedSectorStandard("ISIC Rev.4");
        return Lists.newArrayList(sectorInfoTO);
    }

    /**
     * 额外字段补充
     *
     * @param corpInfoTO
     * @return
     */
    public ExternalApiAdditionalFieldsTO populateAdditionalFields(CorpInfoTO corpInfoTO) {
        if (Objects.isNull(corpInfoTO) || Objects.isNull(corpInfoTO.getBuyBasicDetailTO())) {
            return null;
        }
        CorpBasicDetail4UnitTO buyBasicDetailTO = corpInfoTO.getBuyBasicDetailTO();
        ExternalApiAdditionalFieldsTO additionalFields = new ExternalApiAdditionalFieldsTO();
        // 英文别名类型，A-人工；F-官方；O-默认
        additionalFields.setCompanyEnglishNameSource(StringUtils.getValue(buyBasicDetailTO.getEnNameSourceType(), "O"));
        // 经营日期开始 & 经营日期结束 "End date not designated"
        ExternalApiOperationPeriodTO operationPeriod = new ExternalApiOperationPeriodTO(
                StringUtils.getValue(buyBasicDetailTO.getTermStart(), null),
                StringUtils.getValue(buyBasicDetailTO.getTermEnd(), null));
        additionalFields.setRegAuthority(buyBasicDetailTO.getRegistrationAuthorityEn());
        additionalFields.setOperationPeriod(operationPeriod);
        // 日期 格式转化
        additionalFields.setDateApproval(StringUtils.getValue(buyBasicDetailTO.getDateOfApproval(), null));
        additionalFields.setInsuredEmployee(StringUtils.getValue(buyBasicDetailTO.getInsuredCount(), null));
        HistoryInfoTO historyInfoTO = getHistoryInfoTO(corpInfoTO.getCorpKeyNo());
        if (Objects.nonNull(historyInfoTO) && CollectionUtils.isNotEmpty(historyInfoTO.getAddressList())) {
            String startDate = historyInfoTO.getAddressList().stream().filter(item ->
                            StringUtils.equals(item.getAddress(), buyBasicDetailTO.getRegisteredAddress())).findFirst()
                    .map(HistoryInfoTO.HistoryAddressList::getStartDate).orElse(null);
            additionalFields.setRegAddressAsOf(startDate);
        }
        return additionalFields;
    }

    /**
     * 获取最终受益人信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiUBOInfoTO getCorpUBOInfo(String keyNo) {
        return populateUboInfo(keyNo);
    }

    /**
     * 填充最终收益人信息
     *
     * @param keyNo
     * @return
     */
    private ExternalApiUBOInfoTO populateUboInfo(String keyNo) {
        StockResult4UnitTO corpUBO4Unit;
        try {
            corpUBO4Unit = CompanyDetailsInterface.getCorpUBO4Unit(keyNo, false);
        } catch (Exception e) {
            logger.error("populateUboInfo error, keyNo={}", keyNo, e);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        }
        if (corpUBO4Unit == null) {
            return null;
        }
        ExternalApiUBOInfoTO uboInfoTO = new ExternalApiUBOInfoTO();

        if (StringUtils.isNotBlank(corpUBO4Unit.getRemark())) {
            buildUltimateBeneficialWithRemark(uboInfoTO, corpUBO4Unit);
        } else if (CollectionUtils.isNotEmpty(corpUBO4Unit.getBreakThroughListReport())) {
            buildUltimateBeneficialWithBreakThrough(uboInfoTO, corpUBO4Unit);
        }
        uboInfoTO.setIntermediateCompanyList(populateIntermediateCompanyList(corpUBO4Unit.getIntermediateCompanyList()));
        uboInfoTO.setBeneficialOwnerList(populateBeneficialOwnerList(corpUBO4Unit.getBreakThroughListReportBO()));
        return uboInfoTO;
    }

    private void buildUltimateBeneficialWithRemark(ExternalApiUBOInfoTO uboInfoTO, StockResult4UnitTO corpUBO4Unit) {
        List<ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial> ultimateBeneficialList = new ArrayList<>();

        ultimateBeneficialList.add(buildExecutiveBeneficial(corpUBO4Unit));

        if (CollectionUtils.isNotEmpty(corpUBO4Unit.getExecutives())) {
            for (Executive executive : corpUBO4Unit.getExecutives()) {
                ultimateBeneficialList.add(buildExecutiveBeneficial(executive, corpUBO4Unit));
            }
        } else if (CollectionUtils.isNotEmpty(corpUBO4Unit.getHehuoPersonList()) || CollectionUtils.isNotEmpty(corpUBO4Unit.getOtherBeneList())) {
            List<OtherBeneTO> beneList = CollectionUtils.isNotEmpty(corpUBO4Unit.getHehuoPersonList())
                    ? corpUBO4Unit.getHehuoPersonList()
                    : corpUBO4Unit.getOtherBeneList();

            for (OtherBeneTO otherBeneTO : beneList) {
                ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial beneficial = new ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial();
                beneficial.setName(StringUtils.getValue(otherBeneTO.getName(), null));
                beneficial.setNameEn(StringUtils.getValue(otherBeneTO.getNameEn(), null));
                beneficial.setPositions(handlePosition(otherBeneTO.getRole(), null));
                beneficial.setIdentifiedAs(StringUtils.getValue(corpUBO4Unit.getBenifitTypeEn(), null));
                beneficial.setRulesApplied(StringUtils.getValue(corpUBO4Unit.getRemarkEn(), null));
                ultimateBeneficialList.add(beneficial);
            }
        }
        uboInfoTO.setUltimateBeneficialList(ultimateBeneficialList);
    }

    private void buildUltimateBeneficialWithBreakThrough(ExternalApiUBOInfoTO uboInfoTO, StockResult4UnitTO corpUBO4Unit) {
        List<ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial> ultimateBeneficialList = new ArrayList<>();

        for (StockDetail4UnitTO report : corpUBO4Unit.getBreakThroughListReport()) {
            if (report == null) {
                continue;
            }
            ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial beneficial = new ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial();
            beneficial.setName(StringUtils.getValue(report.getName(), null));
            beneficial.setNameEn(StringUtils.getValue(report.getNameEn(), null));
            beneficial.setTotalShareholding(StringUtils.getValue(report.getTotalStockPercent(), null));
            beneficial.setPositions(handlePosition(report.getRole(), null));
            beneficial.setOwnershipChain(populateBreakThroughInfo(report.getDetailInfoList()));
            ultimateBeneficialList.add(beneficial);
        }
        uboInfoTO.setUltimateBeneficialList(ultimateBeneficialList);
    }

    // 提取公共构建方法
    private ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial buildExecutiveBeneficial(Executive executive, StockResult4UnitTO corpUBO4Unit) {
        ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial beneficial = new ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial();
        beneficial.setName(StringUtils.getValue(executive.getName(), null));
        beneficial.setNameEn(StringUtils.getValue(executive.getNameEn(), null));
        beneficial.setPositions(handlePosition(executive.getPosition(), null));
        beneficial.setIdentifiedAs(StringUtils.getValue(corpUBO4Unit.getBenifitTypeEn(), null));
        beneficial.setRulesApplied(StringUtils.getValue(corpUBO4Unit.getRemarkEn(), null));
        return beneficial;
    }

    // 重载用于根节点
    private ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial buildExecutiveBeneficial(StockResult4UnitTO corpUBO4Unit) {
        ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial beneficial = new ExternalApiUBOInfoTO.ExternalApiUltimateBeneficial();
        beneficial.setName(StringUtils.getValue(corpUBO4Unit.getOperName(), null));
        beneficial.setNameEn(StringUtils.getValue(corpUBO4Unit.getOperNameEn(), null));
        beneficial.setPositions(handlePosition(corpUBO4Unit.getPosition(), null));
        beneficial.setIdentifiedAs(StringUtils.getValue(corpUBO4Unit.getBenifitTypeEn(), null));
        beneficial.setRulesApplied(StringUtils.getValue(corpUBO4Unit.getRemarkEn(), null));
        return beneficial;
    }

    /**
     * 填充UBO-股权穿透数据
     *
     * @param detailInfoList
     * @return
     */
    private List<ExternalApiBreakThroughInfoTO> populateBreakThroughInfo(List<StockDetailTO> detailInfoList) {
        if (CollectionUtils.isEmpty(detailInfoList)) {
            return null;
        }
        List<ExternalApiBreakThroughInfoTO> breakThrough = new ArrayList<>();
        for (StockDetailTO stockDetailTO : detailInfoList) {
            ExternalApiBreakThroughInfoTO breakThroughInfoTO = new ExternalApiBreakThroughInfoTO();
            breakThroughInfoTO.setInvestmentType(StringUtils.getValue(stockDetailTO.getStockType(), null));
            breakThroughInfoTO.setShareholding(StringUtils.getValue(stockDetailTO.getBreakthroughStockPercent(), null));
            breakThroughInfoTO.setPath(StringUtils.getValue(stockDetailTO.getPath(), null));
            breakThroughInfoTO.setPathInfo(populatePathInfo(stockDetailTO.getStockPathInfoList()));
            breakThrough.add(breakThroughInfoTO);
        }
        return breakThrough;
    }

    /**
     * 填充-UBO-股权链路中的路径详情
     *
     * @param stockPathInfoList
     * @return
     */
    private List<ExternalApiPathInfoTO> populatePathInfo(List<StockPathInfo> stockPathInfoList) {
        if (CollectionUtils.isEmpty(stockPathInfoList)) {
            return null;
        }
        List<ExternalApiPathInfoTO> pathInfoTOList = new ArrayList<>();
        for (StockPathInfo stockPathInfo : stockPathInfoList) {
            ExternalApiPathInfoTO pathInfoTO = new ExternalApiPathInfoTO();
            pathInfoTO.setName(StringUtils.getValue(stockPathInfo.getName(), null));
            pathInfoTO.setNameEn(StringUtils.getValue(stockPathInfo.getNameEn(), null));
            pathInfoTO.setShareholding(StringUtils.getValue(stockPathInfo.getPercent(), null));
            // 日期转换
            String formatDateStr = stockPathInfo.getStartDate();
            pathInfoTO.setStartDate(StringUtils.getValue(formatDateStr, null));
            pathInfoTOList.add(pathInfoTO);
        }
        return pathInfoTOList;
    }

    /**
     * 填充-UBO-股权穿透中的中介控股公司信息
     * added for lvcy v2.0.0 KNZT-5060
     *
     * @param intermediateCompanyList
     * @return List<ExternalApiStockIntermediateCompanyTO>
     */
    private List<ExternalApiStockIntermediateCompanyTO> populateIntermediateCompanyList(List<StockIntermediateCompanyTO> intermediateCompanyList) {
        if (CollectionUtils.isEmpty(intermediateCompanyList)) {
            return null;
        }
        List<ExternalApiStockIntermediateCompanyTO> intermediateCompanyTOList = new ArrayList<>();
        for (StockIntermediateCompanyTO intermediateCompanyTO : intermediateCompanyList) {
            ExternalApiStockIntermediateCompanyTO intermediateCompany = new ExternalApiStockIntermediateCompanyTO();
            intermediateCompany.setName(StringUtils.getValue(intermediateCompanyTO.getName(), null));
            intermediateCompany.setNameEn(StringUtils.getValue(intermediateCompanyTO.getNameEn(), null));
            GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getEnumByName(intermediateCompanyTO.getJurisdiction());
            intermediateCompany.setJurisdiction(Objects.nonNull(globalAreaEnum) ? globalAreaEnum.getNameCode() : null);
            intermediateCompany.setRegNo(StringUtils.getValue(intermediateCompanyTO.getRegNo(), null));
            intermediateCompanyTOList.add(intermediateCompany);
        }
        return intermediateCompanyTOList;
    }

    private List<ExternalApiStockDetail4UnitTO> populateBeneficialOwnerList(List<StockDetail4UnitTO> beneficialOwnerList) {
        if (CollectionUtils.isEmpty(beneficialOwnerList)) {
            return null;
        }
        List<ExternalApiStockDetail4UnitTO> intermediateCompanyTOList = new ArrayList<>();
        for (StockDetail4UnitTO intermediateCompanyTO : beneficialOwnerList) {
            ExternalApiStockDetail4UnitTO intermediateCompany = new ExternalApiStockDetail4UnitTO();
            intermediateCompany.setName(StringUtils.getValue(intermediateCompanyTO.getName(), null));
            intermediateCompany.setNameEn(StringUtils.getValue(intermediateCompanyTO.getNameEn(), null));
            List<ExternalApiPositionInfoTO> positions = handlePosition(intermediateCompanyTO.getRole(), null);
            intermediateCompany.setPositions(CollectionUtils.isNotEmpty(positions) ? positions : null);
            intermediateCompany.setShareholding(intermediateCompanyTO.getTotalStockPercent());
            intermediateCompany.setInvestmentType(StringUtils.getValue(intermediateCompanyTO.getBenifitTypeEn(), null));
            intermediateCompanyTOList.add(intermediateCompany);
        }
        return intermediateCompanyTOList;
    }

    public ExternalApiJsonSimpleResultList<ExternalApiOfficerInfoTO> listEmployees(String keyNo, String pageIndex,
                                                                                   String pageSize, CorpBasicDetail4UnitTO basicDetailTO) {
        if (Objects.isNull(basicDetailTO)) {
            basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4ExternalAPI(keyNo, "EmployeeList,PubEmployeeList");
        }
        if (Objects.isNull(basicDetailTO)) {
            logger.error("数据异常, keyNo:{} 工商基本信息查询失败", keyNo);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        }
        String type = basicDetailTO.getPubEmployeeTotalRecords() > 0 ? "IpoEmployees" : "Employees";
        CorpEmployeeResult employeeResult = ECILocalInterface.getEmployeeList(keyNo, type, pageIndex, pageSize, false);
        if (employeeResult != null && employeeResult.getResult() != null) {
            CompanyDetailsInterface.populateEmployee(employeeResult.getResult());
            List<ExternalApiOfficerInfoTO> resultList = ExternalApiOfficerInfoTO.build(employeeResult.getResult());
            int totalCount = employeeResult.getPaging() != null ? employeeResult.getPaging().getTotalRecords() : 0;
            return ExternalApiJsonSimpleResultList.buildSuccess(resultList, totalCount);
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(new ArrayList<>());
    }

    public void removeSensitiveInfo4Shareholders(List<ExternalApiShareHolderInfoTO> shareHolderList, boolean companyCategoryFlag) {
        if (CollectionUtils.isEmpty(shareHolderList)) {
            return;
        }
        if (!companyCategoryFlag) {
            shareHolderList.forEach(p -> {
                p.setJurisdiction(null);
                p.setNationality(null);
            });
        }
    }

    public String removeSensitiveInfo4Shareholders(String jsonDataStr, boolean companyCategoryFlag) {
        if (StringUtils.isBlank(jsonDataStr)) {
            return jsonDataStr;
        }
        if (!companyCategoryFlag) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(jsonDataStr);
                if (jsonObject == null) {
                    return jsonDataStr;
                }
                JSONObject shareholdersJson = jsonObject.getJSONObject("shareholders");
                if (shareholdersJson == null) {
                    return jsonDataStr;
                }
                ExternalApiShareHolderResultTO shareHolderResultTO = shareholdersJson.toJavaObject(ExternalApiShareHolderResultTO.class);
                if (shareHolderResultTO != null && CollectionUtils.isNotEmpty(shareHolderResultTO.getResultList())) {
                    removeSensitiveInfo4Shareholders(shareHolderResultTO.getResultList(), false);
                    shareholdersJson = (JSONObject) JSONObject.toJSON(shareHolderResultTO);
                    jsonObject.put("shareholders", shareholdersJson);
                    jsonDataStr = jsonObject.toJSONString();
                }
            } catch (Exception e) {
                logger.error("解析或处理股东信息失败", e);
            }
        }
        return jsonDataStr;
    }

    /**
     * 构建电商报告数据 异步结果保存，当前所有分页接口，默认取前200条数据
     * basic + UBO + 分支机构 + 子公司 + 关联公司 + 发票抬头 + 税务停业记录 + 税务欺诈记录 + 税务信息 + 店铺信息
     *
     * @param apiOrderTO
     * @return
     */
    private ExternalApiCorpMerchantInfoTO getCorpMerchantInfo4External(ApiOrderRespTO apiOrderTO) {
        ExternalApiCorpMerchantInfoTO merchantInfoTO = new ExternalApiCorpMerchantInfoTO();
        ExternalApiCorpUBOInfoTO uboInfo4External = getCorpBasicAndUBOInfo4External(apiOrderTO.getKeyNo());
        if (Objects.isNull(uboInfo4External)) {
            return null;
        }
        BeanUtil.copyProperties(merchantInfoTO, uboInfo4External);
        merchantInfoTO.setqScore(getQScore(apiOrderTO.getKeyNo()));
        merchantInfoTO.setHistoryQScore(getHistoryQScore(apiOrderTO.getKeyNo()));
        merchantInfoTO.setBranches(populateBranches(apiOrderTO.getKeyNo()));
        merchantInfoTO.setSubsidiaries(populateSubsidiaries(apiOrderTO.getKeyNo()));
        merchantInfoTO.setAffiliates(populateAffiliates(apiOrderTO.getKeyNo()));
        merchantInfoTO.setInvoiceLetterhead(getInvoiceLetterhead(apiOrderTO.getKeyNo()));
        ExternalApiJsonSimpleResultList<ExternalApiTaxInformationTO> taxInformation = listTaxInformation(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setTaxInformation(new ExternalApiTaxInformationResultTO(taxInformation.getResultList(), taxInformation.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiTaxSuspensionRecordTO> taxSuspensionRecord = listTaxSuspensionRecords(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setTaxSuspensionRecords(new ExternalApiTaxSuspensionRecordResultTO(taxSuspensionRecord.getResultList(), taxSuspensionRecord.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiTaxFraudRecordTO> taxFraudRecords = getTaxFraudRecords(apiOrderTO.getKeyNo(), "1", "100");
        merchantInfoTO.setTaxFraudRecords(new ExternalApiTaxFraudRecordResultTO(taxFraudRecords.getResultList(), taxFraudRecords.getTotalCount()));
        ExternalApiJsonResultList<ExternalApiOutstandingTaxTO> outstandingTax = listOutstandingTaxes(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setOutstandingTaxes(
                new ExternalApiOutstandingTaxResultTO(
                        outstandingTax.getResultList(),
                        outstandingTax.getTotalCount(),
                        outstandingTax.getSubTotal())
        );
        // 获取店铺信息 从数据库镜像中获取
        ExternalApiStoreInfoTO apiStoreInfo = getStoreInfo(apiOrderTO.getOrderId(), apiOrderTO.getDataStatus());
        merchantInfoTO.setStoreInfoTO(apiStoreInfo);
        ExternalApiJsonSimpleResultList<ExternalApiCustomFilingTO> customFiling = listCustomFilling(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setCustomFiling(new ExternalApiCustomFilingResultTO(customFiling.getResultList(), customFiling.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiAwardTO> awards = listAwards(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setAwards(new ExternalApiAwardResultTO(awards.getResultList(), awards.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiCertAndLicenseTO> certAndLicense = listCertAndLicense(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setCertAndLicense(new ExternalApiCertAndLicenseResultTO(certAndLicense.getResultList(), certAndLicense.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiPatentTO> patents = listPatents(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setPatents(new ExternalApiPatentResultTO(patents.getResultList(), patents.getTotalCount()));
        ExternalApiJsonSimpleResultList<ExternalApiTrademarkTO> trademarks = listTrademarks(apiOrderTO.getKeyNo(), "1", "200");
        merchantInfoTO.setTrademarks(new ExternalApiTrademarkResultTO(trademarks.getResultList(), trademarks.getTotalCount()));
        return merchantInfoTO;
    }

    public ExternalApiStoreInfoTO getStoreInfo(String orderId, String dataStatus) {
        TblCompReportOrder order = new TblCompReportOrder();
        order.setId(orderId);
        order.setRptStatus(dataStatus);
        CorpMerchantShopTO merchantShop = merchantShopBusinessService.getMerchantShop(order);
        return ExternalApiStoreInfoTO.transfer(merchantShop);
    }

    private ExternalApiSubsidiaryResultTO populateAffiliates(String keyNo) {
        ExternalApiJsonSimpleResultList<ExternalApiSubsidiaryInfoTO> resultList = listAffiliates(keyNo, "1", "200");
        if (resultList != null && CollectionUtils.isNotEmpty(resultList.getResultList())) {
            return new ExternalApiSubsidiaryResultTO(resultList, resultList.getTotalCount());
        }
        return null;
    }

    private ExternalApiSubsidiaryResultTO populateSubsidiaries(String keyNo) {
        ExternalApiJsonSimpleResultList<ExternalApiSubsidiaryInfoTO> result = listSubsidiaries(keyNo, "1", "200");
        if (result != null && CollectionUtils.isNotEmpty(result.getResultList())) {
            return new ExternalApiSubsidiaryResultTO(result, result.getTotalCount());
        }
        return null;
    }

    private List<ExternalApiBranchInfoTO> populateBranches(String keyNo) {
        CompanyBranchResult branchResult = CompanyDetailsInterface.getBranchList(keyNo, "1", "20", false);
        if (branchResult != null && CollectionUtils.isNotEmpty(branchResult.getResultList())) {
            return ExternalApiBranchInfoTO.build(branchResult.getResultList());
        }
        return null;
    }

    /**
     * 获取Q评分信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiQScoreTO getQScore(String keyNo) {
        GetCreditRateTO creditRateTO = CompanyDetailsInterface.getCreditRate(keyNo, false);
        TblGlobalIndustry tblGlobalIndustry = null;
        if (creditRateTO != null && creditRateTO.getIndustryAnalysis() != null
                && StringUtils.isNotBlank(creditRateTO.getIndustryAnalysis().getIndustryDesc())) {
            tblGlobalIndustry = commTblGlobalIndustryService.getCnInd2NameEnByName(creditRateTO.getIndustryAnalysis().getIndustryDesc());
        }
        return ExternalApiQScoreTO.transfer(creditRateTO, tblGlobalIndustry);
    }

    /**
     * 获取历史Q评分信息
     *
     * @param keyNo
     * @return
     */
    public List<ExternalApiHistoryQScoreTO> getHistoryQScore(String keyNo) {
        GetCreditRateTrendTO creditRateTrendTO = CompanyDetailsInterface.getCreditRateTrend(keyNo, false);
        return ExternalApiHistoryQScoreTO.transfer(creditRateTrendTO);
    }

    /**
     * 获取发票抬头信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiInvoiceLetterheadTO getInvoiceLetterhead(String keyNo) {
        GetInvoiceDetailTO invoiceDetailTO = CompanyDetailsInterface.getInvoiceDetail(keyNo, false);
        // 获取企业英文名称需要调用基础信息接口
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        String nameEn = corpInfoTO != null && corpInfoTO.getBuyBasicDetailTO() != null
                ? corpInfoTO.getBuyBasicDetailTO().getCompanyName() : null;
        return ExternalApiInvoiceLetterheadTO.transfer(invoiceDetailTO, nameEn);
    }

    /**
     * 获取税务信息列表
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiTaxInformationTO> listTaxInformation(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<GeneralTaxPayerTO> apiResult = CompanyDetailsInterface.listGeneralTaxPayer(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiTaxInformationTO> resultList = ExternalApiTaxInformationTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取税务暂停记录
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiTaxSuspensionRecordTO> listTaxSuspensionRecords(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<GetTaxUnnormalsTO> apiResult = CompanyDetailsInterface.getTaxUnnormals(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiTaxSuspensionRecordTO> resultList = ExternalApiTaxSuspensionRecordTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取税务欺诈记录
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiTaxFraudRecordTO> getTaxFraudRecords(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<GetIllegalListTO> apiResult = CompanyDetailsInterface.getIllegalList(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiTaxFraudRecordTO> resultList = ExternalApiTaxFraudRecordTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取未缴税款信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonResultList<ExternalApiOutstandingTaxTO> listOutstandingTaxes(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<GetListOfOweNoticeNewTO> apiResult = CompanyDetailsInterface.getListOfOweNoticeNew(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
        }

        List<ExternalApiOutstandingTaxTO> resultList = ExternalApiOutstandingTaxTO.transfer(apiResult.getResultList());
        List<ExternalApiGroupItem> subTotal = ExternalApiOutstandingTaxTO.buildSubTotal(apiResult.getGroupItems());

        return ExternalApiJsonResultList.buildSuccess(resultList, apiResult.getTotalCount(), subTotal);
    }

    /**
     * 获取海关备案信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiCustomFilingTO> listCustomFilling(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<IETO> apiResult = CompanyDetailsInterface.listIE(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiCustomFilingTO> resultList = ExternalApiCustomFilingTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取证书和许可证信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiCertAndLicenseTO> listCertAndLicense(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<CertificationListTO> apiResult = CorpGatewayInterface.getCertificationList(keyNo, pageIndex, pageSize);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiCertAndLicenseTO> resultList = ExternalApiCertAndLicenseTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取奖项信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiAwardTO> listAwards(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<HonorCertificationV2TO> apiResult = CompanyDetailsInterface.listHonorCertificationV2(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiAwardTO> resultList = ExternalApiAwardTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取商标信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiTrademarkTO> listTrademarks(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<TrademarkTO> apiResult = CompanyDetailsInterface.listTrademark(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiTrademarkTO> resultList = ExternalApiTrademarkTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    /**
     * 获取专利信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiPatentTO> listPatents(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<PatentTO> apiResult = CompanyDetailsInterface.listPatent(keyNo, pageIndex, pageSize, false);
        if (Objects.isNull(apiResult)) {
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        }
        List<ExternalApiPatentTO> resultList = ExternalApiPatentTO.transfer(apiResult.getResultList());
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, apiResult.getTotalCount());
    }

    public static List<ExternalApiPositionInfoTO> handlePosition(String job, String dateOfAppointment) {
        return handlePosition(job, dateOfAppointment, null);
    }

    public static List<ExternalApiPositionInfoTO> handlePosition(String job, String dateOfAppointment, String dateOfResignation) {
        if (StringUtils.isBlank(job)) {
            return Lists.newArrayList();
        }
        Map<String, String> map = TranslationMapUtils.getMap(TranslationMapEnum.POSITION_MAP);
        List<ExternalApiPositionInfoTO> result = new ArrayList<>();
        String[] splitPosition = StringUtils.split(job, "兼|（|）|，|,|、");
        for (String subPosition : splitPosition) {
            if (StringUtils.isNotBlank(subPosition)) {
                if (subPosition.matches("^[0-9]*$")) {
                    continue;
                }
                subPosition = subPosition.trim();
            }

            String jobNameEn = null;
            if (map.containsKey(subPosition)) {
                jobNameEn = map.get(subPosition);
            } else {
                List<String> fuzzyMatchList = PositionTraslationUtils.getCommonPositionEnByFuzzyMatch(subPosition);
                if (CollectionUtils.isNotEmpty(fuzzyMatchList)) {
                    if (fuzzyMatchList.size() == 1) {
                        jobNameEn = fuzzyMatchList.get(0);
                    } else if (fuzzyMatchList.size() == 2) {
                        jobNameEn = fuzzyMatchList.get(0) + " and " + fuzzyMatchList.get(1);
                    } else {
                        StringBuilder sb = new StringBuilder();
                        int size = fuzzyMatchList.size();
                        for (int i = 0; i < size; i++) {
                            sb.append(fuzzyMatchList.get(i));
                            if (i == size - 2) {
                                sb.append(" and ");
                            } else if (i != size - 1) {
                                sb.append(", ");
                            }
                        }
                        jobNameEn = sb.toString();
                    }
                }
            }
            ExternalApiPositionInfoTO position = new ExternalApiPositionInfoTO();
            position.setPosition(subPosition);
            position.setPositionEn(jobNameEn);
            position.setDateOfAppointment(StringUtils.getNotBlankStr(dateOfAppointment));
            position.setDateOfResignation(StringUtils.getNotBlankStr(dateOfResignation));
            result.add(position);
        }
        return result;
    }

    /**
     * @param keyNo
     */
    public ExternalApiFinancialTaxDataTO getCorpFinTaxInfo4External(String keyNo) {
        FinancialData financialData = ECILocalInterface.getFinancialTaxDetail(keyNo, false);
        ExternalApiFinancialTaxDataTO financialTaxData = ExternalApiFinancialTaxDataTO.transfer(financialData);
        if (Objects.nonNull(financialTaxData)) {
            CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
            ExternalApiCompanyProfileTO companyBaseProfile = populateCompanyProfile(corpInfoTO);
            ExternalApiShareCapitalTO shareCapital = populateShareCapital(corpInfoTO);
            ExternalApiFinancialTaxDataTO.CompanyProfile companyProfile = new ExternalApiFinancialTaxDataTO.CompanyProfile();
            BeanUtils.copyProperties(companyBaseProfile, companyProfile);
            companyProfile.setContactInfo(populateContactInfo(corpInfoTO));
            String dataReportTime = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS) + " " + "UTC+8";
            financialTaxData.setReportDateTime(dataReportTime);
            financialTaxData.setCompanyProfile(companyProfile);
            financialTaxData.setShareCapital(shareCapital);
        }
        return financialTaxData;
    }

    /**
     * @param keyNo
     */
    public ExternalApiFinancialTaxDataTO getCorpFinTaxInfo4ExternalWithMock(String keyNo) {
        // 存入固定数据
        String apiMockData = apiOrderRespService.getApiRespByCompanyIdAndOrderNo("mockCompanyId", "mockOrderNo");
        if (StringUtils.isBlank(apiMockData)) {
            return null;
        }
        ExternalApiFinancialTaxDataTO financialTaxData = new ExternalApiFinancialTaxDataTO();
        ExternalApiFinancialTaxDataTO.FinancialTaxData financialTax = JSONObject.parseObject(apiMockData, ExternalApiFinancialTaxDataTO.FinancialTaxData.class);
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        ExternalApiCompanyProfileTO companyBaseProfile = populateCompanyProfile(corpInfoTO);
        ExternalApiShareCapitalTO shareCapital = populateShareCapital(corpInfoTO);
        ExternalApiFinancialTaxDataTO.CompanyProfile companyProfile = new ExternalApiFinancialTaxDataTO.CompanyProfile();
        BeanUtils.copyProperties(companyBaseProfile, companyProfile);
        companyProfile.setContactInfo(populateContactInfo(corpInfoTO));
        String dataReportTime = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS) + " " + "UTC+8";
        financialTaxData.setReportDateTime(dataReportTime);
        financialTaxData.setCompanyProfile(companyProfile);
        financialTaxData.setShareCapital(shareCapital);
        financialTaxData.setFinancialTaxData(financialTax);
        return financialTaxData;
    }

    public ExternalApiJsonSimpleResultList<ExternalApiBranchInfoTO> getBranchList(String keyNo, String pageIndex, String pageSize) {
        CompanyBranchResult branchResult = CompanyDetailsInterface.getBranchList(keyNo, pageIndex, pageSize, false);
        if (branchResult != null && CollectionUtils.isNotEmpty(branchResult.getResultList())) {
            return ExternalApiJsonSimpleResultList.buildSuccess(ExternalApiBranchInfoTO.build(branchResult.getResultList()), branchResult.getTotalCount());
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiSubsidiaryInfoTO> listSubsidiaries(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listSubsidiaries(keyNo, pageIndex, pageSize, false);
        List<AllInvestmentTO> allInvestmentTOS = Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(Lists.newArrayList());
        List<ExternalApiSubsidiaryInfoTO> targetResp = ExternalApiSubsidiaryInfoTO.build(allInvestmentTOS);
        Long totalCount = Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L);
        return ExternalApiJsonSimpleResultList.buildSuccess(targetResp, totalCount);
    }

    public ExternalApiJsonSimpleResultList<ExternalApiShareHolderInfoTO> listShareholders(
            String keyNo, String pageIndex, String pageSize,
            boolean companyCategoryFlag, CorpBasicDetail4UnitTO basicDetailTO) {
        if (Objects.isNull(basicDetailTO)) {
            basicDetailTO = CompanyDetailsInterface.getCorpBasicInfo4ExternalAPI(keyNo, "PartnerList,PubPartnerList");
        }
        if (Objects.isNull(basicDetailTO)) {
            logger.error("数据异常, keyNo:{} 工商基本信息查询失败", keyNo);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        }
        String type = basicDetailTO.getPubPartnerTotalRecords() > 0 ? "IpoPartners" : "Partners";
        CompanyPartnerResult resultData = ECILocalInterface.getPartnerWithGroup(keyNo, type, pageIndex, pageSize, false);
        if (resultData != null) {
            List<CorpPartnerTO> partnerTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resultData.getResult())) {
                for (CompanyParnter companyParnter : resultData.getResult()) {
                    CorpPartnerTO partnerTO = ECILocalInterface.convertPartner2GlobalPartner(companyParnter, type);
                    partnerTOList.add(partnerTO);
                }
                CompanyDetailsInterface.populateInfoByAdvanceSearch(partnerTOList, null, false);
            }
            List<ExternalApiShareHolderInfoTO> resultList = ExternalApiShareHolderInfoTO.build(partnerTOList, basicDetailTO.getRegisteredCapital());
            int totalCount = resultData.getPaging() != null ? resultData.getPaging().getTotalRecords() : 0;
            return ExternalApiJsonSimpleResultList.buildSuccess(resultList, totalCount);
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(new ArrayList<>());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiSubsidiaryInfoTO> listAffiliates(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listAffiliates(keyNo, pageIndex, pageSize, false);
        Long totalCount = Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L);
        List<AllInvestmentTO> allInvestmentTOS = Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(Lists.newArrayList());
        List<ExternalApiSubsidiaryInfoTO> targetResp = ExternalApiSubsidiaryInfoTO.build(allInvestmentTOS);
        return ExternalApiJsonSimpleResultList.buildSuccess(targetResp, totalCount);
    }

    private ExternalApiHistoryInfoTO populateChangeHistory(String keyNo, CorpBasicDetail4UnitTO buyBasicDetailTO) {
        HistoryInfoTO historyInfoTO = CompanyDetailsInterface.getCoyHistoryInfo(keyNo, false);
        HistOperResult histOperResult = CompanyDetailsInterface.getHistoryOperList(keyNo, false);
        return ExternalApiHistoryInfoTO.build(historyInfoTO, histOperResult, buyBasicDetailTO);
    }

    public ExternalApiJsonSimpleResultList<ExternalApiHistEmployeeTO> listHistoryEmployees(String keyNo, String pageIndex, String pageSize) {
        HistEmployeeResult histEmployeeResult = CompanyDetailsInterface.getHistoryEmployeeList(keyNo, pageIndex, pageSize, false);
        if (histEmployeeResult != null) {
            List<ExternalApiHistEmployeeTO> resultList = ExternalApiHistEmployeeTO.build(histEmployeeResult.getResultList());
            return ExternalApiJsonSimpleResultList.buildSuccess(resultList, histEmployeeResult.getTotalCount());
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiHistoryInvestmentCompTO> listHistoryInvestment(String keyNo, String pageIndex, String pageSize) {
        ApiCompanyHistoryInvestmentResult investmentResult = CompanyDetailsInterface.listCompanyHistoryInvestment(keyNo, pageIndex, pageSize, false);
        Long resultCount = Optional.ofNullable(investmentResult).map(ApiCompanyHistoryInvestmentResult::getTotalCount).orElse(0L);
        List<ExternalApiHistoryInvestmentCompTO> resultList = ExternalApiHistoryInvestmentCompTO
                .build(Optional.ofNullable(investmentResult)
                        .map(ApiCompanyHistoryInvestmentResult::getResultList)
                        .orElse(Lists.newArrayList()));
        return ExternalApiJsonSimpleResultList.buildSuccess(resultList, resultCount);
    }

    public ExternalApiJsonSimpleResultList<ExternalApiEquityShareChangesByDateResultTO> getEquityShareChangesByDate(String keyNo) {
        try {
            EquityShareChangesByDateResultTO equityShareChangesByDate = CorpGatewayInterface.getEquityShareChangesByDate(keyNo);
            List<ExternalApiEquityShareChangesByDateResultTO> list = ExternalApiEquityShareChangesByDateResultTO.build(equityShareChangesByDate);
            if (CollectionUtils.isNotEmpty(list)) {
                return ExternalApiJsonSimpleResultList.buildSuccess(list, list.size());
            }
            return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
        } catch (Exception e) {
            logger.error("获取股东镜像异常", e);
            throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
        }
    }

    private List<ExternalApiEquityShareChangesByDateResultTO> populateHistoryShareholders(String keyNo) {
        ExternalApiJsonSimpleResultList<ExternalApiEquityShareChangesByDateResultTO> equityShareChangesByDate = getEquityShareChangesByDate(keyNo);
        if (equityShareChangesByDate != null && CollectionUtils.isNotEmpty(equityShareChangesByDate.getResultList())) {
            return equityShareChangesByDate.getResultList();
        }
        return null;
    }

    public ExternalApiJsonSimpleResultList<ExternalApiCourtCaseTO> getCourtCaseList(String keyNo, String pageIndex, String pageSize) {
        CourtCaseForm form = new CourtCaseForm();
        form.setPageIndex(pageIndex);
        form.setPageSize(pageSize);
        form.setKeyNo(keyNo);
        JsonResultList<CourtCaseTO> resultPage = CompanyDetailsInterface.getCourtCaseList(form, false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            return ExternalApiJsonSimpleResultList.buildSuccess(ExternalApiCourtCaseTO.build(resultPage.getResultList()), resultPage.getTotalCount());
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiLawEnforceTO> listJudgmentDebtor(String keyNo, String isValid, String pageIndex, String pageSize) {
        CoutZhiXingResult coutZhiXingResult = CompanyDetailsInterface.getZhiXingList(keyNo, isValid, pageIndex, pageSize, false);
        if (coutZhiXingResult != null && CollectionUtils.isNotEmpty(coutZhiXingResult.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiLawEnforceTO.build4ZhiXing(coutZhiXingResult.getResultList(), isValid),
                    coutZhiXingResult.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiLawEnforceTO> listDishonestJudgmentDebtor(String keyNo, String isValid, String pageIndex, String pageSize) {
        CoutShiXinResult coutShiXinResult = CompanyDetailsInterface.getShiXinList(keyNo, isValid, pageIndex, pageSize, false);
        if (coutShiXinResult != null && CollectionUtils.isNotEmpty(coutShiXinResult.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiLawEnforceTO.build4ShiXin(coutShiXinResult.getResultList(), isValid),
                    coutShiXinResult.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiLawEnforceTO> listHighConsumption(String keyNo, String isValid, String pageIndex, String pageSize) {
        PersonSumptuaryResult personSumptuaryResult = CompanyDetailsInterface.getSumptuaryList(keyNo, isValid, pageIndex, pageSize, false);
        if (personSumptuaryResult != null && CollectionUtils.isNotEmpty(personSumptuaryResult.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiLawEnforceTO.build4HighConsumption(personSumptuaryResult.getResultList(), isValid),
                    personSumptuaryResult.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiLawEnforceTO> getLimitExitList(String keyNo, String pageIndex, String pageSize) {
        LimitExitResult limitExitResult = CompanyDetailsInterface.getLimitExitList(keyNo, pageIndex, pageSize, false);
        if (limitExitResult != null && CollectionUtils.isNotEmpty(limitExitResult.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiLawEnforceTO.build4TravelBan(limitExitResult.getResultList()),
                    limitExitResult.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiRegEnforceTO> getRiskPenaltySumList(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<AdministrativePunishmentTO> resultPage = CompanyDetailsInterface.getRiskPenaltySumList(keyNo, pageIndex, pageSize, false);
        if (resultPage != null) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiRegEnforceTO.build4Penalty(resultPage.getResultList()),
                    resultPage.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiRegEnforceTO> getExceptionList(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<CorpExceptionInfoTO> resultPage = CompanyDetailsInterface.getExceptionList(keyNo, pageIndex, pageSize, false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiRegEnforceTO.build4AbnormalOperation(resultPage.getResultList()),
                    resultPage.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonResultList<ExternalApiRegEnforceTO> getSeriousViolationList(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<CorpSeriousViolationTO> resultPage = CompanyDetailsInterface.getSeriousViolationList(keyNo, pageIndex, pageSize, false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            return ExternalApiJsonResultList.buildSuccess(
                    ExternalApiRegEnforceTO.build4SeriousViolation(resultPage.getResultList()),
                    resultPage.getTotalCount()
            );
        }
        return ExternalApiJsonResultList.buildSuccess(Lists.newArrayList());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiChargeTO> getPledgeList(String keyNo, String pageIndex, String pageSize) {
        JsonResultList<CorpPledgeV2TO> resultPage = CompanyDetailsInterface.getPledgeList(keyNo, pageIndex, pageSize, false);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            return ExternalApiJsonSimpleResultList.buildSuccess(
                    ExternalApiChargeTO.build4Pledge(resultPage.getResultList()),
                    resultPage.getTotalCount()
            );
        }
        return ExternalApiJsonSimpleResultList.buildSuccess(Lists.newArrayList());
    }

    /**
     * 通用方法：获取并添加数据（支持分页循环调用）
     *
     * @param targetList   目标结果列表
     * @param maxRecords   最大记录数
     * @param pageSize     分页大小
     * @param dataProvider 数据提供者
     * @param <T>          数据类型
     */
    private <T> void fetchDataWithPagination(
            List<T> targetList,
            int maxRecords,
            int pageSize,
            PageDataProvider<T> dataProvider) {

        int currentPage = 1;
        int currentTotal = 0;

        do {
            // 计算当前页应该获取的记录数
            int currentPageSize = Math.min(pageSize, maxRecords - currentTotal);
            if (currentPageSize <= 0) {
                break;
            }

            try {
                // 获取分页数据
                Object pageResult = dataProvider.getData(String.valueOf(currentPage), String.valueOf(currentPageSize));

                List<T> pageData = new ArrayList<>();
                if (pageResult != null) {
                    // 根据不同的返回类型提取数据列表
                    if (pageResult instanceof ExternalApiJsonResultList) {
                        ExternalApiJsonResultList<T> result = (ExternalApiJsonResultList<T>) pageResult;
                        if (CollectionUtils.isEmpty(result.getResultList())) {
                            break; // 没有更多数据
                        }
                        pageData = result.getResultList();
                    } else if (pageResult instanceof ExternalApiJsonSimpleResultList) {
                        ExternalApiJsonSimpleResultList<T> result = (ExternalApiJsonSimpleResultList<T>) pageResult;
                        if (CollectionUtils.isEmpty(result.getResultList())) {
                            break; // 没有更多数据
                        }
                        pageData = result.getResultList();
                    }
                }

                if (pageData.isEmpty()) {
                    break; // 没有更多数据
                }

                // 添加到结果列表中
                int remainingSpace = maxRecords - targetList.size();

                if (pageData.size() <= remainingSpace) {
                    targetList.addAll(pageData);
                    currentTotal += pageData.size();
                } else {
                    // 只添加剩余需要的数量
                    targetList.addAll(pageData.subList(0, remainingSpace));
                    break;
                }

                // 如果当前页数据少于请求的数量，说明已经到最后一页
                if (pageData.size() < currentPageSize) {
                    break;
                }

                currentPage++;

            } catch (Exception e) {
                logger.error("获取数据失败，currentPage:{}, pageSize:{}", currentPage, pageSize, e);
                // 直接抛出异常，不进行重试
                throw new ApiStatusException(ApiStatusCodeEnum.STATUS_199);
            }
        } while (currentTotal < maxRecords);
    }

    /**
     * 通用数据提供者函数式接口
     */
    @FunctionalInterface
    private interface PageDataProvider<T> {
        Object getData(String pageIndex, String pageSize);
    }

    /**
     * 获取KYC-Lite信息
     * （特定某个账号，需要补充contactInfo维度数据）
     *
     * @param keyNo
     * @param needAddContactInfo
     * @return
     */
    public ExternalApiCorpLiteInfoTO getCorpLiteInfo(String keyNo, boolean needAddContactInfo) {
        CorpInfoTO corpInfoTO = getCorpBasicInfo(keyNo);
        if (Objects.isNull(corpInfoTO)) {
            return null;
        }
        ExternalApiCorpLiteInfoTO corpLiteInfo = needAddContactInfo ? new ExternalApiCorpLiteSpecialInfoTO() : new ExternalApiCorpLiteInfoTO();
        ExternalApiCompanyProfileTO companyProfile = populateCompanyProfile(corpInfoTO);
        BeanUtils.copyProperties(companyProfile, corpLiteInfo);
        String dataReportTime = DateUtils.getDate(DateUtils.DATE_FORMAT_YMDHMS) + " " + "UTC+8";
        corpLiteInfo.setReportDateTime(dataReportTime);
        if (needAddContactInfo) {
            ContactInfoTO contactInfoTO = corpInfoTO.getBuyBasicDetailTO().getContactInfo();
            ExternalApiContactInfoTO contactInfo = ExternalApiContactInfoTO.build(contactInfoTO, corpInfoTO.getBuyBasicDetailTO().getAddressPostalCode(),
                    corpInfoTO.getBuyBasicDetailTO().getAnnualAddress(), corpInfoTO.getBuyBasicDetailTO().getAnnualAddressEn());
            ((ExternalApiCorpLiteSpecialInfoTO) corpLiteInfo).setContactInfo(contactInfo);
        }
        return corpLiteInfo;
    }

    /**
     * 获取海外企业的KYC-Lite信息
     *
     * @param keyNo
     * @return
     */
    public ExternalApiCorpLiteInfoTO getCorpLiteInfo4Hk(String keyNo) {
        QccOvsBasic qccOvsBasic = ovsQccOvsBasicService.getQccOvsBasicByKeyNo(keyNo);
        ExternalApiCorpLiteInfoTO corpLiteInfoTO = ExternalApiCorpLiteInfoTO.buildOvsCorpLiteInfoTO(qccOvsBasic);
        if (Objects.nonNull(corpLiteInfoTO)) {
            // 企业类型需要映射｜翻译
            String originalLegalForm = corpLiteInfoTO.getLegalForm();
            String legalForm;
            if (StringUtils.isNotBlank(originalLegalForm)) {
                legalForm = OverseaCompanyTypeEnum.getTypeEnByType(originalLegalForm, GlobalAreaEnum.getByNameCode(corpLiteInfoTO.getJurisdiction()));
                if (StringUtils.isBlank(legalForm)) {
                    String sourceLanguage = AliyunTranslateSourceTypeEnum.getSourceLanguageByNationCode(corpLiteInfoTO.getJurisdiction());
                    legalForm = translaterService.getEnglishText(originalLegalForm, null, null, null, sourceLanguage);
                }
                corpLiteInfoTO.setLegalForm(legalForm);
            }
            GlobalAreaEnum globalAreaEnum = GlobalAreaEnum.getByNameCode(corpLiteInfoTO.getJurisdiction());
            TransWrapper transWrapper = TransWrapper.buildWrapper(globalAreaEnum == null ? null : globalAreaEnum.getNameCode());
            transWrapper.addEntryOf(corpLiteInfoTO,
                            ExternalApiCorpLiteInfoTO::setCompanyScriptName, ExternalApiCorpLiteInfoTO::setCompanyEnglishName,
                            ExternalApiCorpLiteInfoTO::getCompanyScriptName, ExternalApiCorpLiteInfoTO::getCompanyEnglishName)
                    .withEntity(TransWrapper.ENTRY_TYPE_CORP, null);
            commonTransService.enPostProcessor(transWrapper);
        }
        return corpLiteInfoTO;
    }


    public ExternalApiCorpLiteInfoTO getCorpLiteInfo4External(String keyNo, String companyId, String leiCode) {
        // 获取三码转换数据 判断是否为海外企业，海外企业则走海外数据库获取数据
        if (StringUtils.startsWithAny(keyNo, Constants.CorpTypePrefix.FOREIGN_CORPORATE_PREFIX,
                Constants.CorpTypePrefix.TAIWAN_CORPORATE_PREFIX, Constants.CorpTypePrefix.HONGKONG_CORPORATE_PREFIX)) {
            ExternalApiCorpLiteInfoTO corpLiteInfo4Hk = getCorpLiteInfo4Hk(keyNo);
            if (Objects.nonNull(corpLiteInfo4Hk) && StringUtils.isNotBlank(leiCode)) {
                addCorpOtherBrnInfo(corpLiteInfo4Hk.getOtherBrn(),
                        new ExternalApiOtherBrnTO(leiCode, "LEI"),
                        ArrayList::new, corpLiteInfo4Hk::setOtherBrn);
            }
            return corpLiteInfo4Hk;
        } else {
            // 针对KYC_LITE类型大陆企业，特定客户需要补充联系信息
            String extValue = sysCompInfoColExtService.getColExtValueByTypeAndKey(companyId,
                    Constants.CompInfoColExt.CommExt.EXT_TYPE, Constants.CompInfoColExt.CommExt.COL_NAME_API_LITE_CONTACT);
            return getCorpLiteInfo(keyNo, Constants.YES.equals(extValue));
        }
    }

    public static <T, C extends Collection<T>> void addCorpOtherBrnInfo(
            C list, T element, Supplier<C> collectionFactory, Consumer<C> setter) {
        if (element == null) return;

        if (list != null) {
            list.add(element);
        } else {
            C newList = collectionFactory.get();
            newList.add(element);
            setter.accept(newList);
        }
    }

    public QccEntDetailInfoTO getQccEntDetailInfoTO(String qccCode) {
        QccEntDetailInfoTO qccEntDetailInfoTO;
        try {
            qccEntDetailInfoTO = CompanyDetailsInterface.getQccDetailByEntCode(qccCode, QccEntCodeTypeEnum.QCC_CODE.getCode());
        } catch (MessageException e) {
            throw new ApiStatusException(ApiStatusCodeEnum.NORMAL_STATUS_201);
        }

        if (Objects.isNull(qccEntDetailInfoTO) || StringUtils.isBlank(qccEntDetailInfoTO.getKeyNo())) {
            throw new ApiStatusException(ApiStatusCodeEnum.NORMAL_STATUS_201);
        }
        return qccEntDetailInfoTO;
    }

    public ExternalApiJsonSimpleResultList<ExternalFuzzySearchPersResultTO> searchPers(String searchTerm, String corpName) {
        try {
            // 查老板
            PersonSearchResultTO apiPersonSearchResult = PersSearchService.search(
                    searchTerm, corpName, "1", "20", false, "KYC", false);
            // 没命中到企业数据，给出了一个默认可能数据
            if (Objects.nonNull(apiPersonSearchResult.getGuessedCorp())) {
                String corpKeyNo = apiPersonSearchResult.getGuessedCorp().getKeyNo();
                List<ApiAutocompleteGlobalPersonTO> autocompleteTOResult = FuzzySearchInterface.listGlobalPersonByKeyNo(corpKeyNo);
                if (CollectionUtils.isNotEmpty(autocompleteTOResult)) {
                    List<ExternalFuzzySearchPersResultTO> collect = autocompleteTOResult.stream()
                            .filter(item -> StringUtils.equals(searchTerm, item.getPersonName()))
                            .map(item -> buildExternalResult(item, apiPersonSearchResult.getGuessedCorp()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        return ExternalApiJsonSimpleResultList.buildSuccess(collect, collect.size());
                    }
                }
            } else if (CollectionUtils.isNotEmpty(apiPersonSearchResult.getPersonList())) {
                List<PersonSearchApiTO> filteredList = apiPersonSearchResult.getPersonList().stream()
                        .filter(item -> CollectionUtils.isNotEmpty(item.getRelatedCorps()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(filteredList)) {
                    List<ExternalFuzzySearchPersResultTO> resultList = ExternalFuzzySearchPersResultTO.transferResult(filteredList);
                    return ExternalApiJsonSimpleResultList.buildSuccess(resultList, resultList.size());
                }
            }
            return ExternalApiJsonSimpleResultList.buildFail(ApiStatusCodeEnum.NORMAL_STATUS_201.getCode(),
                    ApiStatusCodeEnum.NORMAL_STATUS_201.getMsg());
        } catch (Exception e) {
            // 记录日志
            logger.error("搜索人员信息异常", e);
            return ExternalApiJsonSimpleResultList.buildFail(ApiStatusCodeEnum.STATUS_199.getCode(),
                    ApiStatusCodeEnum.STATUS_199.getMsg());
        }
    }

    // 提取字段映射逻辑
    private ExternalFuzzySearchPersResultTO buildExternalResult(ApiAutocompleteGlobalPersonTO item, PersonSearchResultTO.CorpInfo guessedCorp) {
        ExternalFuzzySearchPersResultTO result = new ExternalFuzzySearchPersResultTO();
        result.setName(StringUtils.removeEmTags(item.getPersonName()));
        result.setNameEn(StringUtils.removeEmTags(item.getPersonEnglishName()));
        result.setRegionCode(UnitGroupEnum.CN_UNIT.getGroup());
        result.setCompanyScriptName(StringUtils.removeEmTags(guessedCorp.getName()));
        result.setCompanyEnglishName(StringUtils.removeEmTags(guessedCorp.getNameEn()));
        result.setPersonKeyNo(item.getPersonId());
        return result;
    }

    /**
     * 获取高管信息
     *
     * @param keyNo
     * @param pageIndex
     * @param pageSize
     * @param type
     * @param isValid
     * @return
     */
    public ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonBaseInfoTO> listLegalRepresentative4Person(String keyNo, String pageIndex, String pageSize, String type, String isValid) {
        PageDataBO<JSONObject> pageDataBO = listSeniorPerson(keyNo, pageIndex, pageSize, type, isValid);
        List<ExternalApiSeniorPersonBaseInfoTO> seniorPersonList = ExternalApiSeniorPersonBaseInfoTO.transform(pageDataBO.getList());
        return ExternalApiJsonSimpleResultList.buildSuccess(seniorPersonList, pageDataBO.getTotal());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiSeniorPersonInfoTO> listShareholders4Person(String keyNo, String pageIndex, String pageSize, String type, String isValid) {
        PageDataBO<JSONObject> pageDataBO = listSeniorPerson(keyNo, pageIndex, pageSize, type, isValid);
        List<ExternalApiSeniorPersonInfoTO> seniorPersonInfoList = ExternalApiSeniorPersonInfoTO.transformWithPaidUpCapital(pageDataBO.getList());
        return ExternalApiJsonSimpleResultList.buildSuccess(seniorPersonInfoList, pageDataBO.getTotal());
    }

    public ExternalApiJsonSimpleResultList<ExternalApiSeniorBaseInfoTO> listExecutives4Person(String keyNo, String pageIndex, String pageSize, String type, String isValid) {
        PageDataBO<JSONObject> pageDataBO = listSeniorPerson(keyNo, pageIndex, pageSize, type, isValid);
        List<ExternalApiSeniorBaseInfoTO> seniorBaseInfoList = ExternalApiSeniorBaseInfoTO.transform(pageDataBO.getList());
        return ExternalApiJsonSimpleResultList.buildSuccess(seniorBaseInfoList, pageDataBO.getTotal());
    }

    public PageDataBO<JSONObject> listSeniorPerson(String keyNo, String pageIndex, String pageSize, String type, String isValid) {
        PersonCommonForm form = new PersonCommonForm();
        form.setPersonId(form.getKeyNo());
        form.put("isValid", isValid);
        form.put("pageIndex", pageIndex);
        form.put("pageSize", pageSize);
        form.put("type", type);
        form.setPersonId(keyNo);
        PageDataBO<JSONObject> objectPageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listSeniorPersonCorp", form);
        reportOrderService.handleSeniorPersonInfo(objectPageDataBO);
        return objectPageDataBO;
    }
}  
