package com.backend.common.modules.delivery.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.modules.api.entity.TblCompApiChargeUnit;
import com.backend.common.modules.api.entity.TblCompApiOrder;
import com.backend.common.modules.api.service.CommTblCompApiChargeUnitService;
import com.backend.common.modules.common.model.DictItem;
import com.backend.common.modules.delivery.condition.StatementMonthly4ManagementCondition;
import com.backend.common.modules.delivery.condition.TransactionPageCondition;
import com.backend.common.modules.delivery.entity.TblCompStatementMonthly;
import com.backend.common.modules.delivery.entity.TblContractDelivery;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.backend.common.modules.delivery.entity.TblContractDeliveryProdPeriod;
import com.backend.common.modules.delivery.entity.TblContractDeliveryTransaction;
import com.backend.common.modules.delivery.entity.TblContractDeliveryTransactionBillMonthly;
import com.backend.common.modules.delivery.entity.TblSettlementContractHist;
import com.backend.common.modules.delivery.entity.TblSettlementContractRevenue;
import com.backend.common.modules.delivery.form.FinanceConfirmForm;
import com.backend.common.modules.delivery.model.ChargeRecordsInfoListV2TO;
import com.backend.common.modules.delivery.model.FinanceConfirmMonthBillTO;
import com.backend.common.modules.delivery.model.OrderInfo4CorrectTO;
import com.backend.common.modules.delivery.model.SortedFiled;
import com.backend.common.modules.delivery.model.TransactionBO;
import com.backend.common.modules.delivery.model.TransactionListTO;
import com.backend.common.modules.delivery.model.TransactionListV2TO;
import com.backend.common.modules.delivery.model.TransactionOrderDictTO;
import com.backend.common.modules.delivery.model.TransactionSumByMonthTO;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.entity.TblReportChargeUnit;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.CommTblReportChargeUnitService;
import com.backend.common.modules.setting.condition.CompanyChargeHistoryCondition;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.openapi.KzzApiInterface;
import com.backend.common.openapi.model.KzzContractCreateRequestTO;
import com.backend.common.service.CommCompUserService;
import com.backend.common.service.CommSysCompInfoFuncCountService;
import com.backend.common.service.CommSysCompanyService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.server_cache.SysConfigCacheService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.commons.ienum.ApiTypeEnum;
import com.qcc.frame.commons.ienum.BenefitTypeEnum;
import com.qcc.frame.commons.ienum.CompTrackingRecordSourceEnum;
import com.qcc.frame.commons.ienum.CompTypeEnum;
import com.qcc.frame.commons.ienum.ContractSubjectEnum;
import com.qcc.frame.commons.ienum.DeliveryContracProdAccTypeEnum;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.PayStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.commons.ienum.TransactionTypeEnum;
import com.qcc.frame.commons.ienum.UnitGroupEnum;
import com.qcc.frame.jee.commons.persistence.Page;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.JsonUtils;
import com.qcc.frame.jee.commons.utils.MappingUtils;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.ObjectUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.entity.Company;
import com.qcc.frame.jee.modules.sys.entity.SysCompInfoFuncCount;
import com.qcc.frame.jee.modules.sys.entity.User;
import com.qcc.frame.jee.modules.sys.service.ThreadPoolService;
import com.qcc.frame.jee.modules.sys.service.UserService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * added for v1.8.8 KNZT-3324
 * 交易综合service
 *
 * <AUTHOR>
 * @datetime 2024/7/3 12:24
 */
@Service
public class TransactionBusinessService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommTblContractDeliveryProdAccService contractDeliveryProdAccService;
    @Autowired
    private CommTblContractDeliveryTransactionService contractDeliveryTransactionService;
    @Autowired
    private CommSysCompanyService commSysCompanyService;
    @Autowired
    private CommTblContractDeliveryTransactionBillMonthlyService contractDeliveryTransactionBillMonthlyService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblContractDeliveryProdService prodService;
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private CommSysCompInfoFuncCountService funcCountService;
    @Autowired
    private CommCrmCompTrackingRecordService commCrmCompTrackingRecordService;
    @Autowired
    private CommTblContractDeliveryProdPeriodService prodPeriodService;
    @Autowired
    private SysConfigCacheService sysConfigCacheService;
    @Autowired
    private CommTblReportChargeUnitService commTblReportChargeUnitService;
    @Autowired
    private CommTblCompApiChargeUnitService commTblCompApiChargeUnitService;
    @Autowired
    private CommTblSettlementContractHistService commTblSettlementContractHistService;
    @Autowired
    private CommTblSettlementContractRevenueService commTblSettlementContractRevenueService;
    @Autowired
    private UserService userService;
    @Autowired
    private CommCompUserService commCompUserService;
    @Autowired
    private CommonTransService commonTransService;
    @Autowired
    private ThreadPoolService threadPoolService;

    public Page<FinanceConfirmMonthBillTO> listMonthBill(FinanceConfirmForm form) {
        List<TblContractDeliveryTransactionBillMonthly> list = contractDeliveryTransactionBillMonthlyService.pageByCondition(form.getCondition());
        Page<FinanceConfirmMonthBillTO> page = new Page<>();
        if (CollectionUtils.isEmpty(list)) return page;
        List<TblContractDelivery> contractDeliveryList = contractDeliveryService.getByContractNoList(
                list.stream().map(TblContractDeliveryTransactionBillMonthly::getContractNo).collect(Collectors.toList()));

        Map<String, TblContractDelivery> contractDeliveryMap = contractDeliveryList.stream().collect(Collectors.toMap(TblContractDelivery::getContractNo, Function.identity(), (a, b) -> a));

        List<User> userList = userService.listMainUserByCompanyId(contractDeliveryList.stream().map(TblContractDelivery::getCompanyId).collect(Collectors.toList()));
        Map<String, String> userMap = buildUserMap(contractDeliveryList, userList);
        List<FinanceConfirmMonthBillTO> billList = list.stream().map(bill -> {
            TblContractDelivery contractDelivery = contractDeliveryMap.get(bill.getContractNo());
            String loginName = userMap.get(bill.getContractNo());
            return FinanceConfirmMonthBillTO.build(contractDelivery, bill, loginName);
        }).collect(Collectors.toList());

        BeanUtils.copyProperties(form.getCondition().getPage(), page);
        page.setList(billList);
        return page;
    }

    /**
     * 构建合同编号到用户登录名的映射
     */
    public static Map<String, String> buildUserMap(List<TblContractDelivery> contractDeliveryList, List<User> userList) {
        Map<String, String> userMap = new HashMap<>();
        // 按合同编号分组合同交付信息
        Map<String, List<TblContractDelivery>> deliveryMap = contractDeliveryList.stream()
                .collect(Collectors.groupingBy(TblContractDelivery::getContractNo));
        deliveryMap.forEach((contractNo, deliveries) -> {
            List<String> companyIds = deliveries.stream()
                    .map(TblContractDelivery::getCompanyId)
                    .collect(Collectors.toList());
            String userLoginName = userList.stream()
                    .filter(user -> companyIds.contains(user.getCompanyId()))
                    .map(User::getLoginName)
                    .collect(Collectors.joining(","));
            userMap.put(contractNo, userLoginName);
        });
        return userMap;
    }


    // updated for v2.3.1 fengsw KNZT-7904 财税API 退款流程修改
    public void createTransaction4Refund(String oriOrderId, String companyId, BigDecimal totalUnit, TransactionTypeEnum refundTransactionType, String oriTransactionType) throws MessageException {
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");
        // added for v1.9.8 KNZT-4538
        SysCompInfoFuncCount funcCount = funcCountService.lockByCompanyId(companyId);
        MsgExceptionUtils.checkIsNull(funcCount);
        funcCountService.consumeFuncCount(funcCount, totalUnit.negate());

        if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            logger.info("公司类型是试用，不生成退款流水，companyId:{}", companyId);
            return;
        }

        List<TblContractDeliveryProdAcc> accList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);

        List<TblContractDeliveryTransaction> oriTransactionList = contractDeliveryTransactionService.getByRelId(oriOrderId, oriTransactionType);
        if (CollectionUtils.isEmpty(oriTransactionList)) {
            // 问题场景
            // 1.试用期下的订单，在转签约后进行退款
            // 2.问题数据，缺少订单流水
            logger.error("退款订单流水记录没有找到，检查数据，oriOrderId:{}", oriOrderId);
            return;
        }

        // 一笔订单可能生成多笔流水
        for (TblContractDeliveryTransaction oriTransaction : oriTransactionList) {
            BigDecimal oriTransactionChangedUnit = oriTransaction.getChangedUnit();

            TblContractDeliveryProdAcc oriAcc = accList.stream().filter(acc -> Objects.equals(acc.getId(), oriTransaction.getContractDeliveryProdAccId())).findAny().orElse(null);
            // 如果退款账户有效，则退回到原账户
            if (Objects.nonNull(oriAcc)) {
                TblContractDeliveryTransaction refundTransaction = TblContractDeliveryTransaction
                        .init(companyId, refundTransactionType.getCode(), funcCount.calRemainCount())
                        .injectRelId(oriOrderId)
                        .injectChangedUnit(oriTransactionChangedUnit.negate());
                this.saveTransactionAndChangeAccUnit(refundTransaction, oriAcc);
            } else {
                logger.info("退款账户不生效，不生成退款流水，oriOrderId:{}", oriOrderId);
            }
        }
    }


    public void consumeTransaction(String companyId, String relId, String chargeUnitContractDeliveryId,
                                   BigDecimal totalUnit, Date orderCreateDate, String transactionType,
                                   String highPriorityProdAccId) throws MessageException {
        if (BigDecimal.ZERO.compareTo(totalUnit) == 0) {
            logger.info("credits为0，不生成流水，relId:{}", relId);
            return;
        }
        MsgExceptionUtils.checkIsNull(companyId, "msg:缺少companyId");
        Company company = commSysCompanyService.get(companyId);
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");

        // added for v1.9.8 KNZT-4538
        SysCompInfoFuncCount funcCount = funcCountService.lockByCompanyId(companyId);
        funcCountService.consumeFuncCount(funcCount, totalUnit);

        if (CompTypeEnum.TRIAL.getCode().equals(company.getType())) {
            logger.info("公司类型为试用，不生成流水，companyId:{}", companyId);
            return;
        }

        List<TblContractDeliveryProdAcc> accList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);

        MsgExceptionUtils.checkIsNull(accList, "err.amount.insufficient");
        // 根据规则排序
        Collections.sort(accList);
        // 如果存在高优先级账户，将其移到列表首位
        if (StringUtils.isNotBlank(highPriorityProdAccId)) {
            accList.stream()
                    .filter(acc -> Objects.equals(acc.getId(), highPriorityProdAccId))
                    .findFirst()
                    .ifPresent(acc -> {
                        accList.remove(acc);
                        accList.add(0, acc);
                    });
        }
        // 递归找到产品账户生成流水
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.init(companyId, transactionType, funcCount.calRemainCount())
                .injectRelId(relId)
                .injectChargeUnitContractDeliveryId(chargeUnitContractDeliveryId)
                .injectDateInt(orderCreateDate);
        boolean generateResult = recursionGenerateTransaction(accList, 0, totalUnit, transaction);
        // 如果递归扣减没有成功，则说明没有足够的余额
        MsgExceptionUtils.failBuild(!generateResult, "err.amount.insufficient");
    }

    /**
     * 创建B端订单流水
     *
     * @param order
     * @param chargeUnitContractDeliveryId
     * @return
     */
    public void payOrderAndCreateTransaction(TblCompReportOrder order, String chargeUnitContractDeliveryId, String highPriorityProdAccId) throws MessageException {
        this.consumeTransaction(order.getCompanyId(), order.getId(), chargeUnitContractDeliveryId,
                order.getTotalUnit(), order.getCreateDate(), TransactionTypeEnum.ORDER.getCode(), highPriorityProdAccId);
        order.setPayStatus(PayStatusEnum.PAID.getCode());
        order.setPayResponseTime(new Date());
        commTblCompReportOrderService.save(order);
    }

    public void payOrderAndCreateTransaction(TblCompReportOrder order, String chargeUnitContractDeliveryId) throws MessageException {
        this.payOrderAndCreateTransaction(order, chargeUnitContractDeliveryId, null);
    }
    /*
     *//**
     * 创建自助账户流水
     * added for v1.9.4 KNZT-4324
     *
     * @param order
     * @return
     *//*
    public void createTransaction4SelfAccountOrder(TblCompReportOrder order) throws MessageException {
        TblContractDeliveryTransaction transaction = TblContractDeliveryTransaction.init(order.getCompanyId(), TransactionTypeEnum.ORDER.getCode())
                .injectRelId(order.getId())
                .injectChargeUnitContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID)
                .injectDateInt(order.getCreateDate())
                .injectChangedUnit(order.getTotalUnit());
        transaction.setContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID);
        transaction.setContractDeliveryProdAccId(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID);
        transaction.setBeforeRemainUnit(Constants.MAX_UNIT);
        contractDeliveryTransactionService.save(transaction);
        logger.info("saveTransactionAndChangeAccUnit 生成自助账户订单流水, companyId:{}, changedUnit:{}",
                transaction.getCompanyId(), transaction.getChangedUnit());
    }*/

    /**
     * 创建api订单流水
     * added for v1.9.7 KNZT-4708
     *
     * @param order
     * @param chargeUnitContractDeliveryId
     * @return
     */
    public void createTransaction4ApiOrder(TblCompApiOrder order, String chargeUnitContractDeliveryId) throws MessageException {
        this.consumeTransaction(order.getCompanyId(), order.getId(), chargeUnitContractDeliveryId,
                order.getTotalUnit(), order.getCreateDate(), TransactionTypeEnum.API_ORDER.getCode(), null);
    }


/*    public void processProdAccTransactionDaily(TblContractDeliveryProdAcc prodAcc, LocalDate date) {
        // 计算一天内的流水总和
        Integer dateInt = DateUtils.formatDateToInt(date);
        BigDecimal sumChangedUnit = contractDeliveryTransactionService.sumChangedUnitByProAccAndDate(prodAcc.getId(), dateInt);

        TblContractDeliveryTransactionBillDaily bill = contractDeliveryTransactionBillDailyService.getByProdAccAndDate(prodAcc.getId(), dateInt);
        if (Objects.isNull(bill)) {
            bill = new TblContractDeliveryTransactionBillDaily();
            bill.setContractDeliveryProdAccId(prodAcc.getId());
            bill.setContractDeliveryId(prodAcc.getContractDeliveryId());
            bill.setCompanyId(prodAcc.getCompanyId());
            bill.setDateInt(dateInt);
        }
        bill.setConsumedUnitDay(sumChangedUnit);
        BigDecimal latestTotalConsumedUnit = contractDeliveryTransactionBillDailyService.getLatestTotalConsumedUnitBeforeDate(prodAcc.getId(), dateInt);
        bill.setTotalConsumedUnit(latestTotalConsumedUnit.add(sumChangedUnit));
        bill.setRemainUnit(prodAcc.getTotalUnit().subtract(bill.getTotalConsumedUnit()));

        contractDeliveryTransactionBillDailyService.save(bill);
        logger.info("processProdAccTransactionDaily calculate done, date:{}, contractDeliveryId:{}, consumedUnitDay:{}"
                , date, bill.getContractDeliveryProdAccId(), bill.getConsumedUnitDay());

    }*/

    /**
     * 自助账户流水日账计算
     * added for v1.9.4 KNZT-4324
     *
     * @return
     */
/*    public void processProdAccTransactionDaily4SelfAccountOrder(LocalDate date) {
        Integer dateInt = DateUtils.formatDateToInt(date);
        BigDecimal sumChangedUnit = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID, dateInt, dateInt);

        TblContractDeliveryTransactionBillDaily bill = contractDeliveryTransactionBillDailyService.getByProdAccAndDate(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID, dateInt);
        if (Objects.isNull(bill)) {
            bill = new TblContractDeliveryTransactionBillDaily();
            bill.setContractDeliveryProdAccId(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID);
            bill.setContractDeliveryId(Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID);
            bill.setCompanyId(Constants.DEFAULT_COMPANY_ID);
            bill.setDateInt(dateInt);
        }
        bill.setConsumedUnitDay(sumChangedUnit);
        BigDecimal latestTotalConsumedUnit = contractDeliveryTransactionBillDailyService.getLatestTotalConsumedUnitBeforeDate(Constants.Delivery.SELF_ACCOUNT_PROD_ACC_ID, dateInt);
        bill.setTotalConsumedUnit(latestTotalConsumedUnit.add(sumChangedUnit));
        bill.setRemainUnit(Constants.MAX_UNIT);

        contractDeliveryTransactionBillDailyService.save(bill);
        logger.info("processProdAccTransactionDaily4SelfAccountOrder calculate done, date:{}, contractDeliveryId:{}, consumedUnitDay:{}"
                , date, bill.getContractDeliveryProdAccId(), bill.getConsumedUnitDay());
    }*/

    // updated for lvcy v2.0.8 KNZT-5828
    public void processContractTransactionMonthly(String contractNo, BigDecimal actualDiscountAmount, YearMonth yearMonth) {
        BigDecimal prodTotalAmount = prodService.sumProdTotalAmountByContractNo(contractNo);
        prodTotalAmount = Objects.isNull(prodTotalAmount)
                || prodTotalAmount.compareTo(BigDecimal.ZERO) == 0
                || prodTotalAmount.compareTo(Constants.MAX_UNIT) > 0
                ? Constants.MAX_UNIT : prodTotalAmount;
        BigDecimal prodTotalUnit = prodService.sumProdTotalUnitByContractNo(contractNo);
        prodTotalUnit = Objects.isNull(prodTotalUnit)
                || prodTotalUnit.compareTo(BigDecimal.ZERO) == 0
                || prodTotalUnit.compareTo(Constants.MAX_UNIT) > 0
                ? Constants.MAX_UNIT : prodTotalUnit;
        logger.info("processContractTransactionMonthly contractNo:{}, actualDiscountAmount:{}, yearMonth:{}", contractNo, actualDiscountAmount, yearMonth);

        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate endDayOfMonth = yearMonth.atEndOfMonth();
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal beforeConsumedUnitMonth = contractDeliveryTransactionBillMonthlyService
                .getLatestTotalConsumedUnitBeforeYearMonthByContractNo(contractNo, yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode());
        // 本期消耗单元数 = 月中每日的日账总和
        BigDecimal consumedUnitMonth = contractDeliveryTransactionService.sumChangedUnitByContractNoAndDateRange(
                contractNo, DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        // 累计消耗单元数 = 上期月账累计消耗单元数 + 本期消耗单元数
        BigDecimal totalConsumedUnit = beforeConsumedUnitMonth.add(consumedUnitMonth);
        // 本期消耗金额 = 本期消耗单元数 * 产品实际单价
        BigDecimal consumedAmountMonth = consumedUnitMonth.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // 累计消耗金额 = 累计消耗单元数 * 产品实际单价
        BigDecimal totalConsumedAmount = totalConsumedUnit.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // 如果单元数已经消耗完，对数据作修正 （避免小数点误差问题、避免数据问题导致的对不齐问题）
        if (BigDecimal.ZERO.compareTo(prodTotalUnit) != 0 && totalConsumedUnit.compareTo(prodTotalUnit) >= 0) {
            // 本期消耗单元数 = 产品总单元数 - 上期累计消耗单元数
            consumedUnitMonth = prodTotalUnit.subtract(beforeConsumedUnitMonth);
            // 累计消耗单元数 = 产品总单元数
            totalConsumedUnit = prodTotalUnit;
            // 本期消耗金额 = 产品金额 - 上期累计消耗金额
            BigDecimal beforeTotalConsumedAmount = beforeConsumedUnitMonth.multiply(actualDiscountAmount)
                    .setScale(2, RoundingMode.HALF_UP);
            consumedAmountMonth = prodTotalAmount.subtract(beforeTotalConsumedAmount);
            // 累计消耗金额 = 产品金额
            totalConsumedAmount = prodTotalAmount;
        }
        BigDecimal remainUnit = prodTotalUnit.subtract(totalConsumedUnit);
        BigDecimal remainAmount = prodTotalAmount.subtract(totalConsumedAmount);

        logger.info("processContractTransactionMonthly contractNo:{}, yearMonth:{}, consumedUnitMonth:{}, totalConsumedUnit:{}, remainUnit:{}, consumedAmountMonth:{}, totalConsumedAmount:{}, remainAmount:{}",
                contractNo, yearMonth, consumedUnitMonth, totalConsumedUnit, remainUnit, consumedAmountMonth, totalConsumedAmount, remainAmount);
        TblContractDeliveryTransactionBillMonthly bill = TblContractDeliveryTransactionBillMonthly
                .build(contractNo, yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode())
                .injectUnit(consumedUnitMonth, totalConsumedUnit, remainUnit)
                .injectAmount(consumedAmountMonth, totalConsumedAmount, remainAmount);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);

        // 本期 订单、api订单涉及中国的单元数
        BigDecimal cnConsumedUnitMonth = contractDeliveryTransactionService.sumCnChangedUnitByContractNoAndDateRange(contractNo,
                DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        // QCC 本期消耗单元数 = 订单、api订单涉及中国的单元数 * 0.2 
        BigDecimal qccConsumedUnitMonth = cnConsumedUnitMonth.multiply(BigDecimal.valueOf(0.2)).setScale(2, RoundingMode.HALF_UP);
        // QCC 本期消耗金额 = QCC 本期消耗单元数 * 产品实际单价
        BigDecimal qccConsumedAmountMonth = qccConsumedUnitMonth.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        // QCC 上期月账累计消耗单元数
        BigDecimal cnBeforeTotalConsumedUnitMonth = contractDeliveryTransactionBillMonthlyService.getLatestTotalConsumedUnitBeforeYearMonthByContractNo(
                contractNo, yearMonthInt, ContractSubjectEnum.QCC.getCode());
        // QCC 累计消耗单元数 = QCC 上期月账累计消耗单元数 + QCC 本期消耗单元数
        BigDecimal qccTotalConsumedUnit = cnBeforeTotalConsumedUnitMonth.add(qccConsumedUnitMonth);
        // QCC 累计消耗金额 = QCC 累计消耗单元数 * 产品实际单价
        BigDecimal qccTotalConsumedAmount = qccTotalConsumedUnit.multiply(actualDiscountAmount).setScale(2, RoundingMode.HALF_UP);
        logger.info("processContractTransactionMonthly qccBill contractNo:{}, yearMonth:{}, qccConsumedUnitMonth:{}, qccTotalConsumedUnit:{}, qccConsumedAmountMonth:{}, qccTotalConsumedAmount:{}",
                contractNo, yearMonth, qccConsumedUnitMonth, qccTotalConsumedUnit, qccConsumedAmountMonth, qccTotalConsumedAmount);
        TblContractDeliveryTransactionBillMonthly qccBill = TblContractDeliveryTransactionBillMonthly
                .build(contractNo, yearMonthInt, ContractSubjectEnum.QCC.getCode())
                .injectUnit(qccConsumedUnitMonth, qccTotalConsumedUnit, BigDecimal.ZERO)
                .injectAmount(qccConsumedAmountMonth, qccTotalConsumedAmount, BigDecimal.ZERO);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(qccBill);
        logger.info("processProdAccTransactionMonthly calculate done, contractNo:{}, yearMonth:{}", contractNo, yearMonth);
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 手动触发账单推送
     * <p>
     * updated for v2.1.1 fengsw KNZT-6123
     * update for v2.1.2 fengsw KNZT-6211 推送合同并保存推送记录，推送成功之后，生成收入结算的产品统计信息
     *
     * @param contractNoList
     * @param yearMonth
     */
    public void pushConfirmAmountContract2KzzByManual(List<String> contractNoList, YearMonth yearMonth) {
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);
        List<String> errorContractNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractNoList)) {
            for (String contractNo : contractNoList) {
                // updated for v2.3.0 fengsw KNZT-8045 过滤增加内部测试账号数据
                TblContractDeliveryTransactionBillMonthly monthBill = getMonthBillExcludeTestAccount(contractNo, yearMonthInt);
                String result = pushConfirmAmountContract2KzzFirstDayPerMonth(monthBill, yearMonth);
                if (StringUtils.isNotBlank(result)) {
                    errorContractNoList.add(contractNo);
                }
            }
        }

        String contractNo = pushSelfConfirmAmountContract2KzzByManual(yearMonth);
        if (StringUtils.isNotBlank(contractNo)) {
            errorContractNoList.add(contractNo);
        }
        if (CollectionUtils.isNotEmpty(errorContractNoList)) {// 同步出错数据需要钉钉提醒
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk("收入确认合同推送异常", AlarmTypeEnum.PUSH_KZZ_CRATE_CONTRACT_ERROR, String.join(",", errorContractNoList), String.valueOf(yearMonthInt), DateUtils.getDate());
        }
    }

    /**
     * 排除测试账号数据
     *
     * @param yearMonth
     * @return
     */
    public String pushSelfConfirmAmountContract2KzzByManual(YearMonth yearMonth) {
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);
        TblContractDeliveryTransactionBillMonthly monthBill = getSelfAccMonthBill(yearMonthInt);
        monthBill.setContractNo("KYCOL");
        String result = pushConfirmAmountContract2KzzFirstDayPerMonth(monthBill, yearMonth);
        if (StringUtils.isNotBlank(result)) {
            return monthBill.getContractNo();
        }
        return null;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * <p>
     * 根据金额，构建合同信息，推送合同数据到客找找，如果有问题，需要打印错误日志，并钉钉提醒，
     * 固定主框架合同配置数据，项目缓存初始化的时候，加载到缓存对象中
     * 客户合同信息 调用接口获取合同信息内容 组装部分合同数据
     * 根据确认收入金额，填充回款计划、本次推送合同金额
     * 推送合同到客找找，打印输出日志，异常处理
     * 自助账号 合同签订人、创建人、回款计划跟进人取默认内置用户
     * 合同编号生成规则；正常合同 合同编号-年月；
     * 注：当前每月金额为0，负数都是要推送到客找找的
     *
     * @param monthlyBill
     * @param yearMonth
     */
    public String pushConfirmAmountContract2KzzFirstDayPerMonth(TblContractDeliveryTransactionBillMonthly monthlyBill, YearMonth yearMonth) {
        if (monthlyBill == null || StringUtils.isBlank(monthlyBill.getContractNo()) || monthlyBill.getSubjectConsumedAmountMonth() == null || yearMonth == null) {
            return null;
        }

        String yearMonthStr = yearMonth.toString();
        // 已经同步成功过的数据，不用进行处理
        String kzzContractNo = StringUtils.startsWith(monthlyBill.getContractNo(), "KYCOL") ? "KYCOL" + monthlyBill.getYearMonthInt() : monthlyBill.getContractNo() + "-" + monthlyBill.getYearMonthInt();
        TblSettlementContractHist taskRecord = commTblSettlementContractHistService.getByContractNoAndYearMonth(kzzContractNo, yearMonthStr);
        if (taskRecord != null && StringUtils.equals("S", taskRecord.getSyncStatus())) {
            logger.info("contractNo:{}, has pushed kzz", monthlyBill.getContractNo());
            return null;
        }

        // 生产上的测试数据，不能推送到客找找
        if (StringUtils.contains(monthlyBill.getContractNo(), "TEST")) {
            logger.info("prod test contractNo:{}, doesn't need push kzz", monthlyBill.getContractNo());
            return null;
        }

        logger.info("contractNo: {}, yearMonth:{}, confirm amount: {} push start", monthlyBill.getContractNo(), monthlyBill.getYearMonthInt(), monthlyBill.getSubjectConsumedAmountMonth());

        if (Objects.isNull(taskRecord)) {
            taskRecord = new TblSettlementContractHist();
            taskRecord.setYearMonth(yearMonthStr);
        }

        try {
            BigDecimal amount = monthlyBill.getSubjectConsumedAmountMonth();
            KzzContractCreateRequestTO createRequestTO = createContractRequest(monthlyBill.getContractNo(), amount, monthlyBill.getYearMonthInt(), yearMonth);
            String requestJSONStr = JSON.toJSONString(createRequestTO);
            String responseStr = KzzApiInterface.postJsonRequest("/openapi/kzz/contract/v2/add", requestJSONStr);
            taskRecord.setContractNo(createRequestTO.getCode());
            taskRecord.setReqJson(requestJSONStr);
            taskRecord.setRespJson(responseStr);
            JSONObject jsonObj = JsonUtils.parseObject(responseStr);
            if (jsonObj == null) {
                throw new MessageException("接口响应内容为空");
            }
            if (!"OK".equals(jsonObj.getString("status"))) {
                throw new MessageException(jsonObj.getString("error"));
            }
            taskRecord.setSyncStatus("S");
            commTblSettlementContractHistService.save(taskRecord);
            // 保存收入确认数据, 先查一遍 如果没有则插入，有则更新
            List<TblSettlementContractRevenue> tblSettlementContractRevenueList = commTblSettlementContractRevenueService.getByContractNoAndYearMonth(kzzContractNo, yearMonthStr);
            List<TblContractDelivery> contractInfoList = contractDeliveryService.getByContractNo(monthlyBill.getContractNo());
            String currency = CollectionUtils.isNotEmpty(contractInfoList) ? contractInfoList.get(0).getAmountStd() : "USD";
            TblSettlementContractRevenue tblSettlementContractRevenue = null;
            if (CollectionUtils.isNotEmpty(tblSettlementContractRevenueList)) {
                tblSettlementContractRevenue = tblSettlementContractRevenueList.get(0);// 目前收入确认就一个产品数据
            }
            tblSettlementContractRevenue = buildConfirmContractProductInfo(tblSettlementContractRevenue, createRequestTO, yearMonthStr, currency);
            commTblSettlementContractRevenueService.save(tblSettlementContractRevenue);
            logger.info("contractNo: {}, relate kzz contractNo: {}, year Month:{}, confirm amount: {} push end", monthlyBill.getContractNo(), createRequestTO.getCode(), monthlyBill.getYearMonthInt(), monthlyBill.getSubjectConsumedAmountMonth());
        } catch (Exception e) {
            //除了重复合同以外的创建失败情况都需要关注
            logger.error("客找找收入确认合同：{}，推送失败：{}，请检查", monthlyBill.getContractNo(), e.getMessage());
            if (StringUtils.contains(e.getMessage(), "重复")) {
                return null;
            } else {
                taskRecord.setSyncStatus("F");
                commTblSettlementContractHistService.save(taskRecord);
                return e.getMessage();
            }
        }
        return null;
    }

    public static TblSettlementContractRevenue buildConfirmContractProductInfo(TblSettlementContractRevenue contractRevenue, KzzContractCreateRequestTO createRequestTO, String yearMonth, String currency) {
        if (Objects.isNull(contractRevenue)) {
            contractRevenue = new TblSettlementContractRevenue();
        }
        contractRevenue.setContractNo(createRequestTO.getCode());
        contractRevenue.setCustomerName(ContractSubjectEnum.QCC_GLOBAL.getDesc());
        contractRevenue.setBillMonth(yearMonth);
        contractRevenue.setCurrency(currency);
        contractRevenue.setContractAmount(createRequestTO.getAmount());
        JSONArray jsonArray = createRequestTO.getCustomObjectValues().getJSONArray("CFLD202208300061");
        if (jsonArray != null && jsonArray.size() > 0) {
            contractRevenue.setProduct(jsonArray.getString(0));
            contractRevenue.setChildProduct(jsonArray.getString(0));
        }
        contractRevenue.setSignedDate(DateUtils.parseDate(createRequestTO.getSignerDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setContractStatus("签约");// 默认值
        contractRevenue.setSigner("系统内置用户");// 默认值
        contractRevenue.setInvoicedAmount(BigDecimal.ZERO);
        contractRevenue.setReceivedAmount(BigDecimal.ZERO);
        contractRevenue.setServiceStartDate(DateUtils.parseDate(createRequestTO.getBeginDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setServiceEndDate(DateUtils.parseDate(createRequestTO.getExpiredDate(), DateUtils.DATE_FORMAT));
        contractRevenue.setServicePeriod(1);// 默认固定为 1个月
        return contractRevenue;
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 根据合同和年月信息获取确认收入金额
     * updated for v2.3.0 fengsw KNZT_8045 增加内部测试账号数据过滤
     *
     * @param contractNo
     * @return
     */
    public TblContractDeliveryTransactionBillMonthly getMonthBillExcludeTestAccount(String contractNo, Integer yearMonthInt) {
        return contractDeliveryTransactionBillMonthlyService.getByContractNoAndSubjectAndYearMonthExcludeTestAccount(contractNo, ContractSubjectEnum.QCC.getCode(), yearMonthInt);
    }

    public TblContractDeliveryTransactionBillMonthly getSelfAccMonthBill(Integer yearMonthInt) {
        return contractDeliveryTransactionBillMonthlyService.getSelfAccMonthBillByContractNoAndSubjectAndYearMonth(ContractSubjectEnum.QCC.getCode(), yearMonthInt);
    }

    /**
     * added for v2.0.8 fengsw KNZT-5806
     * 构建创建合同请求
     *
     * @param contractNo
     * @return
     */
    private KzzContractCreateRequestTO createContractRequest(String contractNo, BigDecimal amount, Integer yearMonthInt, YearMonth yearMonth) throws MessageException {
        KzzContractCreateRequestTO createRequestTO = new KzzContractCreateRequestTO();
        // 根据合同编号判断是否为自助或者签约合同账号
        JSONObject customObjectValues = new JSONObject();// 合同自定义字段
        String contractNoKzz;
        // updated by v2.0.9 fengsw KNZT-5929 合同签订人、跟进人取默认内置用户
        String singerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("singerIdSelf");
        MsgExceptionUtils.checkIsNull(singerId1, "签订人信息缺失，请检查");
        long singerId = Long.parseLong(singerId1);
        long currencyId;
        if (contractNo.startsWith("KYCOL")) {// 自助账号合同签订人  // 测试 100740 生产 239729 合同编号 年月日
            contractNoKzz = "KYCOL" + yearMonthInt;
            String currencyIdSelf = sysConfigCacheService.getCreateContractConfigValueByKey("currencyIdSelf");
            MsgExceptionUtils.checkIsNull(currencyIdSelf, "货币单位缺失，请检查");
            currencyId = Long.parseLong(currencyIdSelf);
        } else {
            contractNoKzz = contractNo + "-" + yearMonthInt;
            String dbContractCurrencyId = contractDeliveryTransactionBillMonthlyService.getCurrencyIdByContractNo(contractNo);
            MsgExceptionUtils.checkIsNull(dbContractCurrencyId, "合同货币信息缺失，请检查");
            currencyId = Long.parseLong(dbContractCurrencyId);
        }

        long parentContractId;// 关联主合同id  测试  162114 生产92949
        String parentContractId1 = sysConfigCacheService.getCreateContractConfigValueByKey("parentContractId");
        MsgExceptionUtils.checkIsNull(parentContractId1, "关联主合同缺失，请检查");
        parentContractId = Long.parseLong(parentContractId1);

        long contactId; //联系人 1154585 测试 生产 3108863
        String contactId1 = sysConfigCacheService.getCreateContractConfigValueByKey("contactId");
        MsgExceptionUtils.checkIsNull(contactId1, "联系人缺失，请检查");
        contactId = Long.parseLong(contactId1);

        long customerId;// 客户id 测试 5216091 生产 9815343
        String customerId1 = sysConfigCacheService.getCreateContractConfigValueByKey("customerId");
        MsgExceptionUtils.checkIsNull(customerId1, "客户id缺失，请检查");
        customerId = Long.parseLong(customerId1);

        createRequestTO.setCode(contractNoKzz);// 合同编号
        createRequestTO.setCustomerId(customerId);// 客户id
        createRequestTO.setRenewType(1);// 续约类型 一次性合同
        createRequestTO.setContractType(3);//类型-框架子合同
        createRequestTO.setSkipApproval(1);// 默认跳过审批，否则客找找无法保存指定的开始日期和到期日期

        int serviceLimit;// 服务年限
        String serviceLimit1 = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023042500004");
        MsgExceptionUtils.checkIsNull(serviceLimit1, "服务年限缺失，请检查");
        serviceLimit = Integer.parseInt(serviceLimit1);
        customObjectValues.put("CFLD2023042500004", serviceLimit);//服务年限

        //开始时间 推送周期当月1⽇
        //结束时间 推送周期当月最后⼀天
        LocalDate beginDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth(); // 这个时间同时应用到签订时间 +" 23:59:59"
        createRequestTO.setBeginDate(DateUtils.formatDate(beginDate));
        createRequestTO.setExpiredDate(DateUtils.formatDate(endDate));

        //主产品-QCC与企查查三⽅合同的结算产品
        String mainProduct = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300061");
        MsgExceptionUtils.checkIsNull(mainProduct, "主产品缺失，请检查");
        customObjectValues.put("CFLD202208300061", Lists.newArrayList(mainProduct));

        // 关联产品填充
        KzzContractCreateRequestTO.ContractProduct contractProduct = new KzzContractCreateRequestTO.ContractProduct();
        int mainProductId; // 主产品id 测试 21741；生产 23104
        String mainProductId1 = sysConfigCacheService.getCreateContractConfigValueByKey("mainProductId");
        MsgExceptionUtils.checkIsNull(mainProductId1, "主产品id缺失，请检查");
        mainProductId = Integer.parseInt(mainProductId1);
        contractProduct.setProductId(mainProductId);
        contractProduct.setPeriod(1);
        contractProduct.setTimeLimits(1);
        contractProduct.setPrice(amount);
        contractProduct.setDiscountPrice(amount);
        contractProduct.setDiscountRate(new BigDecimal("100"));
        contractProduct.setQuantity(1);
        contractProduct.setTotalPrice(amount);
        createRequestTO.setProducts(Lists.newArrayList(contractProduct));

        createRequestTO.setAmount(amount);//合同金额
        createRequestTO.setTotal(amount);//合同原金额
        createRequestTO.setOrderDiscountRate(new BigDecimal("100"));// 整单折扣率
        createRequestTO.setComDiscountRate(new BigDecimal("100"));// 综合折扣率

        //开票⽅式-先票后款
        String billType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100006");
        MsgExceptionUtils.checkIsNull(billType, "开票⽅式缺失，请检查");
        customObjectValues.put("CFLD2023033100006", Lists.newArrayList(billType));

        //回款⽅式-后付费
        String paymentType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100004");
        MsgExceptionUtils.checkIsNull(paymentType, "回款⽅式缺失，请检查");
        customObjectValues.put("CFLD2023033100004", Lists.newArrayList(paymentType));

        //结算周期-半年度结算
        String chargeType = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023033100002");
        MsgExceptionUtils.checkIsNull(chargeType, "结算周期缺失，请检查");
        customObjectValues.put("CFLD2023033100002", Lists.newArrayList(chargeType));

        // 回款计划填充
        KzzContractCreateRequestTO.ContractPayment contractPayment = new KzzContractCreateRequestTO.ContractPayment();
        contractPayment.setLeaderId(singerId);
        contractPayment.setCreateBy(singerId);
        contractPayment.setCode(contractNoKzz + ".1");
        contractPayment.setAmount(amount);
        contractPayment.setType(2);// 回款类型，常规

        YearMonth now = YearMonth.now();
        LocalDate localDate = now.atEndOfMonth();
        contractPayment.setPaymentDate(DateUtils.toDate(localDate));// 预计回款时间为推送当前时间的当月30天
        JSONObject obj = new JSONObject();

        // 回款条件-开票之日起
        String paymentConditions = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032100002");
        MsgExceptionUtils.checkIsNull(paymentConditions, "回款条件缺失，请检查");
        obj.put("CFLD2023032100002", Lists.newArrayList(paymentConditions));

        //账龄天数-30
        String agingDays = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202111050008");
        MsgExceptionUtils.checkIsNull(agingDays, "账龄天数缺失，请检查");
        obj.put("CFLD202111050008", Integer.valueOf(agingDays));

        contractPayment.setCustomObjectValues(obj);
        createRequestTO.setPayments(Lists.newArrayList(contractPayment));

        // 收款⽅式-⽹银转账
        String paymentMethod = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300081");
        MsgExceptionUtils.checkIsNull(paymentMethod, "收款⽅式缺失，请检查");
        customObjectValues.put("CFLD202208300081", Lists.newArrayList(paymentMethod));

        //客户需要合同-不需要合同
        String customerNeedContract = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300054");
        MsgExceptionUtils.checkIsNull(customerNeedContract, "客户需要合同配置项缺失，请检查");
        customObjectValues.put("CFLD202208300054", Lists.newArrayList(customerNeedContract));

        //客户是否先⽤印-否
        String needSign = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023032900009");
        MsgExceptionUtils.checkIsNull(needSign, "客户是否先⽤印配置项缺失，请检查");
        customObjectValues.put("CFLD2023032900009", Lists.newArrayList(needSign));

        //是否标准模板-否
        String standardTemplate = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300057");
        MsgExceptionUtils.checkIsNull(standardTemplate, "是否标准模板配置项缺失，请检查");
        customObjectValues.put("CFLD202208300057", Lists.newArrayList(standardTemplate));

        createRequestTO.setContactId(contactId);// 联系人

        //我司签约主体（⼄⽅）- 企查查科技股份有限公司
        String ourSigningParty = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300069");
        MsgExceptionUtils.checkIsNull(ourSigningParty, "我司签约主体（⼄⽅）配置项缺失，请检查");
        customObjectValues.put("CFLD202208300069", Lists.newArrayList(ourSigningParty));

        // 客户开票名称
        String customerInvoicingName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300090");
        MsgExceptionUtils.checkIsNull(customerInvoicingName, "客户开票名称配置项缺失，请检查");
        customObjectValues.put("CFLD202208300090", customerInvoicingName);

        // 收件⼈姓名
        String recipientName = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300098");
        MsgExceptionUtils.checkIsNull(recipientName, "收件⼈姓名配置项缺失，请检查");
        customObjectValues.put("CFLD202208300098", recipientName);

        // 收件人电话
        String recipientPhone = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300101");
        MsgExceptionUtils.checkIsNull(recipientPhone, "收件人电话配置项缺失，请检查");
        customObjectValues.put("CFLD202208300101", recipientPhone);

        // 收件人地址
        String recipientAddress = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD202208300100");
        MsgExceptionUtils.checkIsNull(recipientAddress, "收件人地址配置项缺失，请检查");
        customObjectValues.put("CFLD202208300100", recipientAddress);

        //合同名称 KYC内部结算
        String contractName = sysConfigCacheService.getCreateContractConfigValueByKey("contractName");
        MsgExceptionUtils.checkIsNull(contractName, "合同名称配置项缺失，请检查");
        createRequestTO.setName(contractName);

        // 合同来源-线下合同
        String contractSource = sysConfigCacheService.getCreateContractConfigValueByKey("CFLD2023040400002");
        MsgExceptionUtils.checkIsNull(contractSource, "合同来源配置项缺失，请检查");
        customObjectValues.put("CFLD2023040400002", Lists.newArrayList(contractSource));

        createRequestTO.setCustomObjectValues(customObjectValues);
        createRequestTO.setParentContractId(parentContractId);// 关联主合同
        createRequestTO.setSignerId(singerId);// 签订人
        createRequestTO.setCreateBy(singerId);// 创建人
        createRequestTO.setCurrencyId(currencyId);
        // updated for v2.3.5 fengsw KNZT-8451 签订时间取当前账单月份的最后一天 +23:59:59 作为合同时间
        createRequestTO.setSignerDate(createRequestTO.getExpiredDate() + " 23:59:59");// 签订时间-当前时间,精确到时分秒
        createRequestTO.setCreateDate(DateUtils.getDate(DateUtils.DATETIME_FORMAT));// 创建时间-当前时间

        //合同状态-签约
        String contractStatus = sysConfigCacheService.getCreateContractConfigValueByKey("contractStatus");
        MsgExceptionUtils.checkIsNull(contractStatus, "合同状态配置项缺失，请检查");
        createRequestTO.setStatus(Integer.parseInt(contractStatus));
        return createRequestTO;
    }

    /*    */

    /**
     * 计算自助账户月账
     * added for v1.9.4 KNZT-4324
     *
     * @param yearMonth
     * @return
     *//*
    @Deprecated
    public void processSelfAccountProdAccTransactionMonthly(YearMonth yearMonth) {
        String selfAccountContractDeliveryId = Constants.Delivery.SELF_ACCOUNT_CONTRACT_DELIVERY_ID;
        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal totalConsumed = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(selfAccountContractDeliveryId);
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate endDayOfMonth = yearMonth.atEndOfMonth();
        BigDecimal consumedMonth = contractDeliveryTransactionService.sumChangedUnitByContractDelivery(selfAccountContractDeliveryId,
                DateUtils.formatDateToInt(firstDayOfMonth), DateUtils.formatDateToInt(endDayOfMonth));
        TblContractDeliveryTransactionBillMonthly bill = new TblContractDeliveryTransactionBillMonthly();
        bill.setYearMonthInt(yearMonthInt);
        bill.setSubject(ContractSubjectEnum.QCC_GLOBAL.getCode());
        bill.setProdName("自助账户");
        bill.injectUnitAndAmount4SelfAccount(consumedMonth, totalConsumed);
        bill.setContractNo(Constants.Delivery.SELF_ACCOUNT_CONTRACT_NO);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);
        logger.info("processSelfAccountProdAccTransactionMonthly totalConsumed:{}, consumedMonth:{}", totalConsumed, consumedMonth);
    }*/
    public void processPeriodProdMonthly(String contractNo, YearMonth yearMonth) {
        TblContractDeliveryProdPeriod prodPeriod = prodPeriodService.getProdPeriodByContractNo(contractNo);
        logger.info("processProdAccTransactionMonthly start, prodAccId:{}, yearMonth:{}", prodPeriod.getContractDeliveryProdAccId(), yearMonth);

        TblContractDelivery contractDelivery = contractDeliveryService.get(prodPeriod.getContractDeliveryId());
        if (Objects.isNull(contractDelivery)) {
            logger.error("contractDelivery is null, contractDeliveryId:{}", prodPeriod.getContractDeliveryId());
            return;
        }

        Integer yearMonthInt = DateUtils.formatYearMonthInt(yearMonth);

        BigDecimal totalConsumedAmountBefore = contractDeliveryTransactionBillMonthlyService.getLatestTotalConsumedAmountBeforeYearMonthByContractNo(
                contractDelivery.getContractNo(), yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode());
        if (totalConsumedAmountBefore.compareTo(prodPeriod.getTotalAmount()) >= 0) {
            logger.info("processPeriodProdMonthly periodProd totalAmount has done, totalConsumedUnitBefore:{}, totalAmount:{}",
                    totalConsumedAmountBefore, prodPeriod.getTotalAmount());
            return;
        }

        BigDecimal termAmount = prodPeriod.getTermAmount();
        BigDecimal totalConsumedAmount = totalConsumedAmountBefore.add(termAmount);
        BigDecimal remainAmount = prodPeriod.getTotalAmount().subtract(totalConsumedAmount);

        TblContractDeliveryTransactionBillMonthly bill = TblContractDeliveryTransactionBillMonthly
                .build(contractDelivery.getContractNo(), prodPeriod.getProdName(), yearMonthInt, ContractSubjectEnum.QCC_GLOBAL.getCode())
                .injectUnit(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)
                .injectAmount(termAmount, totalConsumedAmount, remainAmount);
        contractDeliveryTransactionBillMonthlyService.saveOrUpdateBill(bill);
        logger.info("processPeriodProdMonthly calculate done, prodAccId:{}, yearMonth:{}",
                prodPeriod.getContractDeliveryProdAccId(), yearMonth);
    }

    /**
     * 递归找产品账户进行扣减，生成流水
     *
     * @param accList         产品账户集合
     * @param accIndex        使用的账户index
     * @param unit            扣减的unit
     * @param baseTransaction 基本的流水对象
     * @return
     */
    private boolean recursionGenerateTransaction(List<TblContractDeliveryProdAcc> accList, int accIndex, BigDecimal unit,
                                                 TblContractDeliveryTransaction baseTransaction) throws MessageException {
        logger.info("recursionGenerateTransaction companyId:{}, accIndex:{}, unit:{}", baseTransaction.getCompanyId(), accIndex, unit);
        if (unit.compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("recursionGenerateTransaction 扣减完成，companyId:{}", baseTransaction.getCompanyId());
            return true;
        }
        if (accIndex >= accList.size()) {
            logger.info("recursionGenerateTransaction 所有的产品账户没有足够的余额进行扣减流水，companyId:{}", baseTransaction.getCompanyId());
            return false;
        }
        TblContractDeliveryProdAcc acc = accList.get(accIndex);
        BigDecimal remainUnit = acc.calTotalRemainUnit();
        BigDecimal changedUnit = remainUnit.compareTo(unit) >= 0 ? unit : remainUnit;

        if (changedUnit.compareTo(BigDecimal.ZERO) > 0) {
            TblContractDeliveryTransaction transaction = new TblContractDeliveryTransaction();
            BeanUtils.copyProperties(baseTransaction, transaction);
            transaction.setIsNewRecord(false);
            transaction.injectChangedUnit(changedUnit);
            this.saveTransactionAndChangeAccUnit(transaction, acc);
            unit = unit.subtract(changedUnit);
        }

        return recursionGenerateTransaction(accList, accIndex + 1, unit, baseTransaction);
    }


    public void saveTransactionAndChangeAccUnit(TblContractDeliveryTransaction transaction, TblContractDeliveryProdAcc prodAcc) throws MessageException {
        // 保存流水
        transaction.injectAccInfo(prodAcc);
        contractDeliveryTransactionService.save(transaction);
        logger.info("saveTransactionAndChangeAccUnit 生成订单流水, companyId:{}, prodAccId:{}, changedUnit:{}",
                transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), transaction.getChangedUnit());

        // 处理产品账户额度单元
        BigDecimal oriTotalConsumedUnit = prodAcc.getTotalConsumedUnit();
        BigDecimal afterTotalConsumedUnit = oriTotalConsumedUnit.add(transaction.getChangedUnit());
        int accUpdated = contractDeliveryProdAccService.updateConsumedUnit(
                transaction.getContractDeliveryProdAccId(), afterTotalConsumedUnit, oriTotalConsumedUnit);
        MsgExceptionUtils.failBuild(accUpdated <= 0, "err.amount.insufficient");
        logger.info("saveTransactionAndChangeAccUnit 扣减账户余额, companyId:{},  prodAccId:{}, afterTotalConsumedUnit:{}, oriTotalConsumedUnit:{}",
                transaction.getCompanyId(), transaction.getContractDeliveryProdAccId(), afterTotalConsumedUnit, oriTotalConsumedUnit);
    }

    /**
     * 校准订单收入账单
     * added for lvcy v2.0.6 KNZT-5499
     *
     * @param orderId
     * @param trackingContent
     * @return
     */
    public void correctMonthBillByOrder(String orderId, String trackingContent) throws MessageException, IOException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(orderId), "err.email.invalid");

        TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
        MsgExceptionUtils.checkIsNull(order, "msg:对应订单不存在");
        MsgExceptionUtils.failBuild(OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus()), "msg:该订单类型不支持调整收入");
        String companyId = order.getCompanyId();

        // 手动调整退款
        order.transitionStatus(OrderStatusEnum.REFUNDED);
        commTblCompReportOrderService.save(order);
        createTransaction4Refund(order.getId(), order.getCompanyId(), order.getTotalUnit(), TransactionTypeEnum.CORRECT_ORD, TransactionTypeEnum.ORDER.getCode());

        // 同步额度
        funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 保存跟踪记录
        commCrmCompTrackingRecordService.save(order.getCompanyId(), trackingContent, CompTrackingRecordSourceEnum.CORRECT_ORDER.getCode());
    }

    /**
     * 获取订单概要信息 for 调整收入
     * added for lvcy v2.0.6 KNZT-5499
     *
     * @param orderId
     * @return OrderInfo4CorrectTO
     */
    public OrderInfo4CorrectTO getOrderInfo4Correct(String orderId) throws MessageException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(orderId), "err.param.invalid");
        TblCompReportOrder order = commTblCompReportOrderService.get(orderId);
        MsgExceptionUtils.checkIsNull(order, "msg:订单不存在");
        MsgExceptionUtils.failBuild(OrderStatusEnum.getUnSuccessFinalStatus().contains(order.getRptStatus()), "msg:退款订单不允许调整");
        List<TblContractDeliveryTransaction> transactionList = contractDeliveryTransactionService.getByRelId(orderId, TransactionTypeEnum.ORDER.getCode());
        MsgExceptionUtils.checkIsNull(transactionList, "msg:非正式账号的订单");
        TblContractDeliveryTransaction orderTransaction = transactionList.get(0);
        Company company = commSysCompanyService.get(order.getCompanyId());
        MsgExceptionUtils.checkIsNull(company, "msg:公司信息不存在");
        // 构建出参
        LocalDate transactionDate = DateUtils.toLocalDate(orderTransaction.getDateInt());
        YearMonth transactionYearMonth = YearMonth.from(transactionDate);
        String yearMonthStr = transactionYearMonth.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
        return OrderInfo4CorrectTO.from(order, null, company.getName(), yearMonthStr);
    }


    /**
     * 查询交易流水详情
     * added for lvcy v2.1.2 KNZT-4565
     *
     * @param condition 查询条件
     * @return 分页结果
     * @throws MessageException
     */
    public List<TransactionListTO> pageTransaction(TransactionPageCondition condition) throws MessageException {
        // 如果交易流水ID、合同编号、订单编号都为空，则需要校验开始日期和结束日期
        if (Objects.isNull(condition.getTransactionId()) && Objects.isNull(condition.getContractNo()) && Objects.isNull(condition.getOrderNo())) {
            MsgExceptionUtils.failBuild(Objects.isNull(condition.getBeginDate()) || Objects.isNull(condition.getEndDate()), "msg:beginDate endDate不可为null");
            MsgExceptionUtils.failBuild(DateUtils.getDaysBetween(condition.getBeginDate(), condition.getEndDate()) > 366, "msg:时间间隔不能超过1年");
        }

        List<TransactionListTO> transactionList = contractDeliveryTransactionService.pageTransaction(condition);
        transactionList.forEach(item -> {
            item.setTransactionTypeDesc(TransactionTypeEnum.getCodeDesc(item.getTransactionType()));

            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(item.getTransactionType())) {
                item.setOrderTypeDesc(ReportTypeEnum.getDesc(item.getOrderType()));
            } else if (TransactionTypeEnum.getOrderTypeApiList().contains(item.getTransactionType())) {
                item.setOrderTypeDesc(ApiTypeEnum.getDesc4TransactionPage(item.getOrderType()));
            }

            item.setUnitGroupDesc(UnitGroupEnum.getNameByUnitGroup(item.getUnitGroup()));


            if (Objects.nonNull(item.getConfirmAmount())) {
                item.setConfirmAmountStr(NumberUtils.formatRptUnitWithSign(item.getConfirmAmount()));
            }

            if (Objects.nonNull(item.getChangedUnit())) {
                item.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(item.getChangedUnit()));
            }

            if (Objects.nonNull(item.getConfirmAmount()) && UnitGroupEnum.CN_UNIT.getGroup().equals(item.getUnitGroup())) {
                item.setQccConfirmAmount(item.getConfirmAmount().multiply(BigDecimal.valueOf(0.2)));
                item.setQccConfirmAmountStr(NumberUtils.formatRptUnitWithSign(item.getQccConfirmAmount()));
            } else {
                item.setQccConfirmAmount(BigDecimal.ZERO);
                item.setQccConfirmAmountStr("0");
            }

            if (Objects.nonNull(item.getDateInt())) {
                item.setDateStr(DateUtils.formatDateInt(item.getDateInt()));
            }
        });
        return transactionList;
    }

    /**
     * 获取交易流水详情字典
     * added for lvcy v2.1.2 KNZT-4565
     *
     * @return TransactionListDictTO
     */
    public TransactionOrderDictTO getTransactionOrderDict() {
        List<DictItem> transactionDict = Arrays.stream(TransactionTypeEnum.values()).map(item -> new DictItem(item.getCode(), item.getCodeDesc(), item.getTransactionCategory().getCode())).collect(Collectors.toList());

        List<TblReportChargeUnit> chargeUnits = commTblReportChargeUnitService.listChargeUnitByCompanyId(Constants.DEFAULT_COMPANY_ID);
        List<DictItem> orderReportDict = commTblCompReportOrderService.chargeUnitToItemList(chargeUnits);

        List<TblCompApiChargeUnit> apiChargeUnits = commTblCompApiChargeUnitService.getByCompanyId(Constants.DEFAULT_COMPANY_ID);
        List<DictItem> apiOrderTypeDict = commTblCompApiChargeUnitService.apiChargeUnitToItemList(apiChargeUnits);

        TransactionOrderDictTO dictTO = new TransactionOrderDictTO();
        dictTO.setTransactionTypeDict(transactionDict);
        dictTO.setOrderReportTypeDict(orderReportDict);
        dictTO.setApiOrderTypeDict(apiOrderTypeDict);
        return dictTO;
    }

    /**
     * 用户查询全量流水信息
     *
     * @param condition
     * @return
     */
    public List<TransactionListV2TO> pageTransaction4UserComp(TransactionPageCondition condition) throws MessageException {
        List<TransactionBO> transactionBOs = contractDeliveryTransactionService.pageTransaction4UserComp(condition);
        // 定义操作人是公司主账号的类型
        List<String> companyTransactionTypeList = Lists.newArrayList(TransactionTypeEnum.API_ORDER.getCode(), TransactionTypeEnum.API_REFUND_ORDER.getCode(), TransactionTypeEnum.TOP_UP.getCode(), TransactionTypeEnum.TOP_UP_CONSUMED.getCode());
        // 定义操作人是System的类型
        List<String> systemTransactionTypeList = Lists.newArrayList(TransactionTypeEnum.PREPAID.getCode(), TransactionTypeEnum.GIVEN.getCode(), TransactionTypeEnum.CORRECT_ORD.getCode(), TransactionTypeEnum.CONFIRM.getCode(), TransactionTypeEnum.RETURN.getCode(), TransactionTypeEnum.BENEFIT.getCode());
        // 查询主账号邮箱
        Company company = UserUtils.getUser().getCompany();
        User mainUser = userService.get(company.getMainUserId());
        String mainUserLoginName = Objects.nonNull(mainUser) ? mainUser.getLoginName() : null;
        return transactionBOs.stream().map(bo -> {
            TransactionListV2TO vo = new TransactionListV2TO();
            vo.setTransactionId(bo.getId());
            String transactionType = bo.getTransactionType();
            vo.setTransactionType(transactionType);
            vo.setTransactionTypeDesc(TransactionTypeEnum.getDescEn(transactionType));
            BigDecimal changedUnit = bo.getChangedUnit();
            vo.setChangedUnit(changedUnit);
            BigDecimal balance = bo.getCompanyRemainUnit();
            vo.setBalance(balance);
            vo.setCreateDate(bo.getCreateDate());
            vo.setDateStr(DateUtils.formatDateForSg(vo.getCreateDate(), DateUtils.DATETIME_FORMAT_2, Locale.ENGLISH));

            String orderNo = null;
            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(transactionType)) {
                String reportType = Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getReportType).orElse(null);
                vo.setOrderType(reportType);
                String extraInfo1 = Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getExtraInfo1).orElse(null);
                if (ReportTypeEnum.SG_FIN.getCode().equals(reportType) && StringUtils.isNotBlank(extraInfo1)) {
                    vo.setOrderTypeDesc(ReportTypeEnum.SG_FIN.getDesc() + " (" + extraInfo1 + ")");
                } else {
                    vo.setOrderTypeDesc(ReportTypeEnum.getDesc(reportType));
                }
                orderNo = Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getOrderNo).orElse(null);
            } else if (TransactionTypeEnum.getOrderTypeApiList().contains(transactionType)) {
                String apiType = Optional.ofNullable(bo.getCompApiOrder()).map(TransactionBO.CompApiOrderBO::getApiType).orElse(null);
                vo.setOrderType(apiType);
                vo.setOrderTypeDesc(ApiTypeEnum.getDesc4TransactionPage(apiType));
                orderNo = Optional.ofNullable(bo.getCompApiOrder()).map(TransactionBO.CompApiOrderBO::getOrderNo).orElse(null);
            } else if (TransactionTypeEnum.BENEFIT.getCode().equals(transactionType)) {
                String benefitType = Optional.ofNullable(bo.getBenefitDelivery()).map(TransactionBO.BenefitDeliveryBO::getBenefitType).orElse(null);
                vo.setOrderType(benefitType);
                vo.setOrderTypeDesc(BenefitTypeEnum.getNameByCode(benefitType));
            }
            if (TransactionTypeEnum.getOrderTypeWithoutApiList().contains(transactionType)) {
                vo.setUser(Optional.ofNullable(bo.getCompReportOrder()).map(TransactionBO.CompReportOrderBO::getLoginName).orElse(null));
            }
            if (companyTransactionTypeList.contains(transactionType)) {
                vo.setUser(mainUserLoginName);
            } else if (systemTransactionTypeList.contains(transactionType)) {
                vo.setUser("System");
            }

            // 设置默认值
            vo.setChangedUnitStr("0");
            vo.setCreditUsageUnitStr("0");
            vo.setBalanceStr("0");
            if (Objects.nonNull(changedUnit)) {
                if (TransactionTypeEnum.getDeductionTypeList().contains(transactionType) || TransactionTypeEnum.getExpiredTypeList().contains(transactionType)) {
                    vo.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(changedUnit.negate()));
                } else if (TransactionTypeEnum.getTopUpTypeList().contains(transactionType) || TransactionTypeEnum.getRefundTypeList().contains(transactionType) || TransactionTypeEnum.getPromotionTypeList().contains(transactionType)) {
                    vo.setChangedUnitStr(NumberUtils.formatRptUnitWithSign(changedUnit.abs()));
                }
                vo.setCreditUsageUnitStr(NumberUtils.formatRptUnit(changedUnit.abs()));
            }
            String prodAccType = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getType).orElse(null);
            if (Objects.nonNull(balance)) {
                if (DeliveryContracProdAccTypeEnum.AFTER_INF.getCode().equals(prodAccType)) {
                    vo.setBalance(balance.subtract(Constants.MAX_UNIT));
                }
                vo.setBalanceStr(NumberUtils.formatRptUnit(vo.getBalance()));
            }
            String corpCombinedName = null;
            if (bo.getCompReportOrder() != null) {
                TransactionBO.CompReportOrderBO compReportOrder = bo.getCompReportOrder();
                if ("P".equals(compReportOrder.getKeyNoType())) {
                    corpCombinedName = StringUtils.joinIgnoreNull(Lists.newArrayList(compReportOrder.getPersNameEn(), compReportOrder.getPersName()),
                            "\n");
                } else if ("C".equals(compReportOrder.getKeyNoType())) {
                    corpCombinedName = StringUtils.joinIgnoreNull(Lists.newArrayList(compReportOrder.getCorpNameEn(), compReportOrder.getCorpName()),
                            "\n");
                }
            } else if (bo.getCompApiOrder() != null) {
                corpCombinedName = StringUtils.joinIgnoreNull(Lists.newArrayList(bo.getCompApiOrder().getNameEn(), bo.getCompApiOrder().getName()),
                        "\n");
            }
            vo.setCorpCombinedName(corpCombinedName);

            String contractNo = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getContractNo).orElse(null);
            Date contractDeliveryBeginDate = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getBeginDate).orElse(null);
            Date contractDeliveryEndDate = Optional.ofNullable(bo.getContractDelivery()).map(TransactionBO.ContractDeliveryBO::getEndDate).orElse(null);
            Date contractDeliveryProdAccBeginDate = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getBeginDate).orElse(null);
            Date contractDeliveryProdAccEndDate = Optional.ofNullable(bo.getContractDeliveryProdAcc()).map(TransactionBO.ContractDeliveryProdAccBO::getEndDate).orElse(null);
            Date beginDate = ObjectUtils.getNotNull(contractDeliveryBeginDate, contractDeliveryProdAccBeginDate);
            Date endDate = ObjectUtils.getNotNull(contractDeliveryEndDate, contractDeliveryProdAccEndDate);
            String beginDateFormat = DateUtils.formatDateForSg(beginDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
            String endDateFormat = DateUtils.formatDateForSg(endDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
            // 描述处理
            if (TransactionTypeEnum.getDeductionTypeList().contains(transactionType) || TransactionTypeEnum.getRefundTypeList().contains(transactionType)) {// 正常扣费
                if (TransactionTypeEnum.BENEFIT.getCode().equals(transactionType)) {
                    TransactionBO.BenefitDeliveryBO benefitDelivery = bo.getBenefitDelivery();
                    if (benefitDelivery != null) {
                        Integer poolCount = benefitDelivery.getPoolCount();
                        Date benefitBeginDate = benefitDelivery.getBeginDate();
                        Date benefitEndDate = benefitDelivery.getEndDate();
                        vo.setDescription(String.format("Redeemed %s monitor slots valid from %s to %s",
                                poolCount,
                                DateUtils.formatDateForSg(benefitBeginDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH),
                                DateUtils.formatDateForSg(benefitEndDate, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH)));
                    }
                } else {
                    vo.setDescription("Transaction No. : " + orderNo);
                }
            } else {
                if (TransactionTypeEnum.TOP_UP.getCode().equals(transactionType)) {
                    vo.setDescription(String.format("Top-up No. : %s\nCredits valid from %s to %s", contractNo, beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.PREPAID.getCode().equals(transactionType)) { // 预付费充值 只展示合同号
                    vo.setDescription(String.format("Contract No. : %s\nCredits valid from %s to %s", contractNo, beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.TOP_UP_CONSUMED.getCode().equals(transactionType) || TransactionTypeEnum.getExpiredTypeList().contains(transactionType)) {
                    if (TransactionTypeEnum.GIVEN.getCode().equals(prodAccType)) {
                        vo.setDescription("Promotional credit has expired."); // 过期流水来自赠送操作，则给出默认文案
                    } else {
                        vo.setDescription("Top-up No. : " + contractNo);
                    }
                } else if (TransactionTypeEnum.getPromotionTypeList().contains(transactionType)) {
                    vo.setDescription(String.format("Credits valid from %s to %s", beginDateFormat, endDateFormat));
                } else if (TransactionTypeEnum.RETURN.getCode().equals(transactionType)) {
                    String statementMonth = Optional.ofNullable(bo.getCompStatementMonthly()).map(TransactionBO.CompStatementMonthlyBO::getStatementMonth).orElse(null);
                    if (StringUtils.isNotBlank(statementMonth)) {
                        YearMonth yearMonth = YearMonth.parse(statementMonth, DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMM));
                        String yearMonthEn = DateUtils.formatYearMonthEn(yearMonth, DateUtils.DATE_FORMATMMMYYYY);
                        vo.setDescription(String.format("Payment to %s Invoice", yearMonthEn));
                    }
                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 充值记录分页查询
     *
     * @param condition
     * @return
     */
    public Page<ChargeRecordsInfoListV2TO> pageChargeRecords(CompanyChargeHistoryCondition condition) {
        if (CollectionUtils.isEmpty(condition.getSortedFiledList())) {
            condition.setSortedFiledList(Lists.newArrayList(new SortedFiled("createDate", "desc")));
        }
        List<ChargeRecordsInfoListV2TO> chargeRecordsInfoListTOS = contractDeliveryService.pageChargeRecords(condition);
        if (CollectionUtils.isNotEmpty(chargeRecordsInfoListTOS)) {
            chargeRecordsInfoListTOS.forEach(item -> {
                item.setContractSubject(ContractSubjectEnum.QCC_GLOBAL.getDesc());
                if (Objects.nonNull(item.getContractAmount())) {
                    item.setContractAmountStr(NumberUtils.formatRptUnit(item.getContractAmount()));
                }
                if (Objects.nonNull(item.getConsumedAmount())) {
                    item.setConsumedAmountStr(NumberUtils.formatRptUnit(item.getConsumedAmount()));
                }
                if (Objects.nonNull(item.getContractCredits())) {
                    item.setContractCreditsStr(NumberUtils.formatRptUnit(item.getContractCredits()));
                }
                if (Objects.nonNull(item.getConsumedCredits())) {
                    item.setConsumedCreditsStr(NumberUtils.formatRptUnit(item.getConsumedCredits()));
                }
                if (Objects.nonNull(item.getBeginDate())) {
                    item.setBeginDateStr(DateUtils.formatDate(item.getBeginDate()));
                }
                if (Objects.nonNull(item.getEndDate())) {
                    item.setEndDateStr(DateUtils.formatDate(item.getEndDate()));
                }
            });
        }
        condition.getPage().setList(chargeRecordsInfoListTOS);
        return condition.getPage();
    }

    /**
     * 处理过期额度账户，生成过期流水
     *
     * @param account 过期的额度账户列表
     * @throws MessageException 如果处理过程中出现错误
     */
    public void processExpiredAccounts(TblContractDeliveryProdAcc account) throws MessageException {
        String companyId = account.getCompanyId();
        BigDecimal remainUnit = account.getTotalUnit().subtract(account.getTotalConsumedUnit());

        // 获取当前公司的功能计数对象
        SysCompInfoFuncCount funcCount = funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 生成过期流水
        if (BigDecimal.ZERO.compareTo(remainUnit) != 0) {
            TblContractDeliveryTransaction expireTransaction = TblContractDeliveryTransaction
                    .init(companyId, TransactionTypeEnum.CONFIRM.getCode(), funcCount.calRemainCount())
                    .injectAccInfo(account)
                    .injectChangedUnit(remainUnit);
            expireTransaction.injectDateInt(account.getEndDate());
            // 保存流水并更新账户
            this.saveTransactionAndChangeAccUnit(expireTransaction, account);
        } else {
            logger.info("companyId: {} 的额度账户已用完，无需处理", companyId);
        }

        logger.info("处理过期额度账户成功，companyId: {}, accountId: {}, expiredUnit: {}",
                companyId, account.getId(), remainUnit);
    }


    /**
     * 生成回款流水，并同步更新额度
     *
     * @param statementMonthly
     * @throws MessageException
     */
    public void createTransaction4Return(TblCompStatementMonthly statementMonthly) throws MessageException {
        String companyId = statementMonthly.getCompanyId();
        BigDecimal totalAmountDue = statementMonthly.getTotalCreditDue();
        Integer statementMonth = statementMonthly.getStatementMonth();
        if (Objects.isNull(totalAmountDue) || totalAmountDue.compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("totalAmountDue is null or zero, companyId: {}, statementMonth: {}", companyId, statementMonth);
            return;
        }

        List<TblContractDeliveryProdAcc> prodAccList = contractDeliveryProdAccService.lockValidAccByCompanyId(companyId);
        TblContractDeliveryProdAcc prodAcc = prodAccList.stream()
                .filter(acc -> DeliveryContracProdAccTypeEnum.AFTER_INF.getCode().equals(acc.getType()))
                .max(Comparator.comparing(TblContractDeliveryProdAcc::getBeginDate))
                .orElse(null);
        if (Objects.isNull(prodAcc)) {
            logger.error("prodAcc is null, companyId: {}, statementMonth: {}", companyId, statementMonth);
            return;
        }
        prodAcc.setTotalUnit(prodAcc.getTotalUnit().add(totalAmountDue));
        contractDeliveryProdAccService.save(prodAcc);
        logger.info("update prodAcc totalUnit, companyId: {}, totalUnit: {}", companyId, prodAcc.getTotalUnit());

        SysCompInfoFuncCount funcCount = funcCountService.updateFuncCountByValidProdAcc(companyId);

        // 生成回款流水
        TblContractDeliveryTransaction returnTransaction = TblContractDeliveryTransaction
                .init(companyId, TransactionTypeEnum.RETURN.getCode(), funcCount.calRemainCount())
                .injectAccInfo(prodAcc)
                .injectRelId(statementMonthly.getId())
                .injectChangedUnit(totalAmountDue.negate());
        contractDeliveryTransactionService.save(returnTransaction);
    }


    /**
     * 获取companyId当前生效的交付合同编号
     *
     * @param companyId
     * @return
     */
    @Deprecated
    /*public String getContractNo4StatementByCompanyId(String companyId) {
        TblContractDelivery delivery = contractDeliveryService.getLatestDataByCompanyId(companyId);
        if (delivery != null) {
            return delivery.getContractNo();
        }
        return null;
    }*/


    /**
     * added for v2.3.7 fengsw KNZT-8316【后端】Purchase History 增加Monthly Summary列表
     *
     * @return
     */
    public Page<TransactionSumByMonthTO> listTransactionMonthSummary(String companyId, Page page) {
        // 1. 获取公司创建时间
        Company company = commSysCompanyService.get(companyId);
        Date companyCreateDate = company.getCreateDate();

        // 2. 计算总月份数（从公司创建时间到上个月）
        Date lastMonth = DateUtils.addMonths(new Date(), -1); // 上个月
        int totalMonths = DateUtils.calculateMonthsBetween(companyCreateDate, lastMonth) + 1; // 包含上个月

        // 3. 计算分页参数
        int pageNo = page.getPageNo();
        int pageSize = page.getPageSize();

        // 4. 计算需要查询的月份范围（不包含当前月份）
        // 从上个月开始，往前推算
        List<TransactionSumByMonthTO> resultData = new ArrayList<>();

        // 生成需要查询的月份列表
        List<Integer> monthList = new ArrayList<>();
        for (int i = (pageNo - 1) * pageSize; i < Math.min(pageNo * pageSize, totalMonths); i++) {
            Date targetMonth = DateUtils.addMonths(lastMonth, -i);
            String monthKey = DateUtils.formatDate(targetMonth, "yyyyMM");
            Integer monthInt = Integer.parseInt(monthKey);
            monthList.add(monthInt);
        }

        // 5. 查询实际数据（按月分组的汇总数据）
        if (!monthList.isEmpty()) {
            // 获取查询的开始和结束月份
            Integer startMonthInt = monthList.get(monthList.size() - 1); // 最早的月份
            Integer endMonthInt = monthList.get(0); // 最晚的月份（最近的月份）

            // 转换为日期范围
            LocalDate startDate = DateUtils.convertToYearMonth(startMonthInt).atDay(1); // 最早月第一天
            LocalDateTime endDate = DateUtils.convertToMonthEndDateTime(endMonthInt); // 最晚月最后一天的 23:59:59

            List<TransactionSumByMonthTO> actualDataList = contractDeliveryTransactionService.calcCompanyTotalChangeUnitWithDataRange(
                    companyId,
                    DateUtils.toDate(startDate), // 开始日期
                    DateUtils.toDate(endDate) // 结束日期
            );

            // 6. 创建实际数据的映射表
            Map<Integer, TransactionSumByMonthTO> dataMap = actualDataList.stream()
                    .collect(Collectors.toMap(TransactionSumByMonthTO::getMonthInt, Function.identity()));


            // 7. 补全缺失月份的数据
            for (Integer month : monthList) {
                if (dataMap.containsKey(month)) {
                    TransactionSumByMonthTO monthTO = dataMap.get(month);
                    monthTO.setDataFlag(Constants.YES);
                    resultData.add(monthTO);
                } else {
                    // 创建默认的0值记录
                    TransactionSumByMonthTO defaultRecord = new TransactionSumByMonthTO();
                    defaultRecord.setMonthInt(month);
                    defaultRecord.setChangedUnitTotal(BigDecimal.ZERO);
                    defaultRecord.setDataFlag(Constants.NO);
                    resultData.add(defaultRecord);
                }
            }
        }

        // 8. 按月份倒序排列（最新的月份在前）
        resultData.sort((a, b) -> b.getMonthInt().compareTo(a.getMonthInt()));

        // 9. 返回分页结果
        return new Page<TransactionSumByMonthTO>(pageNo, pageSize, (long) totalMonths)
                .setList(resultData);
    }



}
