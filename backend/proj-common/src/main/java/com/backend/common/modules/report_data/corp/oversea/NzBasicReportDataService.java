package com.backend.common.modules.report_data.corp.oversea;

import com.backend.common.modules.trans.base.form.TransWrapper;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report_data.corp.oversea.base.AbstractReportDataService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.overseamongo.entity.RealtimeOrderOfficialWeb;
import com.backend.common.overseamongo.model.RealtimeOrderOfficialWebTO;
import com.backend.common.overseamongo.service.RealtimeOrderOfficialWebService;
import com.google.common.collect.Lists;
import com.qcc.frame.commons.ienum.GlobalAreaEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.NumberUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.qcc.frame.jee.commons.utils.MappingUtils.getValue;

@Service
public class NzBasicReportDataService extends AbstractReportDataService<RealtimeOrderOfficialWebTO> {

    @Autowired
    private RealtimeOrderOfficialWebService realtimeOrderOfficialWebService;

    @Override
    public List<ReportTypeEnum> getReportTypes() {
        return Lists.newArrayList(ReportTypeEnum.NZ_BASIC);
    }

    @Override
    public boolean isDataSuccess(ReportDataGetResultForm form) {
        TblCompReportOrder tblCompReportOrder = commTblCompReportOrderService.get(form.getOrderId());
        if (tblCompReportOrder == null) {
            logger.warn("TblCompReportOrder is null, orderId: " + form.getOrderId());
            return false;
        }
        if (super.doCheckOrderDataSuccess(tblCompReportOrder)) {
            return true;
        }
        boolean dataSuccess = false;
        String apiOrderNo = tblCompReportOrder.getApiOrderNo();
        RealtimeOrderOfficialWeb entity = realtimeOrderOfficialWebService.getByApiOrderNo(apiOrderNo);
        if (entity != null) {
            if (entity.getApiCallback() != null && entity.getApiCallback().equals(1)) {
                dataSuccess = true;
            } else {
                logger.info("RealtimeOrderOfficialWeb ApiCallback is null or not 1, apiOrderNo: " + apiOrderNo);
            }
        } else {
            logger.info("RealtimeOrderOfficialWeb is null, apiOrderNo: " + apiOrderNo);
        }
        return dataSuccess;
    }

    @Override
    public RealtimeOrderOfficialWebTO getData(ReportDataGetResultForm form) {
        TblCompReportOrder tblCompReportOrder = commTblCompReportOrderService.get(form.getOrderId());
        if (tblCompReportOrder == null) {
            logger.warn("TblCompReportOrder is null, orderId: " + form.getOrderId());
            return null;
        }
        RealtimeOrderOfficialWeb entity = realtimeOrderOfficialWebService.getByApiOrderNo(tblCompReportOrder.getApiOrderNo());
        if (entity == null) {
            logger.info("RealtimeOrderOfficialWeb is null, apiOrderNo: " + tblCompReportOrder.getApiOrderNo());
            return null;
        }
        return convertToTO(entity, tblCompReportOrder);
    }

    public RealtimeOrderOfficialWebTO convertToTO(RealtimeOrderOfficialWeb entity, TblCompReportOrder tblCompReportOrder) {
        if (entity == null) {
            return null;
        }

        // 初始化返回对象
        RealtimeOrderOfficialWebTO to = new RealtimeOrderOfficialWebTO();
        RealtimeOrderOfficialWebTO.CompanyInfo companyInfoTO = new RealtimeOrderOfficialWebTO.CompanyInfo();
        RealtimeOrderOfficialWebTO.ContactInfo contactInfoTO = new RealtimeOrderOfficialWebTO.ContactInfo();
        RealtimeOrderOfficialWebTO.ShareHolderInfo shareHolderInfoTO = new RealtimeOrderOfficialWebTO.ShareHolderInfo();
        RealtimeOrderOfficialWebTO.KeyPersonInfo keyPersonInfoTO = new RealtimeOrderOfficialWebTO.KeyPersonInfo();
        RealtimeOrderOfficialWebTO.UltimateHoldingCompanyInfo ultimateHoldingCompanyInfoTO = new RealtimeOrderOfficialWebTO.UltimateHoldingCompanyInfo();
        RealtimeOrderOfficialWebTO.ReceivershipInfo receivershipInfoTO = new RealtimeOrderOfficialWebTO.ReceivershipInfo();
        RealtimeOrderOfficialWebTO.LiquidationInfo liquidationInfoTO = new RealtimeOrderOfficialWebTO.LiquidationInfo();
        RealtimeOrderOfficialWebTO.NameHistoryInfo nameHistoryInfoTO = new RealtimeOrderOfficialWebTO.NameHistoryInfo();
        RealtimeOrderOfficialWebTO.AdditionalInfo additionalInfoTO = new RealtimeOrderOfficialWebTO.AdditionalInfo();
        to.setCompanyInfo(companyInfoTO);
        to.setContactInfo(contactInfoTO);
        to.setShareHolderInfo(shareHolderInfoTO);
        to.setKeyPersonInfo(keyPersonInfoTO);
        to.setUltimateHoldingCompanyInfo(ultimateHoldingCompanyInfoTO);
        to.setReceivershipInfo(receivershipInfoTO);
        to.setLiquidationInfo(liquidationInfoTO);
        to.setNameHistoryInfo(nameHistoryInfoTO);
        to.setAdditionalInfo(additionalInfoTO);
        TransWrapper transWrapper = TransWrapper.buildWrapper(GlobalAreaEnum.NZ.getNameCode());

        RealtimeOrderOfficialWeb.CompanyInfo companyInfo = entity.getCompanyInfo();
        RealtimeOrderOfficialWeb.ContactInformation contactInformation = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getContactInformation);
        List<RealtimeOrderOfficialWeb.CompAddress> compAddressList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompAddress);
        List<RealtimeOrderOfficialWeb.OtherNumber> otherNumbers = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getOtherNumber);
        List<RealtimeOrderOfficialWeb.OtherName> otherNames = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getOtherNames);

        // map TO
        Date orderCreateDate = getValue(tblCompReportOrder, TblCompReportOrder::getCreateDate);
        to.setLastUpdatedDate(DateUtils.formatDate(orderCreateDate));

        // map CompanyInfo
        companyInfoTO.setCompName(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompName));
        transWrapper.addEntryOverwriteOf(companyInfoTO,
                        RealtimeOrderOfficialWebTO.CompanyInfo::setCompName, RealtimeOrderOfficialWebTO.CompanyInfo::getCompName)
                .withEntity(TransWrapper.ENTRY_TYPE_CORP, null);
        companyInfoTO.setCompNo(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompNo));
        companyInfoTO.setBusinessNo(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getBusinessNo));
        companyInfoTO.setGstNumberList(getFromOtherNumbers(otherNumbers, "GST Number(s)"));
        companyInfoTO.setStatus(formatOriginLabel(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompStatus),
                getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getCompStatusLabel)));
        companyInfoTO.setIncorporationDate(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIncorporationDate));
        companyInfoTO.setLegalForm(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getEntityType));
        companyInfoTO.setPlaceOfIncorporation(GlobalAreaEnum.NZ.getShortNameEn());
        companyInfoTO.setRegisteredAddressList(getFromCompAddressList(compAddressList, "RegisteredOfficeAddress"));
        List<RealtimeOrderOfficialWeb.Industry> industryList = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getIndustry);
        if (CollectionUtils.isNotEmpty(industryList)) {
            companyInfoTO.setBusinessActivityList(industryList.stream().map(industry -> new RealtimeOrderOfficialWebTO.Item(industry.getCode(), industry.getDesc())).collect(Collectors.toList()));
        }

        // map ContactInfo
        contactInfoTO.setWebsiteList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getWebsite, new ArrayList<RealtimeOrderOfficialWeb.Website>())
                .stream().map(website -> new RealtimeOrderOfficialWebTO.TypeAndValue(website.getType(), website.getWebsite()))
                .collect(Collectors.toList()));
        contactInfoTO.setPhoneNumberList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getPhone, new ArrayList<RealtimeOrderOfficialWeb.Phone>())
                .stream().map(phone -> new RealtimeOrderOfficialWebTO.TypeAndValue(phone.getType(), phone.getPhone()))
                .collect(Collectors.toList()));
        contactInfoTO.setEmailList(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getEmail, new ArrayList<RealtimeOrderOfficialWeb.Email>())
                .stream().map(email -> new RealtimeOrderOfficialWebTO.TypeAndValue(email.getType(), email.getEmail()))
                .collect(Collectors.toList()));
        contactInfoTO.setAddressForServiceList(getFromCompAddressList(compAddressList, "AddressForService"));
        contactInfoTO.setOfficeAddress(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getOfficeAddress));
        contactInfoTO.setDeliveryAddress(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getDeliveryAddress));
        contactInfoTO.setPostalAddress(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getPostalAddress));
        contactInfoTO.setInvoiceAddress(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getInvoiceAddress));

        // map ShareHolderInfo
        shareHolderInfoTO.setTotalNumberOfShares(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getTotalNumberOfShares));
        shareHolderInfoTO.setDescription(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getShareholdersDescription));
        List<RealtimeOrderOfficialWeb.Shareholder> shareholders = getValue(entity, RealtimeOrderOfficialWeb::getShareholders);
        if (CollectionUtils.isNotEmpty(shareholders)) {
            List<RealtimeOrderOfficialWebTO.ShareHolderItem> shareHolderItems = shareholders.stream().map(shareholderItem -> {
                RealtimeOrderOfficialWebTO.ShareHolderItem shareHolderItemTO = new RealtimeOrderOfficialWebTO.ShareHolderItem();
                shareHolderItemTO.setNumberOfShares(shareholderItem.getNumberOfShares());
                shareHolderItemTO.setPercentOfClass(shareholderItem.getPercentOfClass());
                if (CollectionUtils.isNotEmpty(shareholderItem.getShareholder())) {
                    List<RealtimeOrderOfficialWeb.ShareholderInfo> shareholderItemDetailList = shareholderItem.getShareholder();
                    shareHolderItemTO.setDetails(shareholderItemDetailList.stream().map(shareholderItemDetail -> {
                        RealtimeOrderOfficialWebTO.ShareHolderItemDetail shareHolderItemDetailTO = new RealtimeOrderOfficialWebTO.ShareHolderItemDetail();
                        shareHolderItemDetailTO.setName(shareholderItemDetail.getName());
                        shareHolderItemDetailTO.setKeyNo(StringUtils.getIfNotPersonKeyNo(shareholderItemDetail.getKeyNo()));
                        shareHolderItemDetailTO.setNationCode(shareholderItemDetail.getNationCode());
                        shareHolderItemDetailTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(shareholderItemDetail.getNationCode()));
                        shareHolderItemDetailTO.setIdNo(shareholderItemDetail.getIdentificationNoOrig());
                        shareHolderItemDetailTO.setIdType(shareholderItemDetail.getIdentificationNumberType());
                        shareHolderItemDetailTO.setAddress(shareholderItemDetail.getAddress());
                        Integer entityType4Trans = detectEntityType4Trans(shareholderItemDetail.getEntityType(), shareHolderItemDetailTO.getKeyNo());
                        transWrapper.addEntryOverwriteOf(shareHolderItemDetailTO,
                                        RealtimeOrderOfficialWebTO.ShareHolderItemDetail::setName, RealtimeOrderOfficialWebTO.ShareHolderItemDetail::getName)
                                .withEntity(entityType4Trans, RealtimeOrderOfficialWebTO.ShareHolderItemDetail::getKeyNo);
                        return shareHolderItemDetailTO;
                    }).collect(Collectors.toList()));
                }
                return shareHolderItemTO;
            }).collect(Collectors.toList());
            CollectionUtils.sort(shareHolderItems, sh -> NumberUtils.parseNumber(sh.getNumberOfShares(), 0).intValue(), false);
            shareHolderInfoTO.setItems(shareHolderItems);
        }

        // map KeyPersonInfo
        keyPersonInfoTO.setDescription(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getDirectorsDescription));
        List<RealtimeOrderOfficialWeb.Director> directors = getValue(entity, RealtimeOrderOfficialWeb::getDirectors);
        if (CollectionUtils.isNotEmpty(directors)) {
            List<RealtimeOrderOfficialWebTO.KeyPersonItem> keyPersonItems = directors.stream().map(directorItem -> {
                RealtimeOrderOfficialWebTO.KeyPersonItem keyPersonItemTO = new RealtimeOrderOfficialWebTO.KeyPersonItem();
                keyPersonItemTO.setName(directorItem.getName());
                keyPersonItemTO.setPosition(directorItem.getPosition());
                keyPersonItemTO.setAddress(directorItem.getAddress());
                keyPersonItemTO.setAppointmentDate(directorItem.getAppointmentDate());
                Integer entityType4Trans = detectEntityType4Trans(directorItem.getEntityType(), directorItem.getKeyNo());
                transWrapper.addEntryOverwriteOf(keyPersonItemTO,
                                RealtimeOrderOfficialWebTO.KeyPersonItem::setName, RealtimeOrderOfficialWebTO.KeyPersonItem::getName)
                        .withEntity(entityType4Trans, null);
                return keyPersonItemTO;
            }).collect(Collectors.toList());
            CollectionUtils.sort(keyPersonItems, RealtimeOrderOfficialWebTO.KeyPersonItem::getAppointmentDate, false);
            keyPersonInfoTO.setItems(keyPersonItems);
        }

        // map UltimateHoldingCompanyInfo
        List<RealtimeOrderOfficialWeb.UltimateHoldingCompany> ultimateHoldingCompanyList = getValue(entity, RealtimeOrderOfficialWeb::getUltimateHoldingCompany);
        if (CollectionUtils.isNotEmpty(ultimateHoldingCompanyList)) {
            ultimateHoldingCompanyInfoTO.setItems(ultimateHoldingCompanyList.stream().map(ultimateHoldingCompanyItem -> {
                RealtimeOrderOfficialWebTO.UltimateHoldingCompanyItem ultimateHoldingCompanyItemTO = new RealtimeOrderOfficialWebTO.UltimateHoldingCompanyItem();
                ultimateHoldingCompanyItemTO.setName(ultimateHoldingCompanyItem.getName());
                ultimateHoldingCompanyItemTO.setKeyNo(StringUtils.getIfNotPersonKeyNo(ultimateHoldingCompanyItem.getKeyNo()));
                ultimateHoldingCompanyItemTO.setNationCode(ultimateHoldingCompanyItem.getNationCode());
                ultimateHoldingCompanyItemTO.setNationDesc(GlobalAreaEnum.getShortNameEnByNameCode(ultimateHoldingCompanyItem.getNationCode()));
                ultimateHoldingCompanyItemTO.setIdNo(ultimateHoldingCompanyItem.getCompNo());
                ultimateHoldingCompanyItemTO.setBusinessNo(ultimateHoldingCompanyItem.getBusinessNo());
                ultimateHoldingCompanyItemTO.setLegalForm(ultimateHoldingCompanyItem.getEntityType());
                ultimateHoldingCompanyItemTO.setRegisteredAddress(ultimateHoldingCompanyItem.getRegisteredAddress());
                Integer entityType4Trans = detectEntityType4Trans(ultimateHoldingCompanyItem.getEntityType(), ultimateHoldingCompanyItem.getKeyNo());
                transWrapper.addEntryOverwriteOf(ultimateHoldingCompanyItemTO,
                                RealtimeOrderOfficialWebTO.UltimateHoldingCompanyItem::setName, RealtimeOrderOfficialWebTO.UltimateHoldingCompanyItem::getName)
                        .withEntity(entityType4Trans, RealtimeOrderOfficialWebTO.UltimateHoldingCompanyItem::getKeyNo);
                return ultimateHoldingCompanyItemTO;
            }).collect(Collectors.toList()));
        }

        // map ReceiverShipInfo
        RealtimeOrderOfficialWeb.ReceiverShipInfo receiverShipInfo = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getReceiverShipInfo);
        receivershipInfoTO.setTitle(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getTitle));
        receivershipInfoTO.setPartTitle(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getPartTitle));
        receivershipInfoTO.setStartDate(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getStartDate));
        receivershipInfoTO.setStatus(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getStatus));
        receivershipInfoTO.setPropertyDescription(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getPropertyin));
        receivershipInfoTO.setAgreementDescription(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getAgreement));
        receivershipInfoTO.setAppointedBy(getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getAppointedBy));
        List<RealtimeOrderOfficialWeb.ReceiverShipList> receiverShipList = getValue(receiverShipInfo, RealtimeOrderOfficialWeb.ReceiverShipInfo::getReceiverShipList);
        if (CollectionUtils.isNotEmpty(receiverShipList)) {
            receivershipInfoTO.setItems(receiverShipList.stream().map(receiverShipItem -> {
                RealtimeOrderOfficialWebTO.ReceivershipItem receivershipItemTO = new RealtimeOrderOfficialWebTO.ReceivershipItem();
                receivershipItemTO.setName(receiverShipItem.getName());
                receivershipItemTO.setOrganization(receiverShipItem.getOrganisation());
                receivershipItemTO.setPhone(receiverShipItem.getPhone());
                receivershipItemTO.setEmail(receiverShipItem.getEmail());
                receivershipItemTO.setAddress(receiverShipItem.getAddress());
                receivershipItemTO.setAppointmentDate(receiverShipItem.getAppointmentDate());
                return receivershipItemTO;
            }).collect(Collectors.toList()));
        }

        // map LiquidationInfo
        RealtimeOrderOfficialWeb.LiquidationInfo liquidationInfo = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getLiquidationInfo);
        liquidationInfoTO.setTitle(getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getTitle));
        liquidationInfoTO.setPartTitle(getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getPartTitle));
        liquidationInfoTO.setStartDate(getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getStartDate));
        liquidationInfoTO.setStatus(getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getStatus));
        liquidationInfoTO.setAppointedBy(getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getAppointedBy));
        List<RealtimeOrderOfficialWeb.LiquidationList> liquidationList = getValue(liquidationInfo, RealtimeOrderOfficialWeb.LiquidationInfo::getLiquidationList);
        if (CollectionUtils.isNotEmpty(liquidationList)) {
            liquidationInfoTO.setItems(liquidationList.stream().map(liquidationItem -> {
                RealtimeOrderOfficialWebTO.LiquidationItem receivershipItemTO = new RealtimeOrderOfficialWebTO.LiquidationItem();
                receivershipItemTO.setName(liquidationItem.getName());
                receivershipItemTO.setOrganization(liquidationItem.getOrganisation());
                receivershipItemTO.setPhone(liquidationItem.getPhone());
                receivershipItemTO.setEmail(liquidationItem.getEmail());
                receivershipItemTO.setAddress(liquidationItem.getAddress());
                receivershipItemTO.setAppointmentDate(liquidationItem.getAppointmentDate());
                return receivershipItemTO;
            }).collect(Collectors.toList()));
        }

        // map NameHistoryInfo
        nameHistoryInfoTO.setDescription(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getPreNamesDescription));
        List<RealtimeOrderOfficialWeb.PreviousName> previousNames = getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getPreviousNames);
        if (CollectionUtils.isNotEmpty(previousNames)) {
            List<RealtimeOrderOfficialWebTO.NameHistoryItem> nameHistoryItems = previousNames.stream().map(previousNameItem -> {
                RealtimeOrderOfficialWebTO.NameHistoryItem nameHistoryItemTO = new RealtimeOrderOfficialWebTO.NameHistoryItem();
                nameHistoryItemTO.setStartDate(previousNameItem.getStartDate());
                nameHistoryItemTO.setEndDate(previousNameItem.getEndDate());
                nameHistoryItemTO.setName(previousNameItem.getCompName());
                transWrapper.addEntryOverwriteOf(nameHistoryItemTO,
                                RealtimeOrderOfficialWebTO.NameHistoryItem::setName, RealtimeOrderOfficialWebTO.NameHistoryItem::getName)
                        .withEntity(TransWrapper.ENTRY_TYPE_CORP, null);
                return nameHistoryItemTO;
            }).collect(Collectors.toList());
            CollectionUtils.sort(nameHistoryItems, RealtimeOrderOfficialWebTO.NameHistoryItem::getStartDate, false);
            nameHistoryInfoTO.setItems(nameHistoryItems);
        }

        // map AdditionalInfo
        List<String> tradingNames = getFromOtherNames(otherNames, "Trading Name")
                .stream().map(CommonTransService::upperCaseExceptEmTags).collect(Collectors.toList());
        additionalInfoTO.setTradingNameList(tradingNames);
        additionalInfoTO.setAbn(getFromOtherNumbers(otherNumbers, "Australian Business Number (ABN)"));
        additionalInfoTO.setTradingArea(getValue(contactInformation, RealtimeOrderOfficialWeb.ContactInformation::getTradingArea));
        additionalInfoTO.setConstitutionFiled(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getConstitutionFiled));
        additionalInfoTO.setArFilingMonth(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getArFilingMonth));
        additionalInfoTO.setFraReportingMonth(getValue(companyInfo, RealtimeOrderOfficialWeb.CompanyInfo::getFraReportingMonth));
        commonTransService.enPostProcessor(transWrapper);
        return to;
    }

    private static List<String> getFromOtherNumbers(List<RealtimeOrderOfficialWeb.OtherNumber> otherNumbers, String type) {
        if (CollectionUtils.isEmpty(otherNumbers)) {
            return new ArrayList<>();
        }
        return otherNumbers.stream()
                .filter(k -> type.equals(k.getType()))
                .map(RealtimeOrderOfficialWeb.OtherNumber::getNumber)
                .collect(Collectors.toList());
    }

    private static List<String> getFromOtherNames(List<RealtimeOrderOfficialWeb.OtherName> otherNames, String type) {
        if (CollectionUtils.isEmpty(otherNames)) {
            return new ArrayList<>();
        }
        return otherNames.stream()
                .filter(k -> type.equals(k.getType()))
                .map(RealtimeOrderOfficialWeb.OtherName::getCompName)
                .collect(Collectors.toList());
    }

    private static List<String> getFromCompAddressList(List<RealtimeOrderOfficialWeb.CompAddress> compAddressList, String type) {
        if (CollectionUtils.isEmpty(compAddressList)) {
            return new ArrayList<>();
        }
        return compAddressList.stream()
                .filter(k -> type.equals(k.getType()))
                .map(RealtimeOrderOfficialWeb.CompAddress::getAddress)
                .collect(Collectors.toList());
    }

    private static Integer detectEntityType4Trans(String entityType, String keyNo) {
        Integer entityTypeTrans = null;
        if (StringUtils.equals(entityType, "Individual")
                || StringUtils.getIfPerson(keyNo)) {
            entityTypeTrans = TransWrapper.ENTRY_TYPE_PERSON;
        } else if (StringUtils.equals(entityType, "Company")) {
            entityTypeTrans = TransWrapper.ENTRY_TYPE_CORP;
        }
        return entityTypeTrans;
    }
}
