package com.backend.common.modules.report_data.corp.oversea.base;

import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.service.CommTblCompReportOrderExtService;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report_data.form.ReportDataGetResultForm;
import com.backend.common.modules.report_data.model.ReportDataResultTO;
import com.backend.common.modules.trans.impl.service.CommonTransService;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.qcc.frame.commons.ienum.OrderStatusEnum;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * @param <R> 数据TO
 */
public abstract class AbstractReportDataService<R> {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    protected CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    protected CommTblCompReportOrderExtService reportOrderExtService;
    @Autowired
    protected OvsQccOvsBasicService ovsQccOvsBasicService;
    @Autowired
    protected CommonTransService commonTransService;

    /**
     * 支持的reportType
     *
     * @return
     */
    public abstract List<ReportTypeEnum> getReportTypes();

    /**
     * 支持的版本
     *
     * @return
     */
    public String getDataVersion() {
        return "1";
    }

    public boolean ifSupported(ReportTypeEnum reportTypeEnum, String dataVersion) {
        boolean match = false;
        if (getReportTypes().contains(reportTypeEnum)) {
            if ("1".equals(getDataVersion())) {
                // 当前service为默认版本1, 则支持入参version为空
                match = StringUtils.isBlank(dataVersion) || StringUtils.equals(dataVersion, getDataVersion());
            } else {
                match = StringUtils.equals(dataVersion, getDataVersion());
            }
        }
        return match;
    }

//    public abstract List<CanBuyTO> canOrder(Collection<String> keyNos);

    /**
     * 订单状态是数据已成功
     *
     * @param order
     * @return
     */
    protected boolean doCheckOrderDataSuccess(TblCompReportOrder order) {
        if (order == null) {
            logger.error("TblCompReportOrder is null");
            return false;
        }
        return OrderStatusEnum.getDataSuccessStatus().contains(order.getRptStatus());
    }

    /**
     * 检查数据是否已处理成功
     * 注意: 底层数据落表不代表数据已处理成功(比如股东里公司匹配逻辑会插入记录后再处理, 然后更新),
     * 实现此方法时需要跟数据确认数据处理成功的判断逻辑。
     *
     * @param form
     * @return
     */
    public abstract boolean isDataSuccess(ReportDataGetResultForm form);

    protected ReportDataResultTO<R> initResult(ReportDataGetResultForm form, TblCompReportOrder order) {
        ReportDataResultTO<R> result = new ReportDataResultTO<>();
        if (order != null) {
            result.setOrderId(order.getId());
            result.setReportType(order.getReportType());
            result.setVersion(order.getDataVersion());
            result.setOrderCreateDate(order.getCreateDate());
        }
        doAfterInitOrderResult(result);
        return result;
    }

    /**
     * added for v2.3.3 fengsw KNZT-8350
     * 初始化订单结果后处理，子类可覆盖此方法进行额外操作
     * 当前仅有香港报告Basic、Basic+ar需要处理是否非工作日判断，新加坡Basic处理特定类型的非工作判断
     * 后续其他类型报告需要处理数据，可覆盖此方法重新实现
     *
     * @param result
     */
    protected void doAfterInitOrderResult(ReportDataResultTO<R> result) {
    }

    public ReportDataResultTO<R> getResult(ReportDataGetResultForm form) {
        TblCompReportOrder order = commTblCompReportOrderService.get(form.getOrderId());
        ReportDataResultTO<R> result = initResult(form, order);
        if (!doCheckOrderDataSuccess(order)) {
            return result;
        }
        result.setData(getData(form));
        return result;
    }

    protected abstract R getData(ReportDataGetResultForm form);

    protected String formatCountryCity(String country, String city) {
        if (StringUtils.isBlank(city)) {
            return country;
        } else if (StringUtils.isBlank(country)) {
            return city;
        } else {
            return city + ", " + country;
        }
    }

    /**
     * 用于将映射值和原值格式化成形如"Active (Registered)"
     *
     * @param originVal 原值
     * @param labelVal  映射值
     * @return
     */
    protected static String formatOriginLabel(String originVal, String labelVal) {
        String res = null;
        if (StringUtils.isNotBlank(labelVal)) {
            if (StringUtils.isNotBlank(originVal)) {
                if (StringUtils.equalsIgnoreCase(labelVal, originVal)) {
                    res = labelVal;
                } else {
                    res = labelVal + " (" + originVal + ")";
                }
            } else {
                res = labelVal;
            }
        } else {
            res = originVal;
        }
        return res;
    }

    /**
     * 用于将数据日期yyyy-MM-dd格式化成 dd MMM yyyy
     *
     * @param dateStr yyyy-MM-dd
     * @return dd MMM yyyy
     */
    protected String formatDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            Date date = DateUtils.parseDateStrictly(dateStr, DateUtils.DATE_FORMAT);
            if (date == null) {
                logger.error("日期解析失败, dateStr: " + dateStr);
            } else {
                return DateUtils.formatDateForSg(date, DateUtils.DATE_FORMAT_DDMMMYYYY, Locale.ENGLISH);
            }
        } catch (Exception e) {
            logger.error("日期解析失败, dateStr: " + dateStr);
        }
        return null;
    }
}
