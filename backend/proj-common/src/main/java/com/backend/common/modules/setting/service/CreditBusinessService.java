package com.backend.common.modules.setting.service;

import com.backend.common.service.CommCompUserService;
import com.qcc.frame.commons.ienum.CommDelayedTaskTypeEnum;
import com.backend.common.entity.CompInfoExt;
import com.backend.common.service.CommSysCompInfoExtService;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.modules.sys.model.CompanyChargeTO;
import com.qcc.frame.jee.modules.sys.service.RedisService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.stripe.model.PaymentIntent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.backend.common.modules.delivery.service.CommTblContractDeliveryService;
import com.backend.common.modules.setting.entity.TblChargePlan;
import com.backend.common.modules.setting.form.TopUpCreditForm;
import com.backend.common.modules.setting.form.TopUpResultForm;
import com.backend.common.modules.setting.model.TopUpCreditTO;
import com.backend.common.stripeapi.StripePaymentInterface;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 额度管理 service
 * added for lvcy v2.1.7 KNZT-6487
 * <AUTHOR>
 * @datetime 25/2/2025 11:03 上午
 */
@Service
public class CreditBusinessService {
    @Autowired
    private CommTblContractDeliveryService contractDeliveryService;
    @Autowired
    private CommTblChargePlanService chargePlanService;
    @Autowired
    private CommSysCompInfoExtService sysCompInfoExtService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CommCompUserService compUserService;

    private static final Logger logger = LoggerFactory.getLogger(CreditBusinessService.class);
    
    /**
     * 查询充值结果
     * @param form 查询参数
     * @return 充值是否成功
     */
    public Boolean getTopUpResult(TopUpResultForm form) {
        if (StringUtils.isBlank(form.getTopUpNo())) {
            return false;
        }
        return CollectionUtils.isNotEmpty(contractDeliveryService.getByContractNo(form.getTopUpNo()));
    }

    public TopUpCreditTO topUpCredit(TopUpCreditForm form) throws MessageException {
        MsgExceptionUtils.failBuild(StringUtils.isAnyBlank(form.getChargePlanId()), "err.param.invalid");
        TblChargePlan chargePlan = chargePlanService.get(form.getChargePlanId());
        MsgExceptionUtils.checkIsNull(chargePlan, "err.param.invalid");
        String topUpNo = contractDeliveryService.generateTopUpNo();
        String companyId = UserUtils.getUserCompanyId();
        String userId = UserUtils.getUserId();

        if ("89422f9508b84c65a4e406f2fc5973ac".equals(companyId) || "dd85a39ff0c44f5080ba8baf515b0b21".equals(companyId)) {
            chargePlan.setCreditAmount(BigDecimal.valueOf(0.5));
        }

        // added for v.2.2.7 fengsw KNZT-7785 当前充值金额和用户累计已充值金额之和是否超过系统配置限额
        CompanyChargeTO chargeInfo = compUserService.getTopUpInfo(companyId);
        if (Objects.nonNull(chargeInfo) && chargeInfo.getChargeLimitAmount() != null && chargeInfo.getCurrentChargedAmount() != null && chargeInfo.getChargeLimitAmount().subtract(chargeInfo.getCurrentChargedAmount()).compareTo(chargePlan.getCreditAmount()) < 0) {
            TopUpCreditTO topUpCreditTO = new TopUpCreditTO();
            topUpCreditTO.setTopUpOverLimit(true);
            topUpCreditTO.setChargeLimitAmount(chargeInfo.getChargeLimitAmount());
            return topUpCreditTO;
        }
        PaymentIntent paymentIntent = StripePaymentInterface.createPaymentIntent4TopUp(
                topUpNo, companyId, userId, chargePlan.getCreditAmount(), chargePlan.getCreditCount(), chargePlan.getPeriodValidityYear());

        redisService.addDelayedCommTask(CommDelayedTaskTypeEnum.PAYMENT_INTENT_CANCEL, paymentIntent.getId(), 30 * 60);

        TopUpCreditTO topUpCreditTO = new TopUpCreditTO();
        topUpCreditTO.setTopUpNo(topUpNo);
        topUpCreditTO.setPaymentIntentId(paymentIntent.getId());
        topUpCreditTO.setClientSecret(paymentIntent.getClientSecret());

        // 充值时，默认用户勾选了充值条款
        CompInfoExt compInfoExt = sysCompInfoExtService.retrieveCompInfoExtInfo(companyId, "USER_PAYMENT_TERMS");
        if (Objects.isNull(compInfoExt)) {
            CompInfoExt ext = new CompInfoExt();
            ext.setCompanyId(companyId);
            ext.setType("USER_PAYMENT_TERMS");
            ext.setValue("Y");
            ext.setDesc("用户充值条款保存");
            sysCompInfoExtService.save(ext);
        }
        return topUpCreditTO;
    }

    // 二次充值前查询用户是否勾选过充值条款
    public boolean getDetailBeforePayment() {
        CompInfoExt compInfoExt = sysCompInfoExtService.retrieveCompInfoExtInfo(UserUtils.getUserCompanyId(), "USER_PAYMENT_TERMS");
        return Objects.nonNull(compInfoExt);
    }
}
