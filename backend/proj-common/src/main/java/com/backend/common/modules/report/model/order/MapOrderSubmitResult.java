package com.backend.common.modules.report.model.order;

import com.backend.common.modules.report.entity.TblCompReportOrder;
import lombok.Data;

import java.util.List;

/**
 * 图谱订单提交结果
 *
 * <AUTHOR>
 * @datetime 1/9/2025 4:15 下午
 */

@Data
public class MapOrderSubmitResult {

    /**
     * 成功列表
     */
    private List<OrderSubmitSuccessTO> submitSuccessList;

    /**
     * 保存的图谱id
     */
    private String mapId;
    /**
     * 图谱编号，初次新建时返回，修改时返回null
     */
    private String mapNoFirstCreate;

    /**
     * 数据
     */
    private Object data;


    public static MapOrderSubmitResult build(List<TblCompReportOrder> orderList, String mapId) {
        MapOrderSubmitResult result = new MapOrderSubmitResult();
        result.setSubmitSuccessList(OrderSubmitSuccessTO.build(orderList));
        result.setMapId(mapId);
        return result;
    }
}

