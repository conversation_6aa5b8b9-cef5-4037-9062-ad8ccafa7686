package com.backend.common.modules.delivery.mapper;

import com.backend.common.modules.delivery.condition.CompChargeHistoryCondition;
import com.backend.common.modules.delivery.condition.CompCreditSummaryCondition;
import com.backend.common.modules.delivery.condition.TblContractDeliveryProdAccCondition;
import com.backend.common.modules.delivery.model.CreditsSummaryTO;
import com.backend.common.modules.delivery.model.TopUpHistoryTO;
import org.apache.ibatis.annotations.Param;

import com.backend.common.modules.delivery.entity.TblContractDeliveryProdAcc;
import com.qcc.frame.jee.commons.persistence.CrudDao;
import com.qcc.frame.jee.commons.persistence.annotation.MyBatisDao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MyBatisDao
public interface TblContractDeliveryProdAccDao extends CrudDao<TblContractDeliveryProdAcc> {
    List<TblContractDeliveryProdAcc> getByContractDeliveryId(@Param("companyId") String companyId,
                                                             @Param("contractDeliveryId") String contractDeliveryId);

    List<TblContractDeliveryProdAcc> getByCompanyId(@Param("companyId") String companyId);

    List<TblContractDeliveryProdAcc> lockByCompanyId(@Param("companyId") String companyId, @Param("accDate") Date accDate);

    int updateConsumedUnit(@Param("id") String accId, @Param("afterConsumedUnit") BigDecimal afterConsumedUnit, @Param("beforeConsumedUnit") BigDecimal beforeConsumedUnit);

    List<TblContractDeliveryProdAcc> pageByCreateDate(@Param("size") int size, @Param("lastMinDate") Date lastMinDate);

    TblContractDeliveryProdAcc lockById(String id);

    // added for v1.9.8 KNZT-4538
    List<TblContractDeliveryProdAcc> pageByCondition(@Param("obj") TblContractDeliveryProdAccCondition condition);

    // 余额详情数据-分页查询
    List<CreditsSummaryTO> pageCreditsBreakdownCondition(@Param("obj") CompCreditSummaryCondition condition); 

    // 查询充值记录（预付费、赠送、线上充值）-分页
    List<TopUpHistoryTO> pageChargeRecords(@Param("obj") CompChargeHistoryCondition condition);

    BigDecimal countExpireSoonAccountCredits(@Param("companyId") String companyId, @Param("validity") int validity);

    Integer existChangeProdAccInDateNoContract(@Param("companyId") String companyId, @Param("date") Date date,  @Param("yesterday") Date yesterday);

    /**
     * 查询指定日期过期且未消耗完的额度账户
     * @param expiredDate 过期日期
     * @return 过期但未消耗完的额度账户列表
     */
    List<TblContractDeliveryProdAcc> findUnconsumedExpiredAccounts(@Param("expiredDate") Date expiredDate);

    /**
     * 查询指定日期过期且未消耗完的额度账户ID列表
     * @param expiredDate 过期日期
     * @return 过期但未消耗完的额度账户ID列表
     */
    List<String> findUnconsumedExpiredAccountIds(@Param("expiredDate") Date expiredDate);
}
