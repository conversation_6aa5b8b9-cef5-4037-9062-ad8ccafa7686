package com.backend.admin.thread;

import com.backend.admin.scheduler.BaseSchedulerJob;
import com.qcc.frame.jee.commons.thread.BaseRunnable;
import com.qcc.frame.jee.commons.utils.WebContextHolder;

import java.util.List;

public class TriggerRunnable extends BaseRunnable {
	
	private BaseSchedulerJob baseJob;
	private List<String> triggerCompanyIdList;

	@Override
	public void process() {
		if(baseJob != null) {
			try {
				if(baseJob.checkIfSupportTriggerWithCompanyParam() && triggerCompanyIdList != null && triggerCompanyIdList.size() > 0) {
					WebContextHolder.setTriggerJobCompanyIdList(triggerCompanyIdList);
				} else {
					WebContextHolder.removeTriggerJobCompanyIdList();
				}
				baseJob.process();
			} catch(Exception e) {
				throw e;
			} finally {
				WebContextHolder.removeTriggerJobCompanyIdList();
			}
		}
	}

	public void setBaseJob(BaseSchedulerJob baseJob) {
		this.baseJob = baseJob;
	}

	public void setTriggerCompanyIdList(List<String> triggerCompanyIdList) {
		this.triggerCompanyIdList = triggerCompanyIdList;
	}
}
