package com.backend.admin.scheduler;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.backend.admin.entity.SysScheduleTask;
import com.backend.admin.model.TaskHolder;
import com.backend.admin.service.SysScheduleTaskService;
import com.backend.admin.utils.TaskUtils;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.utils.WebContextHolder;

public abstract class BaseSchedulerJob {
	protected Logger logger = LoggerFactory.getLogger(getClass());
	
	protected SysScheduleTaskService sysScheduleTaskService;
	
	public void initBean() {
		try {
			if(StringUtils.isBlank(getTaskName())) {
				logger.error("Please set task name for Job Class[" + getClass().getName() + "]!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
				return;
			}
			sysScheduleTaskService.initTask(getTaskName());
			TaskHolder taskHolder = new TaskHolder();
			taskHolder.setRunStatus(Constants.Job.RUN_STATUS_WAIT);
			taskHolder.setThat(this);
			TaskUtils.putTask(getTaskName(), taskHolder);
		} catch(Exception e) {
			logger.error("", e);
		}
	}
	
	public final void execute() {
		try {
			if(StringUtils.isBlank(getTaskName())) {
				logger.error("Please set task name for Job Class[" + getClass().getName() + "]!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
				return;
			}
			
			if(!sysScheduleTaskService.enableWebJob(getTaskName())) {
				return;
			}
			
			SysScheduleTask task = sysScheduleTaskService.findActiveByTaskName(getTaskName());
			if(task != null && Constants.Sys.JOB_STATUS_ACTIVE.equalsIgnoreCase(task.getStatus())) {
				process();
			}
		} catch(Throwable e) {
			logger.info("Task [" + getTaskName() + "] error:", e);
		} finally {
			WebContextHolder.setJobCompanyId(null);
			WebContextHolder.setCurrentUser(null);
		}
	}
	
	public final void process() {
		if(!sysScheduleTaskService.enableWebJob(getTaskName())) {
			return;
		}
		TaskHolder taskHolder = TaskUtils.getTask(getTaskName());
		if(taskHolder == null) {
			long tm = System.currentTimeMillis();
			logger.info("Task [" + getTaskName() + "] begin none holder");
			processTask();
			logger.info("Task [" + getTaskName() + "] end none holder" + (System.currentTimeMillis() - tm) + " ms");
		} else {
			if(Constants.Job.RUN_STATUS_RUNING.equalsIgnoreCase(taskHolder.getRunStatus())) {
				logger.info(getTaskName() + " is running");
				return;
			}
			taskHolder.setRunStatus(Constants.Job.RUN_STATUS_RUNING);
			taskHolder.setRunBeginDate(new Date());
			try {
				long tm = System.currentTimeMillis();
				logger.info("Task [" + getTaskName() + "] begin");
				processTask();
				logger.info("Task [" + getTaskName() + "] end took " + (System.currentTimeMillis() - tm) + " ms");
			} catch(Throwable e) {
				logger.info("Task [" + getTaskName() + "] end with error as below:");
				throw e;
			} finally {
				taskHolder.setRunStatus(Constants.Job.RUN_STATUS_WAIT);
				taskHolder.setRunBeginDate(null);
			}
		}
		
	}
	
	public void destroyBean() {
		TaskUtils.clear();
	}

	public final List<String> listTriggerCompanyIdList() {
		return WebContextHolder.getTriggerJobCompanyIdList();
	}

	/**
	 * 仅限BaseCompanySchedulerJob
	 * 定时任务是否支持触发时带CompanyId参数, 跟listTriggerCompanyIdList()组合使用
	 * @return
	 */
	public boolean isSupportTriggerWithCompanyParam() {
		return false;
	}

	public final boolean checkIfSupportTriggerWithCompanyParam() {
		return isSupportTriggerWithCompanyParam() && (this instanceof BaseCompanySchedulerJob || this instanceof BaseParamSchedulerJob);
	}

	public abstract void processTask();
	public abstract String getTaskName();

	public void setSysScheduleTaskService(SysScheduleTaskService sysScheduleTaskService) {
		this.sysScheduleTaskService = sysScheduleTaskService;
	}

	public SysScheduleTaskService getSysScheduleTaskService() {
		return sysScheduleTaskService;
	}

}
