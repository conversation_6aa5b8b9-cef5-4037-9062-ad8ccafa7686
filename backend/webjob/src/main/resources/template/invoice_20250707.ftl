<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>report</title>

  <style>
    @media print {
      ::-webkit-scrollbar {
        display: none;
      }

      body {
        outline: 0;
      }
    }

    @page {
      width: 100%;
      size: 10.5in 14.85in;
      margin: 0;
    }

    /* 告诉浏览器在渲染它时不要对框进行颜色或样式调整 */
    * {
      -webkit-print-color-adjust: exact !important;
      -moz-print-color-adjust: exact !important;
      -ms-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      font-family: Inter;
      box-sizing: border-box;
    }
  </style>
</head>

<body style="margin: 0;font-size: 14px;">
  <div>
    <!-- header -->
    <div style="padding: 20px 50px;background-color: #F8F8F8;">
      <table style="width: 100%;border-collapse: collapse;">
        <tr>
          <td style="vertical-align: middle;">
            <div style="font-size: 44px;font-weight: 600;color: #333333;">
              TAX INVOICE
            </div>
          </td>
          <td style="text-align: right;color: #808080;font-size: 14px;">
            <img style="margin-bottom: 5px;"
              src="https://ali-obs.qcckyc.com/FrontUpd/20250506-d95bc96f-edc3-4085-be68-3e5febd3bd9f-affdb194cc024b398ada13566e1c903b.png" />
            <div>QCC TECH PTE. LTD.</div>
            <div>55 ayer rajah crescent, #01-26, Singapore 139949</div>
            <div>UEN: 202012032Z</div>
            <div>GST Reg No: 202012032Z</div>
          </td>
        </tr>
      </table>
    </div>

    <!-- content -->
    <div style="padding: 0 50px;margin-top: 50px;">
      <table style="width: 100%;border-collapse: collapse;">
        <tr style="padding-bottom: 8px;">
          <td width="150px" style="vertical-align: top;">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              To
            </div>
          </td>
          <td>
            <div style="font-size: 14px;line-height: 22px;color: #111111;">
              ${to!''}
            </div>
            <#if address?? && address !="">
              <div style="font-size: 14px;line-height: 22px;color: #111111;">
                ${address}
              </div>
            </#if>
            <#if taxIdType?? && taxIdType != "" && taxIdNumber?? && taxIdNumber != "">
              <div style="font-size: 14px;line-height: 22px;color: #111111;">
                ${taxIdType}: ${taxIdNumber}
              </div>
            </#if>
          </td>
        </tr>

        <tr style="padding-bottom: 8px;">
          <td width="150px">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              Invoice issue date
            </div>
          </td>
          <td>
            <div style="font-size: 14px;line-height: 22px;color: #111111;">
              ${issueDate!''}
            </div>
          </td>
        </tr>

        <tr>
          <td width="150px">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              Invoice No
            </div>
          </td>
          <td>
            <div style="font-size: 14px;line-height: 22px;color: #111111;">
              ${invoiceNo!''}
            </div>
          </td>
        </tr>
      </table>
    </div>

    <div style="padding: 0 50px;margin-top: 50px;">
      <#-- 动态计算列宽度 -->
      <#assign hasRemarks = remark?? && remark != "">
      <#assign isSGDCurrency = isSGD?? && isSGD == "Y">

      <#if !isSGDCurrency>
        <div style="margin-bottom: 15px;font-size: 12px;line-height: 18px;color: #828890;">
          *You have selected ${amtStd!'USD'} as your preferred payment currency.
        </div>
      </#if>
      

      
      <#if isSGDCurrency && !hasRemarks>
        <#assign descWidth = 32>
        <#assign qtyWidth = 20>
        <#assign unitPriceWidth = 20>
        <#assign totalSGDWidth = 28>
        <#assign remarksWidth = 0>
        <#assign totalUSDWidth = 0>
      <#elseif isSGDCurrency && hasRemarks>
        <#assign descWidth = 26>
        <#assign qtyWidth = 16>
        <#assign unitPriceWidth = 18>
        <#assign remarksWidth = 24>
        <#assign totalSGDWidth = 16>
        <#assign totalUSDWidth = 0>
      <#elseif !isSGDCurrency && !hasRemarks>
        <#assign descWidth = 26>
        <#assign qtyWidth = 17>
        <#assign unitPriceWidth = 17>
        <#assign totalUSDWidth = 20>
        <#assign totalSGDWidth = 20>
        <#assign remarksWidth = 0>
      <#else>
        <#assign descWidth = 20>
        <#assign qtyWidth = 12>
        <#assign unitPriceWidth = 17>
        <#assign remarksWidth = 17>
        <#assign totalUSDWidth = 17>
        <#assign totalSGDWidth = 17>
      </#if>
      
      <table style="width: 100%;border-collapse: collapse;">
        <tr>
          <td width="${descWidth}%" style="padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
            Description
          </td>
          <td width="${qtyWidth}%"
            style="text-align: right;padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
            QTY
          </td>
          <td width="${unitPriceWidth}%"
            style="text-align: right;padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
            Unit Price (${amtStd})
          </td>
          <#if hasRemarks>
            <td width="${remarksWidth}%" style="padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
              Remarks
            </td>
          </#if>
          <#if !isSGDCurrency>
            <td width="${totalUSDWidth}%"
              style="text-align: right;padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
              Total (${amtStd})
            </td>
          </#if>
          <td width="${totalSGDWidth}%"
            style="text-align: right;padding: 0 10px 5px;font-size: 13px;font-weight: bold;color: #111111;">
            Total (SGD)
          </td>
        </tr>

        <#-- 当offsetCreditsQty不为null且不为0时，显示offset行 -->
        <#if offsetCreditsQty??>
          <tr>
            <td
              style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;">
                Credits Offset
              </div>
            </td>
            <td
              style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                ${offsetCreditsQty!}
              </div>
            </td>
            <td
              style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                -
              </div>
            </td>
            <#if hasRemarks>
              <td
                style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
                <div style="line-height: 22px;font-size: 14px;color: #111111;word-break: break-word;">
                  -
                </div>
              </td>
            </#if>
            <#if !isSGDCurrency>
              <td
                style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
                <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                  -
                </div>
              </td>
            </#if>
            <td style="padding: 4px 10px;background-color: #FAFAFA;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                -
              </div>
            </td>
          </tr>
        </#if>

        <tr>
          <td
            style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;">
              ${creditsType!'Credits Used'}
            </div>
            <#if creditDesc?? && creditDesc != "">
              <div style="font-size: 12px;line-height: 18px;color: #828890;">
                ${creditDesc}
              </div>
            </#if>
          </td>
          <td
            style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              ${qty!}
            </div>
          </td>
          <td
            style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              ${unitPrice!}
            </div>
          </td>
          <#if hasRemarks>
            <td
              style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;word-break: break-word;">
                ${remark!}
              </div>
            </td>
          </#if>
          <#if !isSGDCurrency>
            <td
              style="padding: 4px 10px;background-color: #FAFAFA;border-right: 1px solid #ffffff;vertical-align: middle;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                ${totalUsedAmount!}
              </div>
            </td>
          </#if>
          <td style="padding: 4px 10px;background-color: #FAFAFA;vertical-align: middle;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              <#if isSGDCurrency>
                ${totalUsedAmount!}
              <#else>
                ${totalUsedAmountInSGD!}
              </#if>
            </div>
          </td>
        </tr>
      </table>

      <table style="width: 100%;border-collapse: collapse;">
        <#-- 计算底部表格的列宽度 -->
        <#-- 底部表格需要与上方表格的最后几列对齐 -->
        <#if isSGDCurrency>
          <#-- SGD货币：底部表格2列，需要与上方表格最后1列对齐 -->
          <#assign bottomFirstColWidth = 100 - totalSGDWidth>
          <#assign bottomLastColWidth = totalSGDWidth>
        <#else>
          <#-- 非SGD货币：底部表格3列，需要与上方表格最后2列对齐 -->
          <#assign bottomFirstColWidth = 100 - totalUSDWidth - totalSGDWidth>
          <#assign bottomSecondColWidth = totalUSDWidth>
          <#assign bottomLastColWidth = totalSGDWidth>
        </#if>
        
        <tr>
          <td width="${bottomFirstColWidth}%" style="border-top: 1px solid #5B6169;"></td>
          <#if !isSGDCurrency>
            <td width="${bottomSecondColWidth}%" style="border-top: 1px solid #5B6169;"></td>
          </#if>
          <td width="${bottomLastColWidth}%" style="border-top: 1px solid #5B6169;"></td>
        </tr>

        <#if !isSGDCurrency>
          <tr>
            <td style="padding: 4px 10px;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                Exchange Rate vs ${amtStd!}
              </div>
            </td>
            <td style="padding: 4px 10px;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                1.0000
              </div>
            </td>
            <td style="padding: 4px 10px;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                ${exchangeRate!}
              </div>
            </td>
          </tr>
        </#if>

        <tr>
          <td style="padding: 4px 10px;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              Total
            </div>
          </td>
          <#if !isSGDCurrency>
            <td style="padding: 4px 10px;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                ${totalUsedAmount!}
              </div>
            </td>
          </#if>
          <td style="padding: 4px 10px;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              <#if isSGDCurrency>
                ${totalUsedAmount!}
              <#else>
                ${totalUsedAmountInSGD!}
              </#if>
            </div>
          </td>
        </tr>

        <tr>
          <td style="padding: 4px 10px;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              Add GST @ ${taxRate!'9%'}
            </div>
          </td>
          <#if !isSGDCurrency>
            <td style="padding: 4px 10px;">
              <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
                ${taxAmount!}
              </div>
            </td>
          </#if>
          <td style="padding: 4px 10px;">
            <div style="line-height: 22px;font-size: 14px;color: #111111;text-align: right;">
              <#if isSGDCurrency>
                ${taxAmount!}
              <#else>
                ${taxAmountInSGD!}
              </#if>
            </div>
          </td>
        </tr>

        <tr>
          <td style="padding: 4px 10px;border-top: 1px solid #5B6169;">
            <div style="line-height: 22px;font-size: 14px;font-weight: bold;color: #111111;text-align: right;">
              Amount Due
            </div>
          </td>
          <#if !isSGDCurrency>
            <td style="padding: 4px 10px;border-top: 1px solid #5B6169;">
              <div style="line-height: 22px;font-size: 14px;font-weight: bold;color: #111111;text-align: right;">
                ${afterTaxAmount!}
              </div>
            </td>
          </#if>
          <td style="padding: 4px 10px;border-top: 1px solid #5B6169;">
            <div style="line-height: 22px;font-size: 14px;font-weight: bold;color: #111111;text-align: right;">
              <#if isSGDCurrency>
                ${afterTaxAmount!}
              <#else>
                ${afterTaxAmountInSGD!}
              </#if>
            </div>
          </td>
        </tr>
      </table>
    </div>

    <div style="padding: 0 50px;margin-top: 10px;">
      <#if compType?? && (compType == 3 || compType == 4)>
        <div
            style="font-size: 14px;color: #828890;padding: 4px;border-radius: 2px;background: #F7F7F7;width: fit-content;">
            This invoice is for usage tracking purpose as the total amount due has been paid by the credit cards.
        </div>
      </#if>
    </div>

    <!-- Banking Information -->
    <div style="padding: 0 50px;margin-top: 50px;">
      <div
        style="padding-bottom: 10px;border-bottom: 1px solid #EEEEEE;margin-bottom: 12px;font-size: 18px;font-weight: bold;color: #111111;">
        Banking Information
      </div>
      <table style="width: 100%;border-collapse: collapse;">
        <tr>
          <td width="150px" style="padding: 10px 0 3px;">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              Account Name
            </div>
          </td>
          <td style="padding: 10px 0 3px;">
            <div style="font-size: 14px;color: #111111;">
              ${accountName!'QCC TECH PTE. LTD.'}
            </div>
          </td>
        </tr>

        <tr>
          <td width="150px" style="padding: 10px 0 3px;">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              Deposit Bank
            </div>
          </td>
          <td style="padding: 10px 0 3px;">
            <div style="font-size: 14px;color: #111111;">
              ${depositBank!'Oversea-Chinese Banking Corporation Limited'}
            </div>
          </td>
        </tr>

        <tr>
          <td width="150px" style="padding: 10px 0 3px;">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              Account Number
            </div>
          </td>
          <td style="padding: 10px 0 3px;">
            <div style="font-size: 14px;color: #111111;">
              ${accountNumber!'************'}
            </div>
          </td>
        </tr>

        <tr>
          <td width="150px" style="padding: 10px 0 3px;">
            <div style="font-size: 14px;font-weight: bold;color: #111111;">
              SWIFT
            </div>
          </td>
          <td style="padding: 10px 0 3px;">
            <div style="font-size: 14px;color: #111111;">
              ${swiftCode!'OCBCSGSGXXX'}
            </div>
          </td>
        </tr>
      </table>
    </div>

    <!-- Billing Details -->
    <#if userConsumeList?? && userConsumeList?size gt 0>
      <div style="padding: 0 50px;margin-top: 50px;">
        <div
          style="padding-bottom: 10px;border-bottom: 1px solid #EEEEEE;margin-bottom: 20px;font-size: 18px;font-weight: bold;color: #111111;">
          Billing Details
        </div>
        <table style="width: 100%;border-collapse: collapse;">
          <tr>
            <td width="73%" style="padding: 0 10px 5px;">
              <div style="font-size: 13px;font-weight: bold;color: #111111;">
                Username
              </div>
            </td>
            <td width="27%" style="padding: 0 10px 5px;">
              <div style="font-size: 14px;font-weight: bold;color: #111111;text-align: right;">
                Credits
              </div>
            </td>
          </tr>

          <#list userConsumeList as consume>
            <tr>
              <td style="padding: 4px 10px;background-color: <#if consume?index % 2 == 0>#FAFAFA<#else>#ffffff</#if>;border-right: 1px solid #ffffff;">
                <div style="font-size: 14px;line-height: 22px;color: #111111;">
                  ${consume.loginName!}
                </div>
              </td>
              <td style="padding: 4px 10px;background-color: <#if consume?index % 2 == 0>#FAFAFA<#else>#ffffff</#if>;">
                <div style="font-size: 14px;line-height: 22px;color: #111111;text-align: right;">
                  ${consume.creditsStr!}
                </div>
              </td>
            </tr>
          </#list>
        </table>
      </div>
    </#if>

    <div style="text-align: center;margin: 50px 0 100px;font-size: 20px;font-weight: bold;color: #111111;">
      Thank you for your business
    </div>
  </div>
</body>

</html>