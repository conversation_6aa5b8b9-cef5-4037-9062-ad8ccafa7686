<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.qcc.frame</groupId>
  <artifactId>frame</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>frame</name>
  <modules>
  	<module>frame-jee-commons</module>
  	<module>proj-common</module>
  	<module>webjob</module>
	<module>webapi</module>
    <module>openapi</module>
  </modules>

  <properties>
  		<framework.version>0.0.1-SNAPSHOT</framework.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<spring.version>4.3.30.RELEASE</spring.version>
		<slf4j.version>1.7.16</slf4j.version>
		<mybatis.version>3.4.6</mybatis.version>
		<mybatis.spring.version>1.3.3</mybatis.spring.version>
		<shiro.version>1.2.6</shiro.version>
		<jackson.version>2.11.4</jackson.version>

	</properties>

	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
				<qfk.domain>http://localhost:8080</qfk.domain>
				<app.qcc.log.dir>D:/beichacha_data/log</app.qcc.log.dir>
				<app.qcc.upload.dir>D:/beichacha_data/repository</app.qcc.upload.dir>

				<database.jdbc.type>mysql</database.jdbc.type>
				<database.jdbc.driver>com.mysql.cj.jdbc.Driver</database.jdbc.driver>
				<database.jdbc.url><![CDATA[****************************************************************************************************************=true]]></database.jdbc.url>
				<database.jdbc.name>bcc_global_user</database.jdbc.name>
				<database.jdbc.password>3MuI32sRku13EhkFBXGTcUN6</database.jdbc.password>
				<database.jdbc.pool.init>1</database.jdbc.pool.init>
				<database.jdbc.pool.min.idle>1</database.jdbc.pool.min.idle>
				<database.jdbc.pool.max.active>50</database.jdbc.pool.max.active>
				<database.jdbc.test.sql><![CDATA[SELECT 1]]></database.jdbc.test.sql>
				<!--短链数据库-->
				<shorturl.database.jdbc.type>mysql</shorturl.database.jdbc.type>
				<shorturl.database.jdbc.driver>com.mysql.cj.jdbc.Driver</shorturl.database.jdbc.driver>
				<shorturl.database.jdbc.url><![CDATA[****************************************************************************************************************=true]]></shorturl.database.jdbc.url>
				<shorturl.database.jdbc.name>bcc_global_user</shorturl.database.jdbc.name>
				<shorturl.database.jdbc.password>3MuI32sRku13EhkFBXGTcUN6</shorturl.database.jdbc.password>
				<shorturl.database.jdbc.pool.init>1</shorturl.database.jdbc.pool.init>
				<shorturl.database.jdbc.pool.min.idle>1</shorturl.database.jdbc.pool.min.idle>
				<shorturl.database.jdbc.pool.max.active>50</shorturl.database.jdbc.pool.max.active>
				<shorturl.database.jdbc.test.sql><![CDATA[SELECT 1]]></shorturl.database.jdbc.test.sql>
				<!--海外MySQL数据库-->
				<oversea.database.jdbc.type>mysql</oversea.database.jdbc.type>
				<oversea.database.jdbc.driver>com.mysql.cj.jdbc.Driver</oversea.database.jdbc.driver>
				<oversea.database.jdbc.url><![CDATA[**********************************************************************************************************************]]></oversea.database.jdbc.url>
				<oversea.database.jdbc.name>user_global_oversear_r</oversea.database.jdbc.name>
				<oversea.database.jdbc.password>S6uz1ZbbCxIfEb3k2KBaOm_Mh</oversea.database.jdbc.password>
				<oversea.database.jdbc.pool.init>1</oversea.database.jdbc.pool.init>
				<oversea.database.jdbc.pool.min.idle>1</oversea.database.jdbc.pool.min.idle>
				<oversea.database.jdbc.pool.max.active>50</oversea.database.jdbc.pool.max.active>
				<oversea.database.jdbc.test.sql><![CDATA[SELECT 1]]></oversea.database.jdbc.test.sql>

				<!--海外mongodb数据库 访问境外企业信息-->
				<mongodb.oversea.server.address>*************:8635,*************:8635</mongodb.oversea.server.address>
				<mongodb.oversea.server.username>user_global_r</mongodb.oversea.server.username>
				<mongodb.oversea.server.password>VAuFhZT_ISvb39Wkry5jfsriT</mongodb.oversea.server.password>
				
				<redis.host>**************</redis.host>
				<redis.port>6666</redis.port>
				<redis.database>13</redis.database>
				<redis.password>7jt2U_UCREWVTt5pSDgJVrgaR</redis.password>
				<redis.pool.maxActive>10</redis.pool.maxActive>
				<redis.pool.maxIdle>2</redis.pool.maxIdle>
				<redis.pool.minIdle>1</redis.pool.minIdle>
				<redis.pool.maxWaitMillis>10000</redis.pool.maxWaitMillis>
				<redis.pool.testOnBorrow>true</redis.pool.testOnBorrow>
				<project.env>dev</project.env>
				<qcc.pro.interface.domain>http://qfk.greatld.com/api</qcc.pro.interface.domain>
<!--				<qcc.pro.interface.domain>http://localhost:8082/api</qcc.pro.interface.domain>-->
				<picture.upload.max.allowed.size>500</picture.upload.max.allowed.size>
				<!-- 专业版认证信息 -->
				<pro.qcc.com.key>f2d6006d891611eea225fa163e618c8d</pro.qcc.com.key>
				<pro.qcc.com.secretKey>S2RBYMZPCA4GPVYXG9N4HQYA95OQCA4F</pro.qcc.com.secretKey>

				<!-- 企查查云聚接口域名 -->
				<qcc.yunju.interface.domain>http://*************:830</qcc.yunju.interface.domain>
				<!-- 企查查云聚报告接口域名 -->
				<qcc.yunju.report.interface.domain>http://**************:9031</qcc.yunju.report.interface.domain>
				<!-- 企查查云聚报告接口域名(国内) -->
				<qcc.yunju.report.internal.interface.domain>http://**************:9001</qcc.yunju.report.internal.interface.domain>

				<!-- 国际版gateway服务，数据转发，国内阿里云 begin -->
				<qcc.kyc.gateway.interface.domain>https://qcckyc-gw.greatld.com</qcc.kyc.gateway.interface.domain>
				<!-- 国际版gateway服务，数据转发，国内阿里云 end -->

				<!-- 开放平台认证信息 -->
				<openApi.qcc.com.key>d244d2c001de42d194ca740d04a2eb23</openApi.qcc.com.key>
				<openApi.qcc.com.secretKey>50157D3FBCA383B9B2F5F13C6ACD6C0E</openApi.qcc.com.secretKey>

				<qcc.yunju.global.interface.domain>http://**************:930</qcc.yunju.global.interface.domain>
				<!-- 开放平台海外认证信息 -->
				<openApi.global.qcc.com.key>bd27a7b7d22511eea90e54bf649458aa</openApi.global.qcc.com.key>
				<openApi.global.qcc.com.secretKey>104C53961ABD2B0A879354B56305F3F2</openApi.global.qcc.com.secretKey>

				<!-- 开放平台海外管理平台配置 -->
				<openApi.global.admin.qcc.com.domain>http://***************:10308</openApi.global.admin.qcc.com.domain>
				<openApi.global.admin.qcc.com.key>9fb95240bc6f443dbb7efa0c74cfc3f2</openApi.global.admin.qcc.com.key>
				<openApi.global.admin.qcc.com.secretKey>9DE1DCAC0BC44A41A8E7AA559B376B28</openApi.global.admin.qcc.com.secretKey>

				<!-- 开放平台分发中心 -->
				<qcc.intranet.interface.domain>http://***************:10411</qcc.intranet.interface.domain>
				<mail.from><EMAIL></mail.from>
				<mail.smtp.host>smtpdm.aliyun.com</mail.smtp.host>
				<mail.smtp.port>80</mail.smtp.port>
				<mail.smtp.auth>true</mail.smtp.auth>
				<mail.smtp.username><EMAIL></mail.smtp.username>
				<mail.smtp.password>Y12OF0KplYy0iMFoUhjiPMMGr6T0gb9O</mail.smtp.password><!--注:该密码加密规则和专业版不一样, 不能直接复用专业版的值-->

				<!--国际版内部Key, 用于其他服务访问国际版接口-->
				<global.api.internal.key>2bf0dc2458dc4610b3d0f75926eb296d</global.api.internal.key>
				<global.api.internal.secret.key>0IAKOER9TZ7RW021O1X7XRR8N8XOELIG</global.api.internal.secret.key>

				<oss.internal.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.internal.domail.url>
				<oss.external.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.external.domail.url>
				<oss.accesskey>LTAI5tHDzZt2mcEdULQ6AvBo</oss.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<oss.accesskeysecret>******************************</oss.accesskeysecret>
				<oss.bucket>kyc-report-file</oss.bucket>

				<obs.domail.url>https://obs.ap-southeast-3.myhuaweicloud.com</obs.domail.url>
				<obs.accesskey>BAZGA1BJ0OBR8IZKPJWG</obs.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<obs.secretaccesskey>b9tGFLd8qJehQ5OzupF1SHB5lIUoIxxI9DzQOOaD</obs.secretaccesskey>
				<obs.bucket>qcc-kyc</obs.bucket>

				<buildDateTime>${current.time}</buildDateTime>
				<exception.notify.dingtalk.accesstoken>5f78cfeb8ba4d0e307779c05e7d69c4e48c833ea2edf042c86e12b1fada25a07</exception.notify.dingtalk.accesstoken>
				<!--国际版web-convert模块接口域名-->
				<global.web.convert.interface.domain>http://**************:9180/api</global.web.convert.interface.domain>
				<!--国际版定时任务访问ftp配置-->
				<global.webjob.ftp.ip>************</global.webjob.ftp.ip>
				<global.webjob.ftp.port>21</global.webjob.ftp.port>
				<global.webjob.ftp.username>zhaohangyyftp</global.webjob.ftp.username>
				<global.webjob.ftp.password>Pass@dfd03ab8!</global.webjob.ftp.password>
				<!--客找找域名-->
				<kzz.api.domain>http://z.test.greatld.com</kzz.api.domain>
				<kzz.api.access.key>AF9KOvZIaYpjP22oAvVNxA==</kzz.api.access.key>
				<kzz.api.secret.key>qSgQjoaXCpL/zvjnsKAC1/ayHBeIgqddaeFPSQL6s5Y=</kzz.api.secret.key>
				<!--谷歌人机验证域名-->
				<google.recaptcha.url>https://www.recaptcha.net/recaptcha/api/siteverify</google.recaptcha.url>
				<google.recaptcha.key>6LfuBj4qAAAAAFTQgEbnpjN3u_iE1QXIpuM0k8sT</google.recaptcha.key>
				<google.recaptcha.v3.key>6LcvGEoqAAAAAEuzyB1Ik73yU4yV5moObdv9iVjU</google.recaptcha.v3.key>
				<!--stripe secret key-->
				<stripe.api.key>sk_test_51PfI5HI4LopkdiMXguWnwHDWYbL6mERyj1RMRtRCYd6IikRJCJbOQvgHqkIOpwYqbCj20aiUFu2NiaHSVpuaM44a00ymHz1TEE</stripe.api.key>
				<stripe.endpoint.secret>whsec_df89cfc735790ea942f951c730ba93fe53a151173f16c3decb61573e369c8a3a</stripe.endpoint.secret>
				<!--webjob domain-->
				<webjob.domain>http://localhost:8898//webjob</webjob.domain>
				<data.map.node.url>http://nodejs-qcc-kyc-graph-api.sit.office.qichacha.com</data.map.node.url>
				<hs.base.url>http://*************:6262</hs.base.url>

				<!--kafka oversea spider begin -->
				<kafka.oversea.spider.bootstrap.servers>**************:9094,***************:9094,************:9094</kafka.oversea.spider.bootstrap.servers>
				<kafka.oversea.spider.refresh.company.topic>OpenApi-Abroad-Spider-Order-Company-Refresh</kafka.oversea.spider.refresh.company.topic>
				<kafka.oversea.spider.hk.report.refresh.topic>OpenApi-Abroad-Spider-HK-Report-Refresh-SIT</kafka.oversea.spider.hk.report.refresh.topic>
				<kafka.oversea.spider.hk.report.buy.topic>OpenApi-Abroad-Spider-HK-SIT</kafka.oversea.spider.hk.report.buy.topic>
				<kafka.oversea.spider.hk.ird.report.buy.topic>OpenApi-Abroad-Spider-HK-Ird-SIT</kafka.oversea.spider.hk.ird.report.buy.topic>
				<kafka.oversea.spider.hk.ird.search.topic>OpenApi-Abroad-Spider-HK-Ird-Search-SIT</kafka.oversea.spider.hk.ird.search.topic>
				<kafka.oversea.spider.hk.ird.goods.topic>OpenApi-Abroad-Spider-HK-Ird-Goods-SIT</kafka.oversea.spider.hk.ird.goods.topic>
				<kafka.oversea.spider.my.report.buy.topic>OpenApi-Abroad-Spider-MAS-SIT</kafka.oversea.spider.my.report.buy.topic>
				<kafka.oversea.spider.au.report.buy.topic>OpenApi-Abroad-Spider-AU-Asic-SIT</kafka.oversea.spider.au.report.buy.topic>
				<kafka.oversea.spider.nz.report.buy.topic>OpenApi-Abroad-Spider-NZ-SIT</kafka.oversea.spider.nz.report.buy.topic>
				<kafka.oversea.spider.tw.report.buy.topic>OpenApi-Abroad-Spider-TW-SIT</kafka.oversea.spider.tw.report.buy.topic>
				<kafka.oversea.spider.sg.report.buy.topic>dap_spider_sp_handshakes_buy_data</kafka.oversea.spider.sg.report.buy.topic>
                <kafka.oversea.spider.sg.fin.report.buy.topic>dap_spider_overseas_handshakes_finance_buy_data</kafka.oversea.spider.sg.fin.report.buy.topic>
				<!--kafka oversea spider end-->

				<google.api.key>AIzaSyAmF4Zz7FMumyV5GH3ZymikNaSEHk6BZLA</google.api.key>
			</properties>
		</profile>
		<profile>
			<id>sit</id>
			<properties>
				<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
				<qfk.domain>http://**************</qfk.domain>
				<app.qcc.log.dir>/beichacha/data/log</app.qcc.log.dir>
				<app.qcc.upload.dir>/beichacha/data/repository</app.qcc.upload.dir>

				<database.jdbc.type>mysql</database.jdbc.type>
				<database.jdbc.driver>com.mysql.cj.jdbc.Driver</database.jdbc.driver>
				<database.jdbc.url><![CDATA[****************************************************************************************************************=true]]></database.jdbc.url>
				<database.jdbc.name>bcc_global_user</database.jdbc.name>
				<database.jdbc.password>3MuI32sRku13EhkFBXGTcUN6</database.jdbc.password>
				<database.jdbc.pool.init>1</database.jdbc.pool.init>
				<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>
				<database.jdbc.pool.max.active>50</database.jdbc.pool.max.active>
				<database.jdbc.test.sql><![CDATA[SELECT 1]]></database.jdbc.test.sql>
				<!--短链数据库-->
				<shorturl.database.jdbc.type>mysql</shorturl.database.jdbc.type>
				<shorturl.database.jdbc.driver>com.mysql.cj.jdbc.Driver</shorturl.database.jdbc.driver>
				<shorturl.database.jdbc.url><![CDATA[****************************************************************************************************************=true]]></shorturl.database.jdbc.url>
				<shorturl.database.jdbc.name>bcc_global_user</shorturl.database.jdbc.name>
				<shorturl.database.jdbc.password>3MuI32sRku13EhkFBXGTcUN6</shorturl.database.jdbc.password>
				<shorturl.database.jdbc.pool.init>1</shorturl.database.jdbc.pool.init>
				<shorturl.database.jdbc.pool.min.idle>1</shorturl.database.jdbc.pool.min.idle>
				<shorturl.database.jdbc.pool.max.active>50</shorturl.database.jdbc.pool.max.active>
				<shorturl.database.jdbc.test.sql><![CDATA[SELECT 1]]></shorturl.database.jdbc.test.sql>
				<!--海外MySQL数据库-->
				<oversea.database.jdbc.type>mysql</oversea.database.jdbc.type>
				<oversea.database.jdbc.driver>com.mysql.cj.jdbc.Driver</oversea.database.jdbc.driver>
				<oversea.database.jdbc.url><![CDATA[**********************************************************************************************************************]]></oversea.database.jdbc.url>
				<oversea.database.jdbc.name>user_global_oversear_r</oversea.database.jdbc.name>
				<oversea.database.jdbc.password>S6uz1ZbbCxIfEb3k2KBaOm_Mh</oversea.database.jdbc.password>
				<oversea.database.jdbc.pool.init>1</oversea.database.jdbc.pool.init>
				<oversea.database.jdbc.pool.min.idle>1</oversea.database.jdbc.pool.min.idle>
				<oversea.database.jdbc.pool.max.active>50</oversea.database.jdbc.pool.max.active>
				<oversea.database.jdbc.test.sql><![CDATA[SELECT 1]]></oversea.database.jdbc.test.sql>

				<!--海外mongodb数据库 访问境外企业信息-->
				<mongodb.oversea.server.address>*************:8635,*************:8635</mongodb.oversea.server.address>
				<mongodb.oversea.server.username>user_global_r</mongodb.oversea.server.username>
				<mongodb.oversea.server.password>VAuFhZT_ISvb39Wkry5jfsriT</mongodb.oversea.server.password>
				
				<redis.host>**************</redis.host>
				<redis.port>6666</redis.port>
				<redis.database>13</redis.database>
				<redis.password>7jt2U_UCREWVTt5pSDgJVrgaR</redis.password>
				<redis.pool.maxActive>50</redis.pool.maxActive>
				<redis.pool.maxIdle>2</redis.pool.maxIdle>
				<redis.pool.minIdle>1</redis.pool.minIdle>
				<redis.pool.maxWaitMillis>10000</redis.pool.maxWaitMillis>
				<redis.pool.testOnBorrow>true</redis.pool.testOnBorrow>
				<project.env>sit</project.env>
				<qcc.pro.interface.domain>http://qfk.greatld.com/api</qcc.pro.interface.domain>
				<picture.upload.max.allowed.size>500</picture.upload.max.allowed.size>
				<!-- 专业版认证信息 -->
				<pro.qcc.com.key>f2d6006d891611eea225fa163e618c8d</pro.qcc.com.key>
				<pro.qcc.com.secretKey>S2RBYMZPCA4GPVYXG9N4HQYA95OQCA4F</pro.qcc.com.secretKey>

				<!-- 企查查云聚接口域名 -->
				<qcc.yunju.interface.domain>http://*************:830</qcc.yunju.interface.domain>
				<!-- 企查查云聚报告接口域名 -->
				<qcc.yunju.report.interface.domain>http://**************:9031</qcc.yunju.report.interface.domain>
				<!-- 企查查云聚报告接口域名(国内) -->
				<qcc.yunju.report.internal.interface.domain>http://**************:9001</qcc.yunju.report.internal.interface.domain>

				<!-- 国际版gateway服务，数据转发，国内阿里云 begin -->
				<qcc.kyc.gateway.interface.domain>https://qcckyc-gw.greatld.com</qcc.kyc.gateway.interface.domain>
				<!-- 国际版gateway服务，数据转发，国内阿里云 end -->

				<!-- 开放平台认证信息 -->
				<openApi.qcc.com.key>d244d2c001de42d194ca740d04a2eb23</openApi.qcc.com.key>
				<openApi.qcc.com.secretKey>50157D3FBCA383B9B2F5F13C6ACD6C0E</openApi.qcc.com.secretKey>

				<qcc.yunju.global.interface.domain>http://**************:930</qcc.yunju.global.interface.domain>
				<!-- 开放平台海外认证信息 -->
				<openApi.global.qcc.com.key>bd27a7b7d22511eea90e54bf649458aa</openApi.global.qcc.com.key>
				<openApi.global.qcc.com.secretKey>104C53961ABD2B0A879354B56305F3F2</openApi.global.qcc.com.secretKey>

				<!-- 开放平台海外管理平台配置 -->
				<openApi.global.admin.qcc.com.domain>http://***************:10308</openApi.global.admin.qcc.com.domain>
				<openApi.global.admin.qcc.com.key>9fb95240bc6f443dbb7efa0c74cfc3f2</openApi.global.admin.qcc.com.key>
				<openApi.global.admin.qcc.com.secretKey>9DE1DCAC0BC44A41A8E7AA559B376B28</openApi.global.admin.qcc.com.secretKey>

				<!-- 开放平台分发中心 -->
				<qcc.intranet.interface.domain>http://***************:10411</qcc.intranet.interface.domain>
				<mail.from><EMAIL></mail.from>
				<mail.smtp.host>smtpdm.aliyun.com</mail.smtp.host>
				<mail.smtp.port>80</mail.smtp.port>
				<mail.smtp.auth>true</mail.smtp.auth>
				<mail.smtp.username><EMAIL></mail.smtp.username>
				<mail.smtp.password>Y12OF0KplYy0iMFoUhjiPMMGr6T0gb9O</mail.smtp.password><!--注:该密码加密规则和专业版不一样, 不能直接复用专业版的值-->

				<!--国际版内部Key, 用于其他服务访问国际版接口-->
				<global.api.internal.key>2bf0dc2458dc4610b3d0f75926eb296d</global.api.internal.key>
				<global.api.internal.secret.key>0IAKOER9TZ7RW021O1X7XRR8N8XOELIG</global.api.internal.secret.key>

				<oss.internal.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.internal.domail.url>
				<oss.external.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.external.domail.url>
				<oss.accesskey>LTAI5tHDzZt2mcEdULQ6AvBo</oss.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<oss.accesskeysecret>******************************</oss.accesskeysecret>
				<oss.bucket>kyc-report-file</oss.bucket>

				<obs.domail.url>https://obs.ap-southeast-3.myhuaweicloud.com</obs.domail.url>
				<obs.accesskey>BAZGA1BJ0OBR8IZKPJWG</obs.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<obs.secretaccesskey>b9tGFLd8qJehQ5OzupF1SHB5lIUoIxxI9DzQOOaD</obs.secretaccesskey>
				<obs.bucket>qcc-kyc</obs.bucket>

				<buildDateTime>${current.time}</buildDateTime>
				<exception.notify.dingtalk.accesstoken>5f78cfeb8ba4d0e307779c05e7d69c4e48c833ea2edf042c86e12b1fada25a07</exception.notify.dingtalk.accesstoken>
				<!--国际版web-convert模块接口域名-->
				<global.web.convert.interface.domain>http://**************:9180/api</global.web.convert.interface.domain>
				<!--国际版定时任务访问ftp配置-->
				<global.webjob.ftp.ip>************</global.webjob.ftp.ip>
				<global.webjob.ftp.port>21</global.webjob.ftp.port>
				<global.webjob.ftp.username>zhaohangyyftp</global.webjob.ftp.username>
				<global.webjob.ftp.password>Pass@dfd03ab8!</global.webjob.ftp.password>
				<!--客找找域名-->
				<kzz.api.domain>http://z.test.greatld.com</kzz.api.domain>
				<kzz.api.access.key>AF9KOvZIaYpjP22oAvVNxA==</kzz.api.access.key>
				<kzz.api.secret.key>qSgQjoaXCpL/zvjnsKAC1/ayHBeIgqddaeFPSQL6s5Y=</kzz.api.secret.key>
				<!--谷歌人机验证域名-->
				<google.recaptcha.url>https://www.recaptcha.net/recaptcha/api/siteverify</google.recaptcha.url>
				<google.recaptcha.key>6LfuBj4qAAAAAFTQgEbnpjN3u_iE1QXIpuM0k8sT</google.recaptcha.key>
				<google.recaptcha.v3.key>6LcvGEoqAAAAAEuzyB1Ik73yU4yV5moObdv9iVjU</google.recaptcha.v3.key>
				<!--stripe secret key-->
				<stripe.api.key>sk_test_51PfI5HI4LopkdiMXguWnwHDWYbL6mERyj1RMRtRCYd6IikRJCJbOQvgHqkIOpwYqbCj20aiUFu2NiaHSVpuaM44a00ymHz1TEE</stripe.api.key>
				<stripe.endpoint.secret>whsec_WdKlFinOixfEmUDw1npeLY6e3cC4L6rz</stripe.endpoint.secret>
				<!--webjob domain-->
				<webjob.domain>http://**************:34347/webjob</webjob.domain>
				<data.map.node.url>http://nodejs-qcc-kyc-graph-api.sit.office.qichacha.com</data.map.node.url>
				<hs.base.url>http://*************:6262</hs.base.url>

				<!--kafka oversea spider begin -->
				<kafka.oversea.spider.bootstrap.servers>**************:9094,***************:9094,************:9094</kafka.oversea.spider.bootstrap.servers>
				<kafka.oversea.spider.refresh.company.topic>OpenApi-Abroad-Spider-Order-Company-Refresh</kafka.oversea.spider.refresh.company.topic>
				<kafka.oversea.spider.hk.report.refresh.topic>OpenApi-Abroad-Spider-HK-Report-Refresh-SIT</kafka.oversea.spider.hk.report.refresh.topic>
				<kafka.oversea.spider.hk.report.buy.topic>OpenApi-Abroad-Spider-HK-SIT</kafka.oversea.spider.hk.report.buy.topic>
				<kafka.oversea.spider.hk.ird.report.buy.topic>OpenApi-Abroad-Spider-HK-Ird-SIT</kafka.oversea.spider.hk.ird.report.buy.topic>
				<kafka.oversea.spider.hk.ird.search.topic>OpenApi-Abroad-Spider-HK-Ird-Search-SIT</kafka.oversea.spider.hk.ird.search.topic>
				<kafka.oversea.spider.hk.ird.goods.topic>OpenApi-Abroad-Spider-HK-Ird-Goods-SIT</kafka.oversea.spider.hk.ird.goods.topic>
				<kafka.oversea.spider.my.report.buy.topic>OpenApi-Abroad-Spider-MAS-SIT</kafka.oversea.spider.my.report.buy.topic>
				<kafka.oversea.spider.au.report.buy.topic>OpenApi-Abroad-Spider-AU-Asic-SIT</kafka.oversea.spider.au.report.buy.topic>
				<kafka.oversea.spider.nz.report.buy.topic>OpenApi-Abroad-Spider-NZ-SIT</kafka.oversea.spider.nz.report.buy.topic>
				<kafka.oversea.spider.tw.report.buy.topic>OpenApi-Abroad-Spider-TW-SIT</kafka.oversea.spider.tw.report.buy.topic>
				<kafka.oversea.spider.sg.report.buy.topic>dap_spider_sp_handshakes_buy_data</kafka.oversea.spider.sg.report.buy.topic>
                <kafka.oversea.spider.sg.fin.report.buy.topic>dap_spider_overseas_handshakes_finance_buy_data</kafka.oversea.spider.sg.fin.report.buy.topic>
				<!--kafka oversea spider end-->

				<google.api.key>AIzaSyAmF4Zz7FMumyV5GH3ZymikNaSEHk6BZLA</google.api.key>
			</properties>
		</profile>
<!--		<profile>-->
<!--			<id>prd</id>-->
<!--			<properties>-->
<!--				<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>-->
<!--				<qfk.domain>https://todo</qfk.domain>-->
<!--				<app.qcc.log.dir>/beichacha/data/log</app.qcc.log.dir>-->
<!--				<app.qcc.upload.dir>/beichacha/data/repository</app.qcc.upload.dir>-->

<!--				<database.jdbc.type>mysql</database.jdbc.type>-->
<!--				<database.jdbc.driver>com.mysql.cj.jdbc.Driver</database.jdbc.driver>-->
<!--				<database.jdbc.url><![CDATA[************************************************************************************************************************************************************]]></database.jdbc.url>-->
<!--				<database.jdbc.name>bcc_global_user</database.jdbc.name>-->
<!--				<database.jdbc.password>VH7L0A_HMStOhh2yshfOZeVXF</database.jdbc.password>-->
<!--				<database.jdbc.pool.init>1</database.jdbc.pool.init>-->
<!--				<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>-->
<!--				<database.jdbc.pool.max.active>200</database.jdbc.pool.max.active>-->
<!--				<database.jdbc.test.sql><![CDATA[SELECT 1]]></database.jdbc.test.sql>-->


<!--				<redis.host>r-t4ncy7sxz6snoht07u.redis.singapore.rds.aliyuncs.com</redis.host>-->
<!--				<redis.port>6379</redis.port>-->
<!--				<redis.database>8</redis.database>-->
<!--				<redis.password>t4svL1B399uAOY2fyMXNT_s90</redis.password>-->
<!--				<redis.pool.maxActive>150</redis.pool.maxActive>-->
<!--				<redis.pool.maxIdle>5</redis.pool.maxIdle>-->
<!--				<redis.pool.minIdle>5</redis.pool.minIdle>-->
<!--				<redis.pool.maxWaitMillis>10000</redis.pool.maxWaitMillis>-->
<!--				<redis.pool.testOnBorrow>true</redis.pool.testOnBorrow>-->
<!--				<project.env>prd</project.env>-->
<!--				<qcc.pro.interface.domain>http://*************/api</qcc.pro.interface.domain>-->
<!--				<picture.upload.max.allowed.size>500</picture.upload.max.allowed.size>-->
<!--				&lt;!&ndash; 专业版认证信息 &ndash;&gt;-->
<!--				<pro.qcc.com.key>6f02d3938cee11eea3f10c42a106ce72</pro.qcc.com.key>-->
<!--				<pro.qcc.com.secretKey>WT4WVBPC928QMPEKCSMG3VS2Q7HK3J2H</pro.qcc.com.secretKey>-->

<!--				&lt;!&ndash; 企查查云聚接口域名 &ndash;&gt;-->
<!--				<qcc.yunju.interface.domain>http://120.55.233.76</qcc.yunju.interface.domain>-->
<!--				&lt;!&ndash; 企查查云聚报告接口域名 &ndash;&gt;-->
<!--				<qcc.yunju.report.interface.domain>http://**************:9031</qcc.yunju.report.interface.domain>-->
<!--				&lt;!&ndash; 开放平台认证信息 &ndash;&gt;-->
<!--				<openApi.qcc.com.key>3b462b65a51f44598f0ddf32d10199d4</openApi.qcc.com.key>-->
<!--				<openApi.qcc.com.secretKey>34C2019AFA36C8884668B5C2761D095F</openApi.qcc.com.secretKey>-->

<!--				<qcc.yunju.global.interface.domain>https://api.qcckyc.com</qcc.yunju.global.interface.domain>&lt;!&ndash;lidong修改为海外更新钉钉我的文档里的国际版每日站会&ndash;&gt;-->
<!--				&lt;!&ndash; 开放平台海外认证信息 *******************有硬编码&ndash;&gt;-->
<!--				<openApi.global.qcc.com.key>c694e4c1d22511eea90e54bf649458aa</openApi.global.qcc.com.key>-->
<!--				<openApi.global.qcc.com.secretKey>1EDDF6527F963311883312D58A6ECC83</openApi.global.qcc.com.secretKey>-->

<!--				&lt;!&ndash; 开放平台海外管理平台配置 &ndash;&gt;-->
<!--				<openApi.global.admin.qcc.com.domain>http://***************:10301</openApi.global.admin.qcc.com.domain>-->
<!--				<openApi.global.admin.qcc.com.key>9fb95240bc6f443dbb7efa0c74cfc3f2</openApi.global.admin.qcc.com.key>-->
<!--				<openApi.global.admin.qcc.com.secretKey>07D8CEB68BCD494281C3B1B5321B4CEE</openApi.global.admin.qcc.com.secretKey>-->
<!--				<mail.from><EMAIL></mail.from>-->
<!--				<mail.smtp.host>smtpdm.aliyun.com</mail.smtp.host>-->
<!--				<mail.smtp.port>80</mail.smtp.port>-->
<!--				<mail.smtp.auth>true</mail.smtp.auth>-->
<!--				<mail.smtp.username><EMAIL></mail.smtp.username>-->
<!--				<mail.smtp.password>PeS6ozp2yoMyi0PZWlpptRk3Wlp7xe0I</mail.smtp.password>&lt;!&ndash;注:该密码加密规则和专业版不一样, 不能直接复用专业版的值&ndash;&gt;-->

<!--				&lt;!&ndash;国际版内部Key, 用于其他服务访问国际版接口&ndash;&gt;-->
<!--				<global.api.internal.key>2bf0dc2458dc4610b3d0f75926eb296d</global.api.internal.key>-->

<!--				<oss.internal.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.internal.domail.url>-->
<!--				<oss.external.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.external.domail.url>-->
<!--				<oss.accesskey>LTAI5tHDzZt2mcEdULQ6AvBo</oss.accesskey>&lt;!&ndash; 修改密钥需要将生成的固定链接一起修改 &ndash;&gt;-->
<!--				<oss.accesskeysecret>******************************</oss.accesskeysecret>-->
<!--				<oss.bucket>kyc-report-file</oss.bucket>-->

<!--				<buildDateTime>${current.time}</buildDateTime>-->
<!--			</properties>-->
<!--		</profile>-->

		<profile>
			<id>prod</id><!--华为云-->
			<properties>
				<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
				<qfk.domain>https://todo</qfk.domain>
				<app.qcc.log.dir>/beichacha/data/log</app.qcc.log.dir>
				<app.qcc.upload.dir>/beichacha/data/repository</app.qcc.upload.dir>

				<database.jdbc.type>mysql</database.jdbc.type>
				<database.jdbc.driver>com.mysql.cj.jdbc.Driver</database.jdbc.driver>
				<database.jdbc.url><![CDATA[*******************************************************************************************************************]]></database.jdbc.url>
				<database.jdbc.name>bcc_global_user</database.jdbc.name>
				<database.jdbc.password>b6OCeMi2nYClN4NGOj4_T7MAc</database.jdbc.password>
				<database.jdbc.pool.init>1</database.jdbc.pool.init>
				<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>
				<database.jdbc.pool.max.active>200</database.jdbc.pool.max.active>
				<database.jdbc.test.sql><![CDATA[SELECT 1]]></database.jdbc.test.sql>
				<!--短链数据库-->
				<shorturl.database.jdbc.type>mysql</shorturl.database.jdbc.type>
				<shorturl.database.jdbc.driver>com.mysql.cj.jdbc.Driver</shorturl.database.jdbc.driver>
				<shorturl.database.jdbc.url><![CDATA[******************************************************************************************************************************************************************************************]]></shorturl.database.jdbc.url>
				<shorturl.database.jdbc.name>user_short_url_w</shorturl.database.jdbc.name>
				<shorturl.database.jdbc.password>TXaYofTmVP5f18Ru55dq4_ZxI</shorturl.database.jdbc.password>
				<shorturl.database.jdbc.pool.init>1</shorturl.database.jdbc.pool.init>
				<shorturl.database.jdbc.pool.min.idle>3</shorturl.database.jdbc.pool.min.idle>
				<shorturl.database.jdbc.pool.max.active>200</shorturl.database.jdbc.pool.max.active>
				<shorturl.database.jdbc.test.sql><![CDATA[SELECT 1]]></shorturl.database.jdbc.test.sql>
				<!--海外MySQL数据库-->
				<oversea.database.jdbc.type>mysql</oversea.database.jdbc.type>
				<oversea.database.jdbc.driver>com.mysql.cj.jdbc.Driver</oversea.database.jdbc.driver>
				<oversea.database.jdbc.url><![CDATA[********************************************************************************************************************]]></oversea.database.jdbc.url>
				<oversea.database.jdbc.name>user_global_oversear_r</oversea.database.jdbc.name>
				<oversea.database.jdbc.password>S6uz1ZbbCxIfEb3k2KBaOm_Mh</oversea.database.jdbc.password>
				<oversea.database.jdbc.pool.init>1</oversea.database.jdbc.pool.init>
				<oversea.database.jdbc.pool.min.idle>1</oversea.database.jdbc.pool.min.idle>
				<oversea.database.jdbc.pool.max.active>50</oversea.database.jdbc.pool.max.active>
				<oversea.database.jdbc.test.sql><![CDATA[SELECT 1]]></oversea.database.jdbc.test.sql>

				<!--海外mongodb数据库 访问境外企业信息-->
				<mongodb.oversea.server.address>************:8635,***********:8635</mongodb.oversea.server.address>
				<mongodb.oversea.server.username>user_global_r</mongodb.oversea.server.username>
				<mongodb.oversea.server.password>VAuFhZT_ISvb39Wkry5jfsriT</mongodb.oversea.server.password>

				<redis.host>redis-055f40f2-1d6c-4c90-97f0-fc639cda4d5a.ap-southeast-3.dcs.myhuaweicloud.com</redis.host>
				<redis.port>6379</redis.port>
				<redis.database>8</redis.database>
				<redis.password>6leHAHtFtdjt4oCEMqyuXksq</redis.password>
				<redis.pool.maxActive>150</redis.pool.maxActive>
				<redis.pool.maxIdle>5</redis.pool.maxIdle>
				<redis.pool.minIdle>5</redis.pool.minIdle>
				<redis.pool.maxWaitMillis>10000</redis.pool.maxWaitMillis>
				<redis.pool.testOnBorrow>true</redis.pool.testOnBorrow>
				<project.env>prd</project.env>
				<qcc.pro.interface.domain>http://*************/api</qcc.pro.interface.domain>
				<picture.upload.max.allowed.size>500</picture.upload.max.allowed.size>
				<!-- 专业版认证信息 -->
				<pro.qcc.com.key>6f02d3938cee11eea3f10c42a106ce72</pro.qcc.com.key>
				<pro.qcc.com.secretKey>WT4WVBPC928QMPEKCSMG3VS2Q7HK3J2H</pro.qcc.com.secretKey>

				<!-- 企查查云聚接口域名 -->
				<qcc.yunju.interface.domain>https://home.qcckyc.com</qcc.yunju.interface.domain>
				<!-- 企查查云聚报告接口域名 -->
				<qcc.yunju.report.interface.domain>http://************:5002</qcc.yunju.report.interface.domain>
				<!-- 企查查云聚报告接口域名(国内) -->
				<qcc.yunju.report.internal.interface.domain>http://118.31.159.40:8900</qcc.yunju.report.internal.interface.domain>

				<!-- 国际版gateway服务，数据转发，国内阿里云 begin -->
				<qcc.kyc.gateway.interface.domain>https://bridge.qcckyc.com</qcc.kyc.gateway.interface.domain>
				<!-- 国际版gateway服务，数据转发，国内阿里云 end -->

				<!-- 开放平台认证信息 -->
				<openApi.qcc.com.key>3b462b65a51f44598f0ddf32d10199d4</openApi.qcc.com.key>
				<openApi.qcc.com.secretKey>34C2019AFA36C8884668B5C2761D095F</openApi.qcc.com.secretKey>

				<qcc.yunju.global.interface.domain>http://10.10.8.202</qcc.yunju.global.interface.domain><!--lidong修改为海外更新钉钉我的文档里的国际版每日站会-->
				<!-- 开放平台海外认证信息 *******************有硬编码-->
				<openApi.global.qcc.com.key>c694e4c1d22511eea90e54bf649458aa</openApi.global.qcc.com.key>
				<openApi.global.qcc.com.secretKey>1EDDF6527F963311883312D58A6ECC83</openApi.global.qcc.com.secretKey>

				<!-- 开放平台海外管理平台配置 -->
				<openApi.global.admin.qcc.com.domain>http://***************:10301</openApi.global.admin.qcc.com.domain>
				<openApi.global.admin.qcc.com.key>9fb95240bc6f443dbb7efa0c74cfc3f2</openApi.global.admin.qcc.com.key>
				<openApi.global.admin.qcc.com.secretKey>07D8CEB68BCD494281C3B1B5321B4CEE</openApi.global.admin.qcc.com.secretKey>

				<!-- 开放平台分发中心 -->
				<qcc.intranet.interface.domain>http://***************:10410</qcc.intranet.interface.domain>
				<mail.from><EMAIL></mail.from>
				<mail.smtp.host>smtpdm.aliyun.com</mail.smtp.host>
				<mail.smtp.port>80</mail.smtp.port>
				<mail.smtp.auth>true</mail.smtp.auth>
				<mail.smtp.username><EMAIL></mail.smtp.username>
				<mail.smtp.password>Y12OF0KplYy0iMFoUhjiPMMGr6T0gb9O</mail.smtp.password><!--注:该密码加密规则和专业版不一样, 不能直接复用专业版的值-->

				<!--国际版内部Key, 用于其他服务访问国际版接口-->
				<global.api.internal.key>2bf0dc2458dc4610b3d0f75926eb296d</global.api.internal.key>
				<global.api.internal.secret.key>D3F5EF6E8D0211EEA225FA163E618C8D</global.api.internal.secret.key>
				<!--华为云OBS配置，用户名obs-qcc-kyc-yw-->
				<oss.internal.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.internal.domail.url>
				<oss.external.domail.url>http://oss-ap-southeast-1.aliyuncs.com</oss.external.domail.url>
				<oss.accesskey>LTAI5tHDzZt2mcEdULQ6AvBo</oss.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<oss.accesskeysecret>******************************</oss.accesskeysecret>
				<oss.bucket>kyc-report-file</oss.bucket>

				<obs.domail.url>https://obs.ap-southeast-3.myhuaweicloud.com</obs.domail.url>
				<obs.accesskey>BAZGA1BJ0OBR8IZKPJWG</obs.accesskey><!-- 修改密钥需要将生成的固定链接一起修改 -->
				<obs.secretaccesskey>b9tGFLd8qJehQ5OzupF1SHB5lIUoIxxI9DzQOOaD</obs.secretaccesskey>
				<obs.bucket>qcc-kyc</obs.bucket>

				<buildDateTime>${current.time}</buildDateTime>
				<exception.notify.dingtalk.accesstoken>f830b953ab189c24bd54526bd1d73ec39db0f0ac26667de7e1cc7f45d722f41b</exception.notify.dingtalk.accesstoken>
				<!--国际版web-convert模块接口域名-->
				<global.web.convert.interface.domain>http://************:9180/api</global.web.convert.interface.domain>
				<!--国际版定时任务访问ftp配置-->
				<global.webjob.ftp.ip>**********</global.webjob.ftp.ip>
				<global.webjob.ftp.port>21</global.webjob.ftp.port>
				<global.webjob.ftp.username>ftp_ocbc_hk</global.webjob.ftp.username>
				<global.webjob.ftp.password>h95a6f98c90^7eA29b564ad7c4055</global.webjob.ftp.password>
				<!--客找找域名-->
				<kzz.api.domain>https://crm.qcc.com</kzz.api.domain>
				<kzz.api.access.key>U+bD3MYVwv/FOJP29DuXvw==</kzz.api.access.key>
				<kzz.api.secret.key>MrBma1GUGIeoi7dzapImAkEHHNYq0lXecYJTouN6tr8=</kzz.api.secret.key>
				<!--谷歌人机验证域名-->
				<google.recaptcha.url>https://www.google.com/recaptcha/api/siteverify</google.recaptcha.url>
				<google.recaptcha.key>6Lfa-D0qAAAAAOajlNYJ5jHueMcBftdKDKWXez7s</google.recaptcha.key>
				<google.recaptcha.v3.key>6LctGEoqAAAAAKvJSpwtOFoCC8kUMzW7esuegIEd</google.recaptcha.v3.key>
				<!--stripe secret key-->
				<stripe.api.key>***********************************************************************************************************</stripe.api.key>
				<stripe.endpoint.secret>whsec_eHkU7JdPjMkZ8TtkSZBwxhktzeoxAtBh</stripe.endpoint.secret>
				<!--webjob domain-->
				<webjob.domain>http://**********:8090/webjob</webjob.domain>
				<data.map.node.url>http://************</data.map.node.url>
				<hs.base.url>http://*************:6262</hs.base.url>

				<!--kafka oversea spider begin -->
				<kafka.oversea.spider.bootstrap.servers>************:9011,************:9011,************:9011</kafka.oversea.spider.bootstrap.servers>
				<kafka.oversea.spider.refresh.company.topic>OpenApi-Abroad-Spider-Order-Company-Refresh</kafka.oversea.spider.refresh.company.topic>
				<kafka.oversea.spider.hk.report.refresh.topic>OpenApi-Abroad-Spider-HK-Report-Refresh-PRD</kafka.oversea.spider.hk.report.refresh.topic>
				<kafka.oversea.spider.hk.report.buy.topic>OpenApi-Abroad-Spider-HK-PRD</kafka.oversea.spider.hk.report.buy.topic>
				<kafka.oversea.spider.hk.ird.report.buy.topic>OpenApi-Abroad-Spider-HK-Ird-PRD</kafka.oversea.spider.hk.ird.report.buy.topic>
				<kafka.oversea.spider.hk.ird.search.topic>OpenApi-Abroad-Spider-HK-Ird-Search-PRD</kafka.oversea.spider.hk.ird.search.topic>
				<kafka.oversea.spider.hk.ird.goods.topic>OpenApi-Abroad-Spider-HK-Ird-Goods-PRD</kafka.oversea.spider.hk.ird.goods.topic>
				<kafka.oversea.spider.my.report.buy.topic>OpenApi-Abroad-Spider-MAS-PRD</kafka.oversea.spider.my.report.buy.topic>
				<kafka.oversea.spider.au.report.buy.topic>OpenApi-Abroad-Spider-AU-Asic-PRD</kafka.oversea.spider.au.report.buy.topic>
				<kafka.oversea.spider.nz.report.buy.topic>OpenApi-Abroad-Spider-NZ-PRD</kafka.oversea.spider.nz.report.buy.topic>
				<kafka.oversea.spider.tw.report.buy.topic>OpenApi-Abroad-Spider-TW-PRD</kafka.oversea.spider.tw.report.buy.topic>
				<kafka.oversea.spider.sg.report.buy.topic>dap_spider_sp_handshakes_buy_data</kafka.oversea.spider.sg.report.buy.topic>
                <kafka.oversea.spider.sg.fin.report.buy.topic>dap_spider_overseas_handshakes_finance_buy_data</kafka.oversea.spider.sg.fin.report.buy.topic>
				<!--kafka oversea spider end-->

				<google.api.key>AIzaSyAmF4Zz7FMumyV5GH3ZymikNaSEHk6BZLA</google.api.key>
			</properties>
		</profile>

		<profile>
			<id>idea</id>
			<properties>

			</properties>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.33</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<!-- <scope>test</scope> -->
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring.version}</version>
			<!-- <scope>test</scope> -->
		</dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongodb-driver-sync</artifactId>
			<version>4.7.2</version>
		</dependency>
	</dependencies>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>

			<!-- Plugin for git revision -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.1</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<shortRevisionLength>20</shortRevisionLength>
				</configuration>

			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>1.9.1</version>
				<executions>
					<execution>
						<id>timestamp-property</id>
						<goals>
							<goal>timestamp-property</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<name>current.time</name>
					<pattern>yyyy-MM-dd HH:mm:ss</pattern>
					<timeZone>GMT+8</timeZone>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.1.1</version>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<git-SHA-1>${buildNumber}</git-SHA-1>

						</manifestEntries>
					</archive>
					<resources>
					  <resource>
						<directory>${basedir}/webapi/src/main/resources</directory>
						<filtering>true</filtering>
					  </resource>
					</resources>
				</configuration>
			</plugin>


		</plugins>
	</build>
	<scm>
		<!-- Replace the connection below with your project connection -->
		<connection>scm:git:git://gitlab.greatld.com:18888/qcc_global/global_backend</connection>
	</scm>

</project>
