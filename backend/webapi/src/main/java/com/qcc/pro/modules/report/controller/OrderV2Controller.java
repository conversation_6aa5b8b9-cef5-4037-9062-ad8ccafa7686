package com.qcc.pro.modules.report.controller;

import com.backend.common.model.json.JsonSimpleResult;
import com.backend.common.modules.benefit.service.CommTblBenefitPoolService;
import com.backend.common.modules.common.form.KeyNoBaseForm;
import com.backend.common.modules.report.form.MapReportOrderSubmitForm;
import com.backend.common.modules.report.model.order.MapOrderSubmitResult;
import com.backend.common.modules.search.HkIrdService;
import com.backend.common.modules.search.model.HkIrdReportCarOrderTO;
import com.backend.common.modules.setting.model.HkSearcherInfoFillStatusTO;
import com.backend.common.overseamongo.service.OvsQccOvsBasicService;
import com.backend.common.modules.report.form.BatchScanOrderSubmitForm;
import com.backend.common.modules.report.form.InfoBeforeSubmitForm;
import com.backend.common.modules.report.form.ReportOrderLoopForm;
import com.backend.common.modules.report.form.ReportOrderSubmitForm;
import com.backend.common.modules.report.form.TblCompReportOrderDuplicateForm;
import com.backend.common.modules.report.model.InfoBeforeSubmitV2TO;
import com.backend.common.modules.report.model.OrderDuplicateCheckResultTO;
import com.backend.common.modules.report.model.OrderGenerateTimeTO;
import com.backend.common.modules.report.model.OrderPollingResultTO;
import com.backend.common.modules.report.model.ReportOrderInfoOfBillTO;
import com.backend.common.modules.report.model.order.OrderSubmitResult;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.OrderBusinessService;
import com.backend.common.modules.setting.model.CompChargeUnitWithGroupTO;
import com.backend.common.oversea.form.HongKongAnnouncementsListCondition;
import com.backend.common.oversea.model.HongKongAnnouncementsListTO;
import com.backend.common.service.SysDingMsgNewTranService;
import com.backend.common.service.oversea.OverseaHongKongAnnouncementsService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.jee.commons.annotation.RedisLock;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 订单相关接口V2
 * added for lvcy v2.1.5 KNZT-6254
 * <AUTHOR>
 * @datetime 7/2/2025 5:13 下午
 */

@Api(tags = "订单相关接口V2", value = "订单相关接口V2")
@Controller
@RequestMapping(value = "${adminPath}/order/v2")
public class OrderV2Controller {
    private Logger log = LoggerFactory.getLogger(getClass());
    @Autowired
    private OrderBusinessService orderBusinessService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;
    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private OverseaHongKongAnnouncementsService overseaHongKongAnnouncementsService;
    @Autowired
    private OvsQccOvsBasicService ovsQccOvsBasicService;
    @Autowired
    private CommTblBenefitPoolService commTblBenefitPoolService;
    @Autowired
    private HkIrdService hkIrdService;

    @ApiOperation(value = "批量提交订单", httpMethod = "POST", notes = "批量提交订单")
    @RequestMapping(value="/submit", method= RequestMethod.POST)
    @ResponseBody
    @RedisLock(lockId = SysConstants.RedisLock.LOCK_ID_CONSUME_UNIT, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
    public JsonResult<OrderSubmitResult> submitOrder(@RequestBody ReportOrderSubmitForm form) {
        try {
            form.clearInnerParam();
            OrderSubmitResult orderSubmitResult = orderBusinessService.submitOrder(form);
            return JsonResult.buildSuccess(orderSubmitResult);
        } catch (Exception e) {
            return handleOrderException(e);
        }
    }

    @ApiOperation(value = "批量提交扫描订单", httpMethod = "POST", notes = "批量提交扫描订单")
    @RequestMapping(value="/scan/submit", method= RequestMethod.POST)
    @ResponseBody
    @RedisLock(lockId = SysConstants.RedisLock.LOCK_ID_CONSUME_UNIT, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
    public JsonResult<OrderSubmitResult> submitScanOrder(@RequestBody BatchScanOrderSubmitForm form) {
        try {
            OrderSubmitResult orderSubmitResult = orderBusinessService.submitScanOrder(form);
            return JsonResult.buildSuccess(orderSubmitResult);
        } catch (Exception e) {
            return handleOrderException(e);
        }
    }


    @ApiOperation(value = "批量提交订单 for 购物车", httpMethod = "POST", notes = "批量提交订单 for 购物车")
    @RequestMapping(value="/cart/submit", method= RequestMethod.POST)
    @ResponseBody
    @RedisLock(lockId = SysConstants.RedisLock.LOCK_ID_CONSUME_UNIT, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
    public JsonResult<OrderSubmitResult> submitOrder4Cart(@RequestBody ReportOrderSubmitForm form) {
        try {
            OrderSubmitResult orderSubmitResult = orderBusinessService.submitOrder4Cart(form);
            return JsonResult.buildSuccess(orderSubmitResult);
        } catch (Exception e) {
            return handleOrderException(e);
        }
    }

    @ApiOperation(value = "提交图谱订单", httpMethod = "POST", notes = "提交图谱订单")
    @RequestMapping(value="/map/submit", method= RequestMethod.POST)
    @ResponseBody
    @RedisLock(lockId = SysConstants.RedisLock.LOCK_ID_CONSUME_UNIT, lockLevel = SysConstants.RedisLock.LOCK_LEVEL_COMPANY, timeout=60)
    public JsonResult<MapOrderSubmitResult> submitOrder4Map(@RequestBody MapReportOrderSubmitForm form) {
        try {
            form.clearInnerParam();
            return orderBusinessService.submitOrder4Map(form);
        } catch (Exception e) {
            return handleOrderException(e);
        }
    }

    @ApiOperation(value = "获取公司简要信息", httpMethod = "POST", notes = "获取公司简要信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getCorpProfileInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonSimpleResult<InfoBeforeSubmitV2TO> getCorpProfileInfo(@RequestBody InfoBeforeSubmitForm form) {
        JsonSimpleResult<InfoBeforeSubmitV2TO> result = new JsonSimpleResult<>();
        try {
            InfoBeforeSubmitV2TO rtn = orderBusinessService.geBasicDetailsBeforeSubmit(form.getKeyNo());
            result.setResult(rtn);
            result.setStatus(Constants.Result.SUCCESS_STR);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
        }
        return result;
    }

    @ApiOperation(value = "根据报告Group获取公司计费单价", httpMethod = "POST", notes = "根据报告Group获取公司计费单价")
    @RequestMapping(value = "listReportPurchases", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CompChargeUnitWithGroupTO> listReportPurchases(@RequestBody KeyNoBaseForm form) {
        JsonResultList<CompChargeUnitWithGroupTO> jsonResultList = new JsonResultList<>();
        try {
            List<CompChargeUnitWithGroupTO> serviceList = orderBusinessService.listReportPurchases(UserUtils.getUserCompanyId(), form.getKeyNo(), form.getReportGroup());
            jsonResultList = JsonResultList.buildSuccess(serviceList);
        } catch (MessageException e) {
            log.error("listReportPurchases", e);
            jsonResultList.setStatus(Constants.Result.FALSE_STR);
        }
        return jsonResultList;
    }

    @ApiOperation(value = "轮询香港IRD商品状态", httpMethod = "POST", notes = "轮询香港IRD商品状态")
    @RequestMapping(value = "getIrdGoodsByLoop", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HkIrdReportCarOrderTO> getIrdGoodsByLoop(@RequestBody KeyNoBaseForm form) {
        JsonResultList<HkIrdReportCarOrderTO> jsonResultList = null;
        try {
            jsonResultList = JsonResultList.buildSuccess(hkIrdService.getIrdGoodsByLoop(form.getKeyNo()));
        } catch (MessageException e) {
            log.error("listReportPurchases", e);
            jsonResultList = JsonResultList.buildFail(e);
        }
        return jsonResultList;
    }

    @ApiOperation(value = "获取用户是否存在香港企业查册人信息", httpMethod = "GET", notes = "获取用户是否存在香港企业查册人信息")
    @RequestMapping(value = "checkHkSearcherInfoExists", method = RequestMethod.GET)
    @ResponseBody
    public JsonSimpleResult<Boolean> checkHkSearcherInfoExists() {
        JsonSimpleResult<Boolean> jsonSimpleResult = new JsonSimpleResult<>();
        jsonSimpleResult.setResult(orderBusinessService.checkHkSearcherInfoExists());
        jsonSimpleResult.setStatus(Constants.Result.SUCCESS_STR);
        return jsonSimpleResult;
    }

    @ApiOperation(value = "获取用户香港企业查册人填写状态", httpMethod = "GET")
    @GetMapping(value = "getHkSearcherInfoFillStatus")
    @ResponseBody
    public JsonResult<HkSearcherInfoFillStatusTO> getHkSearcherInfoFillStatus() {
        try {
            return JsonResult.buildSuccess(orderBusinessService.getHkSearcherInfoFillStatus());
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }
    }

    // 用户订单逻辑删除，将订单设为不可见
    @ApiOperation(value = "用户订单逻辑删除", httpMethod = "POST", notes = "用户订单逻辑删除")
    @RequestMapping(value = "/deleteUserOrder", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> deleteUserOrder(@RequestBody List<String> orderNoList) {
        orderBusinessService.updateOrderInvisible4User(orderNoList);
        return JsonResult.buildSuccess();
    }

    // 订单重复项校验
    @ApiOperation(value = "订单重复项校验", httpMethod = "POST", notes = "订单重复项校验")
    @RequestMapping(value = "/duplicateOrder/check", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<OrderDuplicateCheckResultTO> checkDuplicateOrder(@RequestBody List<OrderDuplicateCheckResultTO> orderList) {
        try {
            List<OrderDuplicateCheckResultTO> resultList = orderBusinessService.checkDuplicateOrder(orderList);
            return JsonResultList.buildSuccess(resultList, resultList.size());
        } catch (MessageException e) {
            return JsonResultList.buildFail(I18NUtil.getMessage(e));
        }
    }

    // added for v1.8.1 KNZT-3402
    @ApiOperation(value = "获取历史重复订单", httpMethod = "POST", notes = "获取历史重复订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportGroup", value = "报告分组", paramType = "body"),
            @ApiImplicitParam(name = "corpKeyNo", value = "企业keyNo", paramType = "body"),
            @ApiImplicitParam(name = "persKeyNo", value = "人员keyNo", paramType = "body")})
    @RequestMapping(value = "/listHistoryDuplicateOrder", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ReportOrderInfoOfBillTO> listHistoryDuplicateOrder(@RequestBody TblCompReportOrderDuplicateForm form) {
        JsonResultList<ReportOrderInfoOfBillTO> result = new JsonResultList<>();
        try {
            List<ReportOrderInfoOfBillTO> queryResult = orderBusinessService.listDuplicateOrderWithDateRange(form);
            result.setStatus(Constants.Result.SUCCESS_STR);
            if (queryResult != null) {
                CommTblCompReportOrderService.removeCreditIfNecessary(queryResult);
                result.setResultList(queryResult);
                result.setTotalCount(queryResult.size());
            }
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
        }
        return result;
	}

    // 生成下单页面倒计时
    @ApiOperation(value = "生成下单页面倒计时", httpMethod = "POST", notes = "生成下单页面倒计时")
    @RequestMapping(value = "/order-countdown/generate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<OrderGenerateTimeTO> generateOrderCountdown() {
        try {
            OrderGenerateTimeTO result = orderBusinessService.generateOrderCountdown();
            return JsonResult.buildSuccess(result);
        } catch (MessageException e) {
            return JsonResult.buildFail(I18NUtil.getMessage(e));
        }
    }

    // 查询下单页面倒计时
    @ApiOperation(value = "查询下单页面倒计时", httpMethod = "GET", notes = "查询下单页面倒计时")
    @RequestMapping(value = "/order-countdown/query", method = RequestMethod.GET)
    @ResponseBody
    public JsonResult<Long> queryOrderCountdown(@RequestParam("pageKey") String key) {
        try {
            return JsonResult.buildSuccess(orderBusinessService.queryOrderCountdown(key));
        } catch (MessageException e) {
            return JsonResult.buildFail(I18NUtil.getMessage(e));
        }
    }

    @ApiOperation(value = "获取报告数据(通过轮询)", httpMethod = "POST", notes = "对于需要轮询获取数据的, 通过该接口返回")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", paramType = "body")})
    @RequestMapping(value = "/report/loop", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<OrderPollingResultTO> getReportByLoop(@RequestBody ReportOrderLoopForm form) {
        try {
            String orderId = null;
            if (StringUtils.isNotBlank(form.getOrderId()) && form.getOrderId().contains(Constants.TokenSplit.DATA)) {
                orderId = commTblCompReportOrderService.getOrderIdAndCheckDataToken(form.getOrderId());
            } else {
                orderId = form.getOrderId();
            }
            OrderPollingResultTO result = commTblCompReportOrderService.getOrderPollingResult(orderId);
            return JsonResult.buildSuccess(result);
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }

    }
    @ApiOperation(value = "查询香港公告列表", httpMethod = "POST", notes = "查询香港公告列表")
    @PostMapping(value = "/listHKAnnouncement")
    @ResponseBody
    public JsonResultList<HongKongAnnouncementsListTO> listHKAnnouncement(@RequestBody HongKongAnnouncementsListCondition condition) {
        try {
            return overseaHongKongAnnouncementsService.listHKAnnouncement(condition);
        } catch (MessageException e) {
            return JsonResultList.buildFail(e);
        }
    }

    @ApiOperation(value = "查询香港公告刷新时间", httpMethod = "POST", notes = "查询香港公告刷新时间")
    @PostMapping(value = "/getHKAnnouncementUpdateDate")
    @ResponseBody
    public JsonResult<String> getHKAnnouncementUpdateDate(@RequestBody HongKongAnnouncementsListCondition condition) {
        try {
            return JsonResult.buildSuccess(ovsQccOvsBasicService.getDocumentUpdateDate(condition.getKeyNo()));
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }
    }

    @ApiOperation(value = "刷新香港公告", httpMethod = "POST", notes = "刷新香港公告")
    @PostMapping(value = "/refreshHKAnnouncement")
    @ResponseBody
    public JsonResult<Void> refreshHKAnnouncement(@RequestBody HongKongAnnouncementsListCondition condition) {
        try {
            orderBusinessService.refreshHKAnnouncement(condition.getKeyNo());
            return JsonResult.buildSuccess();
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }
    }

    @ApiOperation(value = "获取公司监控服务状态", httpMethod = "POST", notes = "获取公司监控服务状态")
    @PostMapping(value = "/getMonitorServiceStatus")
    @ResponseBody
    public JsonResult<String> getMonitorServiceStatus() {
        return JsonResult.buildSuccess(commTblBenefitPoolService.getMonitorServiceStatus(UserUtils.getUserCompanyId()));
    }

    /**
     * 处理订单异常
     * @param e
     * @return
     */
    private <T> JsonResult<T> handleOrderException(Exception e) {
        if (e instanceof MessageException) {
            MessageException me = (MessageException) e;
            log.info("submit order MessageException", me);
            JsonResult<T> result = JsonResult.buildFail(me);
            CommTblCompReportOrderService.resetStatusAndMsg4Order(me, result);
            if ("err.amount.insufficient".equals(me.getMessage())) {
                sysDingMsgNewTranService.sendBusinessReminder(AlarmTypeEnum.CREDITS_INSUFFICIENT_ERROR);
            }
            return result;
        }
        log.error("submit order Exception", e);
        sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.ORDER_SUBMIT_ERROR, e);
        return JsonResult.buildFail("err.access");
    }
}
