package com.qcc.pro.modules.corp.controller;

import com.backend.common.global.gateway.corp.CorpGatewayInterface;
import com.backend.common.model.json.JsonSimpleResult;
import com.backend.common.modules.dd.model.EnLabelTOResult;
import com.backend.common.modules.industry.entity.TblGlobalIndustry;
import com.backend.common.modules.industry.service.CommTblGlobalIndustryService;
import com.backend.common.modules.report.entity.TblCompReportOrder;
import com.backend.common.modules.report.model.CorpMerchantShopTO;
import com.backend.common.modules.industry.model.TblGlobalIndustryBaseTO;
import com.backend.common.modules.report.model.CorpSimpleInfoTO;
import com.backend.common.modules.report.model.FinancialData;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.modules.report.service.MerchantShopBusinessService;
import com.backend.common.modules.shell.model.ScCompanyListForm;
import com.backend.common.modules.shell.service.ShellLabelService;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.form.CourtCaseForm;
import com.backend.common.yunjuapi.form.ShellCompanySearchForm;
import com.backend.common.yunjuapi.model.*;
import com.backend.common.yunjuapi.model.merchant.*;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.commons.SysConstants;
import com.qcc.frame.commons.ienum.ReportTypeEnum;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.CollectionUtils;
import com.qcc.frame.jee.commons.utils.DateUtils;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.utils.MsgExceptionUtils;
import com.qcc.frame.jee.commons.utils.StringUtils;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.modules.sys.service.TranslaterService;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.qcc.pro.modules.search.form.ProCorpSearchForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.backend.common.yunjuapi.ECILocalInterface;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDate;
import java.util.*;

/**
 * added for v1.1.6 KNZT-821
 */
@Api(tags = "企业详情维度")
@Controller
@RequestMapping(value = "${adminPath}/corp/common/businessInfo/")
public class CommonCorpBusinessInfoController  extends BaseController {

    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;
    @Autowired
    private TranslaterService translaterService;
    @Autowired
    private MerchantShopBusinessService merchantShopBusinessService;
    @Autowired
    private CommTblGlobalIndustryService commTblGlobalIndustryService;

    @ApiOperation(value = "获取企业主要人员", httpMethod = "POST", notes = "获取企业主要人员 工商主要人员和自主公示的主要人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "type", value = "类型 Employees-工商主要人员 * IpoEmployees-自主公示主要人员", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getCorpEmployeeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpEmployeeTO> getCorpEmployeeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpEmployeeTO> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CorpEmployeeResult employeeResult = ECILocalInterface.getEmployeeList(keyNo, form.getType(), form.getPageIndex(), form.getPageSize());
        if (employeeResult != null && employeeResult.getResult() != null) {
            // added for v1.5.5 KNZT-2537 处理人名地区翻译
            CompanyDetailsInterface.populateEmployee(employeeResult.getResult());
            result.setResultList(employeeResult.getResult());
            result.setTotalCount(employeeResult.getPaging() != null ? employeeResult.getPaging().getTotalRecords() : 0);
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "股东信息", httpMethod = "POST", notes = "工商股东，有分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "type", value = "数据类型，可选值 Partners, IpoPartners，默认为：Partners", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "getPartnerList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpPartnerTO> getPartnerList(@RequestBody ProCorpSearchForm form, Model model) {
        JsonResultList<CorpPartnerTO> result = new JsonResultList<>();
        // removed for KNZT-3139
        // added for v1.6.3 KNZT-2826
//        KeyNoResult keyNoResult = null;
//        try {
//            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
//        } catch (MessageException e) {
//            result.setStatus(Constants.Result.FALSE_STR);
//            result.setMsg(I18NUtil.getMessage(e));
//            return result;
//        }
//        if (!keyNoResult.success()) {
//            result.setStatus(Constants.Result.SUCCESS_STR);
//            return result;
//        }
        // removed for v1.6.3 KNZT-2826
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        if (StringUtils.isBlank(form.getType())) {
            form.setType("Partners");
        }
        CompanyPartnerResult resultData = ECILocalInterface.getPartnerWithGroup(keyNo, form.getType(), form.getPageIndex(), form.getPageSize());
        if (resultData != null) {
            List<CorpPartnerTO> partnerTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resultData.getResult())) {
                for (CompanyParnter companyParnter : resultData.getResult()) {
                    CorpPartnerTO partnerTO = ECILocalInterface.convertPartner2GlobalPartner(companyParnter, form.getType());
                    partnerTOList.add(partnerTO);
                }
                CompanyDetailsInterface.populateInfoByAdvanceSearch(partnerTOList, null, true);
            }
            result.setResultList(partnerTOList);
            result.setTotalCount(resultData.getPaging() != null ? resultData.getPaging().getTotalRecords() : 0);
        }
        result.setStatus(Constants.Result.SUCCESS_STR);

        return result;
    }

    /**
     * added for v1.1.8 KNZT-918
     * @param form
     * @param model
     * @return
     */
    @ApiOperation(value = "风险扫描项目", httpMethod = "POST", notes = "风险扫描项目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body")})
    @RequestMapping(value = "getScanningCount", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<CorpScanningCountTO> getScanningCount(@RequestBody ProCorpSearchForm form, Model model) {
        JsonResult<CorpScanningCountTO> result = new JsonResult<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList()); // updated for v2.0.2 chenbl KNZT-5271
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CorpScanningCountTO corpScanning = CompanyDetailsInterface.getCorpScanning(keyNo);
        // updated for v2.0.6 chenbl KNZT-5555
        if (corpScanning == null) {
            corpScanning = new CorpScanningCountTO();
        }
        /* 历史股东镜像国际版中补充逻辑：从工商年报中补充数据
           这里简化逻辑，只是控制前端是否调用历史股东镜像接口，
           实际情况即便有工商年报数据，历史股东镜像也有可能无数据，如果前端调了历史股东镜像接口，但是无数据，前端则会显示无数据 */
        if (corpScanning.getHistoryPartner2Count() == 0) {
            List<AnnualReportAllPartnersTO> annualReportAllPartners = CompanyDetailsInterface.getAnnualReportAllPartners(keyNo, StringUtils.toInteger(DateUtils.getYear()), true);
            corpScanning.setHistoryPartner2Count(CollectionUtils.isEmpty(annualReportAllPartners) ? 0 : 1);
        }
        result.setResult(corpScanning);

        result.setStatus(Constants.Result.SUCCESS_STR);

        return result;
    }
    
    @ApiOperation(value = "获取企业被执行人信息", httpMethod = "POST", notes = "获取企业被执行人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getZhiXingList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CoutZhiXing> getZhiXingList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CoutZhiXing> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CoutZhiXingResult coutZhiXingResult = CompanyDetailsInterface.getZhiXingList(keyNo, form.getIsValid(), form.getPageIndex(), form.getPageSize());
        if (coutZhiXingResult != null && coutZhiXingResult.getResultList() != null && coutZhiXingResult.getResultList().size() > 0) {
            result.setResultList(coutZhiXingResult.getResultList());
            result.setTotalCount(coutZhiXingResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(coutZhiXingResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取企业失信被执行人信息", httpMethod = "POST", notes = "获取企业失信被执行人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getShiXinList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CoutShiXin> getShiXinList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CoutShiXin> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CoutShiXinResult coutShiXinResult = CompanyDetailsInterface.getShiXinList(keyNo, form.getIsValid(), form.getPageIndex(), form.getPageSize());
        if (coutShiXinResult != null && coutShiXinResult.getResultList() != null && coutShiXinResult.getResultList().size() > 0) {
            result.setResultList(coutShiXinResult.getResultList());
            result.setTotalCount(coutShiXinResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(coutShiXinResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取企业限制高消费信息", httpMethod = "POST", notes = "获取企业限制高消费信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "isValid", value = "是否历史 0-历史 1-当前", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getSumptuaryList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<PersonSumptuary> getSumptuaryList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PersonSumptuary> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        PersonSumptuaryResult personSumptuaryResult = CompanyDetailsInterface.getSumptuaryList(keyNo, form.getIsValid(), form.getPageIndex(), form.getPageSize());
        if (personSumptuaryResult != null && personSumptuaryResult.getResultList() != null && personSumptuaryResult.getResultList().size() > 0) {
            result.setResultList(personSumptuaryResult.getResultList());
            result.setTotalCount(personSumptuaryResult.getTotalCount());
            // added for KNZT-1040
            result.setGroupItems(personSumptuaryResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }
    
    @ApiOperation(value = "获取限制出境信息", httpMethod = "POST", notes = "获取企业限制出境信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getLimitExitList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<LimitExit> getLimitExitList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<LimitExit> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        LimitExitResult limitExitResult = CompanyDetailsInterface.getLimitExitList(keyNo, form.getPageIndex(), form.getPageSize());
        if (limitExitResult != null && limitExitResult.getResultList() != null && limitExitResult.getResultList().size() > 0) {
            result.setResultList(limitExitResult.getResultList());
            result.setTotalCount(limitExitResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v1.2.1 KNZT-1044
     */
    @ApiOperation(value = "获取分支机构列表", httpMethod = "POST", notes = "获取企业分支机构列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getBranchList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CompanyBranch> getBranchList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CompanyBranch> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CompanyBranchResult branchResult = CompanyDetailsInterface.getBranchList(keyNo, form.getPageIndex(), form.getPageSize());
        if (branchResult != null && branchResult.getResultList() != null && branchResult.getResultList().size() > 0) {
            result.setResultList(branchResult.getResultList());
            result.setTotalCount(branchResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v1.2.4 KNZT-1226 展示历史股东、历史法定代表人、历史主要人员
     */
    @ApiOperation(value = "获取历史信息", httpMethod = "POST", notes = "展示历史股东、历史法定代表人、历史主要人员，不分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body")})
    @RequestMapping(value = "/getCorpHistoryInfoForInit", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<HistCorpInfo> getCorpHistoryInfoForInit(@RequestBody ProCorpSearchForm form) {
        JsonResult<HistCorpInfo> result = new JsonResult<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        //updated for KNZT-1735【优化】【国际版】大陆公司：UBO，risking scaning，异步加载，指定查询维度，减少接口请求
        HistCorpInfo histCorpInfo = CompanyDetailsInterface.getHistoryCorpInfo(keyNo, form.getHistoryInfoQuery());
        if (histCorpInfo != null ) {
            result.setResult(histCorpInfo);
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    // added for v1.8.0 KNZT-3596
    @ApiOperation(value = "获取工商变更信息", httpMethod = "POST", notes = "获取工商变更信息, 历史注册资本, 历史地址")
    @RequestMapping(value = "/getCoyHistoryInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<HistoryInfoTO> getCoyHistoryInfo(@RequestBody ProCorpSearchForm form) {
        JsonResult<HistoryInfoTO> result = new JsonResult<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        HistoryInfoTO historyInfoTO = CompanyDetailsInterface.getCoyHistoryInfo(keyNo);
        result.setResult(historyInfoTO);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取历史法定代表人", httpMethod = "POST", notes = "获取历史法定代表人")
    @RequestMapping(value = "/getHistoryOperList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistOper> getHistoryOperList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistOper> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        HistOperResult histOperResult = CompanyDetailsInterface.getHistoryOperList(keyNo);
        if (histOperResult != null && histOperResult.getResultList() != null && histOperResult.getResultList().size() > 0) {
            result.setResultList(histOperResult.getResultList());
            result.setTotalCount(histOperResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取历史主要人员", httpMethod = "POST", notes = "获取历史主要人员")
    @RequestMapping(value = "/getHistoryEmployeeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistEmployee> getHistoryEmployeeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistEmployee> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        HistEmployeeResult histEmployeeResult = CompanyDetailsInterface.getHistoryEmployeeList(keyNo, form.getPageIndex(), form.getPageSize());
        if (histEmployeeResult != null && histEmployeeResult.getResultList() != null && histEmployeeResult.getResultList().size() > 0) {
            result.setResultList(histEmployeeResult.getResultList());
            result.setTotalCount(histEmployeeResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取历史股东", httpMethod = "POST", notes = "获取历史股东")
    @RequestMapping(value = "/getHistoryPartnerList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistShareHolder> getHistoryPartnerList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HistShareHolder> result = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        HistShareHolderResult histShareHolderResult = CompanyDetailsInterface.getHistoryPartnerList(keyNo, form.getPageIndex(), form.getPageSize());
        if (histShareHolderResult != null && histShareHolderResult.getResultList() != null && histShareHolderResult.getResultList().size() > 0) {
            result.setResultList(histShareHolderResult.getResultList());
            result.setTotalCount(histShareHolderResult.getTotalCount());
            result.setGroupItems(histShareHolderResult.getGroupItems());//added for V1.3.1 KNZT-1599 历史股东认缴出资额字段名，应根据内容做判断
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }


    /**
     * added for v1.3.1 KNZT-1513
     * 获取控制企业
     */
    @ApiOperation(value = "获取控制企业", httpMethod = "POST", notes = "获取控制企业")
    @RequestMapping(value = "/getVipHoldCompList", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @ResponseBody
    public JsonResultList<ApiVipHoldCompTO> listVipHoldComp4Global(@RequestBody ListVipHoldCompForm form) {
        JsonResultList<ApiVipHoldCompTO> resp = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
        } catch (MessageException e){
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        // updated for v1.8.2 KNZT-3688
        ApiVipHoldCompResult result = CompanyDetailsInterface.listVipHoldComp4Global(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
            resp.setResultList(result.getResultList());
            resp.setTotalCount(result.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1516
     */
    @ApiOperation(value = "获取对外投资企业", httpMethod = "POST", notes = "获取对外投资企业")
    @RequestMapping(value = "/listCompanyInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<InvestmentCompTO> listCompanyInvestment(@RequestBody InvestmentCompForm form) {
        JsonResultList<InvestmentCompTO> resp = new JsonResultList<>();
        // updated for v1.5.8 KNZT-2626
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e){
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        ApiCompanyInvestmentResult result = CompanyDetailsInterface.listCompanyInvestment(keyNo, form.getFundedRatioMin(), form.getFundedRatioMax(), form.getPageIndex(), form.getPageSize());
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1516
     */
    @ApiOperation(value = "获取对外间接投资企业", httpMethod = "POST", notes = "获取对外间接投资企业")
    @RequestMapping(value = "/listCompanyIndirectInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<IndirectInvestmentCompTO> listCompanyIndirectInvestment(@RequestBody IndirectInvestmentCompForm form) {
        JsonResultList<IndirectInvestmentCompTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        ApiCompanyIndirectInvestmentResult result = CompanyDetailsInterface.listCompanyIndirectInvestment(keyNo, form.getStockPercentMin(), form.getStockPercentMax(), form.getPageIndex(), form.getPageSize());
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyIndirectInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyIndirectInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1516
     */
    @ApiOperation(value = "获取对外历史投资企业", httpMethod = "POST", notes = "获取对外历史投资企业")
    @RequestMapping(value = "listCompanyHistoryInvestment", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<HistoryInvestmentCompTO> listCompanyHistoryInvestment(@RequestBody HistoryInvestmentCompForm form) {
        JsonResultList<HistoryInvestmentCompTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        ApiCompanyHistoryInvestmentResult result = CompanyDetailsInterface.listCompanyHistoryInvestment(keyNo, form.getPageIndex(), form.getPageSize());
        resp.setTotalCount(Optional.ofNullable(result).map(ApiCompanyHistoryInvestmentResult::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(result).map(ApiCompanyHistoryInvestmentResult::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.9.4 KNZT-4373
     */
    @ApiOperation(value = "获取子公司（根据投资企业）", httpMethod = "POST", notes = "获取子公司（根据投资企业）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "listSubsidiaries", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AllInvestmentTO> listSubsidiaries(@RequestBody AllInvestmentForm form) {
        JsonResultList<AllInvestmentTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withUBOList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        form.setKeyNo(keyNo);
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listSubsidiaries(form.getKeyNo(), form.getPageIndex(), form.getPageSize());
        resp.setTotalCount(Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.9.4 KNZT-4373
     */
    @ApiOperation(value = "获取分支机构（根据投资企业）", httpMethod = "POST", notes = "获取分支机构（根据投资企业）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "listAffiliates", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AllInvestmentTO> listAffiliates(@RequestBody AllInvestmentForm form) {
        JsonResultList<AllInvestmentTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withUBOList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        form.setKeyNo(keyNo);
        JsonResultList<AllInvestmentTO> resultList = CompanyDetailsInterface.listAffiliates(form.getKeyNo(), form.getPageIndex(), form.getPageSize());
        resp.setTotalCount(Optional.ofNullable(resultList).map(JsonResultList::getTotalCount).orElse(0L));
        resp.setResultList(Optional.ofNullable(resultList).map(JsonResultList::getResultList).orElse(new ArrayList<>(0)));
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1735【优化】【国际版】大陆公司：UBO，risking scaning，异步加载 UBO和实际控制人单独请求
     *
     * @param form
     * @return
     */
    @ApiOperation(value = "获取UBO和实际控制人", httpMethod = "POST", notes = "展示获取UBO和实际控制人，不分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body")})
    @RequestMapping(value = "/getCorpSimpleInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<CorpSimpleInfoTO> getCorpSimpleInfo(@RequestBody ProCorpSearchForm form) {
        JsonResult<CorpSimpleInfoTO> result = new JsonResult<>();
        String keyNo = null;
        try {
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withUBOList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        CorpSimpleInfoTO corpSimpleInfoTO = new CorpSimpleInfoTO();
        try {
            StockResult4UnitTO corpUBO4Unit = CompanyDetailsInterface.getCorpUBO4Unit(keyNo);
            corpSimpleInfoTO.setUboDetailTO(corpUBO4Unit);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
            return result;
        }
        // updated for v1.7.0 KNZT-3073 后端改造去除实控人
//        List<ActualControllerTO> actualControllerList = RelationInterface.getActualControllerV3(keyNo);
//        corpSimpleInfoTO.setActualControllerList(actualControllerList);
        result.setStatus(Constants.Result.SUCCESS_STR);
        result.setResult(corpSimpleInfoTO);
        return result;
    }

    /**
     * added for KNZT-1510
     */
    @ApiOperation(value = "获取投资公司接口", httpMethod = "POST", notes = "获取投资公司接口-股权穿透图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "stocktz", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<RltnOwnshpStrc> getStockTz(@RequestBody StockTzForm form) {
        JsonResult<RltnOwnshpStrc> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.SUCCESS_STR);
            return resp;
        }
        RltnOwnshpStrc result = CompanyDetailsInterface.getStockTz(keyNoResult.getKeyNo());
        if (result != null && CollectionUtils.isNotEmpty(result.getEquityShareDetail())) {
            for (RltnOwnshpStrcDtlList rltnOwnshpStrcDtlList : result.getEquityShareDetail()) {
                rltnOwnshpStrcDtlList.setKeyNo(CommTblCompReportOrderService.generateKeyNoEncrypted(keyNoResult.getOrderId(), keyNoResult.getKeyNoByOrderId(), rltnOwnshpStrcDtlList.getKeyNo()));
            }
        }
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1510
     */
    @ApiOperation(value = "获取股东接口", httpMethod = "POST", notes = "获取股东接口-股权穿透图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "stockgdmix", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<RltnOwnshpStrc> getStockGdMix(@RequestBody StockgdmixForm form) {
        JsonResult<RltnOwnshpStrc> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.SUCCESS_STR);
            return resp;
        }
        RltnOwnshpStrc result = CompanyDetailsInterface.getStockGdMix(keyNoResult.getKeyNo());
        if (result != null && CollectionUtils.isNotEmpty(result.getEquityShareDetail())) {
            for (RltnOwnshpStrcDtlList rltnOwnshpStrcDtlList : result.getEquityShareDetail()) {
                rltnOwnshpStrcDtlList.setKeyNo(CommTblCompReportOrderService.generateKeyNoEncrypted(keyNoResult.getOrderId(), keyNoResult.getKeyNoByOrderId(), rltnOwnshpStrcDtlList.getKeyNo()));
            }
        }
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for KNZT-1510
     */
    @ApiOperation(value = "获取企业概况接口", httpMethod = "POST", notes = "获取企业概况接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body"),
            @ApiImplicitParam(name = "corpType", value = "corpType", paramType = "body")})
    @RequestMapping(value = "enterpriseoverview", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<GraphOverviewResult> getEnterpriseOverview(@RequestBody EnterpriseoverviewForm form) {
        JsonResult<GraphOverviewResult> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.SUCCESS_STR);
            return resp;
        }
        GraphOverviewResult result = CompanyDetailsInterface.getEnterpriseOverview(keyNoResult.getKeyNo(), form.getCorpType());
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.3.7 KNZT-1783
     * 全球参控股企业
     */
    @ApiOperation(value = "获取全球参控股企业", httpMethod = "POST", notes = "获取全球参控股企业")
    @RequestMapping(value = "/listOverSeaRelatedComp", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @ResponseBody
    public JsonResultList<ApiOverseaRelatedCompTO> listOverSeaRelatedComp(@RequestBody ListVipHoldCompForm form) {
        JsonResultList<ApiOverseaRelatedCompTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        ApiOverseaRelatedCompResult result = CompanyDetailsInterface.listOverSeaRelatedComp(keyNo, form.getPageIndex(), form.getPageSize());
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
            resp.setResultList(result.getResultList());
            resp.setTotalCount(result.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }


    /**
     * added for v1.3.7 KNZT-1783
     * 全球股东
     */
    @ApiOperation(value = "获取全球股东", httpMethod = "POST", notes = "获取全球股东")
    @RequestMapping(value = "/listOverseaPartner", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @ResponseBody
    public JsonResultList<ApiOverseaCorpPartnerTO> listOverseaPartner(@RequestBody ListVipHoldCompForm form) {
        JsonResultList<ApiOverseaCorpPartnerTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        ApiOverseaCorpPartnerResult result = CompanyDetailsInterface.listOverseaPartner(keyNo, form.getPageIndex(), form.getPageSize());
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getResultList())) {
            resp.setResultList(result.getResultList());
            resp.setTotalCount(result.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added v1.3.4 KNZT-1517
     * 获取人员概要信息
     */
    @ApiOperation(value = "获取人员概要信息", httpMethod = "POST", notes = "获取人员概要信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getPersonOverview", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<ApiGraphPersonOverviewTO> getPersonOverview(@RequestBody ProCorpSearchForm form) {
        JsonResult<ApiGraphPersonOverviewTO> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.SUCCESS_STR);
            return resp;
        }
        ApiGraphPersonOverviewTO result = CompanyDetailsInterface.getPersonOverview(keyNoResult.getKeyNo());
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }


    /**
     * added v1.3.4 KNZT-1517
     * 获取关系图谱-企业维度信息
     */
    @ApiOperation(value = "获取关系图谱-企业维度信息", httpMethod = "POST", notes = "获取关系图谱-企业维度信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "keyNo", paramType = "body")})
    @RequestMapping(value = "/getEnterpriseGraph", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<ApiCompGraphTO> getEnterpriseGraph(@RequestBody ProCorpSearchForm form) {
        JsonResult<ApiCompGraphTO> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        ApiCompGraphTO result = CompanyDetailsInterface.getEnterpriseGraph(keyNoResult.getKeyNo());
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getChildren())) {
            for (ApiCompGraphTO item : result.getChildren()) {
                if (CollectionUtils.isEmpty(item.getChildren())) {
                    continue;
                }
                for (ApiCompGraphTO child : item.getChildren()) {
                    String keyNoEncrypted = CommTblCompReportOrderService.generateKeyNoEncrypted
                            (keyNoResult.getOrderId(), keyNoResult.getKeyNoByOrderId(), child.getId());
                    child.setId(keyNoEncrypted);
                }
            }
        }
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added v1.3.4 KNZT-1517
     * 获取关系图谱-人员维度信息
     */
    @ApiOperation(value = "获取关系图谱-人员维度信息", httpMethod = "POST", notes = "获取关系图谱-人员维度信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询人的keyNo", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "人关联企业的keyNo", paramType = "body")})
    @RequestMapping(value = "/getPersonGraph", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<ApiCompGraphTO> getPersonGraph(@RequestBody ProCorpSearchForm form) {
        JsonResult<ApiCompGraphTO> resp = new JsonResult<>();
        KeyNoResult keyNoResult = null;
        try {
            keyNoResult = commTblCompReportOrderService.getKeyResult(form.getId(), form.getKeyNo());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (!keyNoResult.success()) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        ApiCompGraphTO result = CompanyDetailsInterface.getPersonGraph(keyNoResult.getKeyNo());
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getChildren())) {
            for (ApiCompGraphTO item : result.getChildren()) {
                if (CollectionUtils.isEmpty(item.getChildren())) {
                    continue;
                }
                for (ApiCompGraphTO child : item.getChildren()) {
                    String keyNoEncrypted = CommTblCompReportOrderService.generateKeyNoEncrypted
                            (keyNoResult.getOrderId(), keyNoResult.getKeyNoByOrderId(), child.getId());
                    child.setId(keyNoEncrypted);
                }
            }
        }
        resp.setResult(result);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.5.1 KNZT-2258
     */
    @ApiOperation(value = "获取实际控制人-最终受益股份", httpMethod = "POST", notes = "获取实际控制人-最终受益股份")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "partnerId", value = "主要人员ID", paramType = "body"),
            @ApiImplicitParam(name = "partnerName", value = "主要人员名称", paramType = "body")})
    @RequestMapping(value = "/getEnterpriseBeneficiaryGraph", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<EnterpriseBeneficiaryGraphTO> getEnterpriseBeneficiaryGraph(@RequestBody ProCorpSearchForm form) {
        JsonResult<EnterpriseBeneficiaryGraphTO> resp = new JsonResult<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        EnterpriseBeneficiaryGraphTO graphTO = CompanyDetailsInterface.getEnterpriseBeneficiaryGraph(keyNo, form.getPartnerId(), form.getPartnerName());
        resp.setResult(graphTO);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v1.5.3 KNZT-2383
     */
    @ApiOperation(value = "获取股东镜像", httpMethod = "POST", notes = "获取股东镜像")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @RequestMapping(value = "/getEquityShareChangesByDate", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<EquityShareChangesByDateResultTO> getEquityShareChangesByDate(@RequestBody ProCorpSearchForm form) {
        JsonResult<EquityShareChangesByDateResultTO> resp = new JsonResult<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
//            keyNo = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg("Locked Information");
            return resp;
        }
        // updated for v2.0.6 chenbl KNZT-5555
        return JsonResult.buildSuccess(CorpGatewayInterface.getEquityShareChangesByDate(keyNo));
    }

    // added for v1.8.1 KNZT-3610
    @ApiOperation(value = "获取行政处罚", httpMethod = "POST", notes = "获取行政处罚")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getRiskPenaltySumList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<AdministrativePunishmentTO> getRiskPenaltySumList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<AdministrativePunishmentTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        JsonResultList<AdministrativePunishmentTO> resultPage = CompanyDetailsInterface.getRiskPenaltySumList(keyNo, form.getPageIndex(), form.getPageSize());
        if (resultPage != null) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
            resp.setGroupItems(resultPage.getGroupItems());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
    @ApiOperation(value = "获取经营异常", httpMethod = "POST", notes = "获取经营异常")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getExceptionList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpExceptionInfoTO> getExceptionList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpExceptionInfoTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        JsonResultList<CorpExceptionInfoTO> resultPage = CompanyDetailsInterface.getExceptionList(keyNo, form.getPageIndex(), form.getPageSize());
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
    @ApiOperation(value = "获取严重违法", httpMethod = "POST", notes = "获取严重违法")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getSeriousViolationList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpSeriousViolationTO> getSeriousViolationList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpSeriousViolationTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        JsonResultList<CorpSeriousViolationTO> resultPage = CompanyDetailsInterface.getSeriousViolationList(keyNo, form.getPageIndex(), form.getPageSize());
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.8.1 KNZT-3610
    @ApiOperation(value = "获取股权出质", httpMethod = "POST", notes = "获取股权出质")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/getPledgeList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CorpPledgeV2TO> getPledgeList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CorpPledgeV2TO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            // added for v1.5.8 KNZT-2626
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        JsonResultList<CorpPledgeV2TO> resultPage = CompanyDetailsInterface.getPledgeList(keyNo, form.getPageIndex(), form.getPageSize());
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v1.9.0 KNZT-4145
    @ApiOperation(value = "获取司法案件", httpMethod = "POST", notes = "获取司法案件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body"),
            @ApiImplicitParam(name = "reason", value = "案由", paramType = "body"),
            @ApiImplicitParam(name = "caseTypeMain", value = "案件类型", paramType = "body"),
            @ApiImplicitParam(name = "roleType", value = "身份", paramType = "body"),
            @ApiImplicitParam(name = "year", value = "年份", paramType = "body")})
    @RequestMapping(value = "/getCourtCaseList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<CourtCaseTO> getCourtCaseList(@RequestBody CourtCaseForm form) {
        JsonResultList<CourtCaseTO> resp = new JsonResultList<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getOrderId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        form.setKeyNo(keyNo);
        JsonResultList<CourtCaseTO> resultPage = CompanyDetailsInterface.getCourtCaseList(form);
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getResultList())) {
            resp.setResultList(resultPage.getResultList());
            resp.setTotalCount(resultPage.getTotalCount());
            resp.setGroupItems(resultPage.getGroupItems());
        }
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    // added for v2.0.2 chenbl KNZT-5271
    @ApiOperation(value = "获取财税数据", httpMethod = "POST", notes = "获取财税数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "orderId", paramType = "body")})
    @RequestMapping(value = "/getFinancialTaxDetail", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<FinancialData> getFinancialTaxDetail(@RequestBody ProCorpSearchForm form) {
        JsonResult<FinancialData> resp = new JsonResult<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.witFinTaxList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        FinancialData financialData = ECILocalInterface.getFinancialTaxDetail(keyNo);
        resp.setResult(financialData);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    @ApiOperation(value = "获取财税现金流量表数据", httpMethod = "POST", notes = "获取财税现金流量表数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "orderId", paramType = "body")})
    @RequestMapping(value = "/getFinancialCashFlowInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<?> getFinancialCashFlowInfo(@RequestBody ProCorpSearchForm form) {
        try {
            String keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.witFinTaxList());
            MsgExceptionUtils.checkIsNull(keyNo);
            return JsonResult.buildSuccess(CorpGatewayInterface.getFinancialCashFlowInfo(keyNo));
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }
    }

    // added for v2.0.2 chenbl KNZT-5353
    @ApiOperation(value = "获取空壳概览", httpMethod = "POST", notes = "获取空壳概览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "orderId", paramType = "body")})
    @RequestMapping(value = "/getShellCompanyLabel", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<EnLabelTOResult> getShellCompanyLabel(@RequestBody ProCorpSearchForm form) {
        JsonResult<EnLabelTOResult> resp = new JsonResult<>();
        String keyNo = null;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            resp.setStatus(Constants.Result.FALSE_STR);
            resp.setMsg(I18NUtil.getMessage(e));
            return resp;
        }
        if (StringUtils.isBlank(keyNo)) {
            resp.setStatus(Constants.Result.FALSE_STR);
            return resp;
        }
        EnLabelTOResult shellCompanyLabel = ShellLabelService.getShellCompanyLabel(keyNo);
        resp.setResult(shellCompanyLabel);
        resp.setStatus(Constants.Result.SUCCESS_STR);
        return resp;
    }

    /**
     * added for v.1.5.5 KNZT-2524【优化】【国际版】提供翻译接口
     */
    @ApiOperation(value = "机器翻译接口", httpMethod = "POST", notes = "机器翻译接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contentText", value = "文本内容", paramType = "body"),
            @ApiImplicitParam(name = "keyNo", value = "公司keyNo(非必填)", paramType = "body")
    })
    @RequestMapping(value = "/translateContent", method = RequestMethod.POST)
    @ResponseBody
    public JsonSimpleResult<String> searchAutoComplete4Global(@RequestBody ProCorpSearchForm form) {
        JsonSimpleResult<String> result = new JsonSimpleResult<>();
        result.setStatus(Constants.Result.SUCCESS_STR);
        if (StringUtils.isNotBlank(form.getContentText())) {
            String enTxt = translaterService.getEnglishText(form.getContentText(), form.getKeyNo(), "企业经营范围翻译", SysConstants.TRANSLATE_HIST_FUNCTION_CORP_SCOPE);
            if (StringUtils.isBlank(enTxt)) {
                result.setStatus(Constants.Result.FALSE_STR);
            }
            result.setResult(enTxt);
        }
        return result;
    }

    /**
     * added for v2.0.2 fengsw KNZT-5353【新增】思远账号，Advanced报告中，中补充空壳扫描数据维度，展示空壳扫描详情，可支持页面内容翻译
     * 获取相同董监高列表
     */
    @RequestMapping(value = "similarExecutiveList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellRelateCompanyDetailTO> getSimilarExecutiveList(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellRelateCompanyDetailTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        form.getCondition().setKeyno(keyNo);
        ShellRelateCompanyDetailResult detailResult =  CompanyDetailsInterface.getSimilarExecutiveList(form, true);
        if(Objects.nonNull(detailResult)){
            result.setResultList(detailResult.getResultList());
            result.setTotalCount(detailResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v2.0.2 fengsw KNZT-5353【新增】思远账号，Advanced报告中，中补充空壳扫描数据维度，展示空壳扫描详情，可支持页面内容翻译
     * 获取相同法人列表
     */
    @RequestMapping(value = "sameCorporateList", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellRelateCompanyDetailTO> getSameCorporateList(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellRelateCompanyDetailTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        form.getCondition().setKeyno(keyNo);
        ShellRelateCompanyDetailResult detailResult = CompanyDetailsInterface.getSimilarExecutiveList(form, true);
        if(Objects.nonNull(detailResult)){
            result.setResultList(detailResult.getResultList());
            result.setTotalCount(detailResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v2.0.2 fengsw KNZT-5353【新增】思远账号，Advanced报告中，中补充空壳扫描数据维度，展示空壳扫描详情，可支持页面内容翻译
     * 同电话/同地址企业列表
     */
    @RequestMapping(value = "sameTelAddressListV2", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellScCompanyRepeatAddressTO> getSameTelAddressListV2(@RequestBody ShellCompanySearchForm form) {
        JsonResultList<ShellScCompanyRepeatAddressTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        form.getCondition().setKeyno(keyNo);
        ShellScCompanyRepeatAddressResult sameTelAddressResult = CompanyDetailsInterface.getSameTelAddressList(form, true);
        if (Objects.nonNull(sameTelAddressResult)) {
            result.setResultList(sameTelAddressResult.getResultList());
            result.setTotalCount(sameTelAddressResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v2.0.2 fengsw KNZT-5353【新增】思远账号，Advanced报告中，中补充空壳扫描数据维度，展示空壳扫描详情，可支持页面内容翻译
     * 经营异常列表-无法联系
     */
    @ApiOperation(value = "经营异常列表-无法联系", httpMethod = "POST", notes = "经营异常列表-无法联系")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @RequestMapping(value = "noContactException", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellCorpExceptionTO> getNoContactExceptionByReason(@RequestBody ProCorpSearchForm form) {
        JsonResultList<ShellCorpExceptionTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withBasicList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        List<ShellCorpExceptionTO> resultList = CompanyDetailsInterface.getNoContactExceptionWithReasonByKeyNo(keyNo, true);
        result.setResultList(resultList);
        result.setTotalCount(CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    /**
     * added for v2.0.2 fengsw KNZT-5353【新增】思远账号，Advanced报告中，中补充空壳扫描数据维度，展示空壳扫描详情，可支持页面内容翻译
     * 经营异常列表-未公示年报
     */
    @RequestMapping(value = "annualReportNotPublish", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ShellScCompanyWnjTO> getAnnualReportNotPublishList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<ShellScCompanyWnjTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        List<ShellScCompanyWnjTO> resultList = CompanyDetailsInterface.getCompanyWnjListByKeyNo(keyNo, true);
        result.setResultList(resultList);
        result.setTotalCount(CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }


    // 获取商户店铺信息 added for lvcy v2.1.1 KNZT-5973
    @ApiOperation(value = "获取商户店铺信息", httpMethod = "POST", notes = "获取商户店铺信息")
    @RequestMapping(value = "/getMerchantShop", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<CorpMerchantShopTO> getMerchantShop(@RequestBody ProCorpSearchForm form) {
        try {
            String orderId = commTblCompReportOrderService.getOrderIdAndCheckDataToken(form.getId());
            TblCompReportOrder order = commTblCompReportOrderService.getByIdAndCompany(orderId, UserUtils.getUserCompanyId());
            if (Objects.isNull(order) || !ReportTypeEnum.withMerchantList().contains(order.getReportType())) {
                return JsonResult.buildFail("Locked Information") ;
            }
            CorpMerchantShopTO corpMerchantShopTO = merchantShopBusinessService.getMerchantShop(order);
            return JsonResult.buildSuccess(corpMerchantShopTO);
        } catch (MessageException e) {
            return JsonResult.buildFail(e);
        }
    }

    // added for v2.1.1 chenbl KNZT-6038
    @ApiOperation(value = "企查分", httpMethod = "POST", notes = "企查分")
    @RequestMapping(value = "getCreditRate", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetCreditRateTO> getCreditRate(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetCreditRateTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResult.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResult.buildFail("Locked Information");
            return result;
        }
        GetCreditRateTO creditRate = CompanyDetailsInterface.getCreditRate(keyNo, true);
        if (creditRate != null) {
            GetCreditRateTO.IndustryAnalysis industryAnalysis = creditRate.getIndustryAnalysis();
            if (industryAnalysis != null && StringUtils.isNotBlank(industryAnalysis.getIndustryDesc())) {
                // updated for v2.1.3 fengsw KNZT-6226 获取二级行业英文名 方法切换到 CommTblGlobalIndustryService
                TblGlobalIndustry tblGlobalIndustry = commTblGlobalIndustryService.getCnInd2NameEnByName(industryAnalysis.getIndustryDesc());
                if (tblGlobalIndustry != null) {
                    industryAnalysis.setIndustryDescEn(tblGlobalIndustry.getInd2NameEn());
                    industryAnalysis.setIndustryCode(tblGlobalIndustry.getInd1Code() + tblGlobalIndustry.getInd2Code());
                }
            }
        }
        result = JsonResult.buildSuccess(creditRate);
        return result;
    }

    @ApiOperation(value = "历史企查分", httpMethod = "POST", notes = "历史企查分")
    @RequestMapping(value = "getCreditRateTrend", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetCreditRateTrendTO> getCreditRateTrend(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetCreditRateTrendTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResult.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResult.buildFail("Locked Information");
            return result;
        }
        GetCreditRateTrendTO creditRateTrend = CompanyDetailsInterface.getCreditRateTrend(keyNo,  true);
        result = JsonResult.buildSuccess(creditRateTrend);
        return result;
    }

    @ApiOperation(value = "发票抬头", httpMethod = "POST", notes = "发票抬头")
    @RequestMapping(value = "getInvoiceDetail", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body")})
    @ResponseBody
    public JsonResult<GetInvoiceDetailTO> getInvoiceDetail(@RequestBody ProCorpSearchForm form) {
        JsonResult<GetInvoiceDetailTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResult.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResult.buildFail("Locked Information");
            return result;
        }
        GetInvoiceDetailTO invoiceDetail = CompanyDetailsInterface.getInvoiceDetail(keyNo, true);
        result = JsonResult.buildSuccess(invoiceDetail);
        return result;
    }

    @ApiOperation(value = "税务基本信息", httpMethod = "POST", notes = "税务基本信息")
    @RequestMapping(value = "listGeneralTaxPayer", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GeneralTaxPayerTO> listGeneralTaxPayer(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GeneralTaxPayerTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<GeneralTaxPayerTO> apiResult = CompanyDetailsInterface.listGeneralTaxPayer(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "税务非正常户核查", httpMethod = "POST", notes = "税务非正常户核查")
    @RequestMapping(value = "getTaxUnnormals", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetTaxUnnormalsTO> getTaxUnnormals(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetTaxUnnormalsTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<GetTaxUnnormalsTO> apiResult = CompanyDetailsInterface.getTaxUnnormals(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "税收违法", httpMethod = "POST", notes = "税收违法")
    @RequestMapping(value = "getIllegalList", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetIllegalListTO> getIllegalList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetIllegalListTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<GetIllegalListTO> apiResult = CompanyDetailsInterface.getIllegalList(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "欠税公告", httpMethod = "POST", notes = "欠税公告")
    @RequestMapping(value = "getListOfOweNoticeNew", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<GetListOfOweNoticeNewTO> getListOfOweNoticeNew(@RequestBody ProCorpSearchForm form) {
        JsonResultList<GetListOfOweNoticeNewTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<GetListOfOweNoticeNewTO> apiResult = CompanyDetailsInterface.getListOfOweNoticeNew(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
            result.setGroupItems(apiResult.getGroupItems());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "进出口信用", httpMethod = "POST", notes = "进出口信用")
    @RequestMapping(value = "listIE", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<IETO> listIE(@RequestBody ProCorpSearchForm form) {
        JsonResultList<IETO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<IETO> apiResult = CompanyDetailsInterface.listIE(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "资质证书", httpMethod = "POST", notes = "资质证书")
    @RequestMapping(value = "getCertificationList", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<CertificationListTO> getCertificationList(@RequestBody ProCorpSearchForm form) {
        JsonResultList<CertificationListTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<CertificationListTO> apiResult = CorpGatewayInterface.getCertificationList(keyNo, form.getPageIndex(), form.getPageSize());
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "荣誉", httpMethod = "POST", notes = "荣誉")
    @RequestMapping(value = "listHonorCertificationV2", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<HonorCertificationV2TO> listHonorCertificationV2(@RequestBody ProCorpSearchForm form) {
        JsonResultList<HonorCertificationV2TO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<HonorCertificationV2TO> apiResult = CompanyDetailsInterface.listHonorCertificationV2(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "商标信息", httpMethod = "POST", notes = "商标信息")
    @RequestMapping(value = "listTrademark", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<TrademarkTO> listTrademark(@RequestBody ProCorpSearchForm form) {
        JsonResultList<TrademarkTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<TrademarkTO> apiResult = CompanyDetailsInterface.listTrademark(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    @ApiOperation(value = "专利信息", httpMethod = "POST", notes = "专利信息")
    @RequestMapping(value = "listPatent", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "body")})
    @ResponseBody
    public JsonResultList<PatentTO> listPatent(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PatentTO> result = null;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<PatentTO> apiResult = CompanyDetailsInterface.listPatent(keyNo, form.getPageIndex(), form.getPageSize(), true);
        if (apiResult != null) {
            result = JsonResultList.buildSuccess(apiResult.getResultList(), apiResult.getTotalCount());
        } else {
            result = JsonResultList.buildSuccess(new ArrayList<>());
        }
        return result;
    }

    /**
     * added for v2.1.1 fengsw KNZT-6127【新增】补充国标-联合国，国标-新加坡，国标-北美，国标-欧盟，国标-新加坡行业映射数据(2025)
     * 获取行业映射信息
     */
    @RequestMapping(value = "getIndustryMappingInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<TblGlobalIndustryBaseTO> getIndustryMappingInfo(@RequestBody ProCorpSearchForm form) {
        JsonResultList<TblGlobalIndustryBaseTO> result = new JsonResultList<>();
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withMerchantList());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        List<TblGlobalIndustryBaseTO> industryList = commTblCompReportOrderService.getIndustryMappingInfo(keyNo, true);
        result.setTotalCount(CollectionUtils.isNotEmpty(industryList) ? industryList.size() : 0);
        result.setResultList(industryList);
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }


    // added for v2.3.8 fengsw KNZT-8675 查询公司疑似异常变化 数据总量限500
    @ApiOperation(value = "查询公司疑似异常变化", httpMethod = "POST", notes = "查询公司疑似异常变化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单ID", paramType = "body"),
            @ApiImplicitParam(name = "labelCodes", value = "标签Code", paramType = "body")})
    @RequestMapping(value = "listScCompanyAbnormalChange", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<ScCompanyAbnormalChangeTO> listScCompanyAbnormalChange(@RequestBody ShellCompanySearchForm form) {
        if (form == null || StringUtils.isBlank(form.getId()) || form.getCondition() == null || CollectionUtils.isEmpty(form.getCondition().getLabelCodes())) {
            return JsonResultList.buildFail("err.param.invalid");
        }
        JsonResultList<ScCompanyAbnormalChangeTO> result;
        String keyNo;
        try {
            keyNo = commTblCompReportOrderService.getKeyNoByDataToken(form.getId(), UserUtils.getUserCompanyId(), ReportTypeEnum.withAdvancedList());
        } catch (MessageException e) {
            result = JsonResultList.buildFail(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(keyNo)) {
            result = JsonResultList.buildFail("Locked Information");
            return result;
        }
        JsonResultList<ScCompanyAbnormalChangeTO> scCompanyAbnormalChangeTOJsonResultList = CompanyDetailsInterface.listScCompanyAbnormalChange(keyNo, form.getCondition().getLabelCodes().get(0));
        if (scCompanyAbnormalChangeTOJsonResultList != null) {
            return JsonResultList.buildSuccess(scCompanyAbnormalChangeTOJsonResultList.getResultList(), scCompanyAbnormalChangeTOJsonResultList.getTotalCount());
        }
        return JsonResultList.buildSuccess(new ArrayList<>());
    }
}
