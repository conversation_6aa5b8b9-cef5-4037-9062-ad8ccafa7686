package com.qcc.pro.modules.open.controller;

import com.backend.common.modules.open.service.PaymentBusinessService;
import com.backend.common.service.SysDingMsgNewTranService;
import com.qcc.frame.commons.ienum.AlarmTypeEnum;
import com.qcc.frame.jee.commons.config.Global;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 提供给支付平台的公开接口
 * added for v1.9.3 KNZT-4193
 *
 * <AUTHOR>
 * @datetime 2024/7/25 14:38
 */
@Api(tags = "对外提供支付相关接口")
@Controller
@RequestMapping(value = "/open/payment")
public class OpenPaymentController {
    @Autowired
    private PaymentBusinessService paymentBusinessService;
    @Autowired
    private SysDingMsgNewTranService sysDingMsgNewTranService;

    private static final String endpointSecret = Global.getConfigDefault("stripe.endpoint.secret", "whsec_df89cfc735790ea942f951c730ba93fe53a151173f16c3decb61573e369c8a3a");

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @ApiOperation(value = "Stripe webhook", httpMethod = "POST", notes = "Stripe webhook")
    @RequestMapping(value = "/webhook")
    @ResponseBody
    public Object webhook(@RequestHeader("Stripe-Signature") String sigHeader, @RequestBody String payload) {
        try {
            Event event = Webhook.constructEvent(payload, sigHeader, endpointSecret);
            paymentBusinessService.handleStripeWebhook(event);
        } catch (Exception e) {
            sysDingMsgNewTranService.newTranSaveSendErrorMsgByDingTalk(AlarmTypeEnum.STRIPE_WEBHOOK_ERROR, e);
            logger.error("failed to handle webhook event. sigHeader:{}, payload:{}", sigHeader, payload, e);
            return ResponseEntity.status(500).body(e.getMessage());
        }
        return ResponseEntity.ok().body("OK");
    }
}
