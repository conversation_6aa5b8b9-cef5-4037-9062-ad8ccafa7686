package com.qcc.pro.modules.datamap.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.modules.report.service.DataMapBusinessService;
import com.backend.common.modules.benefit.service.BenefitBusinessService;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.modules.sys.service.RedisServUtils;
import com.qcc.pro.modules.datamap.form.DataMapExportForm;
import com.qcc.pro.modules.datamap.form.DataMapProxyForm;
import com.qcc.pro.modules.datamap.model.DataMapExportResultTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.backend.common.modules.report.model.DataMapUnitResult;
import com.backend.common.modules.report.model.DataMapOriginalDocTO;
import com.backend.common.modules.report.model.UserBenefitInfoResult;
import com.qcc.pro.modules.datamap.form.DataMapForm;
import com.qcc.pro.modules.datamap.form.DataMapPageForm;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 图谱接口
 * added for lvcy v2.1.5 KNZT-6254
 * <AUTHOR>
 * @datetime 7/2/2025 5:13 下午
 */

@Api(tags = "图谱接口", value = "图谱接口")
@Controller
@RequestMapping(value = "${adminPath}/data-map")
public class DataMapController {
    private Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private DataMapBusinessService dataMapBusinessService;
    @Autowired
    private RedisServUtils redisServUtils;
    @Autowired
    private BenefitBusinessService benefitBusinessService;


    @ApiOperation(value = "图谱代理转发接口", httpMethod = "POST", notes = "图谱代理转发接口")
    @RequestMapping(value="/api/proxy", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> proxyApi(@RequestBody DataMapProxyForm form) {
        try {
            String respJson = dataMapBusinessService.proxyApi(form.getUrl(), form.getBodyJsonMap());
            return JsonResult.buildSuccess(respJson);
        } catch (MessageException e) {
            log.error("图谱代理转发接口异常, url: {}, bodyJsonMap: {}", form.getUrl(), form.getBodyJsonMap(),e);
            return JsonResult.buildFail(e.getMessage());
        }
    }

    @ApiOperation(value = "图谱通过订单代理转发接口", httpMethod = "POST", notes = "图谱通过订单代理转发接口")
    @RequestMapping(value="/api/proxy-by-order", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> proxyApiByOrder(@RequestBody DataMapProxyForm form) {
        try {
            String respJson = dataMapBusinessService.proxyApiByOrder(form.getOrderId(), form.getBodyJsonMap(), form.getUrl());
            return JsonResult.buildSuccess(respJson);
        } catch (MessageException e) {
            log.error("图谱代理转发接口异常, orderId: {}", form.getOrderId(),e);
            return JsonResult.buildFail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询图谱额度信息", httpMethod = "POST", notes = "查询当前剩余额度、图谱关联订单的额度、是否为第一次进入")
    @RequestMapping(value="/credits/info", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<DataMapUnitResult> getCreditsInfo(@RequestBody DataMapForm form) {
        DataMapUnitResult result = dataMapBusinessService.getCreditsInfo(form.getMapNo(), form.getRootKeyNo(), form.getReportType());
        return JsonResult.buildSuccess(result);
    }

    @ApiOperation(value = "查询当前用户套餐情况", httpMethod = "POST", notes = "查询当前用户的图谱权益套餐信息")
    @RequestMapping(value="/user-benefit/info", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<UserBenefitInfoResult> getUserBenefitInfo() {
        UserBenefitInfoResult result = dataMapBusinessService.getUserBenefitInfo();
        return JsonResult.buildSuccess(result);
    }


    @ApiOperation(value = "静默通知", httpMethod = "POST", notes = "静默通知")
    @RequestMapping(value="/silent", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> silent() {
        dataMapBusinessService.silent();
        return JsonResult.buildSuccess();
    }

    @ApiOperation(value = "更新图谱用户引导信息", httpMethod = "POST", notes = "更新图谱访问状态")
    @RequestMapping(value="/user-guide/update", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> updateUserExtInfo(@RequestBody DataMapPageForm form) {
        dataMapBusinessService.updateUserExtInfo4Map(form.getPageMenu());
        return JsonResult.buildSuccess();
    }

    @ApiOperation(value = "导出文件", httpMethod = "POST", notes = "导出文件")
    @RequestMapping(value="/export-data", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> exportData(@RequestBody Map<String, Object> form) {
        form.put("SourceCode", "GLOBAL");
        try {
            String resp = GatewayInvoker.postProxyJson("data-export", "/DataApi/ExportDataPublicPost", form, null);
            log.info("exportData gateway resp: {}", resp);
            JSONObject jsonObject = JSON.parseObject(resp);
            if (Objects.nonNull(resp) && jsonObject.getString("Status").equals("200")) {
                return JsonResult.buildSuccess(resp);
            } else {
                return JsonResult.buildFail(new MessageException("err.access"));
            }
        } catch (MessageException e) {
            log.error("exportData error", e);
            return JsonResult.buildFail(e.getMessage());
        }
    }

    @ApiOperation(value = "导出文件结果轮询获取", httpMethod = "POST", notes = "导出文件结果轮询获取")
    @RequestMapping(value="/export-data/loop-get", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<DataMapExportResultTO> getExportDataResult(@RequestBody DataMapExportForm form) {
        try {
            String value = redisServUtils.getStringById(Constants.RedisKey.DATA_CALLBACK, form.getDocumentId());
            return JsonResult.buildSuccess(new DataMapExportResultTO(form.getDocumentId(), value));
        } catch (Exception e) {
            log.error("导出文件结果轮询获取异常, documentId: {}", form.getDocumentId(), e);
            return JsonResult.buildFail(e.getMessage());
        }

    }

    @ApiOperation(value = "根据mapNo查询原始文档", httpMethod = "POST", notes = "根据图谱编号查询关联的原始文档信息")
    @RequestMapping(value="/original-docs", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<List<DataMapOriginalDocTO>> getOriginalDocsByMapNo(@RequestBody DataMapForm form) {
        try {
            List<DataMapOriginalDocTO> result = dataMapBusinessService.getOriginalDocsByMapNo(form.getMapNo());
            return JsonResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("根据mapNo查询原始文档异常, mapNo: {}", form.getMapNo(), e);
            return JsonResult.buildFail(e.getMessage());
        }
    }

    @ApiOperation(value = "图谱试用申请", httpMethod = "POST", notes = "为预付费、后付费客户申请图谱试用权益")
    @RequestMapping(value="/trial/apply", method= RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> applyDataMapTrial() {
        try {
            benefitBusinessService.applyDataMapTrial();
            return JsonResult.buildSuccess();
        } catch (MessageException e) {
            log.warn("图谱试用申请异常", e);
            return JsonResult.buildFail(e.getMessage());
        }
    }
}
