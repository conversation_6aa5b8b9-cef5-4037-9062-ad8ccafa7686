package com.qcc.pro.modules.person.controller;

import com.backend.common.global.gateway.EntityDetailCommonForm;
import com.backend.common.global.gateway.GatewayInvoker;
import com.backend.common.global.gateway.person.PersonCommonForm;
import com.backend.common.modules.report.service.CommTblCompReportOrderService;
import com.backend.common.yunjuapi.CompanyDetailsInterface;
import com.backend.common.yunjuapi.model.*;
import com.qcc.frame.commons.Constants;
import com.qcc.frame.jee.commons.model.PageDataBO;
import com.qcc.frame.jee.commons.model.json.JsonResult;
import com.qcc.frame.jee.commons.model.json.JsonResultList;
import com.qcc.frame.jee.commons.service.MessageException;
import com.qcc.frame.jee.commons.utils.I18NUtil;
import com.qcc.frame.jee.commons.web.BaseController;
import com.qcc.frame.jee.modules.sys.utils.UserUtils;
import com.qcc.pro.modules.search.form.ProCorpSearchForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * added for v1.0.6 KNZT-336
 */
@Api(tags = "人员解锁后详情")
@Controller
@RequestMapping(value = "${adminPath}/person_info/")
public class PersonInfoController  extends BaseController {

    @Autowired
    private CommTblCompReportOrderService commTblCompReportOrderService;

    private static final String SELF_RISK = "selfRisk";
    private static final String PERSON_INFO = "personInfo";

    @ApiOperation(value = "获取企业董监高人员的对外投资、在外任职及担任法定代表人的公司信息", httpMethod = "POST", notes = "获取企业董监高人员的对外投资、在外任职及担任法定代表人的公司信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "0：担任法定代表人；1：对外投资；2：在外任职", paramType = "query"),
            @ApiImplicitParam(name = "pageIndex", value = "企业keyNo", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "企业keyNo", paramType = "query")})
    @RequestMapping(value = "/listSeniorPerson", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listSeniorPerson(@RequestBody PersonCommonForm form) {
        // updated for v2.2.8 fengsw KNZT-7689
        try {
            String personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            if (StringUtils.isBlank(personId)) {
                return JsonResultList.buildFail("Locked Information");
            }
            form.setPersonId(personId);
            PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listSeniorPersonCorp", form);
            commTblCompReportOrderService.handleSeniorPersonInfo(pageDataBO);
            return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
        } catch (MessageException e) {
            return JsonResultList.buildFail(e);
        }
    }

    /**
     * added for v1.2.2 KNZT-1045
     */
    @ApiOperation(value = "企业高管风险统计", httpMethod = "POST", notes = "企业高管风险统计")
    @RequestMapping(value = "getPersonScanningCount", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<PersonScanningCountTO> getPersonScanningCount(@RequestBody ProCorpSearchForm form) {
        JsonResult<PersonScanningCountTO> result = new JsonResult<>();
        try {
            String personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            if (StringUtils.isBlank(personId)) {
                result.setStatus(Constants.Result.FALSE_STR);
                result.setMsg("Locked Information");
                return result;
            }
            PersonScanningCountTO penaltyCountTO = CompanyDetailsInterface.getPersonScanningCount(personId, SELF_RISK);
            result.setResult(penaltyCountTO);
            result.setStatus(Constants.Result.SUCCESS_STR);
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage(), e.getArgs()));
        }
        return result;
    }

    @ApiOperation(value = "获取企业董监高人员任职履历", httpMethod = "POST", notes = "获取企业董监高人员任职履历")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "query"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "query")})
    @RequestMapping(value = "/listPersonJob", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<PersonJob> listPersonJob(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PersonJob> result = new JsonResultList<>();
        String personId = null;
        try {
            personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
        } catch (MessageException e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e));
            return result;
        }
        if (StringUtils.isBlank(personId)) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg("Locked Information");
            return result;
        }
        PersonJobResult personJobResult = CompanyDetailsInterface.listPersonJob4Global(personId, form.getKeyNo(), form.getRoleType(), form.getExitStatus(), form.getCompanyStatus(), form.getIsRisk(), form.getIsCoreCompany(),
                form.getJobType(), form.getProvince(), form.getIndustry(), form.getStockPercent(), form.getSortField(), form.getIsSortAsc(), form.getPageIndex(), form.getPageSize(), null);
        if (personJobResult != null) {
            result.setResultList(personJobResult.getResultList());
            result.setTotalCount(personJobResult.getTotalCount());
            // 增加统计计数展示
            result.setGroupItems(personJobResult.getGroupItems());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取企业董监高人员关联公司总数", httpMethod = "POST", notes = "获取企业董监高人员关联公司总数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "query"),
            @ApiImplicitParam(name = "pageIndex", value = "pageIndex", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "pageSize", paramType = "query")})
    @RequestMapping(value = "/getPersonRelaCorpCount", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<PersonJob> listPersonRelaCorpNum(@RequestBody ProCorpSearchForm form) {
        JsonResultList<PersonJob> result = new JsonResultList<>();
        PersonJobResult personJobResult = CompanyDetailsInterface.listPersonJob4Global(form.getPersonId(), form.getKeyNo(), form.getRoleType(), form.getExitStatus(), form.getCompanyStatus(), form.getIsRisk(), form.getIsCoreCompany(),
                form.getJobType(), form.getProvince(), form.getIndustry(), form.getStockPercent(), form.getSortField(), form.getIsSortAsc(), form.getPageIndex(), form.getPageSize(), "onlyQueryCount");
        if (personJobResult != null) {
            result.setTotalCount(personJobResult.getTotalCount());
        }
        result.setStatus(Constants.Result.SUCCESS_STR);
        return result;
    }

    @ApiOperation(value = "获取高管合作伙伴", httpMethod = "POST", notes = "获取高管合作伙伴")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单表的id", paramType = "query")})
    @RequestMapping(value = "/listPersonPartners", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listPersonPartners(@RequestBody EntityDetailCommonForm form) {
        try {
            String personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getOrderId());
            if (StringUtils.isBlank(personId)) {
                return JsonResultList.buildFail("Locked Information");
            }
            form.setKeyNo(personId);
            PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listPersonPartners", form);
            return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
        } catch (MessageException e) {
            return JsonResultList.buildFail(e);
        }
    }

    @ApiOperation(value = "获取高管合作伙伴-链路", httpMethod = "POST", notes = "获取高管合作伙伴-链路")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "订单表的id", paramType = "query"),
            @ApiImplicitParam(name = "connectPersonId", value = "关联人员keyNo", paramType = "query"),
            @ApiImplicitParam(name = "compKeyNo", value = "代表性企业keyNo", paramType = "query")})
    @RequestMapping(value = "/getPersonPartnerPath", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<RelationShipsData> getPersonPartnerPath(@RequestBody PersonalPartnerPathForm form) {
        JsonResultList<RelationShipsData> result = new JsonResultList<>();
        try {
            String personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getId());
            if (StringUtils.isBlank(personId)) {
                result.setStatus(Constants.Result.FALSE_STR);
                result.setMsg("Locked Information");
                return result;
            }
            List<RelationShipsData> dataList = CompanyDetailsInterface.getPersonPartnerPath(personId, form.getConnectPersonId(), form.getCompKeyNo());
            result.setResultList(dataList);
            result.setStatus(Constants.Result.SUCCESS_STR);
        } catch (Exception e) {
            result.setStatus(Constants.Result.FALSE_STR);
            result.setMsg(I18NUtil.getMessage(e.getMessage()));
        }
        return result;
    }


    @ApiOperation(value = "获取高管合作伙伴-合作详情", httpMethod = "POST", notes = "获取高管合作伙伴-合作详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", paramType = "body"),
            @ApiImplicitParam(name = "targetKeyNo", value = "目标人员keyno", paramType = "body"),
            @ApiImplicitParam(name = "companyKeyNo", value = "默认关联公司", paramType = "body"),
            @ApiImplicitParam(name = "type", value = "1：正在合作 2：既往合作 0：历史合作", paramType = "body"),
            @ApiImplicitParam(name = "isValid", value = "0：无效 1：有效", paramType = "body"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "body"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "body")})
    @RequestMapping(value = "/listPersonPartnerCooperateDetail", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultList<?> listPersonPartnerCooperateDetail(@RequestBody EntityDetailCommonForm form) {
        try {
            String personId = commTblCompReportOrderService.getCorpNameAndCreditCodeAndPersonNameByIdAndCompanyId(UserUtils.getUserCompanyId(), form.getOrderId());
            if (StringUtils.isBlank(personId)) {
                return JsonResultList.buildFail("Locked Information");
            }
            form.setKeyNo(personId);
            PageDataBO<?> pageDataBO = GatewayInvoker.postJson4Page("/api/global/person/listPersonPartnerCooperateDetail", form);
            return JsonResultList.buildSuccess(pageDataBO.getList(), pageDataBO.getTotal());
        } catch (MessageException e) {
            return JsonResultList.buildFail(e);
        }
    }
}
