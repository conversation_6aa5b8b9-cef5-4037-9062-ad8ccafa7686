#=============================#
#===== Database settings =====#
#=============================#

#mysql database setting
jdbc.type=mysql
#============================#
#===== System settings ======#
#============================#
productName=\u4F01\u67E5\u67E5\u4E13\u4E1A\u7248
copyrightYear=2017-
version=V1.0

qcc.company.id=2bf0dc2458dc4610b3d0f75926eb296d
#qcc.sysadmin.id=1
qcc.sysadmin.role.id=6c62b497994admin
#qcc.office=1
qcc.menu.root=000root
qfk.contact.phone=


# \u6F14\u793A\u6A21\u5F0F: \u4E0D\u80FD\u64CD\u4F5C\u548C\u4FDD\u5B58\u7684\u6A21\u5757\uFF1A sys: area/office/user/role/menu/dict, cms: site/category
demoMode=false

qcc.mobile.verify.code.content=\u3010\u4F01\u67E5\u67E5\u3011\u60A8\u7684\u9A8C\u8BC1\u7801\u662F%s\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u77ED\u4FE1
qcc.mobile.verify.code.content.notice.func.auth=\u3010\u4F01\u67E5\u67E5\u3011\u60A8\u7684\u6388\u6743\u7801\u5DF2\u53D1\u9001\u7ED9%s\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u77ED\u4FE1
qcc.mobile.verify.code.content.func.auth=\u3010\u4F01\u67E5\u67E5\u3011%s\u7684\u6388\u6743\u7801\u662F%s\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u77ED\u4FE1
qcc.mobile.msg.sign=\u3010\u4F01\u67E5\u67E5\u4E13\u4E1A\u7248\u3011

#\u7BA1\u7406\u57FA\u7840\u8DEF\u5F84, \u9700\u540C\u6B65\u4FEE\u6539\uFF1Aweb.xml
adminPathWx=/a
adminPath=/saas
loginPath=/a/login

#\u5206\u9875\u914D\u7F6E
page.pageSize=10

#\u7855\u6B63\u7EC4\u4EF6\u662F\u5426\u4F7F\u7528\u7F13\u5B58
#supcan.useCache=false


#============================#
#==== Framework settings ====#
#============================#

#\u4F1A\u8BDD\u8D85\u65F6\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C 20m=1200000ms, 30m=1800000ms, 60m=3600000ms
session.sessionTimeout=1800000
#\u4F1A\u8BDD\u6E05\u7406\u95F4\u9694\u65F6\u95F4\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C2m=120000ms\u3002
session.validation.interval=600000
#second
redis.session.expire=1800
#7 weeks
cookie.rememberme.maxAge=604800

#\u7D22\u5F15\u9875\u8DEF\u5F84
web.view.index=/a

#\u89C6\u56FE\u6587\u4EF6\u5B58\u653E\u8DEF\u5F84
web.view.prefix=/WEB-INF/views/
web.view.suffix=.jsp

#\u6700\u5927\u6587\u4EF6\u4E0A\u4F20\u9650\u5236\uFF0C\u5355\u4F4D\u5B57\u8282. 10M=10*1024*1024(B)=10485760 bytes\uFF0C\u9700\u540C\u6B65\u4FEE\u6539\uFF1Ackfinder.xml
web.maxUploadSize=268435456
web.maxUploadSize2=10485760


#unit:second
mobile.security.code.expiry.time.interval=60

application.type=webapi
application.type.job.createby=webapi
app.contextPath=webapi

project.env=${project.env}
#for sit env
qcc.web.context=/webapi


login.attempt.count=10
login.mfa.attempt.count=10

qfk.domain=${qfk.domain}
qcc.pro.interface.domain=${qcc.pro.interface.domain}
qcc.yunju.interface.domain=${qcc.yunju.interface.domain}
qcc.intranet.interface.domain=${qcc.intranet.interface.domain}
qcc.yunju.report.interface.domain=${qcc.yunju.report.interface.domain}
qcc.kyc.gateway.interface.domain=${qcc.kyc.gateway.interface.domain}
qcc.yunju.report.internal.interface.domain=${qcc.yunju.report.internal.interface.domain}
qcc.yunju.global.interface.domain=${qcc.yunju.global.interface.domain}
openApi.global.admin.qcc.com.domain=${openApi.global.admin.qcc.com.domain}
picture.upload.max.allowed.size=${picture.upload.max.allowed.size}
global.web.convert.interface.domain=${global.web.convert.interface.domain}


ueditor.upload.path.prefix=/upload/ueditor


#second session timeout
saas.login.session.timout=604800
#\u91CD\u590D\u767B\u5F55\u7684Access Token\u4F1A\u4FDD\u7559\u7684\u65F6\u95F4(\u91CD\u590D\u767B\u5F55\u65F6, \u9700\u8981\u5C06\u5931\u6548\u7684Access Token\u52A0\u5165\u5230\u5F3A\u5236\u767B\u51FA\u961F\u5217)
saas.logout.access.token.expiry.sec=3600
#\u767B\u5F55\u6210\u529F\u540E\u7528\u6237Access Token\u7684\u5931\u6548\u65F6\u95F4(\u524D\u63D0\u662F\u6CA1\u6709\u88ABRefresh)
saas.login.access.token.expiry.sec=604800
#\u4F01\u67E5\u67E5APP\u7528\u6237\u8BBF\u95EEToken\u5931\u6548\u65F6\u95F4
saas.login.qccuser.app.access.token.expiry.sec=86400
#\u524D\u7AEF\u8C03\u7528\u5237\u65B0Access Token\u7684\u95F4\u9694\u65F6\u95F4, \u6BCF\u9694\u4E00\u6BB5\u65F6\u95F4\u8981\u5237\u65B0\u4E00\u6B21Access Token
saas.login.access.token.refresh.interval.sec=0
#\u88AB\u5237\u65B0\u7684Access Token\u7684\u4FDD\u7559\u65F6\u95F4, (\u5F53Access Token\u88AB\u5237\u65B0\u65F6, \u4E4B\u524D\u7684Access Token\u4F1A\u4FDD\u7559\u4E00\u6BB5\u65F6\u95F4, \u4EE5\u9632\u5E76\u53D1\u64CD\u4F5C)
saas.login.access.token.refresh.expiry.interval.sec=180
#Refresh Token\u7684\u5931\u6548\u65F6\u95F4, \u8BBE\u7F6E\u8DDF\u7528\u6237Access Token\u7684\u5931\u6548\u65F6\u95F4\u4E00\u81F4
saas.login.refresh.token.expiry.sec=36000
saas.login.name.aes.key=5d5FO3VBgiRZS1nrR4jEiA==
saas.login.name.aes.iv=qtxhaj622y8oajd0
saas.login.name.sign.key=f80282e34093f52
#MFA Token Expire Seconds
saas.login.mfa.token.expiry.sec=600
saas.hk.searcher.info.aes.key=68k8NEZ2ok1YL7ouOX6cFQ==
saas.hk.searcher.info.aes.iv=rs2bzrjzkkrjk273
common.aes.key=GB2v012SF+5uWFp4cEC0yA==
common.aes.iv=aijoypuknk6va5t8

#menu show mode(module:module show eg:qcc pro, tree:tree menu eg:crm)
menu.show.mode=module

#\u4F01\u67E5\u67E5 key \u548C secretKey
pro.qcc.com.key=${pro.qcc.com.key}
pro.qcc.com.secretKey=${pro.qcc.com.secretKey}
#\u5F00\u653E\u5E73\u53F0 key \u548C secretKey
openApi.qcc.com.key=${openApi.qcc.com.key}
openApi.qcc.com.secretKey=${openApi.qcc.com.secretKey}

openApi.global.qcc.com.key=${openApi.global.qcc.com.key}
openApi.global.qcc.com.secretKey=${openApi.global.qcc.com.secretKey}
openApi.global.admin.qcc.com.key=${openApi.global.admin.qcc.com.key}
openApi.global.admin.qcc.com.secretKey=${openApi.global.admin.qcc.com.secretKey}
#AES\u52A0\u89E3\u5BC6 \u5BC6\u94A5
encdecryption.key=zPd8o1EzXBWLcyO4
encdecryption.secretKey=oP8rkCJpAJNSCFVnCB4GeQ==

enctypt.db.jdbc.password=${database.jdbc.password}

mail.host=${mail.smtp.host}
mail.port=${mail.smtp.port}
mail.auth=${mail.smtp.auth}
mail.timeout=25000
mail.username=${mail.smtp.username}
mail.password=${mail.smtp.password}

oss.internal.domail.url=${oss.internal.domail.url}
oss.external.domail.url=${oss.external.domail.url}
oss.accesskey=${oss.accesskey}
oss.accesskeysecret=${oss.accesskeysecret}
oss.bucket=${oss.bucket}

obs.domail.url=${obs.domail.url}
obs.accesskey=${obs.accesskey}
obs.secretaccesskey=${obs.secretaccesskey}
obs.bucket=${obs.bucket}

#unit:second
email.security.code.expiry.time.interval=300

userfiles.basedir=${app.qcc.upload.dir}
###15995460586,13776098447
exception.notify.dingtalk.mobile=15995460586,13776098447
exception.notify.dingtalk.accesstoken=${exception.notify.dingtalk.accesstoken}

front.rsa.public.key=MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAnE2eKTiporQjVOOEtvS45BfRrlitE6EdfzXtWGqp4GJhR+D34l/5KcoI6Sdfs+6JOMO1Y0KIWpebQcXiH8qAaGgplrYwl5TjwBsYnFTkS/xBG3gIlyYBfnVmTOD00cpmaTBTocfWavoQM5+Y5NBPziYIButt5SU4i31uilaDWiauTyDYEjUP5nuoPIaboOebvLo19hO4s+zmYOE/NxPlm8fs8Dvqn961fX1Aj5WmdOvvYKQzFg246SAbjJ87vMJx4fFHEageJucJLP7tbQvUm70AgYXwjagbJWKSDYqCxfKA4DF9rPFCrtYvN0k4o0a9g6Z3C+Pnz666+tyWZGo2uFe2311+vR9ZXEi/vFhwV9aNb3XWWpOj+kB8UtpnFOcWNKBCaNEqguLmQlQwCiD6ugdYULldeKjfzW7v7BAHmENNxo8gNasRjJv5M0A7+kPN06ff2pt1wYvWiAM3hiYRXouPROI/DeIrCALzjFRtneONqygTA+cD2f/B8qtDxsX7AgMBAAE=
front.rsa.private.key=MIIG/gIBADANBgkqhkiG9w0BAQEFAASCBugwggbkAgEAAoIBgQCcTZ4pOKmitCNU44S29LjkF9GuWK0ToR1/Ne1YaqngYmFH4PfiX/kpygjpJ1+z7ok4w7VjQohal5tBxeIfyoBoaCmWtjCXlOPAGxicVORL/EEbeAiXJgF+dWZM4PTRymZpMFOhx9Zq+hAzn5jk0E/OJggG623lJTiLfW6KVoNaJq5PINgSNQ/me6g8hpug55u8ujX2E7iz7OZg4T83E+Wbx+zwO+qf3rV9fUCPlaZ06+9gpDMWDbjpIBuMnzu8wnHh8UcRqB4m5wks/u1tC9SbvQCBhfCNqBslYpINioLF8oDgMX2s8UKu1i83STijRr2DpncL4+fPrrr63JZkaja4V7bfXX69H1lcSL+8WHBX1o1vddZak6P6QHxS2mcU5xY0oEJo0SqC4uZCVDAKIPq6B1hQuV14qN/Nbu/sEAeYQ03GjyA1qxGMm/kzQDv6Q83Tp9/am3XBi9aIAzeGJhFei49E4j8N4isIAvOMVG2d442rKBMD5wPZ/8Hyq0PGxfsCAwEAAQKCAYAyofowREi/ijc/0ow1cEVMrmKgZd17VF/ny6JdWAA7zeLRlBD45joizW3hiX25HfaZytxBkVX2ctI6lk3QGptKNyPxzIclM3grzRtF2UIzUz4MFfBCdm9ZddJz6ROlujk+rgcJi2DsOqFw96xhwxF3KF7FTxLEfjA5D6L1cV0PoSQqQet8ym+iLECtirVzhFOw6Wl9k5nrvzxJ+e2ue+WPd05L7DzvGQgV5LM7UDEiAyQCXG6oID3cVyhVf2esmBgkrvPZiIlGcZDAKI+qrAkyQo76fpoe3kSNihS/8qP5B/gPrz6BdakvC2OemnYdDg9Td/ZJGwNRYWGwIebSu+nj5EIqeumZ7oJKP34338JpPIZr8UOjkc6FZKDMx3QlBgU+nnrKRjXOvcmSzD/LJVZpeJ0VBmMNRHWhEVoNZJW1WD/c0kmMUvr6GDvNXc4J48WpF6bJqOvWTlDu0/l0TCojwRpYqzUGogWa5gFOA3+bzUpv/UjJ7f7v7ER//f5XInECgcEA7QYiDeoMO55IIvZ48X6dKsJksB+Clvbsg0CALSJDeqo3LJymYULllYbECbhr+tbOIaYT/BmMCNMaWti8M50TiS4EDlSElHkDzlU1UXq0O8yWWUJpKJf+GmlPL+LqldzyK9n+zBJysXs/9ihm9jQSvIr/4Z/VjGnyfyOEBwTIC9GaSUX+3QfDwza1vMZkA70VdEPnjgRaEZMqmKuyC8vCO0uaCGVZMZ3smX2NxzIlKgQX0TvPFVkbSx56Oz5VxPhtAoHBAKjRF400ItyDurlShRWQsOPZrJ1ccBF1BIcXKmEBY9XDB4FIODZuo6f6eKKgrwxgD8cC5egaRspYrzKNw810jIE03SghytsEMbUxJ04SKUxn7azvTTTPtL1vo/egMkh2FZrOG/SEpM1uNKDAN6L5BKfDHyMEgApyKhX+f9q9BizxKwYGtUV7l4XgxFkO5IgS0flopmgimxsYrw5q4kCGdjU52u12Y9k9RXBJS8WbSBlsinSo5dA64004MSoFzV0HBwKBwQC0XoZrsD++3qRmdslyfLd9YpDcth3aY+13tA3WSsyu+/kgqdSzDwVG1Fc6Tm1dZTlXwdpTbd1ymbLkjyoo3M+RBMNxZD+//Jc43P7GEmo1V2rSYgdELOXhk2H/Pt7lTE29sRaElUVSakgyOmyfzuCzOdOfDtKjF7zBo3VFIg5ZcBfIyss3yEDgvTFvhkl1zt4Uq9f56rXfye4QYBgNgfE8zjtZSEany1XROAo8G5zMbHDzeGAODtEGSRsbGkSKr8ECgcB2RxEmOJoYQB8qStu9kUOuUEt1cVd/k6oZcCycDJpDIcL3XH8t+/Sj84RkBQrAM+m5+JjWNtrUWLBfTh4arAbpW/17abm0G8sr9QVh2K2gv8wxdvR0BPbQJxENpOq2BC386V2CwxCocgdjfWk9wRT26Wm+Yh/Hsdc007wGqJtUD6CiNz74MLCn9yP6dcm+YNlG0oCMw3pC0NeN1Wsx5EOb5S4zY9hvU1N8aY4yJCquzXJwbAmgDDJHXxBrLeeFvy8CgcEAjixnpYqNHCqkQY87SSY6SAJZKhECPVpKcCh8riTMeXX4IEALS+OfzHYG3jC3pFXC7jeXMBb3Yo/E/SFpWHkhA9G0TepYN+lLNSKXsoqiRgYaxoDTDhgsmdpH/I850R1iXSoAkr0aE8nOrGePwmNr+xs89MymY1qN/FV4jB8w4F/MsdiJW1XgJ7ivA6xfVW0jVA/nd5ILYYB+8JKftjkpIaQJNaZQCfAD8E3V1f1QxE1owDwlO/xfB+DwS6hQkiXq
verify.rsa.public.key=MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAyNGdVu2+iOAPWSeSm+oPcMN0Wn5Wx19rucAJKY/RN01x2QPiu36IF4o3zCVEEPIMQ2RX6TqMtZ5vpzhDNk7C5Y8cokozXDsEEikHS3oEBh1LMa1IgJi8Nh18FhdnH5E2HtUUunfsjdKK8oHSCXjsoxDz4HASeOq2V1e/ZKUlhKBBsyknpUGdtMov2jlCMt+8M+pn9G47A2S1OEbietOk69N7m6YG2yJG/ZXkwMr4FII8jYKIy4KCx6ujd3x0qnz4qqg1AiiCzKdIy6fQbvjEigeYxbmaya9ZDosKqQbgNsF6b2YGSUJglqTDRh9cREO+AfFbHCR3xUbVfiETqYoP+8Dd5wS2AYXrsyP3jVQvv4I+APiIoJeNggcBviK4evz6AO03ed7PxtOwcO2OIr1F7N5lWDXxKz/fQTXROIuRcQ/v7gKq/U5e54TU1wPO0OAYKcWA+RCEGzPaQmXxhRsWNZugvYU5eI/zOWCORefHPjCe288I1CWrr3yic7e2DTKDAgMBAAE=
verify.aes.key=kctj3tl4+F0QgU8bI2EdKQ==

kzz.api.domain=${kzz.api.domain}
kzz.api.secret.key=${kzz.api.secret.key}
kzz.api.access.key=${kzz.api.access.key}

google.recaptcha.url=${google.recaptcha.url}
google.recaptcha.key=${google.recaptcha.key}
google.recaptcha.v3.key=${google.recaptcha.v3.key}
stripe.api.key=${stripe.api.key}
stripe.endpoint.secret=${stripe.endpoint.secret}
global.api.internal.key=${global.api.internal.key}
global.api.internal.secret.key=${global.api.internal.secret.key}
webjob.domain=${webjob.domain}
data.map.node.url=${data.map.node.url}
hs.base.url=${hs.base.url}

kafka.oversea.spider.bootstrap.servers=${kafka.oversea.spider.bootstrap.servers}
kafka.oversea.spider.client.id=global_webapi
kafka.oversea.spider.refresh.company.topic=${kafka.oversea.spider.refresh.company.topic}
kafka.oversea.spider.hk.report.refresh.topic=${kafka.oversea.spider.hk.report.refresh.topic}
kafka.oversea.spider.hk.report.buy.topic=${kafka.oversea.spider.hk.report.buy.topic}
kafka.oversea.spider.hk.ird.report.buy.topic=${kafka.oversea.spider.hk.ird.report.buy.topic}
kafka.oversea.spider.hk.ird.search.topic=${kafka.oversea.spider.hk.ird.search.topic}
kafka.oversea.spider.hk.ird.goods.topic=${kafka.oversea.spider.hk.ird.goods.topic}
kafka.oversea.spider.my.report.buy.topic=${kafka.oversea.spider.my.report.buy.topic}
kafka.oversea.spider.au.report.buy.topic=${kafka.oversea.spider.au.report.buy.topic}
kafka.oversea.spider.nz.report.buy.topic=${kafka.oversea.spider.nz.report.buy.topic}
kafka.oversea.spider.tw.report.buy.topic=${kafka.oversea.spider.tw.report.buy.topic}
kafka.oversea.spider.sg.report.buy.topic=${kafka.oversea.spider.sg.report.buy.topic}
kafka.oversea.spider.sg.fin.report.buy.topic=${kafka.oversea.spider.sg.fin.report.buy.topic}
kafka.oversea.spider.gb.basic.report.buy.topic=${kafka.oversea.spider.gb.basic.report.buy.topic}

google.api.key=${google.api.key}