<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd">

	<description>Spring MVC Configuration</description>

	<bean id="propertyConfigurer"
		class="com.qcc.frame.jee.commons.config.EncryptPropertyPlaceholderConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="true" />
		<property name="locations">
			<list>
				<value>classpath:qcc.properties</value>
			</list>
		</property>
	</bean>

	<!-- 使用Annotation自动注册Bean,只扫描@Controller -->
	<context:component-scan base-package="com.qcc.webapi.controller.**,com.qcc.pro.modules.**.controller"
		use-default-filters="false"><!-- base-package 如果多个，用“,”分隔 -->
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Controller" />
	</context:component-scan>


	<!-- 默认的注解映射的支持，org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping -->
	<mvc:annotation-driven>
		<mvc:message-converters register-defaults="true">
			<!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8" />
			</bean>
			<!-- 将Jackson2HttpMessageConverter的默认格式化输出为false -->
			<bean
				class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
				<property name="supportedMediaTypes">
					<list>
						<value>application/json;charset=UTF-8</value>
						<value>application/json</value>
						<value>text/html;charset=UTF-8</value>
						<value>text/html</value>
						<value>text/json;charset=UTF-8</value>
						<value>text/html</value>
					</list>
				</property>
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>
	
	<!-- 定义视图文件解析 -->
	<bean
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="${web.view.prefix}" />
		<property name="suffix" value="${web.view.suffix}" />
	</bean>

	<!-- 对静态资源文件的访问， 将无法mapping到Controller的path交给default servlet handler处理 -->
	<mvc:default-servlet-handler />

	<!-- 静态资源映射 -->
	<bean class="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping">
		<property name="urlMap">
			<map>
				<entry key="/resources/**" value="myResourceHandler" />
			</map>
		</property>
		<property name="order" value="100000" />
	</bean>
	<bean id="myResourceHandler" name="myResourceHandler"
		class="org.springframework.web.servlet.resource.ResourceHttpRequestHandler">
		<property name="locations" value="/resources/" />
		<property name="supportedMethods">
			<list>
				<value>GET</value>
				<value>POST</value>
			</list>
		</property>
	</bean>

	<!-- 定义无Controller的path<->view直接映射 -->
	<mvc:view-controller path="/" view-name="redirect:${web.view.index}" />

	<!-- 国际化资源文件 -->
	<bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basenames">
			<list>
				<value>messages</value>
			</list>
		</property>
		<property name="useCodeAsDefaultMessage" value="true"/>
	</bean>
	
	<bean id="localeResolver" class="org.springframework.web.servlet.i18n.SessionLocaleResolver">
		<property name="defaultLocale" value="zh_CN"/>
	</bean>

	<!-- 拦截器配置，拦截顺序：先执行后定义的，排在第一位的最后执行。 -->
	<mvc:interceptors>
		<!-- all: [/cachemgnt, /k8s, /p, /a, /open, /saas, /qcceditor, /v2, /qfkwechat, /swagger-resources] -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<mvc:exclude-mapping path="/cachemgnt/**" />
			<mvc:exclude-mapping path="/k8s/check/**" />
			<mvc:exclude-mapping path="/p/**" />
			<mvc:exclude-mapping path="${adminPathWx}/**"/>
			<mvc:exclude-mapping path="/open/**" />
            <mvc:exclude-mapping path="/plugin/**" />
            <mvc:exclude-mapping path="/qcceditor/**" />
			<mvc:exclude-mapping path="/qfkwechat/**" />
			<mvc:exclude-mapping path="/resources/**" />
			<mvc:exclude-mapping path="/doc/**" />
			<mvc:exclude-mapping path="/systemadmin/**" />
			<!-- swagger begin-->
			<mvc:exclude-mapping path="/v2/**" />
			<mvc:exclude-mapping path="/swagger-resources/**" />
			<mvc:exclude-mapping path="/doc.html" />	
			<mvc:exclude-mapping path="/webjars/**" />
			<!-- swagger end-->
			<bean class="com.qcc.pro.interceptor.AAInterceptor" />
		</mvc:interceptor>

		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<mvc:exclude-mapping path="/cachemgnt/**" />
			<mvc:exclude-mapping path="/k8s/check/**" />
			<mvc:exclude-mapping path="/p/**" />
			<mvc:exclude-mapping path="${adminPathWx}/**"/>
			<mvc:exclude-mapping path="/open/**" />
			<mvc:exclude-mapping path="/plugin/**" />
			<mvc:exclude-mapping path="/qcceditor/**" />
			<mvc:exclude-mapping path="/qfkwechat/**" />
			<mvc:exclude-mapping path="/resources/**" />
			<mvc:exclude-mapping path="/doc/**" />
			<!-- swagger begin-->
			<mvc:exclude-mapping path="/v2/**" />
			<mvc:exclude-mapping path="/swagger-resources/**" />
			<mvc:exclude-mapping path="/doc.html" />
			<mvc:exclude-mapping path="/webjars/**" />
			<!-- swagger end-->
			<bean class="com.qcc.pro.interceptor.RateLimitInterceptor" />
		</mvc:interceptor>
		
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<mvc:exclude-mapping path="/k8s/check/**" />
			<mvc:exclude-mapping path="${adminPathWx}/**"/>
			<mvc:exclude-mapping path="${adminPath}/**"/>
			<mvc:exclude-mapping path="/resources/**" />
			<mvc:exclude-mapping path="/doc/**" />
			<!-- swagger begin-->
			<mvc:exclude-mapping path="/v2/**" />
			<mvc:exclude-mapping path="/swagger-resources/**" />
			<mvc:exclude-mapping path="/doc.html" />	
			<mvc:exclude-mapping path="/webjars/**" />
			<!-- swagger end-->
			<bean class="com.qcc.common.interceptor.OpenInterceptor" />
		</mvc:interceptor>

    </mvc:interceptors>

	<bean
		class="com.qcc.frame.jee.modules.sys.interceptor.QccWebApiMappingExceptionResolver">
		<property name="exceptionMappings">
			<props>
				<prop key="java.lang.Throwable">error/500_json</prop>
			</props>
		</property>
		<property name="warnLogCategory">    
	        <value>com.qcc.frame.jee.modules.sys.interceptor.QccWebApiMappingExceptionResolver</value>  
	    </property>
	</bean>
	
	<!-- 上传文件拦截，设置最大上传文件大小 10M=10*1024*1024(B)=10485760 bytes -->
	<bean id="multipartResolver"
		class="com.qcc.frame.jee.modules.sys.interceptor.QccCommonsMultipartResolver">
		<property name="maxUploadSize" value="${web.maxUploadSize}" />
	</bean>
	<bean name="i18NUtil" class="com.qcc.frame.jee.commons.utils.I18NUtil"></bean>

	<!-- 服务初始化和销毁时回调, 用于初始化对象和关闭资源 -->
	<bean id="serverInitService" class="com.backend.common.service.system.ServerInitService" init-method="init" destroy-method="close">

	</bean>

	<import resource="extention/spring-mvc-*.xml" />
	<import resource="env/spring-mvc-${project.env}.xml" />

	<bean id="restTemplate" class="org.springframework.web.client.RestTemplate" />
	
	
	
</beans>