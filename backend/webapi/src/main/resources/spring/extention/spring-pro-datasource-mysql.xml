<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-4.0.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd"
	default-lazy-init="true">

	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
	    <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
        <property name="basePackage" value="com.qcc.frame.jee.modules.sys.dao;com.backend.common.mapper;com.backend.common.modules.**.mapper" />
        
    </bean>
    <!-- Define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="typeAliasesPackage" value="com.qcc.frame.jee.modules.sys.entity" />
		<property name="typeAliasesSuperType"
			value="com.qcc.frame.jee.commons.persistence.BaseEntity" />
        <property name="configLocation" value="classpath:/mybatis-config.xml" />
    </bean>

	<!-- 定义事务 -->
	<bean id="transactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>

	<!-- 配置 Annotation 驱动，扫描@Transactional注解的类定义事务 -->
	<!-- <tx:annotation-driven transaction-manager="transactionManager"
		proxy-target-class="true" /> -->
	
	<tx:advice id="txAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="readOnly*" propagation="SUPPORTS" /><!-- 表示当前方法不需要事务上下文，但是如果存在当前事务的话，那么该方法会在这个事务中运行, 还有相关控制参考DataAuditMonitor -->
			<tx:method name="newTran*" propagation="REQUIRES_NEW" rollback-for="java.lang.Exception" />
			<tx:method name="*" propagation="REQUIRED" rollback-for="java.lang.Exception" />
		</tx:attributes>
	</tx:advice>

	<aop:config>
		<aop:pointcut id="myPointcut"
			expression="((execution(public * com.qcc.frame.jee..*Service.*(..))) or (execution(public * com.qcc.pro..*Service.*(..)))) or ((execution(public * com.backend..*Service.*(..))))" />
		<aop:advisor advice-ref="txAdvice" pointcut-ref="myPointcut" />
	</aop:config>
	<!-- MyBatis end -->

	<!-- 数据源配置, 使用 BoneCP 数据库连接池 -->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource"
		init-method="init" destroy-method="close">
		<!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass -->
		<property name="driverClassName" value="${database.jdbc.driver}" />
		<property name="url" value="${database.jdbc.url}" />
		<property name="username" value="${database.jdbc.name}" />
		<property name="password" value="${database.jdbc.password}" />
		<property name="initialSize" value="${database.jdbc.pool.init}" />
		<property name="minIdle" value="${database.jdbc.pool.min.idle}" />
		<property name="maxActive" value="${database.jdbc.pool.max.active}" />
		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="60000" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />
		<property name="validationQuery" value="${database.jdbc.test.sql}" />
		<property name="testWhileIdle" value="true" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
<!--		<property name="usePingMethod" value="false" />-->
		<!-- 打开PSCache，并且指定每个连接上PSCache的大小（Oracle使用） <property name="poolPreparedStatements" 
			value="true" /> <property name="maxPoolPreparedStatementPerConnectionSize" 
			value="20" /> -->
		<!-- 配置监控统计拦截的filters -->
		<property name="filters" value="stat" />
	</bean>
		
</beans>