<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-4.0.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd"
	default-lazy-init="true">

	<description>Spring Configuration</description>

	<!-- 加载配置属性文件
	<context:property-placeholder
		ignore-unresolvable="true" location="classpath:qcc.properties" /> -->
		
	<bean id="propertyConfigurer"
		class="com.qcc.frame.jee.commons.config.EncryptPropertyPlaceholderConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="true" />
		<property name="locations">
			<list>
				<value>classpath:qcc.properties</value>
			</list>
		</property>
	</bean>

	<!-- 加载应用属性实例，可通过 @Value("#{APP_PROP['jdbc.driver']}") String jdbcDriver 
		方式引用
	<util:properties id="APP_PROP" location="classpath:qcc.properties"
		local-override="true" /> -->

	<!-- 使用Annotation自动注册Bean，解决事物失效问题：在主容器中不扫描@Controller注解，在SpringMvc中只扫描@Controller注解。 -->
	<context:component-scan
		base-package="com.qcc.common.global_config,com.qcc.frame.jee.commons.utils,com.qcc.frame.jee.commons.mq,com.qcc.frame.jee.modules.sys.service,com.backend.common.service,com.backend.common.modules.**,com.backend.common.mq,com.backend.common.overseamongo,com.qcc.pro.modules.**"><!-- base-package 如果多个，用“,”分隔 -->
		<context:exclude-filter type="annotation"
			expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	
	<aop:aspectj-autoproxy />


	<!-- 配置 JSR303 Bean Validator 定义 -->
	<bean id="validator"
		class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean" />

<!--	<bean id="cacheService" class="com.backend.common.service.CacheService" init-method="initCache">-->
<!--		-->
<!--	</bean>-->
	
	<bean id="threadPoolService" class="com.qcc.frame.jee.modules.sys.service.ThreadPoolService" init-method="initPool" destroy-method="closePool">
		<property name="poolMap">
			<map>
				<entry key="pool.access.log" value="5" />
			</map>
		</property>
	</bean>

	<bean id="qccMailSenderService" class="com.qcc.frame.jee.modules.sys.service.QccMailSenderService">
		<property name="javaMailSender" ref="javaMailSender"></property>
		<property name="fromMail" value="${mail.from}"></property>
	</bean>

	<bean id="javaMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="host" value="${mail.host}"></property>
		<property name="port" value="${mail.port}"></property>
		<property name="defaultEncoding" value="UTF-8"/>
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">${mail.auth}</prop>
				<prop key="mail.smtp.timeout">${mail.timeout}</prop>
				<prop key="mail.smtp.socketFactory.class">javax.net.ssl.SSLSocketFactory</prop>
			</props>
		</property>
		<property name="username" value="${mail.username}"></property>
		<property name="password" value="${mail.password}"></property>
	</bean>

<!--	<bean id="aliyunOssServUtils" class="com.qcc.frame.jee.commons.thirdparty_service.AliyunOssServUtils" init-method="initOss" destroy-method="closeOss">-->
<!--		<property name="ossInternalDomailUrl" value="${oss.internal.domail.url}"></property>-->
<!--		<property name="ossExternalDomailUrl" value="${oss.external.domail.url}"></property>-->
<!--		<property name="ossAccessKey" value="${oss.accesskey}"></property>-->
<!--		<property name="ossAccessKeySecret" value="${oss.accesskeysecret}"></property>-->
<!--		<property name="ossBucket" value="${oss.bucket}"></property>-->
<!--	</bean>removed v1.7.2 KNZT-2428-->


	<import resource="extention/spring-pro-datasource-mysql.xml" />
	<import resource="extention/spring-context-redis.xml" />
	<import resource="extention/spring-context-shorturl-mysql.xml" />
	<import resource="extention/spring-context-mongodb.xml" />
	<import resource="extention/spring-context-oversea-mysql.xml" />

</beans>